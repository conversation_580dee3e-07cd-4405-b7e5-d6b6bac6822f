id: Q188
query: >-
  Do we need different compliance approaches for public vs. private cloud?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/A.6.2.1"
overlap_ids: []
capability_tags:
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Mobile & Teleworking Controls"
    id: "ISO27001:2022/A.6.2.1"
    locator: "Annex A.6.2.1"
ui:
  cards_hint:
    - "Cloud deployment matrix"
  actions:
    - type: "start_workflow"
      target: "cloud_deployment_assessment"
      label: "Assess Deployment Type"
output_mode: "both"
graph_required: false
notes: "Private cloud may need traditional network controls; public cloud uses API-driven controls"
---
### 188) Do we need different compliance approaches for public vs. private cloud?

**Standard terms)**  
- **Teleworking & mobile (A.6.2.1):** ensures secure remote/cloud access.

**Plain-English answer**  
**Private cloud** often mimics on-prem controls (firewalls, VLANs). **Public cloud** relies on IAM, API logging, and provider-native security services. Map your ISMS controls to each environment accordingly.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.6.2.1

**Why it matters**  
Ensures consistent security posture across diverse platforms.

**Do next in our platform**  
- Run **Cloud Deployment Assessment** workflow.  
- Tag each workload as public or private in the **Cloud Register**.

**How our platform will help**  
- **[Workflow]** Environment-specific control templates.  
- **[Dashboard]** Compliance score by deployment type.

**Likely follow-ups**  
- How to manage hybrid-cloud control inheritance?  
- What network controls apply to public cloud?

**Sources**  
- ISO/IEC 27001:2022 Annex A.6.2.1  
