-- File: arioncomply-v1/db/migrations/0005_visualization_mapping_min.sql
-- Migration 0005: Visualization mappings for heatmaps and domains
-- Purpose: Define how assessment outcomes roll up into visual domains
--          (e.g., per ISO domain), using requirement-based mapping.
-- Tables:
--   - framework_visual_domains: catalog of domain codes and labels (global or org-specific)
--   - requirement_visual_map: many-to-many map from requirements to domains (with weights)
-- Usage:
--   - Compute per-domain scores/heatmaps from requirement/question results.
-- Note: FK to standard_requirements will be added after the standards_min migration.

BEGIN;

-- Visual domains (system-wide or org-specific overrides)
CREATE TABLE IF NOT EXISTS framework_visual_domains (
  domain_code text NOT NULL,
  label text NOT NULL,
  org_id uuid NULL, -- NULL means global
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now(),
  PRIMARY KEY (domain_code, COALESCE(org_id, '00000000-0000-0000-0000-000000000000'::uuid))
);

CREATE INDEX IF NOT EXISTS idx_fvd_org ON framework_visual_domains (org_id);
CREATE TRIGGER fvd_set_updated_at BEFORE UPDATE ON framework_visual_domains FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

ALTER TABLE framework_visual_domains ENABLE ROW LEVEL SECURITY;
CREATE POLICY fvd_select ON framework_visual_domains FOR SELECT USING (org_id IS NULL OR org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY fvd_insert ON framework_visual_domains FOR INSERT WITH CHECK (org_id IS NULL OR org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY fvd_update ON framework_visual_domains FOR UPDATE USING (org_id IS NULL OR org_id = app_current_org_id() OR app_has_role('admin')) WITH CHECK (org_id IS NULL OR org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY fvd_delete ON framework_visual_domains FOR DELETE USING (org_id IS NULL OR org_id = app_current_org_id() OR app_has_role('admin'));

-- Mapping requirements to visual domains (FK to requirements later)
CREATE TABLE IF NOT EXISTS requirement_visual_map (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NULL, -- NULL for global default mappings
  requirement_id uuid NOT NULL,
  domain_code text NOT NULL,
  weight numeric(5,2) DEFAULT 1.0,
  UNIQUE (COALESCE(org_id, '00000000-0000-0000-0000-000000000000'::uuid), requirement_id, domain_code)
);

CREATE INDEX IF NOT EXISTS idx_rvm_req ON requirement_visual_map (requirement_id);
CREATE INDEX IF NOT EXISTS idx_rvm_org ON requirement_visual_map (org_id);

ALTER TABLE requirement_visual_map ENABLE ROW LEVEL SECURITY;
CREATE POLICY rvm_select ON requirement_visual_map FOR SELECT USING (org_id IS NULL OR org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY rvm_insert ON requirement_visual_map FOR INSERT WITH CHECK (org_id IS NULL OR org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY rvm_update ON requirement_visual_map FOR UPDATE USING (org_id IS NULL OR org_id = app_current_org_id() OR app_has_role('admin')) WITH CHECK (org_id IS NULL OR org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY rvm_delete ON requirement_visual_map FOR DELETE USING (org_id IS NULL OR org_id = app_current_org_id() OR app_has_role('admin'));

COMMIT;
-- File: arioncomply-v1/db/migrations/0005_visualization_mapping_min.sql
