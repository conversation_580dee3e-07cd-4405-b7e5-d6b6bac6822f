id: Q200
query: >-
  How do we build internal expertise vs. relying on external consultants forever?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/7.2"
overlap_ids: []
capability_tags:
  - "Planner"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Competence & Awareness"
    id: "ISO27001:2022/7.2"
    locator: "Clause 7.2"
ui:
  cards_hint:
    - "Capability roadmap"
  actions:
    - type: "start_workflow"
      target: "capability_building"
      label: "Develop Roadmap"
    - type: "open_register"
      target: "skills_inventory"
      label: "View Skills Inventory"
output_mode: "both"
graph_required: false
notes: "Balance consultant use for heavy-lift tasks with staff training and mentorship"
---
### 200) How do we build internal expertise vs. relying on external consultants forever?

**Standard terms**  
- **Competence (ISO27001 Cl.7.2):** internal staff must be trained and qualified.

**Plain-English answer**  
Create a **Capability Roadmap**: target key roles (ISMS manager, D<PERSON>, security engineer), allocate budgets for certifications, pair staff with consultants on initial projects, then transition ownership internally.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 7.2

**Why it matters**  
Builds sustainable compliance with lower long-term costs.

**Do next in our platform**  
- Launch **Capability Building** workflow.  
- Audit current skills in the **Skills Inventory** register.

**How our platform will help**  
- **[Planner]** Gantt view of skill-development over 12 months.  
- **[Report]** Cost vs internal capacity analysis.

**Likely follow-ups**  
- When should we plan consultant off-boarding?  
- How to measure expertise maturity over time?

**Sources**  
- ISO/IEC 27001:2022 Clause 7.2  




