#!/usr/bin/env python3
"""
File: arioncomply-v1/ai-backend/python-backend/services/preprocessing/generate_mappings_cli.py
File Description: CLI tool to generate synonym/paraphrase mappings from v2 content
Purpose: Extract deterministic mappings from ingested content for preprocessing pipeline
Usage: python generate_mappings_cli.py --org-id ORG --content-path PATH [--output-dir DIR]
Dependencies: mapping_generator.py, v2 JSON content files
Security/RLS: Org-scoped mapping generation with proper tenant isolation
Notes: Integrates with ingestion pipeline; outputs JSON files for QueryPreprocessor
"""

import argparse
import sys
import os
import json
import time
from pathlib import Path
from typing import Optional

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from preprocessing.mapping_generator import create_mapping_generator, MappingGenerationResult


def main():
    """Main CLI interface for mapping generation."""
    parser = argparse.ArgumentParser(
        description="Generate deterministic mappings from v2 JSON content files",
        epilog="""
Examples:
  python generate_mappings_cli.py --org-id org123 --content-path ./content
  python generate_mappings_cli.py --org-id org123 --content-path ./content --output-dir ./mappings --verbose
  python generate_mappings_cli.py --org-id org123 --content-path ./content --export-format json --validate
        """
    )
    
    # Required arguments
    parser.add_argument(
        "--org-id",
        required=True,
        help="Organization ID for RLS/tenant isolation"
    )
    
    parser.add_argument(
        "--content-path", 
        required=True,
        help="Base path where v2 JSON content files are located"
    )
    
    # Optional arguments
    parser.add_argument(
        "--output-dir",
        default="./mappings_output",
        help="Directory to write mapping files (default: ./mappings_output)"
    )
    
    parser.add_argument(
        "--export-format",
        choices=["json", "yaml"],
        default="json",
        help="Export format for mapping files (default: json)"
    )
    
    parser.add_argument(
        "--validate",
        action="store_true",
        help="Validate generated mappings against UI catalog"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose output with detailed statistics"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Generate mappings but don't write output files"
    )
    
    parser.add_argument(
        "--request-id",
        help="Request ID for logging/tracing (optional)"
    )
    
    args = parser.parse_args()
    
    # Validate inputs
    if not validate_inputs(args):
        sys.exit(1)
    
    # Create mapping generator
    try:
        generator = create_mapping_generator(
            org_id=args.org_id,
            content_base_path=args.content_path
        )
        
        if args.verbose:
            print(f"Initialized mapping generator for org: {args.org_id}")
            print(f"Content base path: {args.content_path}")
            print(f"Output directory: {args.output_dir}")
        
    except Exception as e:
        print(f"Error initializing mapping generator: {e}", file=sys.stderr)
        sys.exit(1)
    
    # Generate mappings
    try:
        if args.verbose:
            print("Starting mapping generation...")
        
        start_time = time.time()
        result = generator.generate_mappings(request_id=args.request_id)
        generation_time = time.time() - start_time
        
        if args.verbose:
            print_detailed_results(result, generation_time)
        else:
            print_summary_results(result)
        
    except Exception as e:
        print(f"Error during mapping generation: {e}", file=sys.stderr)
        sys.exit(1)
    
    # Validate mappings if requested
    if args.validate:
        try:
            validation_result = validate_mappings(result, generator)
            if args.verbose:
                print_validation_results(validation_result)
            elif validation_result.get("errors"):
                print(f"Validation found {len(validation_result['errors'])} errors")
                
        except Exception as e:
            print(f"Error during validation: {e}", file=sys.stderr)
            # Continue execution, validation is optional
    
    # Export mappings unless dry-run
    if not args.dry_run:
        try:
            if args.verbose:
                print("Exporting mappings...")
            
            export_files = generator.export_mappings(result, args.output_dir)
            
            print("Mapping files exported:")
            for export_type, file_path in export_files.items():
                print(f"  - {export_type}: {file_path}")
                
        except Exception as e:
            print(f"Error exporting mappings: {e}", file=sys.stderr)
            sys.exit(1)
    else:
        print("Dry-run mode: mappings generated but not exported")
    
    # Exit with error code if there were processing errors
    if result.errors:
        print(f"\nGeneration completed with {len(result.errors)} errors")
        sys.exit(1)
    else:
        print("\nMapping generation completed successfully")


def validate_inputs(args) -> bool:
    """Validate CLI arguments and input paths."""
    
    # Check content path exists
    content_path = Path(args.content_path)
    if not content_path.exists():
        print(f"Error: Content path does not exist: {args.content_path}", file=sys.stderr)
        return False
    
    if not content_path.is_dir():
        print(f"Error: Content path is not a directory: {args.content_path}", file=sys.stderr)
        return False
    
    # Check for basic content structure
    has_content = False
    for item in content_path.iterdir():
        if item.is_dir():
            has_content = True
            break
    
    if not has_content:
        print(f"Warning: No subdirectories found in content path: {args.content_path}")
    
    # Check output directory can be created
    output_path = Path(args.output_dir)
    try:
        output_path.mkdir(parents=True, exist_ok=True)
    except Exception as e:
        print(f"Error: Cannot create output directory {args.output_dir}: {e}", file=sys.stderr)
        return False
    
    # Validate org_id format (basic check)
    if not args.org_id or len(args.org_id) < 3:
        print("Error: org-id must be at least 3 characters", file=sys.stderr)
        return False
    
    return True


def print_summary_results(result: MappingGenerationResult):
    """Print summary statistics for mapping generation."""
    
    stats = result.processing_stats
    
    print(f"Mapping Generation Results:")
    print(f"  Synonym mappings: {stats['synonym_mappings_generated']}")
    print(f"  Paraphrase mappings: {stats['paraphrase_mappings_generated']}")
    print(f"  Canonical ID mappings: {stats['canonical_id_mappings_generated']}")
    print(f"  Processing time: {stats['processing_time_seconds']:.2f} seconds")
    
    if result.errors:
        print(f"  Errors encountered: {len(result.errors)}")


def print_detailed_results(result: MappingGenerationResult, generation_time: float):
    """Print detailed statistics and sample mappings."""
    
    stats = result.processing_stats
    
    print("="*60)
    print("DETAILED MAPPING GENERATION RESULTS")
    print("="*60)
    
    print(f"Organization ID: {stats['org_id']}")
    print(f"Content directories processed: {stats['content_directories_processed']}")
    print(f"Total processing time: {generation_time:.3f} seconds")
    print()
    
    # Synonym mappings detail
    print(f"SYNONYM MAPPINGS: {len(result.synonym_mappings)}")
    if result.synonym_mappings:
        print("Sample synonyms:")
        for i, syn in enumerate(result.synonym_mappings[:3]):
            print(f"  {i+1}. '{syn.canonical_term}' -> {syn.synonyms}")
            print(f"     Confidence: {syn.confidence:.2f}, Context: {syn.context}")
        if len(result.synonym_mappings) > 3:
            print(f"  ... and {len(result.synonym_mappings) - 3} more")
    print()
    
    # Paraphrase mappings detail
    print(f"PARAPHRASE MAPPINGS: {len(result.paraphrase_mappings)}")
    if result.paraphrase_mappings:
        print("Sample paraphrases:")
        for i, para in enumerate(result.paraphrase_mappings[:3]):
            print(f"  {i+1}. {para.canonical_id}")
            print(f"     Original: '{para.original_question}'")
            print(f"     Paraphrases: {para.paraphrases[:2]}")  # Show first 2
            print(f"     Confidence: {para.confidence:.2f}")
        if len(result.paraphrase_mappings) > 3:
            print(f"  ... and {len(result.paraphrase_mappings) - 3} more")
    print()
    
    # Canonical ID mappings detail
    print(f"CANONICAL ID MAPPINGS: {len(result.canonical_id_mappings)}")
    if result.canonical_id_mappings:
        print("Sample ID mappings:")
        for i, can in enumerate(result.canonical_id_mappings[:3]):
            print(f"  {i+1}. {can.canonical_id}")
            print(f"     Alternatives: {can.alternative_ids[:3]}")  # Show first 3
            print(f"     Type: {can.content_type}, Title: {can.title[:50]}...")
        if len(result.canonical_id_mappings) > 3:
            print(f"  ... and {len(result.canonical_id_mappings) - 3} more")
    print()
    
    # Errors detail
    if result.errors:
        print(f"ERRORS ENCOUNTERED: {len(result.errors)}")
        for i, error in enumerate(result.errors[:5]):
            print(f"  {i+1}. {error}")
        if len(result.errors) > 5:
            print(f"  ... and {len(result.errors) - 5} more errors")
    else:
        print("No errors encountered during processing")


def validate_mappings(result: MappingGenerationResult, generator) -> dict:
    """Validate generated mappings against expected patterns and UI catalog."""
    
    validation_result = {
        "synonym_validation": {"valid": 0, "warnings": []},
        "paraphrase_validation": {"valid": 0, "warnings": []},
        "canonical_id_validation": {"valid": 0, "warnings": []},
        "errors": []
    }
    
    try:
        # Load UI catalog for validation
        ui_catalog = generator.load_ui_catalog()
        
        # Validate synonyms
        for syn in result.synonym_mappings:
            if syn.confidence > 0.3 and len(syn.synonyms) > 0:
                validation_result["synonym_validation"]["valid"] += 1
            else:
                validation_result["synonym_validation"]["warnings"].append(
                    f"Low confidence synonym: {syn.canonical_term}"
                )
        
        # Validate paraphrases
        for para in result.paraphrase_mappings:
            if para.confidence > 0.5 and len(para.paraphrases) > 0:
                validation_result["paraphrase_validation"]["valid"] += 1
            else:
                validation_result["paraphrase_validation"]["warnings"].append(
                    f"Low confidence paraphrase: {para.canonical_id}"
                )
        
        # Validate canonical IDs
        for can in result.canonical_id_mappings:
            if can.canonical_id and len(can.alternative_ids) > 0:
                validation_result["canonical_id_validation"]["valid"] += 1
            else:
                validation_result["canonical_id_validation"]["warnings"].append(
                    f"Invalid canonical ID mapping: {can.canonical_id}"
                )
        
        # Check against UI catalog if available
        if ui_catalog:
            # Validate action references in paraphrases
            for para in result.paraphrase_mappings:
                ui_actions = para.metadata.get('ui', {}).get('actions', [])
                for action in ui_actions:
                    action_type = action.get('type')
                    # Basic validation that action types are recognized
                    if action_type and action_type not in ['start_workflow', 'open_register', 'add_tracker']:
                        validation_result["errors"].append(
                            f"Unknown UI action type: {action_type} in {para.canonical_id}"
                        )
        
    except Exception as e:
        validation_result["errors"].append(f"Validation error: {str(e)}")
    
    return validation_result


def print_validation_results(validation_result: dict):
    """Print detailed validation results."""
    
    print("="*60)
    print("VALIDATION RESULTS")
    print("="*60)
    
    print(f"Synonyms: {validation_result['synonym_validation']['valid']} valid")
    if validation_result["synonym_validation"]["warnings"]:
        print(f"  Warnings: {len(validation_result['synonym_validation']['warnings'])}")
    
    print(f"Paraphrases: {validation_result['paraphrase_validation']['valid']} valid")
    if validation_result["paraphrase_validation"]["warnings"]:
        print(f"  Warnings: {len(validation_result['paraphrase_validation']['warnings'])}")
    
    print(f"Canonical IDs: {validation_result['canonical_id_validation']['valid']} valid")
    if validation_result["canonical_id_validation"]["warnings"]:
        print(f"  Warnings: {len(validation_result['canonical_id_validation']['warnings'])}")
    
    if validation_result["errors"]:
        print(f"Errors: {len(validation_result['errors'])}")
        for error in validation_result["errors"][:3]:
            print(f"  - {error}")


if __name__ == "__main__":
    main()