id: Q190
query: >-
  What if we use multiple cloud providers — how do we coordinate compliance?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/A.6.2.1"
  - "GDPR:2016/Art.28"
overlap_ids: []
capability_tags:
  - "Register"
  - "Workflow"
flags:
  - "LOCAL LAW_CHECK"
sources:
  - title: "ISO/IEC 27001:2022 — Teleworking Controls"
    id: "ISO27001:2022/A.6.2.1"
    locator: "Annex A.6.2.1"
  - title: "GDPR — Processor Contracts"
    id: "GDPR:2016/Art.28"
    locator: "Article 28"
ui:
  cards_hint:
    - "Multi-CSP coordination plan"
  actions:
    - type: "open_register"
      target: "cloud_csp_register"
      label: "View CSP List"
    - type: "start_workflow"
      target: "multi_csp_strategy"
      label: "Coordinate CSPs"
output_mode: "both"
graph_required: false
notes: "Align SLAs, incident response, and data-transfer safeguards across providers"
---
### 190) What if we use multiple cloud providers — how do we coordinate compliance?

**Standard terms)**  
- **Teleworking controls (A.6.2.1):** secure access across endpoints.  
- **Processor contracts (GDPR Art.28):** each provider is a separate processor.

**Plain-English answer**  
Maintain a **Cloud CSP Register** listing each provider’s roles, contracts, and controls. Harmonize SLAs (e.g., breach notification), centralize monitoring (SIEM), and apply consistent data-transfer safeguards.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.6.2.1; GDPR Article 28

**Why it matters**  
Prevents compliance gaps when responsibilities are fragmented.

**Do next in our platform**  
1. Populate the **CSP Register** with all providers.  
2. Run **Multi-CSP Strategy** workflow to align policies and SLAs.

**How our platform will help**  
- **[Register]** Central dashboard of provider responsibilities.  
- **[Workflow]** Automated cross-provider incident coordination.

**Likely follow-ups**  
- How to unify logging from all CSPs?  
- Can we use a single SCC template across providers?

**Sources**  
- ISO/IEC 27001:2022 Annex A.6.2.1; GDPR Article 28

**Legal note:** Verify each provider’s contract for local compliance clauses.  
