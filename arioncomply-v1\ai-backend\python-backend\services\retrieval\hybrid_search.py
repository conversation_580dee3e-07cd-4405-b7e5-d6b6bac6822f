"""
File: arioncomply-v1/ai-backend/python-backend/services/retrieval/hybrid_search.py
File Description: Hybrid retrieval client (ChromaDB + Supabase Vector)
Purpose: Search across both local ChromaDB and production Supabase Vector with fallback
Inputs: org_id, query_embedding, search strategy, filters
Outputs: Unified search results from best available source
Notes: Prefers ChromaDB for local dev, falls back to Supabase Vector for reliability
"""

import os
from typing import List, Dict, Any, Optional, Literal
from .supabase_vector import search as supabase_search
from ..ingestion.chromadb_client import chroma_client, CHROMA_ENABLED

SearchStrategy = Literal["chroma_first", "supabase_first", "chroma_only", "supabase_only"]

PREFERRED_STRATEGY = os.getenv("VECTOR_SEARCH_STRATEGY", "chroma_first")


def hybrid_search(
    org_id: str,
    query_embedding: List[float],
    limit: int = 8,
    min_score: Optional[float] = None,
    collection_name: str = "ariocomply-qa-dev",
    strategy: SearchStrategy = PREFERRED_STRATEGY,
    metadata_filter: Optional[Dict[str, Any]] = None
) -> List[Dict[str, Any]]:
    """
    Search hybrid vector stores with configurable strategy.
    
    Args:
        org_id: Tenant UUID for isolation
        query_embedding: Query vector to search with
        limit: Maximum results to return
        min_score: Optional score threshold
        collection_name: ChromaDB collection name (if using ChromaDB)
        strategy: Search strategy preference
        metadata_filter: Optional metadata filters
        
    Returns:
        List of chunks with unified format: {chunk_id, doc_id, score, text, metadata}
    """
    results = []
    
    # Strategy: ChromaDB first (local dev preference)
    if strategy in ["chroma_first", "chroma_only"] and CHROMA_ENABLED:
        try:
            results = chroma_client.search_chunks(
                org_id=org_id,
                collection_name=collection_name,
                query_embedding=query_embedding,
                limit=limit,
                where=metadata_filter
            )
            
            # Filter by score if specified
            if min_score is not None:
                results = [r for r in results if r.get("score", 0) >= min_score]
            
            if results or strategy == "chroma_only":
                return results[:limit]
                
        except Exception as e:
            print(f"[HybridSearch] ChromaDB search failed: {e}")
    
    # Fallback or primary: Supabase Vector
    if strategy in ["chroma_first", "supabase_first", "supabase_only"]:
        try:
            results = supabase_search(
                org_id=org_id,
                query_embedding=query_embedding,
                limit=limit,
                min_score=min_score
            )
            return results[:limit]
            
        except Exception as e:
            print(f"[HybridSearch] Supabase Vector search failed: {e}")
    
    # No results from any source
    return []


def get_search_source_info() -> Dict[str, Any]:
    """Get information about available search sources."""
    return {
        "chroma_enabled": CHROMA_ENABLED,
        "chroma_dir": os.getenv("CHROMA_DIR", "~/llms/chroma"),
        "supabase_vector_url": bool(os.getenv("VECTOR_SUPABASE_URL")),
        "preferred_strategy": PREFERRED_STRATEGY,
        "available_strategies": ["chroma_first", "supabase_first", "chroma_only", "supabase_only"]
    }