id: Q104
query: >-
  What if customers ask for compliance certifications we don't have yet?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "GDPR:2016/Recitals"
overlap_ids:
  - "ISO27001:2022/4.1"
capability_tags:
  - "Report"
flags: []
sources:
  - title: "GDPR Recitals — Trust Signals"
    id: "GDPR:2016/Recitals"
    locator: "Recitals"
  - title: "ISO/IEC 27001:2022 — Context & Scope"
    id: "ISO27001:2022/4.1"
    locator: "Clause 4.1"
ui:
  cards_hint:
    - "Certification roadmap"
  actions:
    - type: "start_workflow"
      target: "cert_roadmap"
      label: "Build Certification Plan"
output_mode: "both"
graph_required: false
notes: "Highlight roadmap and interim measures; use platform to track progress"
---
### 104) What if customers ask for compliance certifications we don't have yet?

**Standard terms)**  
- **Trust signals (Recitals):** certifications demonstrate maturity.  
- **Context (Cl. 4.1):** define current and target scope.

**Plain-English answer**  
Share your **certification roadmap** showing planned milestones and interim evidence (policies, attestations). Offer trial of your platform’s dashboards as proof of progress.

**Applies to**  
- **Primary:** GDPR Recitals; ISO/IEC 27001:2022 Clause 4.1

**Why it matters**  
Maintains customer confidence while you progress toward certification.

**Do next in our platform**  
- Launch **Certification Plan** workflow.  
- Populate roadmap with deliverables and dates.

**How our platform will help**  
- **[Report]** Roadmap Gantt chart.  
- **[Dashboard]** Progress vs. commitment.

**Likely follow-ups**  
- “Can we get interim letters of compliance?” (Platform can auto-generate attestation letters)

**Sources**  
- GDPR Recitals; ISO/IEC 27001:2022 Clause 4.1
