-- File: arioncomply-v1/db/migrations/0015_audit_read_view.sql
-- Migration 0015: Org-scoped audit read view for requests and events
-- Purpose: Provide a single, read-optimized view over api_request_logs and api_event_logs
-- Security/RLS: View reads are constrained by underlying table RLS; additionally, all selects must filter by org_id
-- Verify: SELECT count(*) FROM app_audit_logs_v1;

-- Recreate view idempotently
DROP VIEW IF EXISTS app_audit_logs_v1;

CREATE VIEW app_audit_logs_v1 AS
SELECT
  r.request_id,
  r.org_id,
  r.user_id,
  r.route,
  r.method,
  r.response_status AS status,
  r.trace_id,
  r.session_id,
  r.created_at AS ts,
  NULL::text AS event_type,
  NULL::text AS direction,
  NULL::jsonb AS event_details
FROM api_request_logs r
UNION ALL
SELECT
  e.request_id,
  e.org_id,
  e.user_id,
  NULL::text AS route,
  NULL::text AS method,
  NULL::int AS status,
  e.trace_id,
  e.session_id,
  e.created_at AS ts,
  e.event_type,
  e.direction,
  e.details AS event_details
FROM api_event_logs e;

-- Helpful indexes (safe if they already exist via underlying tables; here for completeness on view usage via PostgREST)
-- Note: Postgres cannot index views directly (unless materialized). Rely on underlying table indexes.

