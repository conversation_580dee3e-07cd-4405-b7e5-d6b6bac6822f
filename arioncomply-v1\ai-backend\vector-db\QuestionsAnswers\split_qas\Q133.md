id: Q133
query: >-
  What if a government requests our data and we're not sure if we should comply?
packs:
  - "GDPR:2016"
primary_ids:
  - "GDPR:2016/Art.29"
overlap_ids: []
capability_tags:
  - "Report"
  - "Workflow"
flags:
  - "LOCAL LAW CHECK"
sources:
  - title: "GDPR — Processing by Public Authorities"
    id: "GDPR:2016/Art.29"
    locator: "Article 29"
ui:
  cards_hint:
    - "Government request log"
  actions:
    - type: "open_register"
      target: "gov_requests"
      label: "View Requests"
    - type: "start_workflow"
      target: "request_assessment"
      label: "Assess Request"
output_mode: "both"
graph_required: false
notes: "Legal obligation vs. privacy rights may conflict—document and escalate"
---
### 133) What if a government requests our data and we're not sure if we should comply?

**Standard terms)**  
- **Public authority processing (GDPR Art. 29):** defines lawful basis for official requests.

**Plain-English answer**  
Log the request, verify authority in your **Government Request Log**, and perform a legal assessment. If in doubt, escalate to legal counsel. Document decisions and any disclosures.

**Applies to**  
- **Primary:** GDPR Article 29

**Why it matters**  
Ensures lawful disclosure and audit traceability.

**Do next in our platform**  
- Record request in **Gov Requests** register.  
- Run **Request Assessment** workflow.

**How our platform will help**  
- **[Tracker]** Captures request details, deadlines, and decisions.  
- **[Workflow]** Guides legal/ privacy review steps.

**Likely follow-ups**  
- “What constitutes a valid government order?” (Depends on jurisdiction **[LOCAL LAW CHECK]**)

**Sources**  
- GDPR Article 29

**Legal note:** Consult qualified counsel before disclosing.  
