id: Q105
query: >-
  Do we need different privacy notices for different types of customers?
packs:
  - "GDPR:2016"
primary_ids:
  - "GDPR:2016/Art.13"
overlap_ids: []
capability_tags:
  - "Draft Doc"
flags: []
sources:
  - title: "GDPR — Information to Data Subjects"
    id: "GDPR:2016/Art.13"
    locator: "Article 13"
ui:
  cards_hint:
    - "Notice version manager"
  actions:
    - type: "start_workflow"
      target: "notice_customization"
      label: "Customize Notices"
output_mode: "both"
graph_required: false
notes: "Tailor notices by region, service, or customer role"
---
### 105) Do we need different privacy notices for different types of customers?

**Standard terms)**  
- **Right to information (GDPR Art. 13):** details vary based on processing context.

**Plain-English answer**  
Yes—if processing differs by region, service, or customer segment, customize notices to reflect specific data uses, retention, and rights. Use umbrella notice with sub-sections or separate versions.

**Applies to**  
- **Primary:** GDPR Article 13

**Why it matters**  
Ensures transparency and compliance across all processing scenarios.

**Do next in our platform**  
- Use **Notice Customization** workflow.  
- Manage versions in versioning register.

**How our platform will help**  
- **[Draft Doc]** Multi-version notice generator.  
- **[Workflow]** Approval routing for each version.

**Likely follow-ups**  
- “How do we maintain consistency across versions?” (Use centralized content blocks)

**Sources**  
- GDPR Article 13
