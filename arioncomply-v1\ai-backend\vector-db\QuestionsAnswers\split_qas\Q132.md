id: Q132
query: >-
  How do we handle breach notification requirements across countries?
packs:
  - "GDPR:2016"
  - "NIS2:2023"
primary_ids:
  - "GDPR:2016/Art.33"
  - "NIS2:2023/Art.22"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Tracker"
flags:
  - "LOCAL LAW CHECK"
sources:
  - title: "GDPR — Notification of Personal Data Breach"
    id: "GDPR:2016/Art.33"
    locator: "Article 33"
  - title: "NIS2 Directive — Incident Notification"
    id: "NIS2:2023/Art.22"
    locator: "Article 22"
ui:
  cards_hint:
    - "Breach notification matrix"
  actions:
    - type: "open_register"
      target: "breach_requirements"
      label: "View Notification Rules"
    - type: "start_workflow"
      target: "breach_notification"
      label: "Notify Authorities"
output_mode: "both"
graph_required: false
notes: "Timelines and contact points vary by jurisdiction—default EU: 72 h, NIS2: 24 h"
---
### 132) How do we handle breach notification requirements across countries?

**Standard terms)**  
- **GDPR breach notification (Art. 33):** report to supervisory authority within 72 hours.  
- **NIS2 incident reporting (Art. 22):** notify relevant authority within 24 hours of awareness.

**Plain-English answer**  
Maintain a **Breach Notification Matrix** listing each country’s timelines (e.g., EU 72 h, UK 72 h, NIS2-member state 24 h). Use automated alerts to trigger workflows and ensure you have regional contacts.

**Applies to**  
- **Primary:** GDPR Article 33; NIS2 Directive Article 22

**Why it matters**  
Missing deadlines triggers fines and reputational damage.

**Do next in our platform**  
- Populate **Breach Requirements** register.  
- Kick off **Breach Notification** workflow on incident.

**How our platform will help**  
- **[Tracker]** Automated SLA timers per jurisdiction.  
- **[Workflow]** Templates for regulatory filings.

**Likely follow-ups**  
- “What info must we include in the notification?” (Details of breach, risk assessment)

**Sources**  
- GDPR Article 33; NIS2 Directive Article 22

**Legal note:** Consult local counsel for non-EU jurisdictions.  
