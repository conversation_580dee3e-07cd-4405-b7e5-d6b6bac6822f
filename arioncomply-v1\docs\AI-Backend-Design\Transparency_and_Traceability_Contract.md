# Transparency and Traceability Contract (MVP → Production)

Purpose
- Guarantee end-to-end, org-scoped auditability of every AI interaction, retrievable on demand, exportable, and safe for compliance reviews.

Scope
- Application (web/mobile) → Edge → AI Backend → DBs (app + vector) → Storage. Contract applies across all layers.

Identifiers (must be present and propagated)
- requestId: UUIDv4, unique per HTTP request (Edge-generated).
- sessionId: UUID/ULID for chat sessions (returned by conversation.start; attached to events).
- processId: ULID for multi-step workflows (optional in MVP; recommended by <PERSON>).
- correlationId: client-supplied, for cross-system tracing.
- orgId, userId: derived from JWT; required in all logs for RLS.

ID Issuance and Responsibility
- requestId: generated at Edge for every inbound request; returned in envelope; echoed by Backend.
- sessionId: issued by Backend on `conversation.start`; stored in app DB (`conversation_sessions.session_id`), returned to client; client must include on subsequent messages; Edge propagates.
- processId: issued by Backend (ULID) at start of long-running flows (e.g., ingestion, proposal pipelines); returned in response and attached to subsequent events.
- correlationId: created by client app per UI-session or task; included in all requests; Edge and Backend persist.

Mandatory Events (api_event_logs.event_type)
- request_received: Edge-only; implicit via api_request_logs row.
- user_message_received: message accepted; include sessionId and length.
- retrieval_run: when backend performs RAG; include backend= supabase|chromedb, topK, latencyMs.
- ai_call: model invocation; include provider, model, anonymized, inputTokens, outputTokens, costUsd, latencyMs, promptTemplateId/version.
- response_sent: assistant reply sent; include sessionId and len.
- stream_started/stream_finished: for SSE flows with durationMs.
- db_read/db_write: material DB interactions (proposals, messages, ingest records) with target and row_count.
- proposal_created/proposal_approved/proposal_rejected: HITL lifecycle with approver id.

Application-Layer Events (app_telemetry)
- ui_action: significant user actions (send_message, approve_proposal, upload_document) with `correlationId`, `sessionId` (if applicable), UI route, and minimal details.
- navigation: route/view transitions with `correlationId` for timeline alignment.
- error_client: client-side errors impacting the flow with `correlationId`.

Required Details (api_event_logs.details JSON)
- For retrieval_run
  - pipelineMode: direct|retrieval
  - retrievalBackend: supabase|chromedb
  - citations: [{ id, type: doc|chunk|entity, score } ...]
  - graphHops: number (if applicable)
- For ai_call
  - provider: sllm|openai|anthropic
  - model: string
  - anonymized: boolean (true only for GLLM path)
  - inputTokens, outputTokens, costUsd, latencyMs
  - promptTemplate: { id, version }
  - fallback: { used: boolean, reason?: timeout|quality|capability }
- For response_sent
  - sessionId, messageId
  - suggestionsCount (if applicable)
- For db_read/db_write
  - target: table/view/procedure name
  - rowCount: integer
  - durationMs
- For HITL events
  - proposalId, approverId (for approvals), status

Data Scope and RLS
- All rows must include org_id; RLS denies access if org_id NULL.
- Admins may have cross-tenant read via role; default users are limited to their org.

Content Safety in Logs
- Do not store raw prompts/responses. Persist lengths, token counts, hashes/digests (optional), and references (messageId, chunkIds).
- Store anonymized flag; do not store anonymized text, only the fact that anonymization occurred.

Export and Retention
- Provide per-org export endpoint (Edge or Backend) to return joined trace for a requestId/sessionId/processId.
- Recommended retention policy by event type documented separately; implement deletion workflows respecting legal holds.

End-to-End Trace Assembly
- Minimum join keys: requestId (Edge/Backend), sessionId (chat), processId (flows), correlationId (client/UI).
- Suggested export: events ordered by `created_at`, grouped by `requestId` then `event_type`, with application events interleaved by timestamp.

Query Recipes (examples)
- Reconstruct a request lifecycle (by requestId)
  SELECT * FROM api_request_logs WHERE request_id = :rid;
  SELECT * FROM api_event_logs   WHERE request_id = :rid ORDER BY created_at;
- Find all ai_call events for an org/day
  SELECT event_type, details->>'provider' AS provider, details->>'model' AS model,
         (details->>'inputTokens')::int AS in_t, (details->>'outputTokens')::int AS out_t,
         (details->>'costUsd')::numeric AS cost
  FROM api_event_logs
  WHERE org_id = :org AND event_type = 'ai_call' AND created_at::date = :day;
- Trace sessions
  SELECT * FROM api_event_logs WHERE (details->>'sessionId') = :sid ORDER BY created_at;

Indexes (recommended)
- api_event_logs: (org_id, created_at DESC), (event_type, created_at DESC)
- Consider GIN index on details for provider/model queries if needed.

Immutability and Integrity (Pilot+)
- Make api_event_logs append-only (no UPDATE/DELETE); enable with trigger or policy.
- Add checksum (optional) over selected fields for tamper-evidence; store in details.checksum.

Client and Service Responsibilities
- Edge
  - Generate requestId, populate RequestMeta with orgId/userId, write api_request_logs start/end, write core events.
  - Forward IDs and hints to backend.
- AI Backend (Python)
  - Emit retrieval_run, ai_call, db_* and HITL events with required details.
  - Persist directly to DB (service role) or POST to Edge logging gateway; logging is mandatory.
 - Application (Web/Mobile)
   - Generate/maintain correlationId per UI session/task and include in every request header `x-correlation-id`.
   - Persist client telemetry events (ui_action/navigation/error_client) to app telemetry table with org_id/user_id/sessionId/correlationId.

Compliance Readiness Checklist
- [ ] requestId/sessionId captured on every event
- [ ] orgId/userId present (non-null) on every row
- [ ] ai_call contains provider/model/tokens/cost/anonymized flag
- [ ] retrieval_run captures citations and backend
- [ ] response_sent recorded with durations
- [ ] Export endpoints available and org-scoped
- [ ] Retention policy documented and applied

Open Decisions
- Backend logging transport: direct DB vs Edge gateway
- Append-only enforcement timeline and checksum scope
- Standardized digest function for payload hashing (if adopted)
 - Header contract: adopt `traceparent`, `compliance-chain-id`, optional `decision-context` (JSON) for explainability chain; Edge forwards; Backend enriches.
