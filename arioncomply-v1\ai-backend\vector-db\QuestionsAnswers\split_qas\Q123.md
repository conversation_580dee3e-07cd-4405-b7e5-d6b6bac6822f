id: Q123
query: >-
  What if we're both a vendor to some companies and a customer to others?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/4.3"
overlap_ids: []
capability_tags:
  - "Report"
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Scope of the ISMS"
    id: "ISO27001:2022/4.3"
    locator: "Clause 4.3"
ui:
  cards_hint:
    - "Dual-role compliance plan"
  actions:
    - type: "start_workflow"
      target: "dual_role_compliance"
      label: "Define Dual-Role Strategy"
output_mode: "both"
graph_required: false
notes: "Map obligations both inbound (as a vendor) and outbound (as a customer)"
---
### 123) What if we're both a vendor to some companies and a customer to others?

**Standard terms**  
- **Scope (Cl. 4.3):** define boundaries for different roles.

**Plain-English answer**  
Create a **dual-role compliance plan**: document your obligations when you provide services and separately track requirements from upstream vendors. Use modular registers to manage each role’s controls.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 4.3

**Why it matters**  
Clarifies overlapping obligations and prevents gaps.

**Do next in our platform**  
- Run **Dual-Role Compliance** workflow.  
- Configure separate vendor and customer modules.

**How our platform will help**  
- **[Workflow]** Role-based compliance modules.  
- **[Report]** Dual-role obligation matrix.

**Likely follow-ups**  
- “How do we align SLAs in both directions?”  

**Sources**  
- ISO/IEC 27001:2022 Clause 4.3
