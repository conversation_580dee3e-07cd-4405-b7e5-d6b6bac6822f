<!-- File: arioncomply-v1/schemas/metadata-v1.README.md -->
# Metadata v1 JSON Schema

- File: `arioncomply-v1/schemas/metadata-v1.json`
- Purpose: Describes metadata entities/fields used by the demo metadata registry (Phase 2)
- Structure: JSON Schema draft; sections include `definitions`, `entities`, `fields`, `views`
- Usage: Validate config and drive Edge function validation for ListView/Form endpoints
- Notes: Keep breaking changes behind version bumps (v1 → v1.1); document changes here

