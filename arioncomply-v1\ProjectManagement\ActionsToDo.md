<!--
File: arioncomply-v1/ProjectManagement/ActionsToDo.md
File Description: Running list of project actions and design improvements.
Purpose: Track outstanding tasks and design resolutions.
Inputs: N/A
Outputs: N/A
Dependencies: Cross-references to design guides.
Security/RLS: N/A
Notes: Update as tasks are completed or added.
-->

**Note that MVP-Assessment features are a subset of MVP-Demo-Light, which is a subset of MVP-Pilot - the transition from Flutter Web to Flutter Native will be at the MVP Pilot stage.**

Developer Note: Follow the File Header & Comment Style Guide when adding/editing code files:
`arioncomply-v1/docs/ArionComplyDesign/File_Header_Style_Guide.md`

**📋 CURRENT SESSION TODO LIST (2025-09-14):**
*Updated automatically from TodoWrite tool to maintain continuity across sessions*

✅ **COMPLETED:**
- [x] Update ActionsToDo.md with current todo list status
- [x] Wire HybridSearchOrchestrator into router.py (replace simple hybrid_search() call) - **PRIORITY 1**
- [x] Implement database persistence for _record_search_confidence() method - **PRIORITY 2**
- [x] Complete ChromaDB client integration (remove TODO stubs) - **PRIORITY 3**
- [x] Add deterministic intent classifier at Edge with intent_detected events - **PHASE 1 TODO**
- [x] Add requestId and sessionId filters to app-audit-read function - **PHASE 1 TODO**
- [x] Add Trace Graph section to workflow-gui Audit tab with Cytoscape.js - **PHASE 1 TODO**
- [x] Define VECTOR_PROFILES env map for global/org retrieval blending - **PHASE 1 TODO**

🔄 **IN PROGRESS:**
*(No items currently in progress)*

⏳ **PENDING (Priority Order):**
*(All session items completed successfully)*

*This section will be updated whenever TodoWrite tool is used to maintain session continuity*

**📋 DESIGN IMPROVEMENTS RESOLVED (Aug 2025):**
✅ ArionComplyDesign inconsistencies reviewed and addressed:
- Database architecture mature with proper multi-tenant patterns
- UI principles aligned to Flutter/Dart (React conflicts resolved)  
- Field naming strategy confirmed (DB snake_case ↔ API camelCase via edge functions)
- Event logging infrastructure sufficient for MVP phases
- Advanced features (full event registry, detailed JSON validation) deferred until schema stabilizes

arioncomply-v1/Mockup screens mapped to the right Workflows, DB schemas, Edge APIs, and AI components so we can translate the design to Flutter Web (demo) and Flutter Native (prod) while staying aligned with [DesignGuidanceAndPrinciples](../docs/ArionComplyDesign/DesignGuidanceAndPrinciples) and [Detailed-Plan](../docs/Detailed-Plan.md).

**Chat + Assessment (MVP-Assessment-APP)** 

* [chatInterface.html](../Mockup/chatInterface.html) → Conversation flows

  * Workflows: [screens-list.md](../docs/ArionComplyDesign/DesignGuidanceAndPrinciples/EventWorkflow%20Design/Workflows/CustomerWorkflows/screens-list.md)
  * DB: `conversation_sessions`, `conversation_messages`
  * Edge: `conversation.start/send/stream` via router

  * AI: Prompt templates, retrieval, structured outputs for suggestions and actions - below are initial references however the Assessment will be based primarily on the chat/avatar interface.
* [wizard.html](../Mockup/wizard.html), [wizard-engine.js](../Mockup/wizard-engine.js), [wizzard.html](../Mockup/wizzard.html), [wizard-chat-integration.js](../Mockup/wizard-chat-integration.js), [onboarding\_questions.json](../Mockup/onboarding_questions.json)

  * Workflows: assessment/wizard workflows

  * DB: [questionnaire-system-schema.md](../docs/ArionComplyDesign/ApplicationDatabase/DBSchema/questionnaire-system-schema.md) (sessions, questions, responses)
  * Edge: assessments.create/answer/score/get

  * AI: ISO/GDPR question phrasing, follow-ups, scoring rationale

**Preset Scenarios (Demo UX) (MVP-Demo-Light)**

* `scenarioLauncher.html`, `scenarioReset.js`

  * Workflows: guided demo scenarios (ISO 27001, GDPR)
  * DB: seed org “TechSecure Inc.”; scenario state per session
  * Edge: scenario reset/init endpoints (idempotent)
  * UI: quick-start buttons; narrative guidance per step

**Documents + DMS (MVP-Demo-Light)**

* [documentEditor.html](../Mockup/documentEditor.html), [fileManager.html](../Mockup/fileManager.html)

  * Workflows: document-editor and file-manager
  * DB: documents, document\_versions, document\_relationships, document\_approvals, storage
  * Edge: documents.generate/get, file upload, metadata handlers
  * AI: SoA/Policy generation (template fill + citations)

**Data Views + Metadata-Driven UI (MVP-Demo-Light)**

* [listView.html](../Mockup/listView.html), [listView-logic.js](../Mockup/listView-logic.js), [listView-content-config.js](../Mockup/listView-content-config.js)

  * Workflows: listview-workflow (metadata-driven)
  * DB: any entity via metadata registry + JSON schemas

  * Edge: listview data API (dynamic queries) per [arioncomply-request-handlers.md](../docs/ArionComplyDesign/DataDriveUISchemaAndFunctions/arioncomply-request-handlers.md)

  * AI: optional assistance for filter/sort/suggested views

**Dashboards + Visuals (MVP-Demo-Light)**

* [dashboard.html](../Mockup/dashboard.html), [chartView.html](../Mockup/chartView.html)

  * Workflows: dashboard-components

  * DB: [metrics-analytics-schema.md](../docs/ArionComplyDesign/ApplicationDatabase/DBSchema/metrics-analytics-schema.md) (KPI aggregations)

  * Edge: dashboard API (metrics, activity, status)
  * AI: narrative summaries (optional, phase 2)

**Framework Intelligence (MVP-Assessment-APP) (MVP-Demo-Light)**

* `frameworkMap.html`, `relationshipView.html`

  * Workflows: control relationship visualization; multi-framework overlap
  * DB: framework catalogs; control mappings; obligations
  * Edge: relationships API (graph/overlap queries)
  * UI: interactive mapping; overlap indicators; guidance

**Workflow + Tasks  (MVP-Demo-Light)**

* [workflowEngine.html](../Mockup/workflowEngine.html), [workflowList.html](../Mockup/workflowList.html), [workflowPreview.html](../Mockup/workflowPreview.html), [workflowStepEdit.html](../Mockup/workflowStepEdit.html), [workflowStepEditor.js](../Mockup/workflowStepEditor.js), [workflowModel.js](../Mockup/workflowModel.js)

  * Workflows: workflow execution and planning

  * DB: [task-management-schema.md](../docs/ArionComplyDesign/ApplicationDatabase/DBSchema/task-management-schema.md), approvals, transitions
  * Edge: workflow.*, tasks.*

  * AI: recommend next steps, sequencing

**Notifications + Events  (MVP-Demo-Light)**

* [notificationCenter.html](../Mockup/notificationCenter.html)

  * Workflows: notification center

  * DB: [notification-system-schema.md](../docs/ArionComplyDesign/ApplicationDatabase/DBSchema/notification-system-schema.md)

  * Edge: subscribe API + push (realtime)
  * AI: message summarization (optional)

**Search + Relationships  (MVP-Demo-Light)**

* [searchInterface.html](../Mockup/searchInterface.html)

  * Workflows: search interface
  * DB: FTS indices; knowledge base tables
  * Edge: search handler (FTS/hybrid per router)
  * AI: hybrid retrieval ranking
* [relationshipMapper.html](../Mockup/relationshipMapper.html)

  * Workflows: relationship visualization
  * DB: relationships (docs, controls, evidence); standards mappings
  * Edge: graph/relations query
  * AI: graph navigation hints (optional)

**Reporting  (MVP-Demo-Light)**

* [reportBuilder.html](../Mockup/reportBuilder.html)

  * Assessment Report - **(MVP-Assessment-APP)**
  * DB: reporting entities, generated outputs
  * Edge: reports.define/generate/get
  * AI: prose generation + executive summaries (MVP-Assessment-APP)

**Security Fundamentals (MVP-Assessment-APP)**

* Logging, RBAC, and data protections wired end-to-end

  * DB: audit logs; RLS policies; encryption-at-rest configs
  * Edge: RBAC enforcement; scoped tokens; PII handling
  * UI: security messaging surfaces; audit log visualization (seeded)

**Technical Foundation (MVP-Assessment-APP)**

* Production DB Integration

  * Supabase connection; tenant isolation; schema compatibility
  * Org profile sync; secure data handling; RLS policies

* API Architecture Implementation

  * Edge Functions: shared router endpoints (assistant_router)
  * Realtime: WebSocket for chat streaming
  * Token management; error handling; optimistic UI updates

**User/Org + Settings (MVP-Assessment-APP)**

* [userManagement.html](../Mockup/userManagement.html), [userProfile.html](../Mockup/userProfile.html)

  * Workflows: user management

  * DB: users, user\_profiles, roles, permissions, user\_roles
  * Edge: users.*, rbac.*
* [settingsPanel.html](../Mockup/settingsPanel.html)


  * DB: organization\_settings
  * Edge: settings.\*

**Planning + Timeline + Kanban (MVP-Demo-Light)** 

* [kanbanBoard.html](../Mockup/kanbanBoard.html), [timelineView.html](../Mockup/timelineView.html), [timelineEnhancedFeatures.html](../Mockup/timelineEnhancedFeatures.html)

  * Workflows: planning and execution
  * DB: tasks, milestones, schedule entities
  * Edge: tasks.*, planning.*

**Navigation + Layout (MVP-Demo-Light)**

* [navigation-config.js](../Mockup/navigation-config.js), [layout-manager.js](../Mockup/layout-manager.js), [sidebar-component.js](../Mockup/sidebar-component.js), [index.html](../Mockup/index.html)

  * Translate to Flutter router + layouts; keep metadata-driven nav and component slots

**Translation to Flutter Web (Demo Web → MVP-Demo-Light)**

* Preserve structure from mockups but implement:

  * Metadata-driven components (forms/listview) per [arioncomply-metadata-json-schemas.md](../docs/ArionComplyDesign/DataDriveUISchemaAndFunctions/arioncomply-metadata-json-schemas.md)
  * Router-first Edge integration (single assistant\_router entrypoint)
  * Supabase auth + RLS (JWT with org\_id, roles\[])

  * Realtime for chat/notifications
* Use the EventWorkflow CustomerWorkflows docs as the canonical UX behavior source:

  * \[CustomerWorkflows docs]\(../docs/ArionComplyDesign/DesignGuidanceAndPrinciples/EventWorkflow Design/Workflows/CustomerWorkflows/)

---

## AI Retrieval, Intent, and Audit Trace — Implementation Status Update

**📋 MAJOR ARCHITECTURE IMPLEMENTED (Sept 2025):**
✅ **Multi-Tier Confidence-Based Retrieval System** - Complete architecture with universal 85% threshold
✅ **Intent Classification System** - 7 compliance categories with automated routing  
✅ **Dual-Vector Architecture** - ChromaDB (public) + Supabase Vector (private) with RLS
✅ **Confidence Scoring Framework** - 8-factor scoring with adaptive thresholds
✅ **Multi-Pipeline Embedding System** - BGE-Large-EN-v1.5 + ONNX quantization primary
✅ **Data Classification System** - Automated public/private content routing
✅ **Human Escalation Framework** - Transparent communication + expert ticketing
✅ **Complete Database Schema** - Enhanced dual-vector schema with intent/confidence tables

**🔄 INTEGRATION GAP IDENTIFIED:**
❌ **Router Integration** - Advanced orchestrator (`HybridSearchOrchestrator`) built but not wired to active router path
❌ **Simple System Active** - Router currently calls basic `hybrid_search()` with no intelligence
❌ **Database Persistence** - Confidence scoring exists but only logs to memory, needs DB writes
❌ **LLM Services Missing** - SLLM/GLLM Tier 2a/2b/2c services need implementation

**Priority Order for Activation:**
1. **IMMEDIATE**: Wire `HybridSearchOrchestrator` into `router.py` (replaces simple `hybrid_search()`)
2. **HIGH**: Implement database persistence for `_record_search_confidence()`  
3. **HIGH**: Complete ChromaDB client integration (remove TODO stubs)
4. **MEDIUM**: Implement SLLM services (Tier 2a - SmolLM3/Mistral7B/Phi3)
5. **MEDIUM**: Implement GLLM services (Tier 2b/2c - GPT-4/Claude with anonymization)

## AI Retrieval, Intent, and Audit Trace — Phase 1 TODO

Goal: Production-safe intent → routing → retrieval (global+org) → SLLM call → final assembly, fully traceable in Audit and viewable as a graph in workflow‑gui.

1) Edge Intent Events + Deterministic Routing (Low Risk)
- [ ] Add `intent_detected` and `intent_routing_decision` events at Edge (metadata only).
- [ ] Deterministic classifier for free‑text/speech (buttons/slash → explicit intent).
- [ ] On low confidence: return clarification options; do not forward yet.

2) Audit Read Filters + Timeline
- [ ] Extend `app-audit-read` to accept `requestId` and `sessionId` filters.
- [ ] Keep pagination and eventType filter (include intent_*).

3) Workflow‑GUI Audit Tab Enhancements
- [ ] Add inputs for `requestId`, `sessionId`, `eventType` + Search.
- [ ] Add “Trace Graph” panel (Cytoscape.js) to render timeline nodes/edges.
- [ ] Human‑readable labels (intent/confidence, retrieval backend/profile/topK/latency, SLLM model/tokens/latency, final mode/evidence).

4) Retrieval Profiles + Blend Global (Chroma) and Org (Supabase Vector)
- [ ] ENV `VECTOR_PROFILES` map: { profileName → { backend, url, key, dim, defaultTopK, defaultMinScore } }.
- [ ] Resolver `resolve_profile(name)` in backend; default prod.
- [ ] Router: parallel global+org searches → merge/rerank (simple weights) → unified citations.
- [ ] Log two `retrieval_run` events (global/org) with `retrieval_profile`, `topK`, `latencyMs`.
- [ ] Mark each citation `source: global|org`; optional tiny meta (corpusTag/shortTitle) for labels.

5) SLLMs on CPU via llama.cpp (SmolLM3, Phi‑3, Mistral‑7B)
- [ ] Run 3 llama.cpp servers (ports 8001/8002/8003), `-t <cores> -c 2048 -ngl 0`, GGUF Q4_K_M/Q5_K_M.
- [ ] Backend `LOCAL_SLLM_ENDPOINTS_PATH` JSON: provider → { baseUrl, model }.
- [ ] Router: select `sllmProvider` by hint; request `logprobs` if supported.
- [ ] `ai_call` event: add decoding params (temp/topP/seed), stopReason, tokens, latencyMs, confidenceHints (avgLogProb/entropy), justification (reasons[], gaps[], decisions{}). No raw content.
- [ ] GUI Compare/Arena: add providers; display tokens/latency if returned.

6) Ingestion & Seeding (Using Our Prepared Documents)
- [ ] Seed global corpora (Chroma): standards, laws, templates, reference Q&A (tag corpus/version/canonical_ids).
- [ ] Seed org corpora (Supabase Vector): org docs with RLS; enforce metadata (canonical_ids, title/source/version/hash).
- [ ] Fix embedding dim; replace placeholder embedder with production provider.

7) Human‑Readable Nodes & Drill‑Down
- [ ] Ensure retrieval logs include `retrieval_profile`; optional `corpusTag/shortTitle` (tiny payload) for top citations.
- [ ] (Optional) Add `app-doc-meta` function to resolve docId → safe metadata (org‑scoped for org docs). Use on node click.

8) Security & RLS
- [ ] orgId strictly from JWT claims at Edge/API (already tightened in `app-audit-read`).
- [ ] Supabase Vector: ENABLE RLS; `org_id NOT NULL`; RPC `match_chunks` enforces `p_org = app_current_org_id()`.
- [ ] Chroma used only for global corpora (no org docs).
- [ ] Logs remain metadata-only (IDs, counts, timings, hashes).

9) SQL Conveniences (Optional)
- [ ] Add view `app_intents_v1` to flatten intent fields (source, intent, confidence, route_to) from `api_event_logs`.
- [ ] Indexes: `(session_id, created_at)` on request/event logs for faster session traces.

10) Docs & Checklists
- [ ] `docs/AI-Backend-Design/Retrieval_Design.md`: profiles, blend/cascade, hints, envelopes, SLOs, examples.
- [ ] `DEPLOY.md`: llama.cpp setup, ENV examples, vector RPC check, smoke tests.
- [ ] `docs/Testing/Requirements.md` & `docs/Testing/Architecture.md`: trace steps, SLLM testing, A/B compare.

11) PR Slices (Suggested Order)
- [ ] PR1: `app-audit-read` requestId/sessionId + GUI filters + basic trace graph.
- [ ] PR2: Edge intent events + deterministic classifier (free‑text → clarify).
- [ ] PR3: Router `retrieval_profile` + citations `source` + dual `retrieval_run` events.
- [ ] PR4: SLLM selection & expanded `ai_call`; GUI provider options.
- [ ] PR5 (opt): `app-doc-meta` + citation drill‑down; `app_intents_v1` view.
- [ ] PR6 (opt): Ops GUI skeleton (`ops/monitoring/`) with Overview/Intents/Trace.

---

## Phase 1 MVP‑Assessment‑App — User Journeys & Workflows TODO

Purpose: Make the core Phase 1 experiences explicit (who, what, when), with acceptance criteria and file touch‑points so engineering and testing align.

1) Personas, Intents, Slots (Edge‑First)
- [ ] Define personas (Compliance Manager, Security Lead, Engineer/Auditor, System) and core intents taxonomy for Phase 1: `start_assessment`, `continue_assessment`, `ask_question`, `create_document`, `audit_query`, `give_feedback`, `navigate`.
- [ ] Define slots per intent (e.g., standard/framework, docType, sessionId). Keep JSON examples.
- [ ] Add a short reference doc: `ProjectManagement/Product/UserJourneys/phase1_intents_slots.md`.

2) Journey: Start Assessment
- [ ] Flow spec: UI (button/command) → Edge (intent_detected + routing) → `ai-conversation-start` → log `request_start/end`, `session_initialized`, `response_sent`.
- [ ] Acceptance: returns `sessionId`, first assistant message; Audit shows the full chain by `requestId` and `sessionId`.
- [ ] Add spec file: `ProjectManagement/Product/UserJourneys/phase1_start_assessment.md` (steps, events, errors).

3) Journey: Continue Assessment
- [ ] Flow spec: UI (message) → Edge `ai-conversation-send` → Backend (optional hints) → `retrieval_run?` (if Q&A) → `ai_call` → `response_sent`.
- [ ] Acceptance: preserves `sessionId`; Audit trace shows user_message_received → ai_call → response_sent with timings.
- [ ] Add spec file: `ProjectManagement/Product/UserJourneys/phase1_continue_assessment.md`.

4) Journey: Ask a Question (Assessment Context)
- [ ] Flow spec: UI free‑text → Edge intent rules (clarify if needed) → Backend router (blend retrieval global+org) → short answer + citations.
- [ ] Acceptance: citations carry IDs; Audit shows dual `retrieval_run` (global/org), `ai_call`, `final_assembled`.
- [ ] Add spec file: `ProjectManagement/Product/UserJourneys/phase1_ask_question.md`.

5) Journey: Create Draft Document (Lightweight)
- [ ] Flow spec: UI (button/slots) → Edge intent → Backend router: draft plan + TODOs + Gap Report (deterministic checks), SLLM first; optional GLLM pass (flagged).
- [ ] Acceptance: returns outline with TODO markers; Gap Report lists missing org fields; Audit shows retrieval + ai_call + final_assembled.
- [ ] Add spec file: `ProjectManagement/Product/UserJourneys/phase1_create_document.md`.

6) Journey: Audit & Trace (Observer)
- [ ] Flow spec: UI Audit tab → `app-audit-read` with filters (time, eventType, requestId/sessionId) → render table/graph.
- [ ] Acceptance: can load a full per‑request timeline graph; human‑readable nodes; drill‑down shows safe metadata.
- [ ] Add spec file: `ProjectManagement/Product/UserJourneys/phase1_audit_trace.md`.

7) Journey: System‑Initiated Action (Optional in Phase 1)
- [ ] Flow spec: scheduler/db trigger/anomaly → system intent router (Edge) → emit `intent_detected` (source=system) → route to backend/edge → `system_job_started/completed`.
- [ ] Acceptance: Audit filter by `triggerType`/`jobId` shows end‑to‑end without user interaction.
- [ ] Add spec file: `ProjectManagement/Product/UserJourneys/phase1_system_intents.md`.

8) Clarification UX (Free‑text Safety)
- [ ] Define 2–3 chip options per ambiguous case (e.g., draft vs question) with slot presets; timeouts/escapes; no backend call until resolved.
- [ ] Acceptance: Edge returns `needsClarification=true` quickly; UI renders chips; chosen option logs as source='ui'.
- [ ] Add spec snippet to each relevant journey.

9) Acceptance Criteria (Common Across Journeys)
- [ ] Every journey emits: `request_start/end` (where applicable), early `intent_detected`, appropriate `retrieval_run`/`ai_call`, and `final_assembled` (if backend used).
- [ ] No raw prompts/answers logged; only metadata (IDs, timings, counts, hashes, small titles/tags if enabled).
- [ ] All events include `org_id`, `request_id`, and when applicable `session_id`, `trace_id`.
- [ ] RLS enforced on all reads (Audit/UI ops prove composable filters are org‑scoped).

10) Trace Graph Design Notes (for GUI)
- [ ] Node labels per event type (intent, routing, retrieval global/org, sllm/gllm call, final assembled) with key numbers (topK, latency, tokens).
- [ ] Edge shows Δt between nodes; click opens details panel; optional “Doc Meta” lookup for citation labels.
- [ ] Add short doc: `ProjectManagement/Product/UserJourneys/phase1_trace_graph_notes.md`.



**Subscription + Entitlements + Billing (MVP-Assessment-APP)(Facilitates Production Transition)** 

* Subscription management: tiers, seats, demo→paid conversion workflow

  * DB: [unified_subscription_schema.md](../docs/ArionComplyDesign/SubscriptionManagement/unified_subscription_schema.md)
  * Edge: [subscription_edge_functions.md](../docs/ArionComplyDesign/SubscriptionManagement/subscription_edge_functions.md)
  * UI: admin subscription screens; data continuity on activation

* Feature flagging: entitlements-driven access control in UI/Edge

  * DB/Config: entitlement mappings per tier
  * UI: indicate premium features; upgrade prompts

* Billing foundation: payment processor hooks and models

  * Invoices, payment methods, billing cycles (activation-ready)

* Tenant data persistence & portability

  * Long-term retention; versioning of assessments/docs; export mechanisms
 
**Next Actions (aligned with Detailed-Plan)**

* Define a Flutter route map mirroring mockup screens and CustomerWorkflows pages.
* Lock API contracts per screen: listview, chat, assessments, documents, dashboard, notifications.
* Author DB migrations for conversation logs, assessments, demo flags, entitlements; seed demo scenarios.

* Implement Edge router handlers with validation and RBAC per [arioncomply-request-handlers.md](../docs/ArionComplyDesign/DataDriveUISchemaAndFunctions/arioncomply-request-handlers.md).

* Build Flutter Web skeleton: Chat + Assessment micro-UI, Heatmap/Charts, Document preview, Registration.

Next to potentially produce = 

* Initial DB migration files and seed scripts for the demo tenant (“TechSecure Inc.”).
* A one-pager “Mockup → Flutter Component Map” with target widget names and API endpoints per screen.
* Initial DB migration files and seed scripts for the demo tenant (“TechSecure Inc.”).
* Edge router handler stubs reflecting the above contracts.

---

AI Backend and Logging/Traceability – Actions To Do

MVP-Assessment (now → next)
- Edge gateway only: forward to backend; no anonymization/retrieval at Edge. DONE
- Logging mandatory at Edge: `LOGGING_REQUIRED=true`; write `api_request_logs` and `api_event_logs` with retry. DONE
- Add W3C trace support at Edge: parse/generate `traceparent`, echo in responses; enrich event details with `traceId/spanId/parentSpanId/correlationId`. DONE
- Ensure `response_sent` and `stream_finished` events emitted. DONE
- Update Transparency/Traceability and Stack/Dataflow docs. DONE
- Backend stub: accept forwarded chat requests; return assistant result (SLLM path). PARTIALLY DONE (sophisticated orchestrator implemented but not wired to active path)
- JWT parsing: finalize claim names for `orgId`/`userId`; extract at Edge and backend. DONE (Edge parses org_id/sub from JWT; header fallback remains for dev)
- Prompt management (MVP): add simple registry and compiler; record template id/version in `ai_call`. DONE
- UI: always send `x-correlation-id`; display returned `request-id`/`traceparent`. TODO
- Vector DB (Supabase) MVP: apply enhanced dual-vector schema `0002_dual_vector_schema_enhanced.sql`; set RLS; create service keys. DONE (schema implemented)
- Ingestion (MVP): implement multi-pipeline embedding architecture with BGE-Large-EN-v1.5 + ONNX. PARTIALLY DONE (pipelines implemented, ChromaDB client needs completion)
  - Multi-pipeline embedding system implemented in `services/embedding/`; BGE-ONNX pipeline ready. DONE
  - ChromaDB client integration has TODO stubs waiting for dependency addition. TODO  
- Retrieval client: sophisticated `HybridSearchOrchestrator` with intent classification implemented but not wired to router. INTEGRATION NEEDED
  - Advanced orchestrator with confidence scoring built in `hybrid_search_orchestration.py`. DONE
  - Router still calls simple `hybrid_search()` function instead of orchestrator. TODO - PRIORITY 1

Follow‑ups (MVP-Assessment hardening)
- Migration 0012 rollout: plan/apply to target Supabase; confirm no rows with NULL org_id or backfill before apply. TODO
- Edge JWT: add basic tests; keep `x-org-id`/`x-user-id` only for local dev; disable in prod. TODO
- Backend JWT: parse claims (`org_id`, `sub`) and set session GUC `app.current_organization_id` for service-role writes. TODO
- Audit read API: org-scoped endpoint with filters (route/status/event_type/time) + OpenAPI; minimal UI panel to view logs. TODO
- SSE proxying: switch conversation.stream to proxy backend streaming endpoint when available. TODO
- Secrets/env: verify `LOGGING_REQUIRED=true`, `AI_BACKEND_URL`, `AI_BACKEND_TIMEOUT_MS`, Supabase keys present in all envs. TODO

MVP-Demo-Light (enable RAG + explainability basics)
- Backend retrieval: implement hybrid retrieval client (Supabase Vector first), emit `retrieval_run` with citations, backend, topK, latencyMs. DONE (sophisticated orchestrator implemented - needs router integration)
- Backend provider routing: SLLM-first; GLLM fallback only if org allows; anonymization only on GLLM path; emit `ai_call` (provider/model/tokens/cost/anonymized). PARTIALLY DONE (architecture designed, services need implementation)
- Explainability headers: adopt `compliance-chain-id` and optional `decision-context`; Edge forwards; backend enriches; log `compliance_chain_started`. TODO
- App telemetry (minimal): client to emit `ui_action: send_message` and `navigation` tied to `correlationId`. TODO
- Paraphrases and synonyms: finalize design; add migrations for `synonyms` and `chunk_paraphrases` with RLS by `org_id`. TODO
 - Hybrid retrieval: add BM25 + RRF fusion; add graph hop expansion via link tables; enrich citations; tune topK. TODO

Additional Demo-Light tasks
- Explainability headers: already forwarded by Edge; backend to consume/enrich and emit `compliance_chain_started`. TODO
- Decision composition: add `decision_composed` with confidence components (retrieval/rules/model) and citations. TODO
- OpenAPI: extend docs/API with audit read + explainability fields; publish JSON for client generation. TODO
- Testing harness: add tabs to show traceparent/requestId; trigger demo scenarios; show retrieval/ai_call entries. TODO

MVP-Pilot (performance + deeper auditability)
- DB columns: add `trace_id`, `span_id`, `parent_span_id`, `session_id`, `process_id` to logs; add `request_id`/`process_id` to `conversation_messages`. TODO
- New tables: `process_runs` (workflow ledger, org-scoped, RLS) and `app_telemetry` (client events). TODO
- Rules registry: table + APIs to persist deterministic rules (id/version/expression/rationale); emit `deterministic_rules_applied`. TODO
- Export traces: org-scoped endpoint to assemble request/session/process traces with compliance decision chain. TODO
- Append-only policy: consider for `api_event_logs`; optional checksums for tamper-evidence. TODO

Additional Pilot tasks
- UI telemetry: implement `app_telemetry` capture for `ui_action` and `navigation`; join with logs by `correlationId`. TODO
- Rules registry: schema + editor; wire `deterministic_rules_applied` with real outcomes and coverage. TODO
- Trace export: build export endpoint (org-scoped) returning joined events by requestId/sessionId/processId/correlationId. TODO

Production (hardening)
- SLO dashboards and alerts: latency, selection rate, tokens, cost; enforce provider/egress allowlist. TODO
- Vector tenancy: shared with RLS vs dedicated instance per tenant; automated migration path; finalize ChromeDB vs Supabase Vector decision. TODO
- HITL proposals: persist proposals and approvals; emit `proposal_created/approved/rejected`; UI review workflows. TODO

Additional Production tasks
- Benchmarks: ChromeDB vs Supabase Vector decision with latency/recall/cost benchmarks; finalize tenancy strategy. TODO
- Provider metrics: dashboards for SLLM/GLLM selection, token usage, latency, cost per org; alerts on SLO breaches. TODO
- Egress allowlist: restrict outbound to approved model/vector endpoints; rotate keys and document break-glass. TODO

Design and Compliance Alignment
- Casing: DB snake_case; API camelCase; continue using conversion helpers and envelopes. ONGOING
- RLS: all new tables include `org_id` with enforced RLS; deny NULL. ONGOING
- Logging content safety: never log raw prompts/responses; store lengths/tokens/hashes and references. ONGOING
- Anonymization: GLLM-only, just-in-time in backend. ONGOING

General
- FieldMapper usage: apply overrides in first data-driven entity (e.g., documents) and validate casing. TODO
- Validation coverage: extend schemas and checks for future endpoints (ListView/Form/Documents). TODO
- Trackers/PRs: keep Production trackers synced; review `feat/data-driven-api-basics` PR and merge once approved. TODO

## Multi-Tier AI Architecture - Implementation Summary (September 2025)

**🎯 IMPLEMENTED SOPHISTICATED ARCHITECTURE:**

**Core Components Built:**
1. **Intent Classification System** (`hybrid_search_orchestration.py`)
   - 7 compliance-specific categories (policy, assessment, compliance, security, standards, implementation, general)
   - Confidence scoring with automatic routing decisions
   - Public/private knowledge requirement detection

2. **Dual-Vector Architecture** (`0002_dual_vector_schema_enhanced.sql`)
   - ChromaDB for public standards/regulations knowledge 
   - Supabase Vector for private organizational data
   - Complete RLS implementation with org-scoped isolation
   - Multi-dimensional embedding support (768, 1024, 1536, 3072)

3. **Multi-Tier Confidence System** (`complete-multi-tier-confidence-flow.md`)
   - Universal 85% confidence threshold across all tiers
   - Progressive escalation: Tier 0→1a→1b→2a→2b→2c→3
   - Transparent human escalation with expert ticketing
   - Complete audit trail and learning system

4. **Multi-Pipeline Embedding System** (`embedding/`)
   - BGE-Large-EN-v1.5 + ONNX quantization (primary)
   - all-mpnet-base-v2 (secondary)
   - OpenAI embeddings (optional)
   - Automated fallback and health checking

5. **Advanced Confidence Scoring** (`confidence_scoring_system.py`)
   - 8-factor confidence calculation
   - Adaptive threshold learning
   - Performance analytics and optimization

**🔴 CRITICAL INTEGRATION GAP:**
- **Router Integration**: Advanced `HybridSearchOrchestrator` is built but NOT wired to active router
- **Active System**: Router calls simple `hybrid_search()` function with no intelligence
- **Result**: Sophisticated multi-tier system exists but is completely bypassed

**📋 IMMEDIATE ACTION ITEMS:**
1. **PRIORITY 1**: Replace `router.py:128` call from simple `hybrid_search()` to `orchestrator.hybrid_search()`
2. **PRIORITY 2**: Implement database persistence for `_record_search_confidence()` stub
3. **PRIORITY 3**: Complete ChromaDB client integration (remove TODO stubs)
4. **PRIORITY 4**: Implement SLLM/GLLM services for Tier 2a/2b/2c

**Expected Outcome After Integration:**
- Intent classification will automatically route queries
- Confidence scoring will ensure 85% quality threshold
- Progressive escalation will optimize cost vs quality
- Human escalation will handle edge cases transparently
- Complete audit trails will enable system improvement

Decisions Pending
- JWT claim names for `orgId`/`userId` and any role claims.
- Backend logging transport: direct Supabase writes vs Edge logging gateway POST.
- ULID vs UUID for sessionId/processId (recommend ULID).
- ChromeDB vs Supabase Vector selection and benchmarks.
