<!-- File: arioncomply-v1/docs/Testing/Requirements.md -->
# Testing Requirements (Phase 1 focus)

Scope
- Validate MVP-Assessment-App flows end-to-end using workflow-gui and Edge + Backend + DB.

Functional Requirements
- Conversation: start/send/stream work with org-scoped logging (request_start/end, ai_call, response_sent, stream_*).
- Audit Read: org-only access; filters by time, route, status, event_type; pagination; no sensitive content.
- Traceability: UI exposes requestId and traceparent values for diagnostics.

Non-Functional Requirements
- Multi-tenant safety (RLS enforced in logs; Edge filters by org_id when using admin client).
- Performance: Audit queries return under 1s for recent windows with pagination (cloud default).
- Security: No raw prompts/responses returned in audit; only metadata.

Out of Scope (Phase 1)
- Notifications, full workflow engine, document generation pipelines.

