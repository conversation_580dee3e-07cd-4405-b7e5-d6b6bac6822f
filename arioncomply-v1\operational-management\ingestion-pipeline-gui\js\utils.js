/**
 * Utility functions for the Ingestion Pipeline Management GUI
 */

/**
 * Format duration in milliseconds to human readable string
 * @param {number} ms - Duration in milliseconds
 * @returns {string} Formatted duration
 */
function formatDuration(ms) {
    if (ms < 1000) return `${ms.toFixed(1)}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
    return `${(ms / 3600000).toFixed(1)}h`;
}

/**
 * Format bytes to human readable string
 * @param {number} bytes - Size in bytes
 * @returns {string} Formatted size
 */
function formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
}

/**
 * Format number to human readable string with units
 * @param {number} num - Number to format
 * @returns {string} Formatted number
 */
function formatNumber(num) {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
}

/**
 * Format timestamp to relative time
 * @param {string|Date} timestamp - Timestamp to format
 * @returns {string} Relative time string
 */
function formatRelativeTime(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now - time;
    
    if (diffMs < 60000) return 'Just now';
    if (diffMs < 3600000) return `${Math.floor(diffMs / 60000)}m ago`;
    if (diffMs < 86400000) return `${Math.floor(diffMs / 3600000)}h ago`;
    if (diffMs < 604800000) return `${Math.floor(diffMs / 86400000)}d ago`;
    
    return time.toLocaleDateString();
}

/**
 * Debounce function to limit the rate of function calls
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Throttle function to limit the rate of function calls
 * @param {Function} func - Function to throttle
 * @param {number} limit - Time limit in milliseconds
 * @returns {Function} Throttled function
 */
function throttle(func, limit) {
    let inThrottle;
    return function executedFunction(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Deep clone an object
 * @param {Object} obj - Object to clone
 * @returns {Object} Cloned object
 */
function deepClone(obj) {
    if (obj === null || typeof obj !== "object") return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    if (typeof obj === "object") {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
}

/**
 * Generate a UUID v4
 * @returns {string} UUID string
 */
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

/**
 * Validate email address
 * @param {string} email - Email to validate
 * @returns {boolean} True if valid email
 */
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

/**
 * Sanitize HTML to prevent XSS
 * @param {string} str - String to sanitize
 * @returns {string} Sanitized string
 */
function sanitizeHTML(str) {
    const temp = document.createElement('div');
    temp.textContent = str;
    return temp.innerHTML;
}

/**
 * Get status color based on status string
 * @param {string} status - Status string
 * @returns {string} CSS class name for status color
 */
function getStatusColor(status) {
    const statusColors = {
        'healthy': 'success',
        'online': 'success',
        'active': 'success',
        'completed': 'success',
        'warning': 'warning',
        'degraded': 'warning',
        'pending': 'warning',
        'error': 'error',
        'failed': 'error',
        'offline': 'error',
        'disabled': 'secondary',
        'unknown': 'secondary'
    };
    
    return statusColors[status?.toLowerCase()] || 'secondary';
}

/**
 * Get pipeline quality tier badge
 * @param {string} tier - Quality tier
 * @returns {string} HTML for quality badge
 */
function getQualityBadge(tier) {
    const badges = {
        'sota': '<span class="status-badge success">SOTA</span>',
        'high': '<span class="status-badge info">High</span>',
        'good': '<span class="status-badge warning">Good</span>',
        'testing_only': '<span class="status-badge error">Testing Only</span>'
    };
    
    return badges[tier] || '<span class="status-badge secondary">Unknown</span>';
}

/**
 * Copy text to clipboard
 * @param {string} text - Text to copy
 * @returns {Promise<boolean>} True if successful
 */
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (err) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
            document.execCommand('copy');
            document.body.removeChild(textArea);
            return true;
        } catch (fallbackErr) {
            document.body.removeChild(textArea);
            return false;
        }
    }
}

/**
 * Download data as file
 * @param {string} data - Data to download
 * @param {string} filename - Filename for download
 * @param {string} type - MIME type
 */
function downloadFile(data, filename, type = 'text/plain') {
    const blob = new Blob([data], { type });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
}

/**
 * Parse JSON safely
 * @param {string} jsonString - JSON string to parse
 * @param {any} defaultValue - Default value if parsing fails
 * @returns {any} Parsed JSON or default value
 */
function safeJSONParse(jsonString, defaultValue = null) {
    try {
        return JSON.parse(jsonString);
    } catch (e) {
        console.warn('JSON parse error:', e);
        return defaultValue;
    }
}

/**
 * Calculate similarity between two arrays (simple cosine similarity)
 * @param {number[]} a - First vector
 * @param {number[]} b - Second vector
 * @returns {number} Similarity score (0-1)
 */
function calculateSimilarity(a, b) {
    if (a.length !== b.length) return 0;
    
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < a.length; i++) {
        dotProduct += a[i] * b[i];
        normA += a[i] * a[i];
        normB += b[i] * b[i];
    }
    
    if (normA === 0 || normB === 0) return 0;
    
    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
}

/**
 * Get pipeline icon based on pipeline name
 * @param {string} pipelineName - Name of the pipeline
 * @returns {string} Font Awesome icon class
 */
function getPipelineIcon(pipelineName) {
    const icons = {
        'bge-large-onnx': 'fas fa-rocket',
        'all-mpnet-base-v2': 'fas fa-server',
        'openai': 'fas fa-cloud',
        'openai-large': 'fas fa-cloud',
        'placeholder': 'fas fa-flask'
    };
    
    return icons[pipelineName] || 'fas fa-cog';
}

/**
 * Create loading element
 * @param {string} message - Loading message
 * @returns {HTMLElement} Loading element
 */
function createLoadingElement(message = 'Loading...') {
    const div = document.createElement('div');
    div.className = 'loading-state';
    div.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span class="loading-text">${message}</span>
        </div>
    `;
    return div;
}

/**
 * Create error element
 * @param {string} message - Error message
 * @returns {HTMLElement} Error element
 */
function createErrorElement(message = 'An error occurred') {
    const div = document.createElement('div');
    div.className = 'error-state';
    div.innerHTML = `
        <div class="error-content">
            <i class="fas fa-exclamation-triangle"></i>
            <span class="error-text">${sanitizeHTML(message)}</span>
        </div>
    `;
    return div;
}

/**
 * Create empty state element
 * @param {string} message - Empty state message
 * @param {string} icon - Icon class
 * @returns {HTMLElement} Empty state element
 */
function createEmptyElement(message = 'No data available', icon = 'fas fa-inbox') {
    const div = document.createElement('div');
    div.className = 'empty-state';
    div.innerHTML = `
        <div class="empty-content">
            <i class="${icon}"></i>
            <span class="empty-text">${sanitizeHTML(message)}</span>
        </div>
    `;
    return div;
}

/**
 * Local storage utility
 */
const storage = {
    get: (key, defaultValue = null) => {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (e) {
            return defaultValue;
        }
    },
    
    set: (key, value) => {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (e) {
            return false;
        }
    },
    
    remove: (key) => {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (e) {
            return false;
        }
    },
    
    clear: () => {
        try {
            localStorage.clear();
            return true;
        } catch (e) {
            return false;
        }
    }
};

/**
 * Event emitter for component communication
 */
class EventEmitter {
    constructor() {
        this.events = {};
    }
    
    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }
    
    off(event, callback) {
        if (!this.events[event]) return;
        this.events[event] = this.events[event].filter(cb => cb !== callback);
    }
    
    emit(event, data) {
        if (!this.events[event]) return;
        this.events[event].forEach(callback => callback(data));
    }
}

// Global event emitter instance
const eventEmitter = new EventEmitter();

// Export for module systems (if used)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        formatDuration,
        formatBytes,
        formatNumber,
        formatRelativeTime,
        debounce,
        throttle,
        deepClone,
        generateUUID,
        validateEmail,
        sanitizeHTML,
        getStatusColor,
        getQualityBadge,
        copyToClipboard,
        downloadFile,
        safeJSONParse,
        calculateSimilarity,
        getPipelineIcon,
        createLoadingElement,
        createErrorElement,
        createEmptyElement,
        storage,
        EventEmitter,
        eventEmitter
    };
}