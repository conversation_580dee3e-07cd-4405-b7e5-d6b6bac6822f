{"$id": "https://iso.arionetworks.com/schemas/metadata-v1.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "title": "ArionComply Metadata Schema v1", "description": "JSON Schema for ArionComply v2 authoring guide metadata validation, compatible with ChromaDB + Supabase Vector hybrid architecture", "type": "object", "required": ["id", "version", "artifact", "standard", "lifecycle", "security", "provenance"], "additionalProperties": false, "properties": {"id": {"type": "string", "description": "Unique identifier (e.g., Q001, POL-001, PROC-001)"}, "version": {"type": "string", "description": "Semantic version (e.g., 1.0.0)"}, "artifact": {"type": "object", "required": ["type", "title", "language", "status"], "properties": {"type": {"enum": ["qa", "policy", "procedure", "register", "tracker", "template", "mapping", "guide"], "description": "Type of artifact"}, "title": {"type": "string", "description": "Human-readable title"}, "description": {"type": "string", "description": "Optional description"}, "language": {"type": "string", "default": "en", "description": "Language code"}, "locale": {"type": "string", "description": "Locale identifier"}, "status": {"enum": ["draft", "in_review", "approved", "published", "deprecated"], "description": "Current status"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Free-form tags"}, "display_date": {"type": "string", "format": "date", "description": "Display date"}}}, "standard": {"type": "object", "required": ["framework", "version"], "properties": {"framework": {"type": "string", "enum": ["ISO27001", "ISO27701", "GDPR", "CPRA", "NIS2", "EUAI"], "description": "Framework identifier"}, "version": {"type": "string", "description": "Framework version (e.g., 2022, 2016)"}, "spec_id": {"type": "string", "description": "Specification identifier"}, "jurisdictions": {"type": "array", "items": {"type": "string"}, "description": "Applicable jurisdictions"}, "clauses": {"type": "array", "items": {"type": "string", "pattern": "^[A-Z0-9]+:[0-9]{4}/.+$", "description": "Canonical clause ID (e.g., ISO27001:2022/A.8.12)"}, "description": "Primary clauses this artifact addresses"}, "mappings": {"type": "array", "items": {"type": "object", "required": ["to", "strength"], "properties": {"to": {"type": "string", "pattern": "^[A-Z0-9]+:[0-9]{4}/.+$", "description": "Target clause ID"}, "strength": {"enum": ["exact", "partial", "related"], "description": "Mapping strength"}, "weight": {"type": "number", "minimum": 0, "maximum": 1, "description": "Mapping weight"}, "rationale": {"type": "string", "description": "Mapping rationale"}}}}}}, "content": {"type": "object", "properties": {"qa": {"type": "object", "properties": {"question": {"type": "string"}, "answer": {"type": "string"}, "answer_steps": {"type": "array", "items": {"type": "string"}}, "audiences": {"type": "array", "items": {"enum": ["exec", "security", "it", "legal", "all"]}}, "difficulty": {"enum": ["intro", "practitioner", "expert"]}, "variants": {"type": "array", "items": {"type": "object", "properties": {"audience": {"type": "string"}, "answer": {"type": "string"}, "redacted": {"type": "boolean"}}}}}}, "document": {"type": "string", "description": "Document content for templates"}, "placeholders": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Template placeholders"}, "score_hint": {"type": "object", "properties": {"criticality": {"enum": ["low", "medium", "high"]}, "audit_impact": {"enum": ["minor", "moderate", "major"]}}}}}, "evidence": {"type": "object", "properties": {"expected": {"type": "array", "items": {"type": "string"}, "description": "Expected evidence types"}, "collection": {"enum": ["manual", "automated", "hybrid"], "description": "Collection method"}, "retention_days": {"type": "integer", "minimum": 1, "description": "Retention period in days"}, "locations": {"type": "array", "items": {"type": "string"}, "description": "Evidence locations"}, "hash": {"type": "string", "description": "Evidence hash"}, "signed_by": {"type": "string", "description": "Signer information"}, "chain_of_custody": {"type": "array", "items": {"type": "string"}, "description": "Chain of custody log"}}}, "retrieval": {"type": "object", "properties": {"embeddings": {"type": "object", "properties": {"model": {"type": "string"}, "chunk_size": {"type": "integer"}, "overlap": {"type": "integer"}}}, "splitter": {"enum": ["markdown", "semantic", "token"]}, "per_section": {"type": "boolean"}, "keywords": {"type": "array", "items": {"type": "string"}}, "summarization": {"type": "object", "properties": {"model": {"type": "string"}, "prompt_hash": {"type": "string"}}}, "rerank": {"type": "object", "properties": {"model": {"type": "string"}, "version": {"type": "string"}}}, "rerank_hint": {"type": "string"}}}, "relations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"enum": ["duplicates", "supersedes", "depends_on", "conflicts_with", "see_also"]}}}}, "governance": {"type": "object", "properties": {"soa_ids": {"type": "array", "items": {"type": "string"}}, "risk_ids": {"type": "array", "items": {"type": "string"}}, "control_ids": {"type": "array", "items": {"type": "string"}}, "test_ids": {"type": "array", "items": {"type": "string"}}, "capa_ids": {"type": "array", "items": {"type": "string"}}, "task_ids": {"type": "array", "items": {"type": "string"}}, "kpi": {"type": "array", "items": {"type": "string"}}}}, "workflow": {"type": "object", "properties": {"state": {"enum": ["draft", "in_review", "approved", "published", "deprecated"]}, "approvals": {"type": "array", "items": {"type": "object", "properties": {"by": {"type": "string"}, "at": {"type": "string", "format": "date-time"}, "signature": {"type": "string"}}}}, "changelog": {"type": "array", "items": {"type": "object", "properties": {"at": {"type": "string", "format": "date-time"}, "by": {"type": "string"}, "note": {"type": "string"}}}}}}, "roles": {"type": "object", "properties": {"owner": {"type": "string"}, "approver": {"type": "string"}, "reviewers": {"type": "array", "items": {"type": "string"}}, "raci": {"type": "array", "items": {"type": "object", "properties": {"role": {"type": "string"}, "level": {"enum": ["R", "A", "C", "I"]}}}}, "tenant_id": {"type": "string"}}}, "i18n": {"type": "object", "properties": {"translations": {"type": "array", "items": {"type": "object", "properties": {"locale": {"type": "string"}, "title": {"type": "string"}, "answer": {"type": "string"}, "keywords": {"type": "array", "items": {"type": "string"}}}}}}}, "privacy": {"type": "object", "properties": {"data_categories": {"type": "array", "items": {"type": "string"}}, "lawful_basis": {"type": "array", "items": {"type": "string"}}, "role": {"enum": ["controller", "processor", "joint"]}, "dpa_refs": {"type": "array", "items": {"type": "string"}}, "dpia_id": {"type": "string"}}}, "security": {"type": "object", "required": ["confidentiality"], "properties": {"confidentiality": {"enum": ["public", "internal", "confidential", "restricted"], "description": "Information classification level"}, "pii": {"type": "boolean", "default": false, "description": "Contains personally identifiable information"}, "field_sensitivity": {"type": "object", "additionalProperties": {"enum": ["public", "internal", "confidential", "restricted"]}, "description": "Field-level sensitivity mapping"}, "export_controls": {"type": "array", "items": {"type": "string"}, "description": "Export control classifications"}, "license_spdx": {"type": "string", "description": "SPDX license identifier"}, "license": {"type": "string", "description": "License text or reference"}}}, "lifecycle": {"type": "object", "required": ["created_at"], "properties": {"created_at": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "description": "Last update timestamp"}, "review_cycle_days": {"type": "integer", "minimum": 1, "description": "Review cycle in days"}, "owners": {"type": "array", "items": {"type": "string"}, "description": "Content owners"}}}, "provenance": {"type": "object", "required": ["source"], "properties": {"source": {"enum": ["human", "llm", "import"], "description": "Content source type"}, "sources": {"type": "array", "items": {"type": "string"}, "description": "Source references"}, "gen_model": {"type": "string", "description": "Generation model name"}, "gen_params": {"type": "object", "description": "Generation parameters"}, "prompt_hash": {"type": "string", "description": "Prompt hash for reproducibility"}, "guardrail_policy_ids": {"type": "array", "items": {"type": "string"}, "description": "Applied guardrail policies"}, "confidence": {"type": "number", "minimum": 0, "maximum": 1, "description": "Content confidence score"}}}, "ui": {"type": "object", "properties": {"cards_hint": {"type": "array", "items": {"type": "string", "maxLength": 40}, "maxItems": 3, "description": "UI card hints (max 40 chars each, max 3 items)"}, "actions": {"type": "array", "items": {"type": "object", "required": ["type", "target", "label"], "properties": {"type": {"enum": ["open_register", "open_tracker", "start_workflow", "open_template", "create_policy", "upload_evidence"], "description": "UI action type"}, "target": {"type": "string", "description": "Target slug - must exist in ui_catalog.json"}, "label": {"type": "string", "description": "Button/action label"}, "params": {"type": "object", "description": "Action parameters"}}}}}}, "packs": {"type": "array", "items": {"type": "string", "pattern": "^[A-Z0-9]+:[0-9]{4}$", "description": "Framework pack IDs (e.g., ISO27001:2022)"}, "description": "Applicable framework packs"}, "primary_ids": {"type": "array", "items": {"type": "string", "pattern": "^[A-Z0-9]+:[0-9]{4}/.+$", "description": "Primary clause IDs in canonical format"}, "description": "Primary framework clauses"}, "overlap_ids": {"type": "array", "items": {"type": "string", "pattern": "^[A-Z0-9]+:[0-9]{4}/.+$", "description": "Related clause IDs in canonical format"}, "description": "Related/overlapping framework clauses"}, "capability_tags": {"type": "array", "items": {"enum": ["NL-Portal", "Draft Doc", "Approval", "Versioning", "Register", "Tracker", "Workflow", "Reminder", "Planner", "Dashboard", "Report", "Virtual Manager", "Classify-Assist", "Evidence-Guided"]}, "minItems": 3, "maxItems": 6, "description": "Platform capability tags (3-6 required)"}, "flags": {"type": "array", "items": {"enum": ["LOCAL LAW CHECK", "CB POLICY VARIES", "MARKET PRACTICE—VALIDATE", "RESEARCH NEEDED"]}, "description": "Decision flags and qualifiers"}, "sources": {"type": "array", "items": {"type": "object", "required": ["title", "id"], "properties": {"title": {"type": "string"}, "id": {"type": "string", "pattern": "^[A-Z0-9]+:[0-9]{4}/.+$", "description": "Canonical clause ID"}, "locator": {"type": "string"}}}, "description": "Source references"}, "vector_db_metadata": {"type": "object", "description": "Vector database specific metadata for ChromaDB + Supabase Vector hybrid", "properties": {"chromadb_collection": {"type": "string", "description": "ChromaDB collection name for local/dev operations"}, "supabase_org_id": {"type": "string", "description": "Organization ID for Supabase Vector RLS isolation"}, "embedding_model": {"type": "string", "default": "text-embedding-3-large", "description": "Embedding model used"}, "chunk_strategy": {"enum": ["semantic", "fixed", "adaptive"], "default": "semantic", "description": "Chunking strategy for vector storage"}}}}}