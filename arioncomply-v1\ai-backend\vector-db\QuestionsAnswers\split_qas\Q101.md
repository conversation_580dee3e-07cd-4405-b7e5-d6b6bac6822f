id: Q101
query: >-
  What does "governance" mean in practical terms for a small company?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/5.1"
overlap_ids:
  - "ISO27001:2022/4.1"
capability_tags:
  - "Register"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Leadership & Commitment"
    id: "ISO27001:2022/5.1"
    locator: "Clause 5.1"
  - title: "ISO/IEC 27001:2022 — Context of the Organization"
    id: "ISO27001:2022/4.1"
    locator: "Clause 4.1"
ui:
  cards_hint:
    - "Governance charters"
  actions:
    - type: "open_register"
      target: "governance"
      label: "Set Up Governance Register"
    - type: "start_workflow"
      target: "governance_framework"
      label: "Define Governance"
output_mode: "both"
graph_required: false
notes: "Scaled-down roles, committees and reporting tailored to smaller teams"
---
### 101) What does "governance" mean in practical terms for a small company?

**Standard terms)**  
- **Leadership & commitment (Cl. 5.1):** top-management’s accountability.  
- **Context (Cl. 4.1):** understanding internal and external issues.

**Plain-English answer**  
Governance means defining clear owners for information security, setting up a simple steering group (e.g., quarterly 1-hour meetings), documenting decision rights, and ensuring regular reporting—scaled to your size.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clauses 5.1; 4.1

**Why it matters**  
Ensures accountability and aligns security activities with business goals.

**Do next in our platform**  
- Create the **Governance Register**.  
- Launch **Governance Framework** workflow.

**How our platform will help**  
- **[Register]** Tracks roles, meetings, and decisions.  
- **[Report]** Generates executive summaries and meeting decks.

**Likely follow-ups**  
- “Who should sit on our steering group?” (Typically CEO, CISO, process owners)

**Sources**  
- ISO/IEC 27001:2022 Clauses 5.1; 4.1
