{
  "_description": "Discovered local SLLM endpoints with extended metadata for test GUI wiring",
  "version": "1.0",
  "generatedAt": "2025-09-09T07:55:51Z",
  "llmsHome": "/Users/<USER>/LocalProjects/GitHubProjectsDocuments/xLLMArionComply/arioncomply-v1/ai-backend/LLMs/.fakehome",
  "system": {"os":"	macOS 	15.5 	24F74","kernel":"Darwin Libors-MacBook-Pro-2.local 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:27 PDT 2025; root:xnu-11417.121.6~2/RELEASE_ARM64_T6041 arm64","cpuBrand":"unknown","cpuCores":0,"cpuPhysicalCores":0,"memBytes":0,"gpuName":"sppci_vendor_Apple","gpuVram":""},
  "localModels": [{"id":"smollm3","port":8081,"base":"http://127.0.0.1:8081","label":"llms.smollm3","launchctl":"running","health":"down","openai_compatible":false,"meta":{"runtime":{"ctx":null,"threads":null,"gpuLayers":null,"batch":null,"flashAttn":false}}},{"id":"mistral7b","port":8082,"base":"http://127.0.0.1:8082","label":"llms.mistral7b","launchctl":"running","health":"down","openai_compatible":false,"meta":{"runtime":{"ctx":null,"threads":null,"gpuLayers":null,"batch":null,"flashAttn":false}}},{"id":"phi3","port":8083,"base":"http://127.0.0.1:8083","label":"llms.phi3","launchctl":"running","health":"down","openai_compatible":false,"meta":{"runtime":{"ctx":null,"threads":null,"gpuLayers":null,"batch":null,"flashAttn":false}}}]
}
