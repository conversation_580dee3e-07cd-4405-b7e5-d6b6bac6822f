id: Q175
query: >-
  What if we use AI services from other companies—who’s responsible for compliance?
packs:
  - "GDPR:2016"
  - "EUAI:2024"
primary_ids:
  - "GDPR:2016/Art.28"
  - "EUAI:2024/Art.2"
overlap_ids: []
capability_tags:
  - "Register"
  - "Workflow"
flags: []
sources:
  - title: "GDPR — Processor Contracts"
    id: "GDPR:2016/Art.28"
    locator: "Article 28"
  - title: "EU AI Act — Scope & Roles"
    id: "EUAI:2024/Art.2"
    locator: "Article 2"
ui:
  cards_hint:
    - "Responsibility matrix"
  actions:
    - type: "open_register"
      target: "compliance_roles"
      label: "View Roles"
    - type: "start_workflow"
      target: "third_party_ai_assessment"
      label: "Assess Third-Party AI"
output_mode: "both"
graph_required: false
notes: "Clarify controller vs. processor obligations for AI providers"
---
### 175) What if we use AI services from other companies—who’s responsible for compliance?

**Standard term(s)**  
- **Processor contracts (GDPR Art. 28):** agreements defining roles, obligations, and safeguards when a vendor processes personal data on your behalf.  
- **Scope & roles (EU AI Act Art. 2):** identifies providers, deployers, importers, and distributors, each with specific responsibilities.

**Plain-English answer**  
If you integrate AI services from another company, your compliance obligations depend on your role. Under GDPR, if you are the **controller** (deciding purposes and means), you remain responsible for lawful processing and must have a compliant processor contract. Under the EU AI Act, the AI service provider is generally the “provider,” but if you substantially modify the system or place it on the market under your name, you may take on provider responsibilities. Even as a deployer, you must meet obligations such as monitoring and human oversight.

**Applies to**  
- **Primary:** GDPR Article 28; EU AI Act Article 2

**Why it matters**  
Misunderstanding role-based obligations can leave compliance gaps and expose you to liability.

**Do next in our platform**  
- Review the **Compliance Roles Register** to map each AI service to the correct GDPR and AI Act role.  
- Launch the **Third-Party AI Assessment** workflow to verify vendor compliance and contracts.

**How our platform will help**  
- **[Register]** Maintain a role and responsibility matrix for all third-party AI services.  
- **[Workflow]** Guided assessment to confirm vendor obligations and your oversight measures.

**Likely follow-ups**  
- “If the AI provider is outside the EU, do EU rules still apply?” (Yes, if the system is placed on or used in the EU market) [LOCAL LAW CHECK]

**Sources**  
- GDPR Article 28  
- EU AI Act Article 2

