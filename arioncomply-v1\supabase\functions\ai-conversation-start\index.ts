// File: arioncomply-v1/supabase/functions/ai-conversation-start/index.ts
// File Description: conversation.start endpoint
// Purpose: Initialize a new chat session; validate input; return session stub.
// Input: POST JSON { title?, context? }
// Output: ApiSuccess with session + first_message (camelCase)
// Notes: Logs request/events; replace stub with DB writes in production.
// conversation.start - initialize a new chat session
// Minimal stub to validate payload and return a session id

import { serve } from "https://deno.land/std@0.224.0/http/server.ts";
import { readJson, nowIso, convertKeys } from "../_shared/utils.ts";
import { apiOk, apiError } from "../_shared/errors.ts";
import { getClientMeta, logEvent, logRequestEnd, logRequestStart } from "../_shared/logger.ts";
import { ConversationStartSchema, validate } from "../_shared/schemas.ts";

type StartBody = {
  title?: string;
  context?: Record<string, unknown>;
};

serve(async (req) => {
  const requestId = crypto.randomUUID();
  const meta = getClientMeta(req, requestId);
  await logRequestStart(meta, req.headers);
  if (req.method !== "POST") {
    const res = apiError("method_not_allowed", "Method not allowed", 405, undefined, requestId);
    await logRequestEnd(meta, 405);
    return res;
  }

  let body: StartBody;
  try {
    body = await readJson<StartBody>(req);
  } catch (err) {
    const res = apiError("invalid_json", "Invalid JSON body", 400, undefined, requestId);
    await logRequestEnd(meta, 400);
    return res;
  }

  const sessionId = crypto.randomUUID();
  const title = body.title?.trim() || "New Conversation";

  // In production: insert into conversation_sessions and return persisted record
  // This stub returns a mock payload aligned with the DB schema
  // Return camelCase per API design
  const payload = {
    session: {
      session_id: sessionId,
      title,
      status: "active",
      metadata: body.context ?? {},
      created_at: nowIso(),
    },
    first_message: {
      message_id: crypto.randomUUID(),
      sender_type: "assistant",
      content_type: "text",
      content_text: `Welcome! How can I help?`,
      created_at: nowIso(),
    },
  };
  await logEvent(meta, { eventType: "session_initialized", direction: "internal", status: "ok", details: { session_id: sessionId } });
  const result = convertKeys(payload, "toCamel");
  // Record response_sent for transparency contract
  await logEvent(meta, { eventType: "response_sent", direction: "outbound", status: "ok", details: { sessionId, messageId: payload.first_message.message_id } });
  const res = apiOk(result, requestId, { headers: { "traceparent": meta.traceHeader ?? "" } });
  await logRequestEnd(meta, 200);
  return res;
});
  // Validate shape (optional fields)
  const cam = convertKeys(body, "toCamel") as any;
  const v = validate(cam, ConversationStartSchema);
  if (!v.valid) {
    const res = apiError("validation_error", "Invalid request", 400, { errors: v.errors }, requestId);
    await logRequestEnd(meta, 400);
    return res;
  }
// File: arioncomply-v1/supabase/functions/ai-conversation-start/index.ts
