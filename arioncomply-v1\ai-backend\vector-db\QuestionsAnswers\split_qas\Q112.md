id: Q112
query: >-
  Do we need special contracts with vendors who handle our data?
packs:
  - "GDPR:2016"
primary_ids:
  - "GDPR:2016/Art.28"
overlap_ids: []
capability_tags:
  - "Draft Doc"
flags: []
sources:
  - title: "GDPR — Processor Contracts"
    id: "GDPR:2016/Art.28"
    locator: "Article 28"
ui:
  cards_hint:
    - "Data processing agreement templates"
  actions:
    - type: "start_workflow"
      target: "dpa_creation"
      label: "Generate DPA"
output_mode: "both"
graph_required: false
notes: "Data processing agreements are mandatory with processors"
---
### 112) Do we need special contracts with vendors who handle our data?

**Standard terms**  
- **Processor contracts (GDPR Art. 28):** mandatory terms for data processors.

**Plain-English answer**  
Yes—when a vendor processes personal data on your behalf, you must sign a Data Processing Agreement (DPA) including GDPR-mandated terms: processing scope, security measures, sub-processor rules, and audit rights.

**Applies to**  
- **Primary:** GDPR Article 28

**Why it matters**  
Ensures legal basis and protects data subjects.

**Do next in our platform**  
- Launch **DPA Creation** workflow.  
- Store signed DPAs in contract register.

**How our platform will help**  
- **[Draft Doc]** DPA template generator.  
- **[Workflow]** Approval routing and signature tracking.

**Likely follow-ups**  
- “What clauses are mandatory?” (Data breach notification, subprocessors, return/deletion)

**Sources**  
- GDPR Article 28
