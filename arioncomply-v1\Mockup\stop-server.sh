#!/bin/bash
#
# File: arioncomply-v1/Mockup/stop-server.sh
# File Description: Stop the ArionComply UI Mockup server
# Purpose: Gracefully terminate the static file server and cleanup resources
#
# Usage: bash stop-server.sh [--force]
#
# Options:
#   --force    Force kill server processes (SIGKILL instead of SIGTERM)
#   --help     Show usage information
#
# How it works:
#   1. Reads server PID from .mockup-server.pid file if available
#   2. Attempts graceful shutdown with SIGTERM
#   3. Falls back to port-based cleanup for orphaned processes
#   4. Cleans up log files and PID files
#   5. Verifies server has stopped completely
#
# Dependencies: lsof (for port cleanup), kill command
# Security/RLS: Local development only
# Notes: Complements start-server.sh for complete mockup server lifecycle management
#

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd "$SCRIPT_DIR"

DEFAULT_PORT="8080"
FORCE_KILL=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --force|-f)
            FORCE_KILL=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [--force]"
            echo "Stop ArionComply UI Mockup server"
            echo ""
            echo "Options:"
            echo "  --force    Force kill server processes"
            echo "  --help     Show this help message"
            echo ""
            echo "Notes:"
            echo "  • Gracefully stops the static file server"
            echo "  • Cleans up PID and log files"
            echo "  • Use --force if normal shutdown fails"
            exit 0
            ;;
        *)
            echo "❌ Unknown argument: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

echo "🛑 Stopping ArionComply UI Mockup server..."

# Attempt to stop via PID file first
if [ -f ".mockup-server.pid" ]; then
    MOCKUP_PID="$(cat .mockup-server.pid 2>/dev/null || echo "")"
    if [ -n "$MOCKUP_PID" ] && kill -0 "$MOCKUP_PID" 2>/dev/null; then
        echo "📋 Found mockup server process ID: $MOCKUP_PID"
        
        if [ "$FORCE_KILL" = true ]; then
            echo "💥 Force killing mockup server process..."
            kill -9 "$MOCKUP_PID" 2>/dev/null || true
        else
            echo "🏃 Gracefully shutting down mockup server..."
            kill -TERM "$MOCKUP_PID" 2>/dev/null || true
            
            # Wait for graceful shutdown
            sleep 2
            
            # Check if process is still running
            if kill -0 "$MOCKUP_PID" 2>/dev/null; then
                echo "⚠️  Process still running, force killing..."
                kill -9 "$MOCKUP_PID" 2>/dev/null || true
            fi
        fi
        
        echo "✅ Mockup server process terminated"
    else
        echo "⚠️  PID file exists but process not running"
    fi
    
    # Clean up PID file
    rm -f .mockup-server.pid
else
    echo "📋 No PID file found, checking port-based cleanup..."
fi

# Fallback: Clean up any processes on the default port
echo "🧹 Cleaning up any remaining processes on port $DEFAULT_PORT..."
PROCESSES_ON_PORT=$(lsof -ti:"$DEFAULT_PORT" 2>/dev/null || echo "")

if [ -n "$PROCESSES_ON_PORT" ]; then
    echo "🔍 Found processes on port $DEFAULT_PORT: $PROCESSES_ON_PORT"
    
    if [ "$FORCE_KILL" = true ]; then
        echo "💥 Force killing port-bound processes..."
        echo "$PROCESSES_ON_PORT" | xargs kill -9 2>/dev/null || true
    else
        echo "🏃 Gracefully terminating port-bound processes..."
        echo "$PROCESSES_ON_PORT" | xargs kill -TERM 2>/dev/null || true
        
        # Wait and force kill if needed
        sleep 2
        REMAINING_PROCESSES=$(lsof -ti:"$DEFAULT_PORT" 2>/dev/null || echo "")
        if [ -n "$REMAINING_PROCESSES" ]; then
            echo "💥 Force killing remaining processes..."
            echo "$REMAINING_PROCESSES" | xargs kill -9 2>/dev/null || true
        fi
    fi
    
    echo "✅ Port cleanup completed"
else
    echo "✅ No processes found on port $DEFAULT_PORT"
fi

# Clean up log files (archive with timestamp)
if [ -f ".mockup-server.log" ]; then
    echo "🗑️  Archiving mockup server logs..."
    mv .mockup-server.log ".mockup-server.log.$(date +%Y%m%d_%H%M%S)" 2>/dev/null || rm -f .mockup-server.log
    echo "✅ Log files archived"
fi

# Verify server has stopped
sleep 1
if curl -s --connect-timeout 2 "http://127.0.0.1:$DEFAULT_PORT/" > /dev/null 2>&1; then
    echo "⚠️  Server may still be running on port $DEFAULT_PORT"
    echo "💡 Try running with --force flag"
    exit 1
else
    echo "✅ Mockup server confirmed stopped"
fi

echo ""
echo "🎯 ArionComply UI Mockup server stopped successfully"
echo "🎨 Mockup files remain available for manual viewing"
echo "💡 To restart: bash ./start-server.sh"
echo ""
echo "🔧 Other development servers status:"
echo "   🧪 Workflow GUI:  Check http://localhost:10000"
echo "   🤖 AI Backend:    Check http://localhost:9000"
echo ""