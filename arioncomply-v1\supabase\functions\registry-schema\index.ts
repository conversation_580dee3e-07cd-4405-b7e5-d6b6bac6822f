// File: arioncomply-v1/supabase/functions/registry-schema/index.ts
// File Description: Registry schema Edge function
// Purpose: Serve schemas/metadata-v1.json as API endpoint for validation
// Input: GET request
// Output: JSON Schema for metadata validation
// Notes: Returns metadata-v1.json with proper CORS and request tracking

import { serve } from "https://deno.land/std@0.224.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { apiOk, apiError } from "../_shared/errors.ts";
import { getClientMeta, logRequestStart, logRequestEnd, logEvent } from "../_shared/logger.ts";

// Import the metadata schema - this is the validation schema
import metadataSchema from "../../../schemas/metadata-v1.json" assert { type: "json" };

serve(async (req) => {
  const requestId = crypto.randomUUID();
  const meta = getClientMeta(req, requestId);
  await logRequestStart(meta, req.headers);

  // CORS preflight
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  if (req.method !== "GET") {
    const res = apiError("METHOD_NOT_ALLOWED", "Only GET method is allowed", 405, undefined, requestId);
    await logRequestEnd(meta, 405);
    return res;
  }

  try {
    // Log schema access for analytics
    await logEvent(meta, {
      eventType: "registry_schema_accessed",
      direction: "internal", 
      status: "ok",
      details: {
        schemaId: metadataSchema.$id,
        schemaTitle: metadataSchema.title,
        propertiesCount: Object.keys(metadataSchema.properties || {}).length,
        requiredFields: metadataSchema.required?.length || 0,
      }
    });

    // Return schema as-is (already in proper JSON Schema format)
    const res = apiOk(metadataSchema, requestId, {
      headers: {
        ...corsHeaders,
        "Cache-Control": "public, max-age=600", // 10-minute cache for schemas
        "Content-Type": "application/json"
      }
    });
    
    await logRequestEnd(meta, 200);
    return res;
    
  } catch (error) {
    console.error("Registry schema error:", error);
    
    const res = apiError(
      "REGISTRY_SCHEMA_ERROR",
      "Failed to load metadata schema", 
      500,
      { error: String(error) },
      requestId
    );
    
    await logRequestEnd(meta, 500);
    return res;
  }
});