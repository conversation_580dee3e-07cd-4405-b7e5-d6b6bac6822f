<!-- File: arioncomply-v1/ai-backend/python-backend/README.md -->
# ArionComply AI Backend (Skeleton)

Purpose
- Provide a Python service surface for chat orchestration and backend event logging.

Routes
- `POST /ai/chat`: accepts `{ sessionId?, orgId?, userId?, messages[], hints?, explainability? }` and returns `{ provider, model, text, usage? }`.

Event Emission (per request)
- `retrieval_run`: before model call; includes backend, topK, citations, latencyMs.
- `ai_call`: per model invocation; includes provider, model, anonymized, tokens, cost, latencyMs, promptTemplate, promptHash.
- `deterministic_rules_applied` (Pilot+): captures rule evaluation outcomes.

Configuration
- Direct Supabase logs: set `SUPABASE_URL` and `SUPABASE_SERVICE_ROLE_KEY`.
- Transport: `AC_LOG_TRANSPORT=direct|edge` (default: `direct`).
- Optional Edge gateway logging: `EDGE_LOG_URL` (if using edge to persist).

Caveats
- This is a scaffold. Add real retrieval and model providers, JWT parsing, and RLS-compliant policies.
<!-- File: arioncomply-v1/ai-backend/python-backend/README.md -->
