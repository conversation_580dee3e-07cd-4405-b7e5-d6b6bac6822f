id: Q058
query: >-
  What happens if our compliance project takes longer than expected?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/6.1"
  - "ISO27001:2022/9.1"
overlap_ids: []
capability_tags:
  - "Planner"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Planning (6.1)"
    id: "ISO27001:2022/6.1"
    locator: "Clause 6.1"
  - title: "ISO/IEC 27001:2022 — Monitoring & Evaluation (9.1)"
    id: "ISO27001:2022/9.1"
    locator: "Clause 9.1"
ui:
  cards_hint:
    - "Timeline adjustment planner"
  actions:
    - type: "start_workflow"
      target: "timeline_replan"
      label: "Replan Project Timeline"
output_mode: "both"
graph_required: false
notes: "Use PDCA cycle to adjust scope and timeline"
---
### 58) What happens if our compliance project takes longer than expected?

**Standard terms**  
- **Planning (Cl. 6.1):** revise objectives and plans.  
- **Monitoring (Cl. 9.1):** measure progress and update.

**Plain-English answer**  
Use the **Plan-Do-Check-Act** cycle: revisit your risk treatment plan, adjust scope or resources, and re-baseline timelines. Regular monitoring (metrics, dashboards) flags delays early.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clauses 6.1; 9.1

**Why it matters**  
Adaptive planning maintains momentum and prevents scope creep.

**Do next in our platform**  
- Trigger **Replan Timeline** workflow.  
- Update schedule & reassign tasks.

**How our platform will help**  
- **[Planner]** Timeline rebaseline tool.  
- **[Report]** Delay impact analysis.

**Likely follow-ups**  
- “How to accelerate lagging tasks?” (Add resources or defer low-risk items)

**Sources**  
- ISO/IEC 27001:2022 Clauses 6.1; 9.1
