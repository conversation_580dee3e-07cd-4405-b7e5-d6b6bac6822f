id: Q201
query: >-
  When should we hire consultants vs. try to do compliance internally?
packs: []
primary_ids: []
overlap_ids: []
capability_tags:
  - "Report"
  - "Planner"
flags: []
sources: []
ui:
  cards_hint:
    - "Consultant vs internal decision tree"
  actions:
    - type: "start_workflow"
      target: "consultant_decision"
      label: "Assess Need for Consultants"
    - type: "open_register"
      target: "resource_planning"
      label: "Review Resource Plan"
output_mode: "both"
graph_required: false
notes: "Balance platform automation with selective external expertise"
---
### 201) When should we hire consultants vs. try to do compliance internally?

**Standard terms)**
_None_

**Plain-English answer**
Use our **Resource Planner** to map tasks by complexity and risk. If tasks exceed internal capacity or require deep legal/technical expertise (pen testing, legal interpretations), engage consultants. Routine ISMS setup, documentation, and tracking can be managed internally via the platform.

**Applies to**
_None_

**Why it matters**
Optimizes cost by leveraging internal tools first and reserving consultant spend for critical gaps.

**Do next in our platform**
1. Run **Consultant Decision** workflow to score tasks.
2. Review **Resource Planning** register for internal capacity.

**How our platform will help**
- **[Report]** Task complexity vs internal skill heatmap.
- **[Planner]** Timeline blending internal and consultant activities.

**Likely follow-ups**
- Which compliance areas typically need consultants?
---