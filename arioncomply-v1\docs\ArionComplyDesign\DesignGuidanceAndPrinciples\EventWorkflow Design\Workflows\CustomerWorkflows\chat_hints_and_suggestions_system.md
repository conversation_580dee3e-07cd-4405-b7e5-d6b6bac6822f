# Chat Suggestion Chips System
**Implementation of Existing Design Specifications for Phase 1 MVP**

*Version 1.0 - September 15, 2025*

---

## Overview

This document defines the phased implementation strategy for the suggestion chips system, progressing from predetermined chips to fully contextual AI-driven suggestions.

**Implementation Strategy:**
- **Phase 1**: **Predetermined chips** for rapid MVP launch and reliable UX
- **Phase 2+**: **Contextual AI-driven chips** based on conversation analysis and user journey stage

**Core Specifications (All Phases):**
- **Exactly 4 chips per assistant turn** (as specified in existing design)
- **Educational defaults prioritized** for compliance beginners
- **Backend endpoint integration** via `ui-suggestions-defaults`
- **Safe prefill behavior** with optional navigation actions
- **Org-scoped customization** via RLS database architecture

**Design Alignment**: This implementation aligns with existing specifications in `.kiro/specs/standards-compliance-platform/design.md` and requirements.

**Key Functions:**
- **Reduce Cognitive Load**: Eliminate "what do I ask next?" uncertainty
- **Discover Features**: Surface relevant platform capabilities naturally
- **Maintain Momentum**: Keep conversations flowing productively
- **Personalize Experience**: Adapt suggestions to user profile and context
- **Convert Effectively**: Guide toward assessment completion and pilot interest

---

## Phase 1: Predetermined Chips Implementation

### **1.1 Static Chip Configuration**

**MVP Implementation Strategy:**
- **Rapid Launch**: Pre-configured chip sets ensure consistent, reliable user experience
- **No AI Dependencies**: Reduces complexity and potential failure points for Phase 1
- **User Testing**: Allows collection of usage data to inform future contextual logic
- **Performance**: Fast response times with no AI processing delays

```mermaid
graph TD
    A[AI Response Generated] --> B[Determine User Journey Stage]
    B --> C{Journey Stage}

    C --> D[Welcome/Onboarding]
    C --> E[Assessment Discussion]
    C --> F[Report Generated]
    C --> G[Conversion Phase]

    D --> H[Load Welcome Chip Set]
    E --> I[Load Assessment Chip Set]
    F --> J[Load Post-Report Chip Set]
    G --> K[Load Conversion Chip Set]

    H --> L[Display 4 Predetermined Chips]
    I --> L
    J --> L
    K --> L

    L --> M[Track User Chip Selections]
    M --> N[(Usage Analytics DB)]
```

### **1.2 Predetermined Chip Sets by Journey Stage**

**Welcome/Onboarding Chips:**
```json
{
  "stage": "welcome",
  "chips": [
    {"text": "Tell me about your organization", "action": {"type": "nav", "target": "org_profile"}},
    {"text": "Explain compliance basics", "action": {"type": "start_workflow", "target": "education_intro"}},
    {"text": "Start with a quick assessment", "action": {"type": "start_workflow", "target": "assessment_intro"}},
    {"text": "What standards apply to me?", "action": {"type": "nav", "target": "standards_guide"}}
  ]
}
```

**Assessment Discussion Chips:**
```json
{
  "stage": "assessment_active",
  "chips": [
    {"text": "I need help with this question"},
    {"text": "Give me an example"},
    {"text": "Why is this important?"},
    {"text": "Skip to next section", "action": {"type": "start_workflow", "target": "assessment_skip"}}
  ]
}
```

**Post-Assessment Chips:**
```json
{
  "stage": "assessment_complete",
  "chips": [
    {"text": "Generate my detailed report", "action": {"type": "start_workflow", "target": "report_generation"}},
    {"text": "Explain my compliance score"},
    {"text": "What should I prioritize first?"},
    {"text": "Tell me about guided implementation", "action": {"type": "nav", "target": "pilot_program"}}
  ]
}
```

**Conversion Phase Chips:**
```json
{
  "stage": "conversion",
  "chips": [
    {"text": "Learn about the pilot program", "action": {"type": "nav", "target": "pilot_info"}},
    {"text": "Schedule a consultation", "action": {"type": "nav", "target": "schedule"}},
    {"text": "Compare to traditional consulting"},
    {"text": "See implementation timeline"}
  ]
}
```

### **1.3 Phase 1 Implementation Requirements**

**Technical Implementation:**
```typescript
// Phase 1 Chip Selection Logic
interface ChipStageMapping {
  welcome: ChipSet;
  assessment_active: ChipSet;
  assessment_complete: ChipSet;
  conversion: ChipSet;
}

const getChipsForStage = (journeyStage: string): ChipSet => {
  const predeterminedChips: ChipStageMapping = {
    welcome: WELCOME_CHIPS,
    assessment_active: ASSESSMENT_CHIPS,
    assessment_complete: POST_ASSESSMENT_CHIPS,
    conversion: CONVERSION_CHIPS
  };

  return predeterminedChips[journeyStage] || WELCOME_CHIPS;
}
```

**Data Collection for Future Contextual Engine:**
- Track which chips are clicked most frequently by stage
- Monitor user journey progression and drop-off points
- Collect conversation topics and user classification data
- Measure conversion rates by chip interaction patterns

---

## Transition Strategy: Predetermined → Contextual

### **1.4 Hybrid Implementation Approach**

**Phase 1.5 (Transition Phase):**
- **Base Layer**: Predetermined chips ensure reliability
- **Enhancement Layer**: Begin introducing contextual variations based on data
- **A/B Testing**: Test contextual chips against predetermined baselines
- **Gradual Rollout**: Introduce contextual chips to percentage of users

```mermaid
graph TD
    A[User Interaction] --> B{Implementation Phase}

    B -->|Phase 1| C[100% Predetermined Chips]
    B -->|Phase 1.5| D[70% Predetermined + 30% Contextual]
    B -->|Phase 2| E[30% Predetermined + 70% Contextual]
    B -->|Phase 2+| F[100% Contextual with Fallbacks]

    C --> G[Display Static Chip Set]
    D --> H{A/B Test Assignment}
    H -->|Control| G
    H -->|Test| I[Generate Contextual Chips]
    I -->|Fallback on Error| G

    E --> J[Generate Contextual Chips]
    J -->|Fallback on Error| G

    F --> K[Full AI-Driven Chips]
    K -->|Fallback on Error| L[Emergency Predetermined Set]
```

---

## Phase 2+: Contextual AI-Driven Chips

### **2.1 Contextual Suggestion Engine**

**Future Implementation (Phase 2+):**
Once Phase 1 data collection provides insights into user behavior and preferences, the system will evolve to generate contextual chips based on:

#### **2.1.1 Dynamic Chip Generation Workflow**
```mermaid
graph TD
    A[User Message Sent] --> B[Analyze Conversation Context]
    B --> C[Determine User Journey Stage]
    C --> D[Identify User Classification]
    D --> E[Check Recent Topics Discussed]
    E --> F[Generate Contextual Hints]

    F --> G{Hint Categories}
    G --> H[Assessment Advancement]
    G --> I[Educational Deep-dive]
    G --> J[Feature Discovery]
    G --> K[Next Steps Guidance]

    H --> L[Present Suggestion Bubbles]
    I --> L
    J --> L
    K --> L

    L --> M[Track User Selection]
    M --> N[Update Context Model]

    B --> O[(Conversation History)]
    D --> P[(User Profile)]
    F --> Q[(Hint Templates)]
    M --> R[(Engagement Analytics)]
```

#### **1.2 Chip Categories and Triggers**

**Assessment Advancement Chips:**
```json
{
  "triggers": [
    "User completes initial questions but hasn't started formal assessment",
    "User shows interest in specific compliance framework",
    "Assessment partially completed but stalled"
  ],
  "chips": [
    {"text": "Ready for your ISO 27001 assessment?", "action": {"type": "start_workflow", "target": "iso27001_assessment"}},
    {"text": "See how your security measures compare", "action": {"type": "start_workflow", "target": "security_baseline"}},
    {"text": "Complete your compliance evaluation", "action": {"type": "nav", "target": "assessment"}},
    {"text": "Focus on your biggest gaps", "action": {"type": "nav", "target": "gap_analysis"}}
  ]
}
```

**Educational Deep-dive Chips:**
```json
{
  "triggers": [
    "User asks basic questions about compliance concepts",
    "Shows confusion or requests clarification",
    "Expresses being overwhelmed by complexity"
  ],
  "chips": [
    {"text": "What is ISO 27001 and why does it matter?"},
    {"text": "Walk me through GDPR basics"},
    {"text": "How do small companies approach compliance?"},
    {"text": "Show me examples for my industry"}
  ]
}
```

**Feature Discovery Hints:**
```
Triggers:
- User hasn't discovered key platform capabilities
- Completed basic assessment but hasn't explored advanced features
- Shows interest in specific compliance areas

Example Bubbles:
📋 "Generate a detailed compliance report"
🎯 "Get personalized improvement recommendations"
📈 "Compare your progress to industry benchmarks"
🤝 "Learn about our pilot program"
```

**Next Steps Guidance Hints:**
```
Triggers:
- Assessment completed but user seems uncertain about next steps
- High engagement but no conversion action taken
- Long pause in conversation

Example Bubbles:
🚀 "What should I focus on first?"
📅 "Help me create an implementation timeline"
💬 "Schedule a consultation with a compliance expert"
🎯 "How can I prepare for an audit?"
```

---

### **2. User Journey Specific Hint Strategies**

#### **2.1 Startup Reality Check Journey Hints**

**Early Stage Hints:**
```
Context: Startup founder, overwhelmed by compliance requirements
Suggested Bubbles:
💰 "What does compliance cost for a startup like mine?"
⏰ "How long does it take to implement basic compliance?"
🎯 "Which compliance standards should startups prioritize?"
📈 "How does compliance help with investor due diligence?"
```

**Assessment Phase Hints:**
```
Context: Engaged in assessment, needs encouragement and context
Suggested Bubbles:
✅ "How are we doing compared to other startups?"
🎯 "What's the biggest risk I should address first?"
💡 "Show me quick wins for startup compliance"
📋 "What documentation do I actually need?"
```

**Conversion Phase Hints:**
```
Context: Assessment complete, considering next steps
Suggested Bubbles:
🚀 "Help me prioritize these recommendations"
📅 "Create a realistic implementation timeline"
🤝 "Tell me about guided implementation support"
💬 "Connect me with a startup compliance expert"
```

---

#### **2.2 Non-Profit Guidance Journey Hints**

**Mission-Context Hints:**
```
Context: Non-profit organization, budget-conscious, mission-focused
Suggested Bubbles:
💛 "How does compliance support our mission?"
💰 "What are cost-effective compliance approaches?"
🎁 "Are there grants available for compliance initiatives?"
🤝 "How do other non-profits handle compliance?"
```

**Practical Implementation Hints:**
```
Context: Understanding requirements, need practical guidance
Suggested Bubbles:
📋 "Show me templates I can customize"
👥 "How to train volunteers on compliance requirements"
📊 "Simple reporting processes for small teams"
🎯 "Compliance priorities for donor trust"
```

---

#### **2.3 Beginner Education Journey Hints**

**Concept Building Hints:**
```
Context: Learning fundamental compliance concepts
Suggested Bubbles:
🎓 "Explain this in simpler terms"
📚 "What are some real-world examples?"
🔍 "Why is this important for my business?"
➡️ "What should I learn next?"
```

**Confidence Building Hints:**
```
Context: Building understanding and confidence
Suggested Bubbles:
✅ "Quiz me on what I've learned"
🎯 "Show me how this applies to my situation"
📈 "Am I ready for the next topic?"
💪 "Give me an action I can take today"
```

---

#### **2.4 Enterprise Assessment Journey Hints**

**Efficiency-Focused Hints:**
```
Context: Enterprise user, time-conscious, results-oriented
Suggested Bubbles:
⚡ "Skip to advanced assessment topics"
📊 "Compare against industry benchmarks"
🎯 "Focus on high-impact improvements"
📈 "Show ROI calculations for recommendations"
```

**Strategic Planning Hints:**
```
Context: Planning comprehensive compliance program
Suggested Bubbles:
🗓️ "Create a multi-year compliance roadmap"
👥 "Discuss team structure requirements"
🔄 "Integrate with existing processes"
📋 "Prepare for board-level reporting"
```

---

### **3. Technical Implementation Specifications**

#### **3.1 Hint Presentation Logic**

**Bubble Display Rules:**
```typescript
interface HintDisplayRules {
  maxSimultaneousBubbles: 3;
  displayDelay: number; // 2-3 seconds after AI response
  autoHideDelay?: number; // 30 seconds, or null for persistent
  priorityOrdering: 'relevance' | 'conversion' | 'engagement';
  adaptToScreenSize: boolean;
}

// Example Implementation
const displayHints = (context: ConversationContext) => {
  const hints = generateContextualHints(context);
  const prioritizedHints = prioritizeHints(hints, context.userProfile);
  const topHints = prioritizedHints.slice(0, 3);

  displayWithDelay(topHints, 2500); // 2.5 second delay
}
```

**Bubble Interaction Handling:**
```typescript
interface HintInteraction {
  hintId: string;
  userAction: 'clicked' | 'dismissed' | 'ignored';
  timestamp: Date;
  contextAtDisplay: ConversationContext;
  resultingUserMessage?: string;
}

const handleHintClick = (hintId: string, context: ConversationContext) => {
  const hintTemplate = getHintTemplate(hintId);
  const personalizedMessage = personalize(hintTemplate, context);

  // Auto-populate user input field
  populateUserInput(personalizedMessage);

  // Track engagement
  trackHintEngagement(hintId, 'clicked', context);
}
```

---

#### **3.2 Flutter Web Implementation Guidelines**

**UI Component Structure:**
```dart
class ChatHintBubbles extends StatefulWidget {
  final List<ChatHint> hints;
  final Function(ChatHint) onHintSelected;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: hints.map((hint) => HintBubble(
          hint: hint,
          onTap: () => onHintSelected(hint),
        )).toList(),
      ),
    );
  }
}

class HintBubble extends StatelessWidget {
  final ChatHint hint;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Theme.of(context).primaryColor.withOpacity(0.3),
            ),
          ),
          child: Text(
            hint.displayText,
            style: TextStyle(
              color: Theme.of(context).primaryColor,
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }
}
```

**Responsive Design Considerations:**
```dart
class ResponsiveHintBubbles extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth < 600) {
          // Mobile: Stack bubbles vertically, show fewer
          return Column(children: buildMobileHints());
        } else {
          // Desktop: Horizontal layout, show more bubbles
          return Wrap(children: buildDesktopHints());
        }
      },
    );
  }
}
```

---

### **4. Integration with Existing Workflows**

#### **4.1 Updated Chat Interface Workflow**

**Enhanced Conversation Flow with Hints:**
```mermaid
graph TD
    A[User Sends Message] --> B[AI Processes & Responds]
    B --> C[Analyze Conversation Context]
    C --> D[Generate Contextual Hints]
    D --> E[Display Suggestion Bubbles]

    E --> F{User Action}
    F -->|Clicks Hint| G[Auto-populate Input]
    F -->|Types Own Message| H[Track Hint Dismissal]
    F -->|No Action| I[Auto-hide After 30s]

    G --> J[Process Selected Hint]
    H --> K[Continue Natural Conversation]
    I --> K

    J --> L[Update Context Model]
    K --> L
    L --> M[Next Response Cycle]

    D --> N[(Hint Analytics)]
    F --> N
```

**Integration Points:**
1. **After AI Response Generation**: Generate and display contextual hints
2. **User Input Processing**: Handle hint selections as natural user messages
3. **Context Updates**: Incorporate hint interactions into conversation context
4. **Analytics Collection**: Track hint effectiveness and user preferences

---

#### **4.2 Enhanced User Journey Touchpoints**

**Registration/Onboarding:**
- Welcome hints to reduce new user confusion
- Feature discovery prompts
- Assessment invitation bubbles

**Assessment Process:**
- Progress encouragement hints
- Topic exploration suggestions
- Clarification and help prompts

**Post-Assessment:**
- Report exploration hints
- Next steps guidance bubbles
- Pilot program introduction prompts

**Nurturing/Re-engagement:**
- Return visit welcome suggestions
- New feature announcement hints
- Seasonal compliance reminders

---

### **5. Analytics and Optimization**

#### **5.1 Hint Performance Metrics**
- **Click-Through Rate**: Percentage of displayed hints that are clicked
- **Conversion Impact**: Hint clicks that lead to assessment completion
- **User Satisfaction**: Survey feedback on hint helpfulness
- **Context Relevance**: User feedback on hint appropriateness

#### **5.2 A/B Testing Framework**
- **Hint Timing**: Test different display delays and durations
- **Hint Content**: Test different wording and emojis
- **Hint Quantity**: Test optimal number of simultaneous bubbles
- **Hint Positioning**: Test different UI placement options

#### **5.3 Machine Learning Optimization**
- **Personalization**: Learn individual user hint preferences
- **Context Improvement**: Improve hint relevance over time
- **Conversion Optimization**: Identify highest-converting hint patterns
- **Seasonal Adjustment**: Adapt hints based on time of year and industry cycles

---

This hint system transforms the chat interface from a passive conversation tool into an active guidance system that reduces user uncertainty, increases engagement, and drives toward desired outcomes while maintaining a natural, non-intrusive user experience.