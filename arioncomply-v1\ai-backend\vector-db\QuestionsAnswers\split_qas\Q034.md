id: Q034
query: >-
  What software and tools do we need to buy for compliance?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/A.12"
  - "GDPR:2016/Art.32"
overlap_ids: []
capability_tags:
  - "Register"
  - "Draft Doc"
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Cryptography & Operations"
    id: "ISO27001:2022/A.12"
    locator: "Annex A.12"
  - title: "GDPR — Security of Processing"
    id: "GDPR:2016/Art.32"
    locator: "Article 32"
ui:
  cards_hint:
    - "Tool needs analysis"
  actions:
    - type: "open_register"
      target: "tool_inventory"
      label: "Start Tool Assessment"
output_mode: "both"
graph_required: false
notes: "Platform provides all registers, workflows, docs—minimize add-ons"
---
### 34) What software and tools do we need to buy for compliance?

**Standard terms)**
- **Operations & cryptography (ISO 27001 A.12):** tools for patching, backup, and encryption.
- **Security (GDPR Art. 32):** measures like access controls and encryption.

**Plain-English answer**
Beyond basic IT (firewalls, backups, MFA, EDR), you need registers, workflow, and document-management tools. Our platform includes registers, workflows, docs, and dashboards—so you only need point tools for specialized functions (e.g., SIEM, DLP).

**Applies to**
- **Primary:** ISO/IEC 27001:2022 Annex A.12; GDPR Article 32

**Why it matters**
Avoid over-licensing by reusing built-in platform modules.

**Do next in our platform**
- Inventory your **existing tools**.
- Identify **gaps** against control requirements.
- Plan procurement for missing capabilities.

**How our platform will help**
- **[Register]** Tool inventory.
- **[Workflow]** Gap analysis.
- **[Draft Doc]** Procurement templates.

**Likely follow-ups**
- “Can we integrate SIEM with platform?” (Yes—via APIs)

**Sources**
- ISO/IEC 27001:2022 Annex A.12
- GDPR Article 32
