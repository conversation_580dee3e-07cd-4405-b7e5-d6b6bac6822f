id: Q231
query: >-
  What if we have a data breach before we're fully compliant — what do we do?
packs:
  - "GDPR:2016"
  - "ISO27001:2022"
primary_ids:
  - "GDPR:2016/Rec.85"
  - "ISO27001:2022/A.16.1.3"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Report"
flags:
  - "LOCAL LAW_CHECK"
sources:
  - title: "GDPR — Breach Notification Recitals"
    id: "GDPR:2016/Rec.85"
    locator: "Recital 85"
  - title: "ISO/IEC 27001:2022 — Incident Response"
    id: "ISO27001:2022/A.16.1.3"
    locator: "Annex A.16.1.3"
ui:
  cards_hint:
    - "Rapid incident response"
  actions:
    - type: "start_workflow"
      target: "early_incident_response"
      label: "Activate Response Plan"
    - type: "open_register"
      target: "incident_log"
      label: "Log Incident"
output_mode: "both"
graph_required: false
notes: "Mitigate harm immediately, then document gaps for improvement"
---
### 231) What if we have a data breach before we're fully compliant — what do we do?

**Standard terms)**  
- **Breach notification (Rec.85):** principles for timely notification.  
- **Incident response (A.16.1.3):** defined process for managing incidents.

**Plain-English answer**  
Immediately invoke your **Early Incident Response** plan: contain the breach, assess affected data, notify authorities within applicable deadlines (e.g., GDPR 72 h), then document lessons learned and adjust your compliance roadmap to close gaps.

**Applies to**  
- **Primary:** GDPR Recital 85; ISO/IEC 27001:2022 Annex A.16.1.3

**Why it matters**  
Swift action reduces legal fines and reputational damage.

**Do next in our platform**  
- Launch **Early Incident Response** workflow.  
- Record details in the **Incident Log**.

**How our platform will help**  
- **[Workflow]** Automated containment and notification steps.  
- **[Report]** Post-mortem reports with gap analysis.

**Likely follow-ups**  
- How to prioritize breaches while remediating compliance gaps?

**Sources**  
- GDPR Recital 85; ISO/IEC 27001:2022 Annex A.16.1.3

**Legal note:** Consult local breach laws for exact deadlines.  
