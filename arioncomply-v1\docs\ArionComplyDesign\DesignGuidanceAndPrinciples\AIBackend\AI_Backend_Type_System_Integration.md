# AI Backend Type System Integration

## Overview
This document outlines how the ArionComply type system integrates with the Python-based AI backend. It serves as a placeholder with basic information on how the type system should be implemented in the AI backend components, focusing on the input/router function pattern.

## 1. AI Backend Architecture Integration

### Core Integration Principles
- AI backend must follow the same type definitions as the rest of the platform
- Validate data using the same validation rules as database and API
- Maintain field naming consistency with camelCase for API interaction and snake_case for database storage
- Support multi-tenant isolation through organization context

### Input/Router Function Pattern
The AI backend uses an input/router function pattern to preprocess requests and route them to specialized processing modules:

```
User Request → Input/Router Function → [Pre-processing] → Route to Specialized Handler → Response
```

## 2. Python Type System Implementation

### Type Definition Client

```python
# arioncomply-v1/ai-backend/lib/type_system/client.py

import json
import requests
from typing import Dict, Any, List, Optional, Union, TypedDict

class TypeDefinition(TypedDict):
    type_name: str
    pg_type: str
    api_type: str
    ui_type: str
    validation_schema: Dict[str, Any]
    conversion_rules: Optional[Dict[str, Any]]
    metadata: Optional[Dict[str, Any]]

class TypeSystemClient:
    """Client for interacting with ArionComply type system"""
    
    def __init__(self, supabase_url: str, supabase_key: str):
        self.supabase_url = supabase_url
        self.supabase_key = supabase_key
        self.type_cache: Dict[str, TypeDefinition] = {}
        self.entity_fields_cache: Dict[str, Dict[str, str]] = {}
        self.field_mappings_cache: Dict[str, Dict[str, Dict[str, str]]] = {}
    
    def get_type_definition(self, type_name: str, organization_id: Optional[str] = None) -> TypeDefinition:
        """Get type definition from cache or database"""
        cache_key = f"{type_name}:{organization_id}"
        
        if cache_key in self.type_cache:
            return self.type_cache[cache_key]
        
        # Set organization context for RLS if provided
        headers = {
            "apikey": self.supabase_key,
            "Authorization": f"Bearer {self.supabase_key}",
            "Content-Type": "application/json"
        }
        
        params = {"type_name": type_name}
        
        if organization_id:
            # First set the organization context
            self._set_organization_context(organization_id)
        
        # Fetch type definition
        response = requests.post(
            f"{self.supabase_url}/rest/v1/rpc/get_type_definition",
            headers=headers,
            json=params
        )
        
        if response.status_code != 200:
            raise Exception(f"Failed to get type definition: {response.text}")
        
        type_def = response.json()
        self.type_cache[cache_key] = type_def
        
        return type_def
    
    def get_entity_fields(self, entity_name: str, organization_id: Optional[str] = None) -> Dict[str, str]:
        """Get field types for an entity"""
        cache_key = f"{entity_name}:{organization_id}"
        
        if cache_key in self.entity_fields_cache:
            return self.entity_fields_cache[cache_key]
        
        # Set organization context for RLS if provided
        if organization_id:
            self._set_organization_context(organization_id)
        
        # Fetch entity fields
        headers = {
            "apikey": self.supabase_key,
            "Authorization": f"Bearer {self.supabase_key}"
        }
        
        response = requests.get(
            f"{self.supabase_url}/rest/v1/system_metadata.entity_fields?entity_name=eq.{entity_name}&select=field_name,field_type",
            headers=headers
        )
        
        if response.status_code != 200:
            raise Exception(f"Failed to get entity fields: {response.text}")
        
        fields_data = response.json()
        field_types = {field["field_name"]: field["field_type"] for field in fields_data}
        
        self.entity_fields_cache[cache_key] = field_types
        
        return field_types
    
    def get_field_mappings(self, entity_name: str) -> Dict[str, Dict[str, str]]:
        """Get field mappings for an entity"""
        if entity_name in self.field_mappings_cache:
            return self.field_mappings_cache[entity_name]
        
        # Fetch field mappings
        headers = {
            "apikey": self.supabase_key,
            "Authorization": f"Bearer {self.supabase_key}"
        }
        
        response = requests.get(
            f"{self.supabase_url}/rest/v1/field_mappings?table_name=eq.{entity_name}&select=db_to_api_mapping,api_to_db_mapping",
            headers=headers
        )
        
        if response.status_code != 200:
            raise Exception(f"Failed to get field mappings: {response.text}")
        
        mappings_data = response.json()
        
        if not mappings_data:
            raise Exception(f"No field mappings found for entity: {entity_name}")
        
        mappings = {
            "db_to_api": mappings_data[0]["db_to_api_mapping"],
            "api_to_db": mappings_data[0]["api_to_db_mapping"]
        }
        
        self.field_mappings_cache[entity_name] = mappings
        
        return mappings
    
    def validate_field(self, field_value: Any, type_name: str, organization_id: Optional[str] = None) -> bool:
        """Validate a field value against its type definition"""
        type_def = self.get_type_definition(type_name, organization_id)
        validation_schema = type_def["validation_schema"]
        
        # Convert value to JSON for validation
        json_value = field_value
        
        # Validate using JSON schema
        return self._validate_against_schema(validation_schema, json_value)
    
    def convert_to_api(self, field_value: Any, type_name: str, organization_id: Optional[str] = None) -> Any:
        """Convert a database value to API format"""
        type_def = self.get_type_definition(type_name, organization_id)
        
        # Apply conversion rules if defined
        if "conversion_rules" in type_def and "db_to_api" in type_def["conversion_rules"]:
            conversion_rules = type_def["conversion_rules"]["db_to_api"]
            
            # Apply transformation if defined
            if "transform" in conversion_rules:
                # This is a simplified approach - in production, you'd need
                # to implement the actual transformation logic
                # For now, just return the value as-is
                pass
        
        # Handle basic type conversions
        if type_def["pg_type"] == "TIMESTAMPTZ" and isinstance(field_value, str):
            # Convert timestamp to ISO format
            # In production, use proper datetime handling
            return field_value
        
        if type_def["pg_type"] == "JSONB" and isinstance(field_value, str):
            # Parse JSON string to object
            try:
                return json.loads(field_value)
            except:
                return field_value
        
        # Default: return value as-is
        return field_value
    
    def convert_to_db(self, field_value: Any, type_name: str, organization_id: Optional[str] = None) -> Any:
        """Convert an API value to database format"""
        type_def = self.get_type_definition(type_name, organization_id)
        
        # Apply conversion rules if defined
        if "conversion_rules" in type_def and "api_to_db" in type_def["conversion_rules"]:
            conversion_rules = type_def["conversion_rules"]["api_to_db"]
            
            # Apply transformation if defined
            if "transform" in conversion_rules:
                # This is a simplified approach - in production, you'd need
                # to implement the actual transformation logic
                # For now, just return the value as-is
                pass
        
        # Handle basic type conversions
        if type_def["pg_type"] == "JSONB" and not isinstance(field_value, str):
            # Convert object to JSON string
            return json.dumps(field_value)
        
        # Default: return value as-is
        return field_value
    
    def _set_organization_context(self, organization_id: str) -> None:
        """Set organization context for RLS"""
        headers = {
            "apikey": self.supabase_key,
            "Authorization": f"Bearer {self.supabase_key}",
            "Content-Type": "application/json"
        }
        
        params = {
            "setting_name": "app.current_organization_id",
            "setting_value": organization_id,
            "is_local": True
        }
        
        response = requests.post(
            f"{self.supabase_url}/rest/v1/rpc/set_config",
            headers=headers,
            json=params
        )
        
        if response.status_code != 200:
            raise Exception(f"Failed to set organization context: {response.text}")
    
    def _validate_against_schema(self, schema: Dict[str, Any], value: Any) -> bool:
        """Validate a value against a JSON schema"""
        # This is a simplified implementation
        # In production, use a proper JSON schema validator like jsonschema
        
        # Type validation
        if "type" in schema:
            schema_type = schema["type"]
            
            if schema_type == "string" and not isinstance(value, str):
                return False
            
            if schema_type == "number" and not isinstance(value, (int, float)):
                return False
            
            if schema_type == "integer" and not isinstance(value, int):
                return False
            
            if schema_type == "boolean" and not isinstance(value, bool):
                return False
            
            if schema_type == "object" and not isinstance(value, dict):
                return False
            
            if schema_type == "array" and not isinstance(value, list):
                return False
        
        # Required properties
        if "required" in schema and isinstance(value, dict):
            for field in schema["required"]:
                if field not in value:
                    return False
        
        # Enum validation
        if "enum" in schema and value not in schema["enum"]:
            return False
        
        # Format validation
        if "format" in schema and schema["format"] == "email" and isinstance(value, str):
            import re
            email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_regex, value):
                return False
        
        # Length validation
        if isinstance(value, str):
            if "minLength" in schema and len(value) < schema["minLength"]:
                return False
            
            if "maxLength" in schema and len(value) > schema["maxLength"]:
                return False
        
        # Number validation
        if isinstance(value, (int, float)):
            if "minimum" in schema and value < schema["minimum"]:
                return False
            
            if "maximum" in schema and value > schema["maximum"]:
                return False
        
        return True
```

## 3. Input/Router Function Implementation

### Example Router Function

```python
# arioncomply-v1/ai-backend/routers/input_router.py

import json
from typing import Dict, Any, Optional, List, Union
from fastapi import FastAPI, Request, HTTPException, Depends
from fastapi.responses import JSONResponse

from lib.type_system.client import TypeSystemClient
from lib.auth.token_validator import validate_token, get_user_from_token

# Initialize type system client
type_client = TypeSystemClient(
    supabase_url="https://your-project.supabase.co",
    supabase_key="your-service-role-key"
)

app = FastAPI()

@app.post("/ai/process")
async def process_input(
    request: Request,
    user_data: Dict[str, Any] = Depends(get_user_from_token)
):
    """
    Main AI input router function
    
    This function:
    1. Validates the input data against type definitions
    2. Pre-processes the request
    3. Routes to the appropriate specialized handler
    4. Returns the response
    """
    try:
        # Get organization context
        organization_id = user_data.get("organization_id")
        if not organization_id:
            raise HTTPException(status_code=400, detail="Organization ID not found in user data")
        
        # Parse request body
        body = await request.json()
        
        # Extract request details
        entity_type = body.get("entityType")
        operation = body.get("operation")
        data = body.get("data", {})
        
        if not entity_type or not operation:
            raise HTTPException(status_code=400, detail="Missing required fields: entityType, operation")
        
        # Validate entity type
        try:
            entity_fields = type_client.get_entity_fields(entity_type, organization_id)
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Invalid entity type: {str(e)}")
        
        # Validate data fields
        validation_errors = {}
        for field_name, field_value in data.items():
            # Convert from camelCase to snake_case
            try:
                field_mappings = type_client.get_field_mappings(entity_type)
                db_field_name = field_mappings["api_to_db"].get(field_name)
                
                if not db_field_name:
                    validation_errors[field_name] = "Unknown field"
                    continue
                
                field_type = entity_fields.get(db_field_name)
                if not field_type:
                    validation_errors[field_name] = "Unknown field type"
                    continue
                
                # Validate field value
                is_valid = type_client.validate_field(field_value, field_type, organization_id)
                if not is_valid:
                    validation_errors[field_name] = "Invalid value"
            except Exception as e:
                validation_errors[field_name] = str(e)
        
        if validation_errors:
            return JSONResponse(
                status_code=400,
                content={"error": "Validation failed", "details": validation_errors}
            )
        
        # Pre-process request
        processed_data = preprocess_request(entity_type, operation, data, organization_id)
        
        # Route to specialized handler
        response = route_to_handler(entity_type, operation, processed_data, organization_id)
        
        return response
    
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

def preprocess_request(
    entity_type: str,
    operation: str,
    data: Dict[str, Any],
    organization_id: str
) -> Dict[str, Any]:
    """Pre-process request data before routing"""
    # Convert fields from camelCase to snake_case
    field_mappings = type_client.get_field_mappings(entity_type)
    processed_data = {}
    
    for field_name, field_value in data.items():
        # Get database field name
        db_field_name = field_mappings["api_to_db"].get(field_name)
        if not db_field_name:
            continue
        
        # Get field type
        entity_fields = type_client.get_entity_fields(entity_type, organization_id)
        field_type = entity_fields.get(db_field_name)
        
        if not field_type:
            continue
        
        # Convert value to database format
        db_value = type_client.convert_to_db(field_value, field_type, organization_id)
        processed_data[db_field_name] = db_value
    
    # Add metadata for AI processing
    processed_data["ai_metadata"] = {
        "generated_by_ai": False,
        "processing_context": {
            "entity_type": entity_type,
            "operation": operation,
            "organization_id": organization_id,
            "timestamp": import datetime; datetime.datetime.now().isoformat()
        }
    }
    
    return processed_data

def route_to_handler(
    entity_type: str,
    operation: str,
    data: Dict[str, Any],
    organization_id: str
) -> Dict[str, Any]:
    """Route request to specialized handler based on entity type and operation"""
    # This is a placeholder implementation
    # In production, you would have specialized handlers for different entity types and operations
    
    # Example routing logic
    if entity_type == "policies" and operation == "generate":
        return policy_generator_handler(data, organization_id)
    
    if entity_type == "controls" and operation == "suggest":
        return control_suggestion_handler(data, organization_id)
    
    if entity_type == "risks" and operation == "assess":
        return risk_assessment_handler(data, organization_id)
    
    # Default handler
    return default_handler(entity_type, operation, data, organization_id)

# Placeholder specialized handlers
def policy_generator_handler(data: Dict[str, Any], organization_id: str) -> Dict[str, Any]:
    """Handle policy generation requests"""
    # Placeholder implementation
    return {
        "result": "success",
        "generated_policy": {
            "title": "Generated Policy",
            "content": "This is a placeholder for generated policy content"
        }
    }

def control_suggestion_handler(data: Dict[str, Any], organization_id: str) -> Dict[str, Any]:
    """Handle control suggestion requests"""
    # Placeholder implementation
    return {
        "result": "success",
        "suggested_controls": [
            {"id": "ctrl-1", "title": "Access Control Policy"},
            {"id": "ctrl-2", "title": "Data Encryption Standard"}
        ]
    }

def risk_assessment_handler(data: Dict[str, Any], organization_id: str) -> Dict[str, Any]:
    """Handle risk assessment requests"""
    # Placeholder implementation
    return {
        "result": "success",
        "risk_assessment": {
            "risk_level": "medium",
            "findings": ["Finding 1", "Finding 2"],
            "recommendations": ["Recommendation 1", "Recommendation 2"]
        }
    }

def default_handler(
    entity_type: str,
    operation: str,
    data: Dict[str, Any],
    organization_id: str
) -> Dict[str, Any]:
    """Default handler for unrecognized entity types or operations"""
    return {
        "result": "success",
        "message": f"Processed {operation} for {entity_type}",
        "processed_data": data
    }
```

## 4. Field Mapping Utilities

### Snake Case ↔ CamelCase Conversion

```python
# arioncomply-v1/ai-backend/lib/utils/field_mapper.py

import re
from typing import Dict, Any, List, Union

def snake_to_camel(snake_str: str) -> str:
    """Convert snake_case to camelCase"""
    components = snake_str.split('_')
    return components[0] + ''.join(x.title() for x in components[1:])

def camel_to_snake(camel_str: str) -> str:
    """Convert camelCase to snake_case"""
    snake_str = re.sub('([a-z0-9])([A-Z])', r'\1_\2', camel_str)
    return snake_str.lower()

def convert_dict_keys_to_camel(snake_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Convert all dictionary keys from snake_case to camelCase"""
    return {snake_to_camel(k): v for k, v in snake_dict.items()}

def convert_dict_keys_to_snake(camel_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Convert all dictionary keys from camelCase to snake_case"""
    return {camel_to_snake(k): v for k, v in camel_dict.items()}

def convert_nested_dict_to_camel(data: Any) -> Any:
    """Convert all keys in a nested dictionary from snake_case to camelCase"""
    if isinstance(data, dict):
        return {snake_to_camel(k): convert_nested_dict_to_camel(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [convert_nested_dict_to_camel(item) for item in data]
    else:
        return data

def convert_nested_dict_to_snake(data: Any) -> Any:
    """Convert all keys in a nested dictionary from camelCase to snake_case"""
    if isinstance(data, dict):
        return {camel_to_snake(k): convert_nested_dict_to_snake(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [convert_nested_dict_to_snake(item) for item in data]
    else:
        return data
```

## 5. JSON Schema Validation

### Python JSON Schema Validator

```python
# arioncomply-v1/ai-backend/lib/validation/json_schema.py

import json
import re
from typing import Dict, Any, List, Optional, Union, Tuple

def validate_schema(schema: Dict[str, Any], data: Any) -> Tuple[bool, Optional[Dict[str, str]]]:
    """
    Validate data against a JSON schema
    
    Returns:
        Tuple of (is_valid, error_details)
        is_valid: bool - True if validation passed, False otherwise
        error_details: Dict[str, str] - Details of validation errors, or None if valid
    """
    errors = {}
    
    # Type validation
    if "type" in schema:
        schema_type = schema["type"]
        type_valid, type_error = validate_type(data, schema_type)
        
        if not type_valid:
            errors["type"] = type_error
            return False, errors
    
    # Object validation
    if schema.get("type") == "object" and isinstance(data, dict):
        # Required properties
        if "required" in schema:
            for prop in schema["required"]:
                if prop not in data:
                    errors[f"required.{prop}"] = f"Missing required property: {prop}"
        
        # Properties validation
        if "properties" in schema and isinstance(data, dict):
            for prop, prop_schema in schema["properties"].items():
                if prop in data:
                    prop_valid, prop_errors = validate_schema(prop_schema, data[prop])
                    if not prop_valid:
                        for error_key, error_msg in prop_errors.items():
                            errors[f"{prop}.{error_key}"] = error_msg
    
    # Array validation
    if schema.get("type") == "array" and isinstance(data, list):
        # Items validation
        if "items" in schema:
            for i, item in enumerate(data):
                item_valid, item_errors = validate_schema(schema["items"], item)
                if not item_valid:
                    for error_key, error_msg in item_errors.items():
                        errors[f"[{i}].{error_key}"] = error_msg
        
        # Array length
        if "minItems" in schema and len(data) < schema["minItems"]:
            errors["minItems"] = f"Array length {len(data)} is less than minimum {schema['minItems']}"
        
        if "maxItems" in schema and len(data) > schema["maxItems"]:
            errors["maxItems"] = f"Array length {len(data)} exceeds maximum {schema['maxItems']}"
    
    # String validation
    if schema.get("type") == "string" and isinstance(data, str):
        # String length
        if "minLength" in schema and len(data) < schema["minLength"]:
            errors["minLength"] = f"String length {len(data)} is less than minimum {schema['minLength']}"
        
        if "maxLength" in schema and len(data) > schema["maxLength"]:
            errors["maxLength"] = f"String length {len(data)} exceeds maximum {schema['maxLength']}"
        
        # Pattern
        if "pattern" in schema:
            try:
                pattern = re.compile(schema["pattern"])
                if not pattern.match(data):
                    errors["pattern"] = f"String does not match pattern: {schema['pattern']}"
            except re.error:
                errors["pattern"] = f"Invalid regex pattern: {schema['pattern']}"
        
        # Format
        if "format" in schema:
            format_valid, format_error = validate_format(data, schema["format"])
            if not format_valid:
                errors["format"] = format_error
    
    # Number validation
    if schema.get("type") in ["number", "integer"] and isinstance(data, (int, float)):
        # Minimum/maximum
        if "minimum" in schema and data < schema["minimum"]:
            errors["minimum"] = f"Value {data} is less than minimum {schema['minimum']}"
        
        if "maximum" in schema and data > schema["maximum"]:
            errors["maximum"] = f"Value {data} exceeds maximum {schema['maximum']}"
        
        # MultipleOf
        if "multipleOf" in schema and data % schema["multipleOf"] != 0:
            errors["multipleOf"] = f"Value {data} is not a multiple of {schema['multipleOf']}"
    
    # Enum validation
    if "enum" in schema and data not in schema["enum"]:
        enum_values = ", ".join(str(v) for v in schema["enum"])
        errors["enum"] = f"Value {data} is not one of the allowed values: {enum_values}"
    
    # Result
    is_valid = len(errors) == 0
    return is_valid, errors if not is_valid else None

def validate_type(data: Any, expected_type: str) -> Tuple[bool, Optional[str]]:
    """Validate that data matches the expected JSON Schema type"""
    if expected_type == "null" and data is None:
        return True, None
    
    if expected_type == "boolean" and isinstance(data, bool):
        return True, None
    
    if expected_type == "object" and isinstance(data, dict):
        return True, None
    
    if expected_type == "array" and isinstance(data, list):
        return True, None
    
    if expected_type == "number" and isinstance(data, (int, float)):
        return True, None
    
    if expected_type == "integer" and isinstance(data, int):
        return True, None
    
    if expected_type == "string" and isinstance(data, str):
        return True, None
    
    return False, f"Expected {expected_type}, got {type(data).__name__}"

def validate_format(value: str, format_name: str) -> Tuple[bool, Optional[str]]:
    """Validate string against a format constraint"""
    if format_name == "date-time":
        # Simplified ISO 8601 date-time validation
        # In production, use a more robust implementation
        pattern = r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(.\d+)?(Z|[+-]\d{2}:\d{2})?$'
        return bool(re.match(pattern, value)), "Invalid ISO 8601 date-time format"
    
    if format_name == "date":
        pattern = r'^\d{4}-\d{2}-\d{2}$'
        return bool(re.match(pattern, value)), "Invalid ISO 8601 date format"
    
    if format_name == "email":
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, value)), "Invalid email format"
    
    if format_name == "uri":
        pattern = r'^https?://[\w.-]+(:\d+)?([\w/.-]*)?(\?\S*)?$'
        return bool(re.match(pattern, value)), "Invalid URI format"
    
    if format_name == "uuid":
        pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
        return bool(re.match(pattern, value)), "Invalid UUID format"
    
    # Unknown format - consider valid by default
    return True, None
```

## Implementation Guidelines

### AI Backend Integration Approach
1. **Type System Client**: Initialize the type system client in AI services
2. **Data Validation**: Validate all incoming and outgoing data against type definitions
3. **Field Mapping**: Convert between snake_case and camelCase consistently
4. **Organization Context**: Maintain organization context for multi-tenant isolation
5. **AI Metadata**: Track AI processing metadata using the established schema

### Implementation Phases
1. **Phase 1**: Implement the core type system client and field mapping utilities
2. **Phase 2**: Integrate the input/router function with pre-processing
3. **Phase 3**: Implement specialized handlers for different entity types and operations
4. **Phase 4**: Add comprehensive validation and error handling

## Assumptions
- Python 3.8+ for AI backend implementation
- FastAPI for API endpoints
- Supabase for database access
- JSON Schema for validation
- Deterministic AI processing with explicit metadata tracking

## Review Flags
- [ ] Performance impact of remote type definition queries
- [ ] Type conversion consistency across platforms
- [ ] Multi-tenant isolation enforcement
- [ ] Error handling and validation thoroughness
- [ ] AI metadata schema compliance

## Completeness Checklist
- [x] Type system client placeholder
- [x] Input/router function placeholder
- [x] Field mapping utilities placeholder
- [x] JSON Schema validation placeholder
- [ ] Specialized AI handlers (to be implemented)
- [ ] Integration with LLM services (to be implemented)
- [ ] Comprehensive testing (to be implemented)
- [ ] Documentation (to be expanded)