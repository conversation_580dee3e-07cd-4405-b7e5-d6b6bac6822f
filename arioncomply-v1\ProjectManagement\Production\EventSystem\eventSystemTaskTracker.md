<!-- File: arioncomply-v1/ProjectManagement/Production/EventSystem/eventSystemTaskTracker.md -->
# ArionComply Event System Technical Task Tracker

**Document Version:** 1.0  
**Date:** August 27, 2025  
**Status:** Active  
**Reference Documents:** 
- arioncomply-v1/docs/ArionComplyDesign/ApplicationDatabase/DBSchema/notification-system-schema.md
- arioncomply-v1/docs/ArionComplyDesign/DataDriveUISchemaAndFunctions/arioncomply-metadata-architecture-overview.md
- arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js

---

## MVP-Assessment-App Priority Scope (Events)

- [ ] Event emission from conversation and assessment endpoints via `_shared/logger.ts`.
- [ ] Minimal taxonomy covering: `request_received`, `user_message_received`, `assistant_message_generated`, `assessment_answer_recorded`, `assessment_scored`, `response_sent`.
- [ ] No notification delivery required for first test pass; logs suffice.
- [ ] Audit Read endpoint (org-scoped, paginated, filters) — In Progress

### Audit & Logging Taxonomy (MVP-Assessment-App)
- [ ] Finalize minimal taxonomy: `request_received`, `response_sent`, `user_message_received`, `assistant_message_generated`, `ai_call`, `db_read`, `db_write`, `stream_started`, `stream_finished`
- [ ] Publish event naming and required fields (request_id, org_id, user_id, direction, status, details) to Edge team — Dependencies: taxonomy approved

## MVP-Demo-Light-App Priority Scope (Events)

- [ ] Scenario lifecycle events (init, reset) and document generation events logged.

## MVP-Pilot Scope (Events)

- [ ] Events for register updates and plan/task creation; ensure auditability of certification workflows.

## Event/Workflow System Status (Post-Review - Aug 2025)

**IMPLEMENTED - Working Infrastructure:**
- ✅ **Event Logging**: Tables `api_request_logs`, `api_event_logs` with proper RLS (migration 0011)
- ✅ **Event Taxonomy**: Defined for MVP phases with standardized fields (request_id, org_id, event_type, direction, details)
- ✅ **Architecture Design**: `event_workflow_design_principles.md` provides comprehensive metadata-driven event system design

**IMPLEMENTATION GAP - DEFER TO LATER PHASE:**
- ⏸️ **Event Registry Tables**: `system_metadata.event_registry` not implemented - sufficient logging exists for current needs
- ⏸️ **Workflow Engine**: Execution infrastructure not implemented - business process workflows well-documented
- ⏸️ **Metadata-Driven Triggers**: `emit_metadata_event()` function designed but not implemented

**RECOMMENDATION:**
- Current event logging (0011) provides adequate audit trail for MVP phases
- Comprehensive workflow documentation guides business processes  
- Defer full metadata-driven event system until core features stabilize
- Priority: **LOW** - well-designed, adequate current functionality, complex infrastructure can wait

## Production Scope (Later Phases)

- [ ] Event registry/config tables, hook processing, DB triggers.
- [ ] Notification channels (in-app/email/push) and delivery retries.
- [ ] Analytics/reporting events and dashboards.

## Testing Readiness Deployment Checklist (Event System)

- [ ] Confirm `api_request_logs` and `api_event_logs` (0011 migration) exist for event/audit inspection.
- [ ] Define initial event taxonomy and minimal hooks to emit events from conversation endpoints (e.g., `user_message_received`, `assistant_message_generated`).
- [ ] Verify events written via `_shared/logger.ts` with proper `request_id`, `org_id`, and timestamps.
- [ ] Plan notification wiring to follow (not required for first API testing pass).

---

## 1. Core Event Infrastructure

### 1.1 Event Registry and Configuration
- [ ] **Task:** Implement event registry and configuration tables
  - **Dependencies:** Metadata Registry Core
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DataDriveUISchemaAndFunctions/arioncomply-metadata-architecture-overview.md
  - **Components:**
    - [ ] Event types registry
    - [ ] Event hook configuration
    - [ ] Event subscription management
    - [ ] Event permission settings
    - [ ] Event trigger conditions

### 1.2 Hook Processing System
- [ ] **Task:** Implement hook processing system
  - **Dependencies:** Event Registry and Configuration
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] Before/after operation hooks
    - [ ] Conditional hook execution
    - [ ] Hook chaining and sequencing
    - [ ] Success/failure handling
    - [ ] Hook execution logging

### 1.3 Database Triggers and Listeners
- [ ] **Task:** Implement database triggers and listeners
  - **Dependencies:** Hook Processing System
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DataDriveUISchemaAndFunctions/arioncomply-metadata-architecture-overview.md
  - **Components:**
    - [ ] Create/update/delete triggers
    - [ ] State change listeners
    - [ ] Relationship modification triggers
    - [ ] Custom trigger conditions
    - [ ] Trigger debouncing

---

## 2. Notification Integration

### 2.1 Notification Generation
- [ ] **Task:** Implement notification generation from events
  - **Dependencies:** Hook Processing System, Core Notification System
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/ApplicationDatabase/DBSchema/notification-system-schema.md
  - **Components:**
    - [ ] Template-based notification creation
    - [ ] Context enrichment for notifications
    - [ ] Priority determination
    - [ ] Recipient resolution
    - [ ] Action URL generation

### 2.2 Multi-Channel Delivery
- [ ] **Task:** Implement multi-channel notification delivery
  - **Dependencies:** Notification Generation
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/ApplicationDatabase/DBSchema/notification-system-schema.md
  - **Components:**
    - [ ] In-app notification delivery
    - [ ] Email notification delivery
    - [ ] Mobile push notification integration
    - [ ] Delivery status tracking
    - [ ] Delivery retry mechanism

### 2.3 Flutter Integration for Real-Time Notifications
- [ ] **Task:** Implement Flutter integration for real-time notifications
  - **Dependencies:** Multi-Channel Delivery
  - **Verification Document:** arioncomply-v1/docs/functional-definitions/Workflows/notification-workflow.md
  - **Components:**
    - [ ] WebSocket connection for real-time updates
    - [ ] Local notification storage and management
    - [ ] Notification badge and count handling
    - [ ] Notification drawer implementation
    - [ ] Deep linking from notifications

---

## 3. Audit and Compliance Tracking

### 3.1 Audit Trail Generation
- [ ] **Task:** Implement audit trail generation from events
  - **Dependencies:** Hook Processing System
  - **Verification Document:** arioncomply-v1/docs/functional-definitions/Mappings/db-security-mapping.md
  - **Components:**
    - [ ] Detailed change tracking
    - [ ] User action logging
    - [ ] System event recording
    - [ ] Compliance-specific audit records
    - [ ] Audit entry classification

### 3.2 Compliance Event Handling
- [ ] **Task:** Implement compliance event handling
  - **Dependencies:** Audit Trail Generation
  - **Verification Document:** arioncomply-v1/docs/functional-definitions/Mappings/db-security-mapping.md
  - **Components:**
    - [ ] Evidence collection triggers
    - [ ] Compliance status updates
    - [ ] Deadline and milestone tracking
    - [ ] Control implementation events
    - [ ] Compliance deviation alerts

### 3.3 Reporting and Analytics Events
- [ ] **Task:** Implement reporting and analytics event processing
  - **Dependencies:** Compliance Event Handling
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/ApplicationDatabase/DBSchema/metrics-analytics-schema.md
  - **Components:**
    - [ ] Metric update events
    - [ ] Dashboard refresh triggers
    - [ ] Report generation events
    - [ ] Analytics data collection
    - [ ] Trend analysis triggers

---

## 4. Workflow Integration (Immediate Need)

### 4.1 Predefined Workflow Transitions
- [ ] **Task:** Implement predefined workflow transition events
  - **Dependencies:** Hook Processing System
  - **Verification Document:** arioncomply-v1/docs/functional-definitions/Mappings/db-workflow-mapping.md
  - **Components:**
    - [ ] State transition events
    - [ ] Approval/rejection events
    - [ ] Stage completion events
    - [ ] Workflow initialization events
    - [ ] Deadline events

### 4.2 Task Management Events
- [ ] **Task:** Implement task management events
  - **Dependencies:** Predefined Workflow Transitions
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/ApplicationDatabase/DBSchema/task-management-schema.md
  - **Components:**
    - [ ] Task assignment events
    - [ ] Task status change events
    - [ ] Task completion events
    - [ ] Task dependency resolution events
    - [ ] Task overdue events

### 4.3 Document Workflow Events
- [ ] **Task:** Implement document workflow events
  - **Dependencies:** Predefined Workflow Transitions
  - **Verification Document:** arioncomply-v1/docs/functional-definitions/Mappings/db-workflow-mapping.md
  - **Components:**
    - [ ] Document creation events
    - [ ] Review/approval events
    - [ ] Version control events
    - [ ] Publication events
    - [ ] Expiration/renewal events

---

## 5. Future Extensions (Workflow Engine Integration)

### 5.1 Workflow Engine Hooks
- [ ] **Task:** Design workflow engine hook integration
  - **Dependencies:** Predefined Workflow Transitions
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Engine state synchronization
    - [ ] Custom workflow trigger points
    - [ ] Process orchestration integration
    - [ ] Dynamic workflow routing
    - [ ] Cross-workflow coordination

### 5.2 Rule-Based Event Processing
- [ ] **Task:** Design rule-based event processing
  - **Dependencies:** Workflow Engine Hooks
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Event rule definitions
    - [ ] Conditional processing rules
    - [ ] Action sequence definitions
    - [ ] Complex event pattern detection
    - [ ] Time-based event correlation

### 5.3 Metadata-Driven Workflow Events
- [ ] **Task:** Design metadata-driven workflow events
  - **Dependencies:** Rule-Based Event Processing
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Dynamic workflow definition from metadata
    - [ ] Custom event type registration
    - [ ] User-configurable event handlers
    - [ ] Event transformation pipelines
    - [ ] Organization-specific workflow extensions
