id: Q159
query: >-
  How do we handle customer data from international clients?
packs:
  - "GDPR:2016"
  - "EUAI:2024"
primary_ids:
  - "GDPR:2016/Art.44"
  - "EUAI:2024/Art.77"
overlap_ids: []
capability_tags:
  - "Register"
  - "Workflow"
flags:
  - "LOCAL LAW CHECK"
sources:
  - title: "GDPR — Transfers Subject to Appropriate Safeguards"
    id: "GDPR:2016/Art.44"
    locator: "Article 44"
  - title: "EU AI Act — Cross-Border Transfers"
    id: "EUAI:2024/Art.77"
    locator: "Article 77"
ui:
  cards_hint:
    - "Cross-border transfer register"
  actions:
    - type: "open_register"
      target: "transfer_register"
      label: "View Transfers"
    - type: "start_workflow"
      target: "transfer_impacts"
      label: "Assess Transfer Impacts"
output_mode: "both"
graph_required: false
notes: "Use Standard Contractual Clauses or adequacy decisions; check local laws"
---
### 159) How do we handle customer data from international clients?

**Standard terms**  
- **Data transfers (GDPR Art. 44):** must use appropriate safeguards.  
- **AI Act transfers (Art. 77):** restrictions on exporting data.

**Plain-English answer**  
Record each transfer in a **Cross-Border Transfer Register**, apply SCCs or rely on adequacy decisions, encrypt data in transit, and review national laws for specific restrictions **[LOCAL LAW CHECK]**.

**Applies to**  
- **Primary:** GDPR Article 44; EU AI Act Article 77

**Why it matters**  
Ensures lawful international data flows and avoids enforcement.

**Do next in our platform**  
- Populate **Transfer Register**.  
- Run **Transfer Impacts** workflow.

**How our platform will help**  
- **[Register]** Pre-built transfer safeguards library.  
- **[Workflow]** Automated SCC insertion and review tasks.

**Likely follow-ups**  
- “Which countries have adequacy decisions?” (EU Commission list)

**Sources**  
- GDPR Article 44; EU AI Act Article 77

**Legal note:** We’re not your lawyers. For jurisdiction-specific guidance, consult qualified counsel.
::contentReference[oaicite:0]{index=0}








Sources
You said:
let's keep going


ChatGPT said:
Here are Q132–Q140, fully compliant with the Authoring Guide:

