-- File: arioncomply-v1/ai-backend/supabase_migrations/vector/0002_dual_vector_schema_enhanced.sql
-- File Description: Migration 0002 enhances vector project with dual-vector architecture
-- Purpose: Upgrade schema for dual-vector architecture with data classification and multi-pipeline support
-- Security/RLS: Enhanced RLS with data classification, audit trails, retention management
-- Notes: Supports multiple embedding dimensions, intent classification, and confidence scoring

BEGIN;

-- Add data classification and enhanced metadata to documents
ALTER TABLE documents ADD COLUMN IF NOT EXISTS data_classification text NOT NULL DEFAULT 'private';
ALTER TABLE documents ADD COLUMN IF NOT EXISTS document_type text;
ALTER TABLE documents ADD COLUMN IF NOT EXISTS sensitivity_level text NOT NULL DEFAULT 'internal';
ALTER TABLE documents ADD COLUMN IF NOT EXISTS owner_department text;
ALTER TABLE documents ADD COLUMN IF NOT EXISTS compliance_frameworks jsonb DEFAULT '[]';
ALTER TABLE documents ADD COLUMN IF NOT EXISTS retention_period_days int;
ALTER TABLE documents ADD COLUMN IF NOT EXISTS retention_expiry timestamptz;
ALTER TABLE documents ADD COLUMN IF NOT EXISTS content_hash text;
ALTER TABLE documents ADD COLUMN IF NOT EXISTS updated_at timestamptz NOT NULL DEFAULT now();

-- Add constraints for data classification
ALTER TABLE documents ADD CONSTRAINT IF NOT EXISTS check_data_classification 
  CHECK (data_classification IN ('private', 'internal', 'confidential', 'restricted'));
ALTER TABLE documents ADD CONSTRAINT IF NOT EXISTS check_sensitivity_level 
  CHECK (sensitivity_level IN ('internal', 'confidential', 'restricted', 'public_internal'));

-- Enhanced chunks table with privacy and intent metadata
ALTER TABLE chunks ADD COLUMN IF NOT EXISTS content_type text;
ALTER TABLE chunks ADD COLUMN IF NOT EXISTS intent_classification jsonb;
ALTER TABLE chunks ADD COLUMN IF NOT EXISTS privacy_metadata jsonb DEFAULT '{}';
ALTER TABLE chunks ADD COLUMN IF NOT EXISTS compliance_tags jsonb DEFAULT '[]';
ALTER TABLE chunks ADD COLUMN IF NOT EXISTS chunk_hash text;
ALTER TABLE chunks ADD COLUMN IF NOT EXISTS updated_at timestamptz NOT NULL DEFAULT now();

-- Enhanced embeddings table for multi-pipeline support
ALTER TABLE embeddings ADD COLUMN IF NOT EXISTS pipeline_name text NOT NULL DEFAULT 'unknown';
ALTER TABLE embeddings ADD COLUMN IF NOT EXISTS model_version text;
ALTER TABLE embeddings ADD COLUMN IF NOT EXISTS data_classification text NOT NULL DEFAULT 'private';
ALTER TABLE embeddings ADD COLUMN IF NOT EXISTS embedding_quality_score float;
ALTER TABLE embeddings ADD COLUMN IF NOT EXISTS updated_at timestamptz NOT NULL DEFAULT now();

-- Add constraint for data classification on embeddings
ALTER TABLE embeddings ADD CONSTRAINT IF NOT EXISTS check_embedding_classification 
  CHECK (data_classification IN ('private', 'internal', 'public_shared'));

-- Create multi-dimensional embedding support
-- Note: We'll use JSONB for flexible dimension storage initially, 
-- then migrate to specific vector columns as needed

-- Create separate tables for different embedding dimensions
CREATE TABLE IF NOT EXISTS embeddings_768 (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL,
  chunk_id uuid NOT NULL REFERENCES chunks(id) ON DELETE CASCADE,
  pipeline_name text NOT NULL,
  model text NOT NULL,
  data_classification text NOT NULL DEFAULT 'private',
  embedding vector(768) NOT NULL,
  quality_score float,
  created_at timestamptz NOT NULL DEFAULT now(),
  CONSTRAINT check_768_classification CHECK (data_classification IN ('private', 'internal', 'public_shared'))
);

CREATE TABLE IF NOT EXISTS embeddings_1024 (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL,
  chunk_id uuid NOT NULL REFERENCES chunks(id) ON DELETE CASCADE,
  pipeline_name text NOT NULL,
  model text NOT NULL,
  data_classification text NOT NULL DEFAULT 'private',
  embedding vector(1024) NOT NULL,
  quality_score float,
  created_at timestamptz NOT NULL DEFAULT now(),
  CONSTRAINT check_1024_classification CHECK (data_classification IN ('private', 'internal', 'public_shared'))
);

CREATE TABLE IF NOT EXISTS embeddings_1536 (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL,
  chunk_id uuid NOT NULL REFERENCES chunks(id) ON DELETE CASCADE,
  pipeline_name text NOT NULL,
  model text NOT NULL,
  data_classification text NOT NULL DEFAULT 'private',
  embedding vector(1536) NOT NULL,
  quality_score float,
  created_at timestamptz NOT NULL DEFAULT now(),
  CONSTRAINT check_1536_classification CHECK (data_classification IN ('private', 'internal', 'public_shared'))
);

CREATE TABLE IF NOT EXISTS embeddings_3072 (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL,
  chunk_id uuid NOT NULL REFERENCES chunks(id) ON DELETE CASCADE,
  pipeline_name text NOT NULL,
  model text NOT NULL,
  data_classification text NOT NULL DEFAULT 'private',
  embedding vector(3072) NOT NULL,
  quality_score float,
  created_at timestamptz NOT NULL DEFAULT now(),
  CONSTRAINT check_3072_classification CHECK (data_classification IN ('private', 'internal', 'public_shared'))
);

-- Create indexes for multi-dimensional embeddings
CREATE INDEX IF NOT EXISTS idx_embeddings_768_org_pipeline ON embeddings_768 (org_id, pipeline_name);
CREATE INDEX IF NOT EXISTS idx_embeddings_768_vec ON embeddings_768 USING ivfflat (embedding vector_l2_ops) WITH (lists = 100);
CREATE INDEX IF NOT EXISTS idx_embeddings_768_classification ON embeddings_768 (data_classification, org_id);

CREATE INDEX IF NOT EXISTS idx_embeddings_1024_org_pipeline ON embeddings_1024 (org_id, pipeline_name);
CREATE INDEX IF NOT EXISTS idx_embeddings_1024_vec ON embeddings_1024 USING ivfflat (embedding vector_l2_ops) WITH (lists = 100);
CREATE INDEX IF NOT EXISTS idx_embeddings_1024_classification ON embeddings_1024 (data_classification, org_id);

CREATE INDEX IF NOT EXISTS idx_embeddings_1536_org_pipeline ON embeddings_1536 (org_id, pipeline_name);
CREATE INDEX IF NOT EXISTS idx_embeddings_1536_vec ON embeddings_1536 USING ivfflat (embedding vector_l2_ops) WITH (lists = 100);
CREATE INDEX IF NOT EXISTS idx_embeddings_1536_classification ON embeddings_1536 (data_classification, org_id);

CREATE INDEX IF NOT EXISTS idx_embeddings_3072_org_pipeline ON embeddings_3072 (org_id, pipeline_name);
CREATE INDEX IF NOT EXISTS idx_embeddings_3072_vec ON embeddings_3072 USING ivfflat (embedding vector_l2_ops) WITH (lists = 100);
CREATE INDEX IF NOT EXISTS idx_embeddings_3072_classification ON embeddings_3072 (data_classification, org_id);

-- Intent classification and confidence scoring tables
CREATE TABLE IF NOT EXISTS intent_classifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL,
  query_hash text NOT NULL,
  query_text text NOT NULL,
  intent_category text NOT NULL,
  intent_subcategory text,
  confidence_score float NOT NULL,
  requires_public_knowledge boolean NOT NULL DEFAULT false,
  requires_private_knowledge boolean NOT NULL DEFAULT false,
  suggested_vector_stores jsonb NOT NULL DEFAULT '[]',
  classification_metadata jsonb DEFAULT '{}',
  classifier_version text,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS idx_intent_org_category ON intent_classifications (org_id, intent_category);
CREATE INDEX IF NOT EXISTS idx_intent_query_hash ON intent_classifications (query_hash);
CREATE INDEX IF NOT EXISTS idx_intent_confidence ON intent_classifications (confidence_score DESC);

-- Search confidence tracking
CREATE TABLE IF NOT EXISTS search_confidence_scores (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL,
  query_hash text NOT NULL,
  vector_store text NOT NULL, -- 'chromadb', 'supabase_vector', 'hybrid'
  pipeline_used text NOT NULL,
  search_results jsonb NOT NULL,
  confidence_score float NOT NULL,
  tier_used text NOT NULL, -- 'tier_0_deterministic', 'tier_1_dual_vector', 'tier_2_cloud_llm', 'tier_3_clarify'
  escalation_reason text,
  response_quality_score float,
  user_satisfaction_score float,
  search_metadata jsonb DEFAULT '{}',
  created_at timestamptz NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS idx_confidence_org_tier ON search_confidence_scores (org_id, tier_used);
CREATE INDEX IF NOT EXISTS idx_confidence_pipeline ON search_confidence_scores (pipeline_used, confidence_score DESC);
CREATE INDEX IF NOT EXISTS idx_confidence_vector_store ON search_confidence_scores (vector_store, confidence_score DESC);

-- Audit and retention management
CREATE TABLE IF NOT EXISTS document_audit_log (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL,
  document_id uuid REFERENCES documents(id) ON DELETE SET NULL,
  operation text NOT NULL, -- 'ingested', 'accessed', 'updated', 'deleted', 'retention_warning', 'retention_expired'
  user_id uuid,
  operation_details jsonb DEFAULT '{}',
  ip_address inet,
  user_agent text,
  success boolean NOT NULL DEFAULT true,
  error_details jsonb,
  created_at timestamptz NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS idx_audit_org_operation ON document_audit_log (org_id, operation, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_document ON document_audit_log (document_id, operation, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_created ON document_audit_log (created_at DESC);

-- Enhanced RLS for new tables
ALTER TABLE embeddings_768 ENABLE ROW LEVEL SECURITY;
ALTER TABLE embeddings_1024 ENABLE ROW LEVEL SECURITY;
ALTER TABLE embeddings_1536 ENABLE ROW LEVEL SECURITY;
ALTER TABLE embeddings_3072 ENABLE ROW LEVEL SECURITY;
ALTER TABLE intent_classifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_confidence_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_audit_log ENABLE ROW LEVEL SECURITY;

-- RLS policies for multi-dimensional embeddings
CREATE POLICY embeddings_768_org_isolation ON embeddings_768 
  FOR ALL USING (org_id = app_current_org_id() OR app_has_role('admin')) 
  WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));

CREATE POLICY embeddings_1024_org_isolation ON embeddings_1024 
  FOR ALL USING (org_id = app_current_org_id() OR app_has_role('admin')) 
  WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));

CREATE POLICY embeddings_1536_org_isolation ON embeddings_1536 
  FOR ALL USING (org_id = app_current_org_id() OR app_has_role('admin')) 
  WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));

CREATE POLICY embeddings_3072_org_isolation ON embeddings_3072 
  FOR ALL USING (org_id = app_current_org_id() OR app_has_role('admin')) 
  WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));

-- RLS policies for intent and confidence tables
CREATE POLICY intent_classifications_org_isolation ON intent_classifications 
  FOR ALL USING (org_id = app_current_org_id() OR app_has_role('admin')) 
  WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));

CREATE POLICY search_confidence_org_isolation ON search_confidence_scores 
  FOR ALL USING (org_id = app_current_org_id() OR app_has_role('admin')) 
  WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));

CREATE POLICY audit_log_org_isolation ON document_audit_log 
  FOR ALL USING (org_id = app_current_org_id() OR app_has_role('admin')) 
  WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));

-- Enhanced search functions for multi-dimensional support
CREATE OR REPLACE FUNCTION match_chunks_multi_dim(
  p_org uuid, 
  p_query_768 vector(768) DEFAULT NULL,
  p_query_1024 vector(1024) DEFAULT NULL,
  p_query_1536 vector(1536) DEFAULT NULL,
  p_query_3072 vector(3072) DEFAULT NULL,
  p_pipeline_name text DEFAULT NULL,
  p_limit int DEFAULT 8, 
  p_min_score float DEFAULT NULL
)
RETURNS TABLE(
  chunk_id uuid,
  doc_id uuid,
  score float,
  text text,
  metadata jsonb,
  pipeline_name text,
  embedding_dimension int,
  data_classification text
) LANGUAGE sql STABLE AS $$
  WITH ranked_768 AS (
    SELECT e.chunk_id, c.doc_id, (e.embedding <-> p_query_768) AS score,
           c.text, c.metadata, e.pipeline_name, 768 as dim, e.data_classification
    FROM embeddings_768 e
    JOIN chunks c ON c.id = e.chunk_id
    WHERE e.org_id = p_org AND c.org_id = p_org
      AND p_query_768 IS NOT NULL
      AND (p_pipeline_name IS NULL OR e.pipeline_name = p_pipeline_name)
  ), ranked_1024 AS (
    SELECT e.chunk_id, c.doc_id, (e.embedding <-> p_query_1024) AS score,
           c.text, c.metadata, e.pipeline_name, 1024 as dim, e.data_classification
    FROM embeddings_1024 e
    JOIN chunks c ON c.id = e.chunk_id
    WHERE e.org_id = p_org AND c.org_id = p_org
      AND p_query_1024 IS NOT NULL
      AND (p_pipeline_name IS NULL OR e.pipeline_name = p_pipeline_name)
  ), ranked_1536 AS (
    SELECT e.chunk_id, c.doc_id, (e.embedding <-> p_query_1536) AS score,
           c.text, c.metadata, e.pipeline_name, 1536 as dim, e.data_classification
    FROM embeddings_1536 e
    JOIN chunks c ON c.id = e.chunk_id
    WHERE e.org_id = p_org AND c.org_id = p_org
      AND p_query_1536 IS NOT NULL
      AND (p_pipeline_name IS NULL OR e.pipeline_name = p_pipeline_name)
  ), ranked_3072 AS (
    SELECT e.chunk_id, c.doc_id, (e.embedding <-> p_query_3072) AS score,
           c.text, c.metadata, e.pipeline_name, 3072 as dim, e.data_classification
    FROM embeddings_3072 e
    JOIN chunks c ON c.id = e.chunk_id
    WHERE e.org_id = p_org AND c.org_id = p_org
      AND p_query_3072 IS NOT NULL
      AND (p_pipeline_name IS NULL OR e.pipeline_name = p_pipeline_name)
  ), all_ranked AS (
    SELECT * FROM ranked_768
    UNION ALL
    SELECT * FROM ranked_1024
    UNION ALL
    SELECT * FROM ranked_1536
    UNION ALL
    SELECT * FROM ranked_3072
  )
  SELECT r.chunk_id, r.doc_id, r.score, r.text, r.metadata, 
         r.pipeline_name, r.dim, r.data_classification
  FROM all_ranked r
  WHERE p_min_score IS NULL OR r.score <= p_min_score
  ORDER BY r.score
  LIMIT p_limit;
$$;

-- Intent classification helper function
CREATE OR REPLACE FUNCTION classify_query_intent(
  p_org uuid,
  p_query_text text,
  p_classifier_version text DEFAULT 'v1'
)
RETURNS TABLE(
  intent_category text,
  intent_subcategory text,
  confidence_score float,
  requires_public_knowledge boolean,
  requires_private_knowledge boolean,
  suggested_vector_stores jsonb
) LANGUAGE sql STABLE AS $$
  -- This is a placeholder that would integrate with the ML intent classifier
  -- For now, return basic heuristic classification
  SELECT 
    CASE 
      WHEN p_query_text ILIKE '%policy%' OR p_query_text ILIKE '%procedure%' THEN 'policy_inquiry'
      WHEN p_query_text ILIKE '%assessment%' OR p_query_text ILIKE '%audit%' THEN 'assessment_inquiry'
      WHEN p_query_text ILIKE '%compliance%' OR p_query_text ILIKE '%regulation%' THEN 'compliance_inquiry'
      WHEN p_query_text ILIKE '%incident%' OR p_query_text ILIKE '%security%' THEN 'security_inquiry'
      WHEN p_query_text ILIKE '%standard%' OR p_query_text ILIKE '%iso%' OR p_query_text ILIKE '%gdpr%' THEN 'standards_inquiry'
      ELSE 'general_inquiry'
    END as intent_category,
    
    CASE 
      WHEN p_query_text ILIKE '%company%' OR p_query_text ILIKE '%our%' OR p_query_text ILIKE '%internal%' THEN 'company_specific'
      WHEN p_query_text ILIKE '%how to%' OR p_query_text ILIKE '%implement%' THEN 'implementation_guidance'
      WHEN p_query_text ILIKE '%what is%' OR p_query_text ILIKE '%define%' THEN 'definition_inquiry'
      ELSE 'general'
    END as intent_subcategory,
    
    0.75 as confidence_score, -- Placeholder confidence
    
    -- Requires public knowledge (standards, regulations)
    CASE WHEN p_query_text ILIKE '%standard%' OR p_query_text ILIKE '%regulation%' OR p_query_text ILIKE '%iso%' OR p_query_text ILIKE '%gdpr%' OR p_query_text ILIKE '%compliance%'
         THEN true ELSE false END as requires_public_knowledge,
    
    -- Requires private knowledge (company-specific)
    CASE WHEN p_query_text ILIKE '%company%' OR p_query_text ILIKE '%our%' OR p_query_text ILIKE '%policy%' OR p_query_text ILIKE '%internal%'
         THEN true ELSE false END as requires_private_knowledge,
    
    -- Suggested vector stores
    CASE 
      WHEN p_query_text ILIKE '%company%' OR p_query_text ILIKE '%our%' OR p_query_text ILIKE '%internal%' THEN '["supabase_vector"]'::jsonb
      WHEN p_query_text ILIKE '%standard%' OR p_query_text ILIKE '%regulation%' THEN '["chromadb"]'::jsonb
      ELSE '["chromadb", "supabase_vector"]'::jsonb
    END as suggested_vector_stores;
$$;

-- Update triggers for timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_chunks_updated_at BEFORE UPDATE ON chunks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_embeddings_updated_at BEFORE UPDATE ON embeddings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add indexes for enhanced querying
CREATE INDEX IF NOT EXISTS idx_documents_classification ON documents (org_id, data_classification, document_type);
CREATE INDEX IF NOT EXISTS idx_documents_retention ON documents (retention_expiry) WHERE retention_expiry IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_documents_compliance ON documents USING GIN (compliance_frameworks);

CREATE INDEX IF NOT EXISTS idx_chunks_intent ON chunks USING GIN (intent_classification);
CREATE INDEX IF NOT EXISTS idx_chunks_compliance ON chunks USING GIN (compliance_tags);
CREATE INDEX IF NOT EXISTS idx_chunks_content_type ON chunks (org_id, content_type);

-- Add comments for documentation
COMMENT ON TABLE documents IS 'Enhanced document metadata with data classification and retention management for dual-vector architecture';
COMMENT ON TABLE chunks IS 'Text chunks with intent classification and privacy metadata for hybrid search orchestration';
COMMENT ON TABLE embeddings_768 IS 'Vector embeddings for 768-dimensional models (all-mpnet-base-v2, placeholder)';
COMMENT ON TABLE embeddings_1024 IS 'Vector embeddings for 1024-dimensional models (BGE-Large-EN-v1.5)';
COMMENT ON TABLE embeddings_1536 IS 'Vector embeddings for 1536-dimensional models (OpenAI text-embedding-3-small)';
COMMENT ON TABLE embeddings_3072 IS 'Vector embeddings for 3072-dimensional models (OpenAI text-embedding-3-large)';
COMMENT ON TABLE intent_classifications IS 'Query intent classification for routing to appropriate vector stores';
COMMENT ON TABLE search_confidence_scores IS 'Search confidence tracking for multi-tier escalation decisions';
COMMENT ON TABLE document_audit_log IS 'Comprehensive audit trail for document access and lifecycle management';

COMMENT ON FUNCTION match_chunks_multi_dim IS 'Multi-dimensional vector search supporting all embedding pipelines with org-scoped RLS';
COMMENT ON FUNCTION classify_query_intent IS 'Intent classification for dual-vector routing (placeholder implementation)';

COMMIT;
