id: Q096
query: >-
  How do we secure our Wi-Fi and networks beyond basic passwords?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/A.13.1.1"
overlap_ids: []
capability_tags:
  - "Draft Doc"
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Annex A.13.1.1 Network Controls"
    id: "ISO27001:2022/A.13.1.1"
    locator: "Annex A.13.1.1"
ui:
  cards_hint:
    - "Network hardening guide"
  actions:
    - type: "start_workflow"
      target: "network_security_setup"
      label: "Secure Networks"
output_mode: "both"
graph_required: false
notes: "Use WPA3, VLAN segmentation, NAC, and regular firmware updates"
---
### 96) How do we secure our Wi-Fi and networks beyond basic passwords?

**Standard terms**  
- **Network controls (A.13.1.1):** protect network infrastructure.

**Plain-English answer**  
Implement **WPA3** encryption, segment networks with VLANs, deploy Network Access Control (NAC), disable legacy protocols, and keep firmware patched. Monitor network traffic for anomalies.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.13.1.1

**Why it matters**  
Strong network controls prevent unauthorized access and lateral movement.

**Do next in our platform**  
- Launch **Network Security Setup** workflow.  
- Document network segmentation plan.

**How our platform will help**  
- **[Draft Doc]** Network security policy templates.  
- **[Workflow]** Configuration checklists.

**Likely follow-ups**  
- “What tools monitor wireless threats?”  

**Sources**  
- ISO/IEC 27001:2022 Annex A.13.1.1
