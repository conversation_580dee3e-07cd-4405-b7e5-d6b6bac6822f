# Human-in-the-Loop (HITL) Workflow (Placeholder)

Decisions
- HITL primarily post-generation: customers review and approve changes to DB state and documents.
- No mandatory pre-generation gating in <PERSON>; may add later for sensitive actions.

Workflow
- Assistant proposes: DB mutations (upserts/links), document edits, or generated drafts.
- UI displays proposed changes with diffs and provenance.
- User approves, rejects, or requests edits.
- Edge records approvals as `db_write` events; persists audit trail with approver identity and timestamps.

Open Questions
- Approval policies by role; batch approvals; required reviewers.
- Offline review queues and SLAs.

Next Steps
- Define event schema for `proposal_created`, `proposal_approved`, `proposal_rejected`.
- Add proposal storage tables with `org_id` and RLS.
- Wire UI to show diffs for documents and DB record previews.

