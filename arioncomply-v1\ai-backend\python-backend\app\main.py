"""
File: arioncomply-v1/ai-backend/python-backend/app/main.py
File Description: FastAPI app entrypoint (skeleton)
Purpose: Expose chat routes that orchestrate retrieval + model calls and emit events
Inputs: POST /ai/chat with { sessionId?, messages[], hints?, explainability? }
Outputs: JSON { provider, model, text, usage? } (no streaming in this stub)
Dependencies: services.router (business flow), services.logging.events (event persistence)
Security/RLS: Org/user derived from headers/JWT in a real deployment; this stub trusts headers
Notes: Skeleton for integration; wire proper auth/claims and retrieval/model providers later
"""

from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any

from ..services.router import handle_chat, Meta


class ChatMessage(BaseModel):
    role: str
    content: str


class ChatRequest(BaseModel):
    sessionId: Optional[str] = None
    orgId: Optional[str] = None
    userId: Optional[str] = None
    messages: List[ChatMessage]
    hints: Optional[Dict[str, Any]] = None
    explainability: Optional[Dict[str, Any]] = None


app = FastAPI(title="ArionComply AI Backend (Skeleton)")


@app.post("/ai/chat")
async def ai_chat(req: Request, body: ChatRequest):
    # Derive meta (in production, parse JWT claims)
    request_id = req.headers.get("x-request-id") or "internal-req"
    session_id = body.sessionId
    org_id = body.orgId or req.headers.get("x-org-id")
    user_id = body.userId or req.headers.get("x-user-id")
    traceparent = req.headers.get("traceparent")

    meta = Meta(
        request_id=request_id,
        org_id=org_id,
        user_id=user_id,
        session_id=session_id,
        traceparent=traceparent,
    )

    result = await handle_chat(
        messages=[m.dict() for m in body.messages],
        meta=meta,
        hints=body.hints or {},
        explainability=body.explainability or {},
    )
    return JSONResponse({
        "provider": result.get("provider", "backend"),
        "model": result.get("model"),
        "text": result.get("text", ""),
        "usage": result.get("usage"),
        # NEW: Pass through all backend preprocessing and output mode features
        "output_mode": result.get("output_mode", "both"),
        "evidence": result.get("evidence", []),
        "cards_hint": result.get("cards_hint", []),
        "suggestions": result.get("suggestions", []),
        "deterministic": result.get("deterministic"),
        "preprocessing": result.get("preprocessing"),
        "typing_simulation": result.get("typing_simulation"),
    })
