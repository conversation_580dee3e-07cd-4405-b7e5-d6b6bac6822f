"""
File: arioncomply-v1/ai-backend/python-backend/services/preprocessing/graph_crawler.py
File Description: Multi-hop graph crawling for deterministic query resolution
Purpose: Traverse knowledge graph to find relevant content through relationship paths
Inputs: Initial seed nodes, query context, traversal parameters
Outputs: Scored content matches with graph path explanations
Dependencies: graph_service, content_index, framework_mappings
Security/RLS: Org-scoped graph access with proper tenant isolation
Notes: Implements weighted multi-hop traversal with decay and path aggregation
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Set, Tuple, Any
from enum import Enum
import heapq
import time
from collections import defaultdict, deque

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from logging.events import log_event


class EdgeType(Enum):
    """
    Types of edges in the knowledge graph, each with different semantic meanings
    and default weights for traversal scoring.
    """
    FRAMEWORK_MAPPING = "framework_mapping"      # ISO27001 A.5.1 → ISO27701 6.2.1 (weight: 0.9)
    CONTROL_DEPENDENCY = "control_dependency"    # A.5.1 prerequisites A.5.2 (weight: 0.8)
    SEMANTIC_SIMILARITY = "semantic_similarity"  # Content chunks with similar meaning (weight: 0.7)
    CITATION_REFERENCE = "citation_reference"    # Document cites another document (weight: 0.6)
    CATEGORY_GROUPING = "category_grouping"      # Controls in same category (weight: 0.5)
    IMPLEMENTATION_EXAMPLE = "implementation"    # Policy → Implementation guide (weight: 0.8)
    EVIDENCE_RELATIONSHIP = "evidence"           # Control → Required evidence (weight: 0.7)


@dataclass
class GraphNode:
    """
    Represents a node in the knowledge graph with content and metadata.
    """
    node_id: str                    # Unique identifier (e.g., "Q001", "ISO27001:2022/A.5.1")
    content_type: str              # "qa", "policy", "control", "document", etc.
    canonical_ids: List[str]       # Framework IDs this node represents
    title: str                     # Human-readable title
    content_snippet: str           # Brief content preview for explanations
    metadata: Dict[str, Any]       # Additional metadata from v2 JSON
    org_id: str                    # Tenant isolation


@dataclass
class GraphEdge:
    """
    Represents a weighted directed edge between nodes in the knowledge graph.
    """
    from_node_id: str             # Source node identifier
    to_node_id: str               # Target node identifier
    edge_type: EdgeType           # Type of relationship
    weight: float                 # Edge weight (0.0 to 1.0)
    confidence: float             # Confidence in this relationship
    explanation: str              # Human-readable explanation of the relationship
    metadata: Dict[str, Any]      # Additional edge metadata


@dataclass
class TraversalPath:
    """
    Represents a complete path through the graph from seed to target node.
    """
    seed_node_id: str             # Starting node
    target_node_id: str           # Ending node
    path_nodes: List[str]         # All nodes in the path (including seed and target)
    path_edges: List[GraphEdge]   # All edges traversed
    total_score: float            # Aggregated path score
    hop_count: int                # Number of hops in this path
    decay_factor: float           # Applied decay based on hop count
    explanation: str              # Human-readable path explanation


@dataclass
class GraphCrawlResult:
    """
    Result of a graph crawl operation with scored matches and explanations.
    """
    query: str                           # Original query
    seed_nodes: List[GraphNode]          # Initial matching nodes
    discovered_content: List[GraphNode]  # Content found through graph traversal
    traversal_paths: List[TraversalPath] # All paths that contributed to results
    aggregated_scores: Dict[str, float]  # Final scores per content item
    confidence: float                    # Overall confidence in results
    processing_time_ms: int              # Time taken for crawl
    explanation: str                     # Human-readable explanation of findings


class GraphCrawler:
    """
    Multi-hop knowledge graph crawler that discovers relevant content through
    relationship traversal using weighted path scoring and decay functions.
    
    Algorithm Overview:
    1. Start from seed nodes (initial matches from canonical ID, synonym, etc.)
    2. Perform breadth-first traversal with priority queue (highest weights first)
    3. Apply hop-based decay to prevent over-weighting distant relationships
    4. Aggregate scores from multiple paths to same content
    5. Return top-K results with path explanations
    """
    
    def __init__(self, graph_service, org_id: str):
        """
        Initialize graph crawler with org-scoped graph access.
        
        Args:
            graph_service: Service providing graph nodes and edges
            org_id: Organization ID for tenant isolation
        """
        self.graph_service = graph_service
        self.org_id = org_id
        
        # Traversal parameters (configurable per org if needed)
        self.max_hops = 3                    # Maximum traversal depth
        self.min_edge_weight = 0.3           # Ignore edges below this weight
        self.min_path_score = 0.4            # Ignore paths below this score
        self.max_paths_per_seed = 50         # Limit paths from each seed to prevent explosion
        self.convergence_bonus = 0.2         # Bonus for content reached via multiple paths
        
        # Hop decay factors (exponential decay to favor closer relationships)
        self.hop_decay_factors = {
            1: 1.0,    # Direct relationships maintain full weight
            2: 0.7,    # 2-hop relationships get 70% weight
            3: 0.4,    # 3-hop relationships get 40% weight
        }
        
        # Edge type default weights (can be overridden by specific edge instances)
        self.edge_type_weights = {
            EdgeType.FRAMEWORK_MAPPING: 0.9,
            EdgeType.CONTROL_DEPENDENCY: 0.8,
            EdgeType.IMPLEMENTATION_EXAMPLE: 0.8,
            EdgeType.SEMANTIC_SIMILARITY: 0.7,
            EdgeType.EVIDENCE_RELATIONSHIP: 0.7,
            EdgeType.CITATION_REFERENCE: 0.6,
            EdgeType.CATEGORY_GROUPING: 0.5,
        }
    
    async def crawl_from_seeds(
        self,
        query: str,
        seed_nodes: List[GraphNode],
        request_id: Optional[str] = None
    ) -> GraphCrawlResult:
        """
        Main entry point for graph crawling from initial seed nodes.
        
        Algorithm:
        1. Initialize priority queue with seed nodes (score = 1.0, hop = 0)
        2. For each node, expand to neighbors via weighted edges
        3. Apply hop decay and edge weights to calculate path scores
        4. Continue until max hops reached or no more viable paths
        5. Aggregate scores for content reached via multiple paths
        6. Return top results with path explanations
        
        Args:
            query: Original user query for context
            seed_nodes: Initial nodes to start traversal from
            request_id: For logging and traceability
            
        Returns:
            GraphCrawlResult with discovered content and explanations
        """
        start_time = time.time()
        
        if not seed_nodes:
            # No seeds provided, return empty result
            return GraphCrawlResult(
                query=query,
                seed_nodes=[],
                discovered_content=[],
                traversal_paths=[],
                aggregated_scores={},
                confidence=0.0,
                processing_time_ms=0,
                explanation="No seed nodes provided for graph traversal"
            )
        
        # Initialize traversal state
        # Priority queue: (negative_score, hop_count, current_node_id, path_so_far)
        priority_queue = []
        visited_paths = set()  # Track (node_id, hop_count) to prevent cycles
        all_paths = []  # Store all completed traversal paths
        content_scores = defaultdict(float)  # Aggregate scores per content
        content_path_count = defaultdict(int)  # Count paths reaching each content
        
        # Add seed nodes to priority queue with maximum score
        for seed_node in seed_nodes:
            heapq.heappush(priority_queue, (
                -1.0,  # Negative for max-heap behavior
                0,     # Hop count starts at 0
                seed_node.node_id,
                TraversalPath(
                    seed_node_id=seed_node.node_id,
                    target_node_id=seed_node.node_id,
                    path_nodes=[seed_node.node_id],
                    path_edges=[],
                    total_score=1.0,
                    hop_count=0,
                    decay_factor=1.0,
                    explanation=f"Seed node: {seed_node.title}"
                )
            ))
            # Seed nodes contribute to final scores
            content_scores[seed_node.node_id] = 1.0
            content_path_count[seed_node.node_id] = 1
        
        # Main traversal loop using priority queue (best-first search)
        paths_explored = 0
        max_paths_total = len(seed_nodes) * self.max_paths_per_seed
        
        while priority_queue and paths_explored < max_paths_total:
            # Get highest scoring path to expand
            neg_score, current_hop, current_node_id, current_path = heapq.heappop(priority_queue)
            current_score = -neg_score
            
            paths_explored += 1
            
            # Skip if we've reached maximum hops or score is too low
            if current_hop >= self.max_hops or current_score < self.min_path_score:
                continue
            
            # Prevent revisiting same node at same hop depth (cycle prevention)
            path_key = (current_node_id, current_hop)
            if path_key in visited_paths:
                continue
            visited_paths.add(path_key)
            
            # Get outgoing edges from current node
            try:
                outgoing_edges = await self.graph_service.get_outgoing_edges(
                    current_node_id, 
                    self.org_id
                )
            except Exception as e:
                # Log error but continue traversal
                if request_id:
                    await self._log_crawl_error(request_id, f"Failed to get edges for {current_node_id}: {e}")
                continue
            
            # Expand to neighbor nodes via weighted edges
            for edge in outgoing_edges:
                # Skip edges that don't meet minimum weight threshold
                effective_weight = edge.weight * edge.confidence
                if effective_weight < self.min_edge_weight:
                    continue
                
                # Calculate new path score with hop decay
                next_hop = current_hop + 1
                decay_factor = self.hop_decay_factors.get(next_hop, 0.0)
                if decay_factor == 0.0:
                    continue  # Beyond maximum hops
                
                # New path score = current_score × edge_weight × decay_factor
                new_path_score = current_score * effective_weight * decay_factor
                
                if new_path_score < self.min_path_score:
                    continue  # Path score too low to be useful
                
                # Create new traversal path
                new_path = TraversalPath(
                    seed_node_id=current_path.seed_node_id,
                    target_node_id=edge.to_node_id,
                    path_nodes=current_path.path_nodes + [edge.to_node_id],
                    path_edges=current_path.path_edges + [edge],
                    total_score=new_path_score,
                    hop_count=next_hop,
                    decay_factor=decay_factor,
                    explanation=self._build_path_explanation(current_path, edge)
                )
                
                # Add to priority queue for further expansion
                heapq.heappush(priority_queue, (
                    -new_path_score,  # Negative for max-heap
                    next_hop,
                    edge.to_node_id,
                    new_path
                ))
                
                # Record this path for final results
                all_paths.append(new_path)
                
                # Aggregate score for target content
                content_scores[edge.to_node_id] += new_path_score
                content_path_count[edge.to_node_id] += 1
        
        # Apply convergence bonus for content reached via multiple paths
        for node_id, path_count in content_path_count.items():
            if path_count > 1:
                convergence_bonus = min(self.convergence_bonus * (path_count - 1), 0.5)
                content_scores[node_id] += convergence_bonus
        
        # Get top-scoring content nodes (excluding seeds)
        seed_node_ids = {node.node_id for node in seed_nodes}
        discovered_scores = {
            node_id: score for node_id, score in content_scores.items()
            if node_id not in seed_node_ids and score > self.min_path_score
        }
        
        # Sort by score and get top-K results
        top_content_ids = sorted(discovered_scores.keys(), 
                                key=lambda x: discovered_scores[x], reverse=True)[:10]
        
        # Fetch full content nodes for results
        discovered_content = []
        for node_id in top_content_ids:
            try:
                node = await self.graph_service.get_node(node_id, self.org_id)
                if node:
                    discovered_content.append(node)
            except Exception as e:
                if request_id:
                    await self._log_crawl_error(request_id, f"Failed to get node {node_id}: {e}")
        
        # Calculate overall confidence based on result quality
        confidence = self._calculate_result_confidence(
            discovered_scores, content_path_count, all_paths
        )
        
        # Build explanation of the crawl results
        explanation = self._build_result_explanation(
            query, seed_nodes, discovered_content, all_paths, discovered_scores
        )
        
        processing_time_ms = int((time.time() - start_time) * 1000)
        
        # Log crawl completion for monitoring
        if request_id:
            await self._log_crawl_completion(
                request_id, len(seed_nodes), len(discovered_content), 
                len(all_paths), processing_time_ms, confidence
            )
        
        return GraphCrawlResult(
            query=query,
            seed_nodes=seed_nodes,
            discovered_content=discovered_content,
            traversal_paths=all_paths,
            aggregated_scores=discovered_scores,
            confidence=confidence,
            processing_time_ms=processing_time_ms,
            explanation=explanation
        )
    
    def _build_path_explanation(self, current_path: TraversalPath, edge: GraphEdge) -> str:
        """
        Build human-readable explanation for a traversal path.
        
        Args:
            current_path: Path up to current node
            edge: Edge being traversed to extend the path
            
        Returns:
            Human-readable path explanation
        """
        base_explanation = current_path.explanation
        
        # Add edge explanation based on edge type
        edge_explanations = {
            EdgeType.FRAMEWORK_MAPPING: f"maps to related framework requirement",
            EdgeType.CONTROL_DEPENDENCY: f"has prerequisite/dependent control",
            EdgeType.SEMANTIC_SIMILARITY: f"is semantically related to",
            EdgeType.CITATION_REFERENCE: f"references/cites",
            EdgeType.CATEGORY_GROUPING: f"belongs to same category as",
            EdgeType.IMPLEMENTATION_EXAMPLE: f"provides implementation guidance for",
            EdgeType.EVIDENCE_RELATIONSHIP: f"requires evidence from"
        }
        
        edge_desc = edge_explanations.get(edge.edge_type, "is related to")
        
        return f"{base_explanation} → {edge_desc} → {edge.explanation}"
    
    def _calculate_result_confidence(
        self,
        discovered_scores: Dict[str, float],
        path_counts: Dict[str, int],
        all_paths: List[TraversalPath]
    ) -> float:
        """
        Calculate overall confidence in the graph crawl results.
        
        Factors considered:
        - Number of high-scoring results found
        - Convergence (multiple paths to same content)
        - Average path quality
        - Diversity of path types used
        
        Args:
            discovered_scores: Final scores for discovered content
            path_counts: Number of paths reaching each content
            all_paths: All traversal paths found
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        if not discovered_scores:
            return 0.0
        
        # Factor 1: Quality of top results (0-0.4 points)
        top_scores = sorted(discovered_scores.values(), reverse=True)[:3]
        avg_top_score = sum(top_scores) / len(top_scores) if top_scores else 0
        quality_factor = min(avg_top_score * 0.4, 0.4)
        
        # Factor 2: Convergence bonus (0-0.3 points)
        converged_content = sum(1 for count in path_counts.values() if count > 1)
        total_content = len(discovered_scores)
        convergence_ratio = converged_content / total_content if total_content > 0 else 0
        convergence_factor = convergence_ratio * 0.3
        
        # Factor 3: Path diversity (0-0.2 points)
        edge_types_used = set()
        for path in all_paths:
            for edge in path.path_edges:
                edge_types_used.add(edge.edge_type)
        diversity_factor = min(len(edge_types_used) / len(EdgeType), 1.0) * 0.2
        
        # Factor 4: Result quantity (0-0.1 points)
        quantity_factor = min(len(discovered_scores) / 5.0, 1.0) * 0.1
        
        total_confidence = quality_factor + convergence_factor + diversity_factor + quantity_factor
        return min(total_confidence, 1.0)
    
    def _build_result_explanation(
        self,
        query: str,
        seed_nodes: List[GraphNode],
        discovered_content: List[GraphNode],
        all_paths: List[TraversalPath],
        scores: Dict[str, float]
    ) -> str:
        """
        Build human-readable explanation of graph crawl results.
        
        Args:
            query: Original user query
            seed_nodes: Initial seed nodes
            discovered_content: Content discovered through traversal
            all_paths: All paths explored
            scores: Final content scores
            
        Returns:
            Human-readable explanation
        """
        if not discovered_content:
            return f"No additional content found through graph traversal from {len(seed_nodes)} initial matches."
        
        # Start with seed summary
        seed_titles = [node.title[:50] + "..." if len(node.title) > 50 else node.title for node in seed_nodes[:3]]
        explanation_parts = [
            f"Started graph traversal from {len(seed_nodes)} initial matches: {', '.join(seed_titles)}"
        ]
        
        # Add discovery summary
        explanation_parts.append(
            f"Discovered {len(discovered_content)} additional relevant items through relationship traversal."
        )
        
        # Highlight top findings with path reasoning
        top_findings = discovered_content[:3]  # Top 3 results
        for i, content in enumerate(top_findings, 1):
            score = scores.get(content.node_id, 0)
            
            # Find best path to this content for explanation
            paths_to_content = [p for p in all_paths if p.target_node_id == content.node_id]
            if paths_to_content:
                best_path = max(paths_to_content, key=lambda p: p.total_score)
                explanation_parts.append(
                    f"{i}. {content.title[:60]}{'...' if len(content.title) > 60 else ''} "
                    f"(score: {score:.2f}, {best_path.hop_count}-hop path)"
                )
        
        return " ".join(explanation_parts)
    
    async def _log_crawl_error(self, request_id: str, error_message: str):
        """Log graph crawl errors for debugging."""
        await log_event(
            request_id=request_id,
            org_id=self.org_id,
            user_id=None,
            event_type="graph_crawl_error",
            direction="internal",
            details={"error": error_message}
        )
    
    async def _log_crawl_completion(
        self,
        request_id: str,
        seed_count: int,
        discovered_count: int,
        path_count: int,
        processing_time_ms: int,
        confidence: float
    ):
        """Log successful graph crawl completion for monitoring."""
        await log_event(
            request_id=request_id,
            org_id=self.org_id,
            user_id=None,
            event_type="graph_crawl_completed",
            direction="internal",
            details={
                "seed_nodes": seed_count,
                "discovered_content": discovered_count,
                "paths_explored": path_count,
                "processing_time_ms": processing_time_ms,
                "confidence": confidence
            }
        )


# Factory function for creating graph crawler instances
def create_graph_crawler(org_id: str, graph_service=None):
    """
    Factory function to create org-scoped graph crawler instances.
    
    Args:
        org_id: Organization ID for tenant isolation
        graph_service: Graph service instance (injected or loaded)
        
    Returns:
        Configured GraphCrawler instance
    """
    if not graph_service:
        from ..graph.graph_service import GraphService
        graph_service = GraphService(org_id)
    
    return GraphCrawler(graph_service, org_id)