-- File: arioncomply-v1/db/migrations/0007_document_management_min.sql
-- Migration 0007: Minimal Document Management System (DMS)
-- Purpose: Provide core tables for documents, versions, relationships, and approvals
-- Notes:
--   - Multi-tenant via org_id with RLS and admin bypass
--   - Markdown is source of truth; generated files live in storage and are referenced by URL
--   - Aligns to docs/Document_Management_System.md (initial/min viable subset)

BEGIN;

-- Documents (Markdown source of truth)
CREATE TABLE IF NOT EXISTS documents (
  doc_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL REFERENCES organizations(org_id) ON DELETE CASCADE,
  title text NOT NULL,
  status text NOT NULL DEFAULT 'draft' CHECK (status IN ('draft','published','archived')),
  category_id text,
  tags text[],
  version_current integer NOT NULL DEFAULT 1,
  markdown_content text,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid,
  updated_at timestamptz NOT NULL DEFAULT now(),
  updated_by uuid,
  deleted_at timestamptz,
  deleted_by uuid,
  CONSTRAINT chk_documents_metadata_json CHECK (jsonb_typeof(metadata) = 'object')
);

CREATE INDEX IF NOT EXISTS idx_documents_org_title ON documents (org_id, title)
  WHERE deleted_at IS NULL;

ALTER TABLE documents ENABLE ROW LEVEL SECURITY;

CREATE POLICY documents_select ON documents
  FOR SELECT USING (org_id = app_current_org_id() OR app_has_role('admin'));

CREATE POLICY documents_insert ON documents
  FOR INSERT WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));

CREATE POLICY documents_update ON documents
  FOR UPDATE USING (org_id = app_current_org_id() OR app_has_role('admin'))
  WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));

CREATE POLICY documents_delete ON documents
  FOR DELETE USING (org_id = app_current_org_id() OR app_has_role('admin'));

CREATE TRIGGER documents_set_updated_at
  BEFORE UPDATE ON documents
  FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

-- Document versions (immutable history)
CREATE TABLE IF NOT EXISTS document_versions (
  version_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL REFERENCES organizations(org_id) ON DELETE CASCADE,
  doc_id uuid NOT NULL REFERENCES documents(doc_id) ON DELETE CASCADE,
  version_number integer NOT NULL,
  markdown_content text,
  generated_pdf_url text,
  generated_docx_url text,
  checksum text,
  change_summary text,
  created_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid
);

CREATE UNIQUE INDEX IF NOT EXISTS uq_document_versions_doc_ver ON document_versions (doc_id, version_number);
CREATE INDEX IF NOT EXISTS idx_document_versions_doc ON document_versions (doc_id, version_number DESC);

ALTER TABLE document_versions ENABLE ROW LEVEL SECURITY;

-- RLS: Join to documents via doc_id to check org
CREATE POLICY document_versions_rw ON document_versions
  FOR ALL USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  ) WITH CHECK (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

-- Document relationships (references between documents)
CREATE TABLE IF NOT EXISTS document_relationships (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL REFERENCES organizations(org_id) ON DELETE CASCADE,
  source_doc_id uuid NOT NULL REFERENCES documents(doc_id) ON DELETE CASCADE,
  target_doc_id uuid NOT NULL REFERENCES documents(doc_id) ON DELETE CASCADE,
  relation_type text NOT NULL,
  created_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid
);

CREATE INDEX IF NOT EXISTS idx_document_relationships_org ON document_relationships (org_id);
CREATE INDEX IF NOT EXISTS idx_document_relationships_src ON document_relationships (source_doc_id);
CREATE INDEX IF NOT EXISTS idx_document_relationships_tgt ON document_relationships (target_doc_id);

ALTER TABLE document_relationships ENABLE ROW LEVEL SECURITY;

CREATE POLICY document_relationships_rw ON document_relationships
  FOR ALL USING (org_id = app_current_org_id() OR app_has_role('admin'))
  WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));

-- Document approvals (simple workflow)
CREATE TABLE IF NOT EXISTS document_approvals (
  approval_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL REFERENCES organizations(org_id) ON DELETE CASCADE,
  doc_id uuid NOT NULL REFERENCES documents(doc_id) ON DELETE CASCADE,
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending','approved','rejected')),
  approver_user_id uuid,
  decision_at timestamptz,
  comment text,
  created_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid,
  updated_at timestamptz NOT NULL DEFAULT now(),
  updated_by uuid
);

CREATE INDEX IF NOT EXISTS idx_document_approvals_doc ON document_approvals (doc_id, status);

ALTER TABLE document_approvals ENABLE ROW LEVEL SECURITY;

CREATE POLICY document_approvals_rw ON document_approvals
  FOR ALL USING (org_id = app_current_org_id() OR app_has_role('admin'))
  WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));

CREATE TRIGGER document_approvals_set_updated_at
  BEFORE UPDATE ON document_approvals
  FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

COMMIT;
-- File: arioncomply-v1/db/migrations/0007_document_management_min.sql
