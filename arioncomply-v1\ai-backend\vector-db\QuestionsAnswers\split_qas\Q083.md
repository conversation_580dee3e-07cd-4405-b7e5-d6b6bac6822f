id: Q083
query: >-
  How do we document our current processes when they're mostly informal?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/4.3"
  - "ISO27001:2022/7.5"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Draft Doc"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Scope of the ISMS"
    id: "ISO27001:2022/4.3"
    locator: "Clause 4.3"
  - title: "ISO/IEC 27001:2022 — Documented Information"
    id: "ISO27001:2022/7.5"
    locator: "Clause 7.5"
ui:
  cards_hint:
    - "Process mapping toolkit"
  actions:
    - type: "start_workflow"
      target: "process_mapping"
      label: "Map Current Processes"
output_mode: "both"
graph_required: false
notes: "Use workshops and whiteboarding before formalizing"
---
### 83) How do we document our current processes when they're mostly informal?

**Standard terms**  
- **Scope (Cl. 4.3):** capture relevant boundaries.  
- **Documented information (Cl. 7.5):** requirement to record processes.

**Plain-English answer**  
Run **mapping workshops**: facilitate team sessions to outline steps on whiteboards, capture roles and tools, then translate into formal flowcharts and procedures using platform templates.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clauses 4.3; 7.5

**Why it matters**  
Accurate mapping uncovers hidden risks and aligns team understanding.

**Do next in our platform**  
- Kick off **Process Mapping** workflow.  
- Populate maps into procedure templates.

**How our platform will help**  
- **[Workflow]** Guided process-mapping steps.  
- **[Draft Doc]** Auto-generate procedures from maps.

**Likely follow-ups**  
- “What tool formats are supported?” (Visio, Lucidchart, embedded canvas)

**Sources**  
- ISO/IEC 27001:2022 Clause 4.3  
- ISO/IEC 27001:2022 Clause 7.5
