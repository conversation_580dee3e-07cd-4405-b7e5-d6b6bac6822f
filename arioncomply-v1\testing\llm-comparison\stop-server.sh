#!/bin/bash
# File: arioncomply-v1/testing/llm-comparison/stop-server.sh
# Purpose: Stop the LLM comparison static server
# Usage: bash stop-server.sh

set -euo pipefail

# Always run from this script's directory
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd "$SCRIPT_DIR"

PORT=8000

echo "🛑 Stopping web server on port $PORT..."

# Stop server using saved PID
if [ -f ".server.pid" ]; then
    SERVER_PID=$(cat .server.pid)
    if kill "$SERVER_PID" 2>/dev/null; then
        echo "✅ Server stopped (PID: $SERVER_PID)"
    else
        echo "⚠️  Server was already stopped"
    fi
    rm -f .server.pid
else
    echo "⚠️  No server PID found"
fi

# Kill any Python servers on port as backup
lsof -ti:"$PORT" | xargs kill -9 2>/dev/null || true

echo "✅ Port $PORT is now free"
