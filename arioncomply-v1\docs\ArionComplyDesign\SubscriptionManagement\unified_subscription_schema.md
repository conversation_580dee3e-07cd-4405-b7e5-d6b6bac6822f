# Unified Subscription Management Schema Design

## Overview

This document presents a revised subscription management system that follows ArionComply's metadata-driven architecture and database design principles. It treats all subscription types (including demo) as standard subscription plans with different configurations, integrating seamlessly with role-based access control.

## Key Benefits

1. **Simplified Schema**: Fewer tables, no special demo fields
2. **Consistent Access Control**: Same mechanism for all subscription types
3. **Flexible Configuration**: Easy to add new subscription types without schema changes
4. **Better Integration**: Seamless integration with role-based access control
5. **Easier Analytics**: Unified tracking system for all subscription types
6. **Simplified Transitions**: Cleaner transitions between subscription types

## Database Design Principles Alignment

This schema follows ArionComply's database design principles as outlined in `arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/AppDatabase/database_schema_design_principles.md`:

1. **Data Types**: Uses TEXT with CHECK constraints instead of custom ENUMs
2. **Audit Fields**: Includes standard audit fields on all tables
3. **Multi-Tenant Support**: Implements organization_id for tenant isolation
4. **JSON Configuration**: Uses JSONB for flexible configuration with validation
5. **Relationships**: Maintains explicit foreign key relationships

## Core Tables

### 1. `sub_plans` - Subscription Plans

```sql
CREATE TABLE sub_plans (
    plan_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    plan_code TEXT NOT NULL UNIQUE,
    plan_name TEXT NOT NULL,
    plan_description TEXT,
    plan_type TEXT NOT NULL CHECK (plan_type IN ('demo', 'assessment', 'basic', 'professional', 'enterprise')),
    plan_is_active BOOLEAN DEFAULT true,
    plan_is_public BOOLEAN DEFAULT true,
    plan_duration_days INTEGER,
    plan_billing_cycle TEXT CHECK (plan_billing_cycle IN ('monthly', 'quarterly', 'annual', 'one_time')),
    plan_price_amount DECIMAL(10,2),
    plan_price_currency CHAR(3) DEFAULT 'USD',
    plan_user_limit INTEGER,
    plan_storage_limit_gb INTEGER,
    plan_configuration JSONB,
    organization_id UUID REFERENCES organizations(org_id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(user_id) ON DELETE SET NULL,
    updated_by UUID REFERENCES users(user_id) ON DELETE SET NULL,
    CONSTRAINT valid_plan_configuration CHECK (plan_configuration IS NULL OR jsonb_typeof(plan_configuration) = 'object')
);

-- Indexes for efficient querying
CREATE INDEX idx_sub_plans_code ON sub_plans(plan_code);
CREATE INDEX idx_sub_plans_type ON sub_plans(plan_type);
CREATE INDEX idx_sub_plans_active_public ON sub_plans(plan_is_active, plan_is_public);
CREATE INDEX idx_sub_plans_organization ON sub_plans(organization_id);
CREATE INDEX idx_sub_plans_configuration ON sub_plans USING gin(plan_configuration);

-- RLS Policy for multi-tenant isolation
ALTER TABLE sub_plans ENABLE ROW LEVEL SECURITY;

CREATE POLICY sub_plans_isolation_policy ON sub_plans
    USING (
        organization_id IS NULL OR  -- System-wide plans visible to all
        organization_id = current_setting('app.current_organization_id', true)::UUID  -- Org-specific plans
    );
```

**Purpose**: Defines all available subscription plans including demo, assessment, and paid tiers

**Key Features**:
- Universal plan structure for all subscription types
- Flexible JSON configuration for feature limits
- Multi-tenant support with organization_id
- System-wide and organization-specific plans

**plan_configuration Example**:
```json
{
  "feature_limits": {
    "policies": 10,
    "controls": 25,
    "risks": 15,
    "documents": 20
  },
  "feature_access": {
    "advanced_reporting": false,
    "risk_management": true,
    "audit_trails": false,
    "custom_templates": false
  },
  "ui_customization": {
    "custom_branding": false,
    "dashboard_customization": true
  }
}
```

### 2. `org_subscriptions` - Organization Subscriptions

```sql
CREATE TABLE org_subscriptions (
    sub_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sub_org_id UUID NOT NULL REFERENCES organizations(org_id) ON DELETE CASCADE,
    sub_plan_id UUID NOT NULL REFERENCES sub_plans(plan_id) ON DELETE RESTRICT,
    sub_status TEXT NOT NULL CHECK (sub_status IN ('active', 'expired', 'canceled', 'pending', 'trial', 'replaced')),
    sub_start_date TIMESTAMPTZ NOT NULL,
    sub_end_date TIMESTAMPTZ,
    sub_auto_renew BOOLEAN DEFAULT false,
    sub_billing_reference TEXT,
    sub_canceled_at TIMESTAMPTZ,
    sub_canceled_by UUID REFERENCES users(user_id) ON DELETE SET NULL,
    sub_cancel_reason TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(user_id) ON DELETE SET NULL,
    updated_by UUID REFERENCES users(user_id) ON DELETE SET NULL
);

-- Indexes for efficient querying
CREATE INDEX idx_org_subscriptions_org ON org_subscriptions(sub_org_id);
CREATE INDEX idx_org_subscriptions_plan ON org_subscriptions(sub_plan_id);
CREATE INDEX idx_org_subscriptions_status ON org_subscriptions(sub_status);
CREATE INDEX idx_org_subscriptions_dates ON org_subscriptions(sub_start_date, sub_end_date);
CREATE UNIQUE INDEX idx_org_active_subscriptions ON org_subscriptions(sub_org_id, sub_status) 
WHERE sub_status = 'active';

-- RLS Policy for multi-tenant isolation
ALTER TABLE org_subscriptions ENABLE ROW LEVEL SECURITY;

CREATE POLICY org_subscriptions_isolation_policy ON org_subscriptions
    USING (sub_org_id = current_setting('app.current_organization_id', true)::UUID);
```

**Purpose**: Links organizations to their active subscription plans and tracks subscription lifecycle

**Key Features**:
- One active subscription per organization
- Status tracking (active, expired, canceled, etc.)
- Time-bound subscriptions with end dates
- Integration with billing systems

### 3. `roles` - User Roles

```sql
CREATE TABLE roles (
    role_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role_name TEXT NOT NULL,
    role_description TEXT,
    role_is_system BOOLEAN DEFAULT false,
    organization_id UUID REFERENCES organizations(org_id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(user_id) ON DELETE SET NULL,
    updated_by UUID REFERENCES users(user_id) ON DELETE SET NULL
);

-- Unique constraint for role names within an organization
CREATE UNIQUE INDEX idx_roles_name_org ON roles(role_name, COALESCE(organization_id, '00000000-0000-0000-0000-000000000000'));
CREATE INDEX idx_roles_system ON roles(role_is_system);
CREATE INDEX idx_roles_organization ON roles(organization_id);

-- RLS Policy for multi-tenant isolation
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;

CREATE POLICY roles_isolation_policy ON roles
    USING (
        role_is_system OR  -- System roles visible to all
        organization_id = current_setting('app.current_organization_id', true)::UUID  -- Org-specific roles
    );
```

**Purpose**: Defines roles that group permissions together

**Key Features**:
- System roles (predefined, cannot be modified)
- Custom roles (organization-specific)
- Multi-tenant support with organization_id

### 4. `permissions` - Feature Permissions

```sql
CREATE TABLE permissions (
    permission_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    permission_code TEXT NOT NULL UNIQUE,
    permission_description TEXT,
    feature_area TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(user_id) ON DELETE SET NULL,
    updated_by UUID REFERENCES users(user_id) ON DELETE SET NULL
);

-- Indexes for efficient querying
CREATE INDEX idx_permissions_code ON permissions(permission_code);
CREATE INDEX idx_permissions_area ON permissions(feature_area);

-- No RLS policy as permissions are system-wide
```

**Purpose**: Defines granular permissions that control access to specific features

**Key Features**:
- Action-based permissions (create, read, update, delete)
- Feature-specific permissions (policies, controls, reports)
- Organized by feature area

### 5. `role_permissions` - Role to Permission Mapping

```sql
CREATE TABLE role_permissions (
    role_permission_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role_id UUID NOT NULL REFERENCES roles(role_id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(permission_id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(user_id) ON DELETE SET NULL,
    UNIQUE(role_id, permission_id)
);

-- Indexes for efficient querying
CREATE INDEX idx_role_permissions_role ON role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission ON role_permissions(permission_id);

-- RLS Policy inherits from roles table
ALTER TABLE role_permissions ENABLE ROW LEVEL SECURITY;

CREATE POLICY role_permissions_isolation_policy ON role_permissions
    USING (
        EXISTS (
            SELECT 1 FROM roles
            WHERE roles.role_id = role_permissions.role_id
            AND (
                roles.role_is_system OR
                roles.organization_id = current_setting('app.current_organization_id', true)::UUID
            )
        )
    );
```

**Purpose**: Maps permissions to roles, defining what actions each role can perform

**Key Features**:
- Many-to-many relationship between roles and permissions
- Foundation of role-based access control

### 6. `plan_roles` - Plan to Role Mapping

```sql
CREATE TABLE plan_roles (
    plan_role_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    plan_id UUID NOT NULL REFERENCES sub_plans(plan_id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(role_id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(user_id) ON DELETE SET NULL,
    UNIQUE(plan_id, role_id)
);

-- Indexes for efficient querying
CREATE INDEX idx_plan_roles_plan ON plan_roles(plan_id);
CREATE INDEX idx_plan_roles_role ON plan_roles(role_id);

-- RLS Policy inherits from plans table
ALTER TABLE plan_roles ENABLE ROW LEVEL SECURITY;

CREATE POLICY plan_roles_isolation_policy ON plan_roles
    USING (
        EXISTS (
            SELECT 1 FROM sub_plans
            WHERE sub_plans.plan_id = plan_roles.plan_id
            AND (
                sub_plans.organization_id IS NULL OR
                sub_plans.organization_id = current_setting('app.current_organization_id', true)::UUID
            )
        )
    );
```

**Purpose**: Defines which roles are automatically available with each subscription plan

**Key Features**:
- Different plans provide access to different role sets
- Demo plans include limited roles with restricted permissions

### 7. `user_roles` - User to Role Assignment

```sql
CREATE TABLE user_roles (
    user_role_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(role_id) ON DELETE CASCADE,
    assigned_by UUID REFERENCES users(user_id) ON DELETE SET NULL,
    assigned_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, role_id)
);

-- Indexes for efficient querying
CREATE INDEX idx_user_roles_user ON user_roles(user_id);
CREATE INDEX idx_user_roles_role ON user_roles(role_id);

-- RLS Policy for multi-tenant isolation
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;

CREATE POLICY user_roles_isolation_policy ON user_roles
    USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.user_id = user_roles.user_id
            AND users.organization_id = current_setting('app.current_organization_id', true)::UUID
        )
    );
```

**Purpose**: Associates users with roles to determine their permissions

**Key Features**:
- Tracks who assigned the role and when
- Users can have multiple roles with cumulative permissions

### 8. `feature_usage` - Usage Tracking

```sql
CREATE TABLE feature_usage (
    usage_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    usage_org_id UUID NOT NULL REFERENCES organizations(org_id) ON DELETE CASCADE,
    usage_sub_id UUID NOT NULL REFERENCES org_subscriptions(sub_id) ON DELETE CASCADE,
    usage_user_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    usage_feature_code TEXT NOT NULL,
    usage_count INTEGER DEFAULT 1,
    usage_first TIMESTAMPTZ DEFAULT NOW(),
    usage_last TIMESTAMPTZ DEFAULT NOW(),
    usage_details JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT valid_usage_details CHECK (usage_details IS NULL OR jsonb_typeof(usage_details) = 'object')
);

-- Indexes for efficient querying
CREATE INDEX idx_feature_usage_org_feature ON feature_usage(usage_org_id, usage_feature_code);
CREATE INDEX idx_feature_usage_sub ON feature_usage(usage_sub_id);
CREATE INDEX idx_feature_usage_user ON feature_usage(usage_user_id);
CREATE INDEX idx_feature_usage_details ON feature_usage USING gin(usage_details);

-- RLS Policy for multi-tenant isolation
ALTER TABLE feature_usage ENABLE ROW LEVEL SECURITY;

CREATE POLICY feature_usage_isolation_policy ON feature_usage
    USING (usage_org_id = current_setting('app.current_organization_id', true)::UUID);
```

**Purpose**: Tracks usage of features across all subscription types for analytics and limits enforcement

**Key Features**:
- Unified tracking for all subscription types
- Records first use, last use, and usage count
- Detailed usage context in JSON field

### 9. `sub_transitions` - Subscription Transitions

```sql
CREATE TABLE sub_transitions (
    transition_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transition_org_id UUID NOT NULL REFERENCES organizations(org_id) ON DELETE CASCADE,
    transition_user_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    transition_from_sub_id UUID REFERENCES org_subscriptions(sub_id) ON DELETE SET NULL,
    transition_to_sub_id UUID REFERENCES org_subscriptions(sub_id) ON DELETE SET NULL,
    transition_from_plan_id UUID REFERENCES sub_plans(plan_id) ON DELETE SET NULL,
    transition_to_plan_id UUID REFERENCES sub_plans(plan_id) ON DELETE SET NULL,
    transition_date TIMESTAMPTZ DEFAULT NOW(),
    transition_reason TEXT,
    transition_source TEXT,
    transition_notes TEXT,
    transition_data JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(user_id) ON DELETE SET NULL,
    CONSTRAINT valid_transition_data CHECK (transition_data IS NULL OR jsonb_typeof(transition_data) = 'object')
);

-- Indexes for efficient querying
CREATE INDEX idx_transitions_org ON sub_transitions(transition_org_id);
CREATE INDEX idx_transitions_from_sub ON sub_transitions(transition_from_sub_id);
CREATE INDEX idx_transitions_to_sub ON sub_transitions(transition_to_sub_id);
CREATE INDEX idx_transitions_date ON sub_transitions(transition_date);
CREATE INDEX idx_transitions_data ON sub_transitions USING gin(transition_data);

-- RLS Policy for multi-tenant isolation
ALTER TABLE sub_transitions ENABLE ROW LEVEL SECURITY;

CREATE POLICY sub_transitions_isolation_policy ON sub_transitions
    USING (transition_org_id = current_setting('app.current_organization_id', true)::UUID);
```

**Purpose**: Tracks transitions between subscription types, especially conversions from demo to paid

**Key Features**:
- Records both "from" and "to" subscription details
- Tracks reason and source of transition
- Documents which data was preserved during transition

## Relationships Diagram

```
sub_plans
    ↑ references
    | sub_plan_id
org_subscriptions
    ↑ references     ↑ references
    | sub_id         | references
    |                |
sub_transitions     feature_usage
    
roles
    ↑ references     ↑ references
    | role_id        | role_id
    |                |
plan_roles         role_permissions
    ↑ references     ↑ references
    | plan_id        | permission_id
    |                |
sub_plans          permissions
    
user_roles
    ↑ references
    | role_id
    |
roles
```

## JSON Schema Validation

All JSON fields in the database should follow the defined schema for validation. Here are the schemas for the main JSON fields:

### plan_configuration Schema

```json
{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "type": "object",
  "properties": {
    "feature_limits": {
      "type": "object",
      "properties": {
        "policies": { "type": "integer", "minimum": 0 },
        "controls": { "type": "integer", "minimum": 0 },
        "risks": { "type": "integer", "minimum": 0 },
        "documents": { "type": "integer", "minimum": 0 },
        "users": { "type": "integer", "minimum": 0 },
        "storage_gb": { "type": "integer", "minimum": 0 }
      },
      "additionalProperties": false
    },
    "feature_access": {
      "type": "object",
      "properties": {
        "advanced_reporting": { "type": "boolean" },
        "risk_management": { "type": "boolean" },
        "audit_trails": { "type": "boolean" },
        "custom_templates": { "type": "boolean" },
        "custom_integrations": { "type": "boolean" },
        "api_access": { "type": "boolean" }
      },
      "additionalProperties": false
    },
    "ui_customization": {
      "type": "object",
      "properties": {
        "custom_branding": { "type": "boolean" },
        "dashboard_customization": { "type": "boolean" },
        "report_customization": { "type": "boolean" }
      },
      "additionalProperties": false
    }
  },
  "additionalProperties": false
}
```

### transition_data Schema

```json
{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "type": "object",
  "properties": {
    "preserved_data": {
      "type": "array",
      "items": {
        "type": "string",
        "enum": ["policies", "controls", "risks", "documents", "users", "settings"]
      }
    },
    "migration_settings": {
      "type": "object",
      "properties": {
        "data_retention_days": { "type": "integer", "minimum": 0 },
        "transfer_user_roles": { "type": "boolean" },
        "copy_configurations": { "type": "boolean" }
      }
    },
    "transition_metadata": {
      "type": "object",
      "properties": {
        "initiated_by": { "type": "string" },
        "approval_reference": { "type": "string" },
        "sales_reference": { "type": "string" }
      }
    }
  },
  "additionalProperties": false
}
```

## Initial Data

The system requires some initial data to function properly. Here's the SQL to create the minimum required data:

```sql
-- Create standard permissions
INSERT INTO permissions (permission_code, permission_description, feature_area) VALUES
('view:dashboard', 'View dashboard', 'dashboard'),
('create:policy', 'Create policy', 'policies'),
('view:policy', 'View policy', 'policies'),
('update:policy', 'Update policy', 'policies'),
('delete:policy', 'Delete policy', 'policies'),
('create:control', 'Create control', 'controls'),
('view:control', 'View control', 'controls'),
('update:control', 'Update control', 'controls'),
('delete:control', 'Delete control', 'controls'),
('create:risk', 'Create risk', 'risks'),
('view:risk', 'View risk', 'risks'),
('update:risk', 'Update risk', 'risks'),
('delete:risk', 'Delete risk', 'risks'),
('export:report', 'Export report', 'reports'),
('view:report', 'View report', 'reports'),
('create:document', 'Create document', 'documents'),
('view:document', 'View document', 'documents'),
('update:document', 'Update document', 'documents'),
('delete:document', 'Delete document', 'documents'),
('manage:users', 'Manage users', 'administration'),
('manage:roles', 'Manage roles', 'administration'),
('manage:subscriptions', 'Manage subscriptions', 'administration'),
('view:analytics', 'View analytics', 'analytics');

-- Create standard roles
INSERT INTO roles (role_id, role_name, role_description, role_is_system) VALUES
('11111111-1111-1111-1111-111111111111', 'Admin', 'Full administrative access', true),
('22222222-2222-2222-2222-222222222222', 'Demo User', 'Limited access for demo users', true),
('33333333-3333-3333-3333-333333333333', 'Basic User', 'Standard user access', true),
('44444444-4444-4444-4444-444444444444', 'Auditor', 'Read-only access for auditing', true);

-- Assign permissions to roles
-- Admin role - all permissions
INSERT INTO role_permissions (role_id, permission_id) 
SELECT '11111111-1111-1111-1111-111111111111', permission_id 
FROM permissions;

-- Demo User role - limited permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT permission_id 
FROM permissions
WHERE permission_code IN (
    'view:dashboard', 'view:policy', 'create:policy', 
    'view:control', 'create:control', 'view:risk', 
    'view:document', 'create:document', 'view:report'
);

-- Basic User role - standard permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT permission_id 
FROM permissions
WHERE permission_code NOT LIKE 'manage:%';

-- Auditor role - read-only permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT permission_id 
FROM permissions
WHERE permission_code LIKE 'view:%';

-- Create standard plans
INSERT INTO sub_plans (
    plan_id, 
    plan_code, 
    plan_name, 
    plan_description, 
    plan_type, 
    plan_is_active, 
    plan_is_public,
    plan_duration_days,
    plan_billing_cycle,
    plan_price_amount,
    plan_price_currency,
    plan_user_limit,
    plan_storage_limit_gb,
    plan_configuration
) VALUES
(
    '55555555-5555-5555-5555-555555555555',
    'demo-30',
    '30-Day Demo',
    'Full-featured 30-day demo with limited record counts',
    'demo',
    true,
    true,
    30,
    'one_time',
    0.00,
    'USD',
    5,
    10,
    '{"feature_limits": {"policies": 10, "controls": 25, "risks": 15, "documents": 20}, "feature_access": {"advanced_reporting": false, "risk_management": true, "audit_trails": false, "custom_templates": false}}'
),
(
    '66666666-6666-6666-6666-666666666666',
    'basic-monthly',
    'Basic Plan - Monthly',
    'Essential compliance management for small organizations',
    'basic',
    true,
    true,
    null,
    'monthly',
    99.99,
    'USD',
    10,
    50,
    '{"feature_limits": {"policies": 50, "controls": 100, "risks": 75, "documents": 100}, "feature_access": {"advanced_reporting": false, "risk_management": true, "audit_trails": true, "custom_templates": false}}'
),
(
    '77777777-7777-7777-7777-777777777777',
    'professional-monthly',
    'Professional Plan - Monthly',
    'Advanced compliance management for growing organizations',
    'professional',
    true,
    true,
    null,
    'monthly',
    199.99,
    'USD',
    25,
    100,
    '{"feature_limits": {"policies": 100, "controls": 250, "risks": 150, "documents": 250}, "feature_access": {"advanced_reporting": true, "risk_management": true, "audit_trails": true, "custom_templates": true}}'
),
(
    '88888888-8888-8888-8888-888888888888',
    'enterprise-annual',
    'Enterprise Plan - Annual',
    'Comprehensive compliance management for large organizations',
    'enterprise',
    true,
    true,
    null,
    'annual',
    4999.99,
    'USD',
    null,
    500,
    '{"feature_limits": null, "feature_access": {"advanced_reporting": true, "risk_management": true, "audit_trails": true, "custom_templates": true, "custom_integrations": true}}'
);

-- Associate roles with plans
-- Demo plan gets Demo User role
INSERT INTO plan_roles (plan_id, role_id) VALUES
('55555555-5555-5555-5555-555555555555', '22222222-2222-2222-2222-222222222222');

-- Basic plan gets Basic User role
INSERT INTO plan_roles (plan_id, role_id) VALUES
('66666666-6666-6666-6666-666666666666', '33333333-3333-3333-3333-333333333333');

-- Professional plan gets Basic User and Auditor roles
INSERT INTO plan_roles (plan_id, role_id) VALUES
('77777777-7777-7777-7777-777777777777', '33333333-3333-3333-3333-333333333333'),
('77777777-7777-7777-7777-777777777777', '44444444-4444-4444-4444-444444444444');

-- Enterprise plan gets all roles
INSERT INTO plan_roles (plan_id, role_id) VALUES
('88888888-8888-8888-8888-888888888888', '33333333-3333-3333-3333-333333333333'),
('88888888-8888-8888-8888-888888888888', '44444444-4444-4444-4444-444444444444'),
('88888888-8888-8888-8888-888888888888', '11111111-1111-1111-1111-111111111111');
```

## Integration with Application

### Row-Level Security (RLS) Integration

The subscription system integrates with ArionComply's RLS framework to enforce multi-tenant isolation:

1. **Organization Context**: All queries must set the organization context:
   ```sql
   SELECT set_config('app.current_organization_id', organization_id, true);
   ```

2. **RLS Policies**: Each table has an RLS policy to enforce tenant isolation:
   ```sql
   CREATE POLICY tenant_isolation_policy ON table_name 
   USING (organization_id = current_setting('app.current_organization_id', true)::UUID);
   ```

3. **Cross-Tenant Access**: System roles and system-wide plans are accessible across tenants:
   ```sql
   USING (
     organization_id IS NULL OR 
     organization_id = current_setting('app.current_organization_id', true)::UUID
   );
   ```

### Permission Checking

The permission system is used throughout the application to enforce access control:

```sql
-- Function to check if a user has a specific permission
CREATE OR REPLACE FUNCTION check_permission(
    p_organization_id UUID,
    p_user_id UUID,
    p_permission_code TEXT
) RETURNS BOOLEAN AS $$
DECLARE
    v_has_permission BOOLEAN;
BEGIN
    -- Check if user has the permission through any of their roles
    SELECT EXISTS (
        SELECT 1
        FROM user_roles ur
        JOIN role_permissions rp ON ur.role_id = rp.role_id
        JOIN permissions p ON rp.permission_id = p.permission_id
        WHERE ur.user_id = p_user_id
        AND p.permission_code = p_permission_code
    ) INTO v_has_permission;
    
    RETURN v_has_permission;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Next Steps

The database schema defined in this document forms the foundation of the subscription management system. The following documents build upon this foundation:

1. `arioncomply-v1/docs/ApplicationDatabase/Subscription-Management-Functions-Triggers.md` - Database functions and triggers
2. `arioncomply-v1/docs/ApplicationDatabase/Unified-Subscription-Management-Metadata-Implementation.md` - Metadata-driven implementation
3. `arioncomply-v1/docs/API/Subscription-Management-Edge-Functions.md` - Edge function components
4. `arioncomply-v1/docs/Frontend/Subscription-Management-React-Components.md` - React UI components
5. `arioncomply-v1/docs/Workflows/Subscription-Management-Workflows.md` - Workflow definitions
