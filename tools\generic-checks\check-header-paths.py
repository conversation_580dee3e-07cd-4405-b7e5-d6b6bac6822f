#!/usr/bin/env python3
# File: tools/generic-checks/check-header-paths.py
# File Description: Generic checker for verifying top-of-file path headers
# Purpose: Ensure files include a "File: <repo-relative-path>" header matching their actual path

import argparse
import os
import re
import subprocess
import sys
from pathlib import Path
from fnmatch import fnmatch

HEADER_RE = re.compile(r"File:\s*([^\s>]+)")

DEFAULT_INCLUDE_EXTS = {"ts","tsx","js","jsx","py","sh","sql","md","html","css","yaml","yml","toml"}
DEFAULT_SKIP_EXTS = {"png","jpg","jpeg","gif","svg","ico","zip","gz","bz2","xz","rar","7z","pdf","woff","woff2","eot","ttf","otf","mp3","mp4","mov","avi"}
DEFAULT_SKIP_DIRS = {".git","node_modules","dist","build",".venv",".mypy_cache",".pytest_cache"}

def repo_root() -> Path:
    try:
        out = subprocess.check_output(["git","rev-parse","--show-toplevel"], text=True).strip()
        return Path(out)
    except Exception:
        return Path.cwd()

def list_files(root: Path) -> list[Path]:
    try:
        out = subprocess.check_output(["rg","--files","--hidden","--no-ignore-vcs","-g","!**/.git/**","-g","!**/node_modules/**"], cwd=root, text=True)
        return [root / p for p in out.splitlines()]
    except Exception:
        return [p for p in root.rglob("*") if p.is_file()]

def within_scope(rel: str, includes: list[str], excludes: list[str]) -> bool:
    if includes:
        if not any(fnmatch(rel, pat) for pat in includes):
            return False
    if excludes:
        if any(fnmatch(rel, pat) for pat in excludes):
            return False
    return True

def find_header_path(p: Path, max_lines: int) -> str | None:
    try:
        with p.open("r", encoding="utf-8", errors="ignore") as f:
            for _ in range(max_lines):
                line = f.readline()
                if not line:
                    break
                m = HEADER_RE.search(line)
                if m:
                    return m.group(1).strip().strip('"\' )')
        return None
    except Exception:
        return None

def main() -> int:
    root = repo_root()

    parser = argparse.ArgumentParser(description="Validate top-of-file path headers")
    parser.add_argument("--prefix", default=os.getenv("HEADERCHECK_PREFIX","auto"), help="Expected path prefix in header: auto|none|<string>")
    parser.add_argument("--include", action="append", default=(os.getenv("HEADERCHECK_INCLUDE","" ).split() or []), help="Include glob (repeatable)")
    parser.add_argument("--exclude", action="append", default=(os.getenv("HEADERCHECK_EXCLUDE","" ).split() or []), help="Exclude glob (repeatable)")
    parser.add_argument("--ext", action="append", default=[], help="Additional extensions to include")
    parser.add_argument("--skip-ext", action="append", default=[], help="Additional extensions to skip")
    parser.add_argument("--max-lines", type=int, default=10)
    parser.add_argument("--strict-prefix", action="store_true", default=True)
    parser.add_argument("--no-strict-prefix", dest="strict_prefix", action="store_false")
    parser.add_argument("--write-report", default=None)
    args = parser.parse_args()

    include_exts = set(DEFAULT_INCLUDE_EXTS) | set(args.ext or [])
    skip_exts = set(DEFAULT_SKIP_EXTS) | set(args.skip_ext or [])
    skip_dirs = set(DEFAULT_SKIP_DIRS)

    files = []
    for p in list_files(root):
        try:
            rel = str(p.relative_to(root)).replace("\\","/")
        except Exception:
            continue
        if any(seg in skip_dirs for seg in Path(rel).parts):
            continue
        ext = p.suffix.lstrip('.')
        if ext in skip_exts:
            continue
        if ext not in include_exts and p.name not in ("Makefile",):
            continue
        if not within_scope(rel, args.include or [], args.exclude or []):
            continue
        files.append((p, rel, ext))

    # Determine prefix
    repo_name = root.name
    if args.prefix == "auto":
        expected_prefix = f"{repo_name}/"
    elif args.prefix == "none":
        expected_prefix = None
    else:
        expected_prefix = args.prefix

    missing: list[str] = []
    mismatches: list[tuple[str,str]] = []
    ok = 0
    for p, rel, _ in files:
        header_path = find_header_path(p, args.max_lines)
        if not header_path:
            missing.append(rel)
            continue
        expected_rel = rel
        declared = header_path
        # Normalize declared
        declared = declared.strip()
        # Build expected header string
        if expected_prefix is None:
            expected = expected_rel
            # allow headers that include repo-name anyway
            if declared.endswith(expected_rel) and '/' in declared:
                # treat as ok if suffix matches
                ok += 1
                continue
        else:
            expected = f"{expected_prefix}{expected_rel}"
            if args.strict_prefix and not declared.startswith(expected_prefix):
                mismatches.append((declared, expected))
                continue
        if declared != expected:
            mismatches.append((declared, expected))
        else:
            ok += 1

    out_lines = []
    if missing:
        out_lines.append("Files missing header (add: 'File: <path>'):\n")
        out_lines.extend([f"  {m}" for m in missing])
        out_lines.append("")
    if mismatches:
        out_lines.append("Files with header path mismatch (declared vs expected):\n")
        for declared, expected in mismatches:
            out_lines.append(f"  declared: {declared}\n  expected: {expected}\n")
    if not missing and not mismatches:
        out_lines.append(f"All checked files have correct header paths. OK={ok}")

    text = "\n".join(out_lines)
    if args.write_report:
        Path(args.write_report).parent.mkdir(parents=True, exist_ok=True)
        Path(args.write_report).write_text(text)
    else:
        print(text)

    return 0 if (not missing and not mismatches) else 1

if __name__ == "__main__":
    sys.exit(main())

