<!-- File: arioncomply-v1/Mockup/reportBuilder.html -->
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ArionComply - Report Builder</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link href="mystyles.css" rel="stylesheet" />
    <style>
      .report-builder-container {
        display: grid;
        grid-template-columns: 280px 1fr;
        gap: 2rem;
        height: calc(100vh - 200px);
      }

      .report-sidebar {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
      }

      .sidebar-section {
        background: var(--bg-white);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-subtle);
        padding: 1.5rem;
        overflow-y: auto;
      }

      .report-main {
        background: var(--bg-white);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-subtle);
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .section-title {
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--text-dark);
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .template-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 0.5rem;
      }

      .template-item {
        padding: 0.75rem;
        border: 1px solid var(--border-light);
        border-radius: var(--border-radius-sm);
        cursor: pointer;
        transition: all 0.15s ease;
        background: var(--bg-white);
      }

      .template-item:hover {
        border-color: var(--primary-blue);
        background: var(--bg-light);
      }

      .template-item.selected {
        border-color: var(--primary-blue);
        background: rgba(37, 99, 235, 0.05);
      }

      .template-name {
        font-weight: 500;
        margin-bottom: 0.25rem;
        font-size: 0.875rem;
      }

      .template-desc {
        font-size: 0.75rem;
        color: var(--text-gray);
        line-height: 1.2;
      }

      .template-framework {
        display: inline-block;
        padding: 0.125rem 0.5rem;
        background: var(--bg-light);
        border-radius: 9999px;
        font-size: 0.625rem;
        color: var(--text-gray);
        margin-top: 0.5rem;
      }

      .data-source-list {
        list-style: none;
      }

      .data-source-item {
        display: flex;
        align-items: center;
        padding: 0.5rem;
        margin-bottom: 0.25rem;
        border-radius: var(--border-radius-sm);
        cursor: pointer;
        transition: all 0.15s ease;
      }

      .data-source-item:hover {
        background: var(--bg-light);
      }

      .source-checkbox {
        margin-right: 0.75rem;
      }

      .source-icon {
        margin-right: 0.5rem;
        width: 16px;
        text-align: center;
        color: var(--primary-blue);
      }

      .source-label {
        flex: 1;
        font-size: 0.875rem;
      }

      .source-count {
        font-size: 0.75rem;
        color: var(--text-gray);
        background: var(--bg-gray);
        padding: 0.125rem 0.5rem;
        border-radius: 9999px;
      }

      .report-header {
        padding: 1.5rem 2rem;
        border-bottom: 1px solid var(--border-light);
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .report-info {
        flex: 1;
      }

      .report-title-input {
        font-size: 1.25rem;
        font-weight: 600;
        border: none;
        outline: none;
        background: transparent;
        width: 100%;
        margin-bottom: 0.5rem;
      }

      .report-meta {
        display: flex;
        gap: 2rem;
        font-size: 0.875rem;
        color: var(--text-gray);
      }

      .report-actions {
        display: flex;
        gap: 0.5rem;
      }

      .report-content {
        flex: 1;
        padding: 2rem;
        overflow-y: auto;
      }

      .report-section {
        margin-bottom: 3rem;
        padding: 1.5rem;
        border: 1px solid var(--border-light);
        border-radius: var(--border-radius);
        position: relative;
      }

      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
      }

      .section-title-input {
        font-size: 1.125rem;
        font-weight: 600;
        border: none;
        outline: none;
        background: transparent;
        color: var(--text-dark);
      }

      .section-controls {
        display: flex;
        gap: 0.25rem;
      }

      .section-control-btn {
        width: 24px;
        height: 24px;
        border: none;
        background: none;
        cursor: pointer;
        border-radius: var(--border-radius-sm);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.15s ease;
        font-size: 0.75rem;
        color: var(--text-gray);
      }

      .section-control-btn:hover {
        background: var(--bg-gray);
        color: var(--text-dark);
      }

      .section-content {
        min-height: 200px;
      }

      .chart-placeholder {
        background: var(--bg-light);
        border: 2px dashed var(--border-light);
        border-radius: var(--border-radius);
        height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        color: var(--text-gray);
        cursor: pointer;
        transition: all 0.15s ease;
      }

      .chart-placeholder:hover {
        border-color: var(--primary-blue);
        background: rgba(37, 99, 235, 0.05);
      }

      .chart-placeholder i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
      }

      .table-container {
        border: 1px solid var(--border-light);
        border-radius: var(--border-radius);
        overflow: hidden;
      }

      .report-table {
        width: 100%;
        border-collapse: collapse;
      }

      .report-table th,
      .report-table td {
        padding: 0.75rem 1rem;
        text-align: left;
        border-bottom: 1px solid var(--border-light);
        font-size: 0.875rem;
      }

      .report-table th {
        background: var(--bg-gray);
        font-weight: 600;
        color: var(--text-gray);
      }

      .report-table tr:last-child td {
        border-bottom: none;
      }

      .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
      }

      .metric-card {
        background: var(--bg-light);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        text-align: center;
      }

      .metric-value {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
      }

      .metric-value.success {
        color: var(--success-green);
      }

      .metric-value.warning {
        color: var(--warning-amber);
      }

      .metric-value.danger {
        color: var(--danger-red);
      }

      .metric-value.primary {
        color: var(--primary-blue);
      }

      .metric-label {
        font-size: 0.875rem;
        color: var(--text-gray);
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .metric-change {
        font-size: 0.75rem;
        margin-top: 0.25rem;
      }

      .metric-change.positive {
        color: var(--success-green);
      }

      .metric-change.negative {
        color: var(--danger-red);
      }

      .text-content {
        line-height: 1.6;
        color: var(--text-gray);
      }

      .text-content h3 {
        color: var(--text-dark);
        margin: 1.5rem 0 0.75rem 0;
      }

      .text-content h4 {
        color: var(--text-dark);
        margin: 1rem 0 0.5rem 0;
      }

      .text-content p {
        margin-bottom: 1rem;
      }

      .text-content ul {
        margin-bottom: 1rem;
        padding-left: 1.5rem;
      }

      .text-content li {
        margin-bottom: 0.5rem;
      }

      .add-section-btn {
        width: 100%;
        padding: 2rem;
        border: 2px dashed var(--border-light);
        border-radius: var(--border-radius);
        background: var(--bg-light);
        cursor: pointer;
        transition: all 0.15s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        color: var(--text-gray);
      }

      .add-section-btn:hover {
        border-color: var(--primary-blue);
        background: rgba(37, 99, 235, 0.05);
      }

      .add-section-btn i {
        font-size: 2rem;
        margin-bottom: 0.5rem;
      }

      .filter-group {
        margin-bottom: 1.5rem;
      }

      .filter-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-gray);
        margin-bottom: 0.5rem;
      }

      .filter-select {
        width: 100%;
        padding: 0.5rem 0.75rem;
        border: 1px solid var(--border-light);
        border-radius: var(--border-radius-sm);
        background: var(--bg-white);
        cursor: pointer;
        font-size: 0.875rem;
      }

      .date-range {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
      }

      .date-input {
        padding: 0.5rem;
        border: 1px solid var(--border-light);
        border-radius: var(--border-radius-sm);
        font-size: 0.875rem;
      }

      .export-options {
        background: var(--bg-light);
        border-radius: var(--border-radius-sm);
        padding: 1rem;
      }

      .export-title {
        font-weight: 600;
        margin-bottom: 0.75rem;
        font-size: 0.875rem;
      }

      .export-format-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
      }

      .export-format {
        padding: 0.5rem;
        border: 1px solid var(--border-light);
        border-radius: var(--border-radius-sm);
        background: var(--bg-white);
        cursor: pointer;
        text-align: center;
        font-size: 0.75rem;
        transition: all 0.15s ease;
      }

      .export-format:hover {
        border-color: var(--primary-blue);
        background: var(--primary-blue);
        color: white;
      }

      .section-add-menu {
        position: absolute;
        top: 100%;
        right: 0;
        background: var(--bg-white);
        border: 1px solid var(--border-light);
        border-radius: var(--border-radius);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        padding: 0.5rem;
        z-index: 100;
        display: none;
        min-width: 200px;
      }

      .section-add-menu.show {
        display: block;
      }

      .menu-item {
        padding: 0.5rem 0.75rem;
        cursor: pointer;
        border-radius: var(--border-radius-sm);
        transition: all 0.15s ease;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .menu-item:hover {
        background: var(--bg-light);
      }

      .menu-item i {
        width: 16px;
        text-align: center;
        color: var(--primary-blue);
      }
    </style>
  </head>
  <body>
    <div class="app-container">
      <!-- Sidebar and Header will be injected here by LayoutManager -->

      <main class="main-content">
        <!-- Header will be injected here if needed -->

        <div class="content">
          <div class="page-header">
            <div>
              <h1 class="page-title">Report Builder</h1>
              <p class="page-subtitle">
                Create Custom Compliance Reports & Analytics
              </p>
            </div>
            <div class="page-actions">
              <button class="btn btn-secondary" onclick="loadTemplate()">
                <i class="fas fa-file-import"></i>
                Load Template
              </button>
              <button class="btn btn-secondary" onclick="previewReport()">
                <i class="fas fa-eye"></i>
                Preview
              </button>
              <button class="btn btn-primary" onclick="generateReport()">
                <i class="fas fa-play"></i>
                Generate Report
              </button>
            </div>
          </div>

          <div class="report-builder-container">
            <!-- Report Sidebar -->
            <div class="report-sidebar">
              <!-- Templates -->
              <div class="sidebar-section">
                <div class="section-title">Report Templates</div>
                <div class="template-grid">
                  <div
                    class="template-item selected"
                    onclick="selectTemplate(this, 'iso27001-audit')"
                  >
                    <div class="template-name">ISO 27001 Audit Report</div>
                    <div class="template-desc">
                      Comprehensive audit findings and compliance status
                    </div>
                    <div class="template-framework">ISO 27001</div>
                  </div>

                  <div
                    class="template-item"
                    onclick="selectTemplate(this, 'gdpr-compliance')"
                  >
                    <div class="template-name">GDPR Compliance Report</div>
                    <div class="template-desc">
                      Privacy compliance metrics and DPIA summaries
                    </div>
                    <div class="template-framework">GDPR</div>
                  </div>

                  <div
                    class="template-item"
                    onclick="selectTemplate(this, 'risk-dashboard')"
                  >
                    <div class="template-name">Risk Dashboard</div>
                    <div class="template-desc">
                      Executive risk overview and trend analysis
                    </div>
                    <div class="template-framework">Multi-Framework</div>
                  </div>

                  <div
                    class="template-item"
                    onclick="selectTemplate(this, 'ai-governance')"
                  >
                    <div class="template-name">AI Governance Report</div>
                    <div class="template-desc">
                      AI system compliance and risk assessment
                    </div>
                    <div class="template-framework">EU AI Act</div>
                  </div>

                  <div
                    class="template-item"
                    onclick="selectTemplate(this, 'soc2-readiness')"
                  >
                    <div class="template-name">SOC 2 Readiness</div>
                    <div class="template-desc">
                      Security controls assessment and gaps
                    </div>
                    <div class="template-framework">SOC 2</div>
                  </div>
                </div>
              </div>

              <!-- Data Sources -->
              <div class="sidebar-section">
                <div class="section-title">Data Sources</div>
                <ul class="data-source-list">
                  <li class="data-source-item">
                    <input type="checkbox" class="source-checkbox" checked />
                    <i class="fas fa-exclamation-triangle source-icon"></i>
                    <span class="source-label">Risk Register</span>
                    <span class="source-count">127</span>
                  </li>
                  <li class="data-source-item">
                    <input type="checkbox" class="source-checkbox" checked />
                    <i class="fas fa-robot source-icon"></i>
                    <span class="source-label">AI Systems</span>
                    <span class="source-count">23</span>
                  </li>
                  <li class="data-source-item">
                    <input type="checkbox" class="source-checkbox" />
                    <i class="fas fa-clipboard-check source-icon"></i>
                    <span class="source-label">Audit Findings</span>
                    <span class="source-count">45</span>
                  </li>
                  <li class="data-source-item">
                    <input type="checkbox" class="source-checkbox" />
                    <i class="fas fa-file-contract source-icon"></i>
                    <span class="source-label">Policies</span>
                    <span class="source-count">48</span>
                  </li>
                  <li class="data-source-item">
                    <input type="checkbox" class="source-checkbox" />
                    <i class="fas fa-graduation-cap source-icon"></i>
                    <span class="source-label">Training Records</span>
                    <span class="source-count">156</span>
                  </li>
                  <li class="data-source-item">
                    <input type="checkbox" class="source-checkbox" />
                    <i class="fas fa-shield-alt source-icon"></i>
                    <span class="source-label">Incidents</span>
                    <span class="source-count">12</span>
                  </li>
                </ul>
              </div>

              <!-- Filters -->
              <div class="sidebar-section">
                <div class="section-title">Filters</div>
                <div class="filter-group">
                  <label class="filter-label">Time Period</label>
                  <select class="filter-select">
                    <option>Last 30 Days</option>
                    <option>Last 90 Days</option>
                    <option>Last 6 Months</option>
                    <option>Last Year</option>
                    <option>Custom Range</option>
                  </select>
                </div>

                <div class="filter-group">
                  <label class="filter-label">Framework</label>
                  <select class="filter-select">
                    <option>All Frameworks</option>
                    <option>ISO 27001</option>
                    <option>GDPR</option>
                    <option>SOC 2</option>
                    <option>EU AI Act</option>
                    <option>PCI DSS</option>
                  </select>
                </div>

                <div class="filter-group">
                  <label class="filter-label">Risk Level</label>
                  <select class="filter-select">
                    <option>All Risk Levels</option>
                    <option>Critical</option>
                    <option>High</option>
                    <option>Medium</option>
                    <option>Low</option>
                  </select>
                </div>

                <div class="filter-group">
                  <label class="filter-label">Date Range</label>
                  <div class="date-range">
                    <input type="date" class="date-input" value="2024-11-22" />
                    <input type="date" class="date-input" value="2024-12-22" />
                  </div>
                </div>
              </div>

              <!-- Export Options -->
              <div class="sidebar-section">
                <div class="export-options">
                  <div class="export-title">Export Format</div>
                  <div class="export-format-grid">
                    <div class="export-format" onclick="exportReport('pdf')">
                      <i class="fas fa-file-pdf"></i><br />PDF
                    </div>
                    <div class="export-format" onclick="exportReport('excel')">
                      <i class="fas fa-file-excel"></i><br />Excel
                    </div>
                    <div class="export-format" onclick="exportReport('word')">
                      <i class="fas fa-file-word"></i><br />Word
                    </div>
                    <div
                      class="export-format"
                      onclick="exportReport('powerpoint')"
                    >
                      <i class="fas fa-file-powerpoint"></i><br />PowerPoint
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Report Main -->
            <div class="report-main">
              <!-- Report Header -->
              <div class="report-header">
                <div class="report-info">
                  <input
                    type="text"
                    class="report-title-input"
                    value="ISO 27001 Audit Report - Q4 2024"
                    placeholder="Report Title"
                  />
                  <div class="report-meta">
                    <div><strong>Template:</strong> ISO 27001 Audit Report</div>
                    <div><strong>Generated:</strong> December 22, 2024</div>
                    <div><strong>Author:</strong> Compliance Team</div>
                  </div>
                </div>
                <div class="report-actions">
                  <button class="btn btn-secondary" onclick="saveReport()">
                    <i class="fas fa-save"></i>
                    Save
                  </button>
                  <button class="btn btn-primary" onclick="shareReport()">
                    <i class="fas fa-share"></i>
                    Share
                  </button>
                </div>
              </div>

              <!-- Report Content -->
              <div class="report-content">
                <!-- Executive Summary Section -->
                <div class="report-section">
                  <div class="section-header">
                    <input
                      type="text"
                      class="section-title-input"
                      value="Executive Summary"
                      placeholder="Section Title"
                    />
                    <div class="section-controls">
                      <button
                        class="section-control-btn"
                        onclick="moveSection(this, 'up')"
                        title="Move Up"
                      >
                        <i class="fas fa-arrow-up"></i>
                      </button>
                      <button
                        class="section-control-btn"
                        onclick="moveSection(this, 'down')"
                        title="Move Down"
                      >
                        <i class="fas fa-arrow-down"></i>
                      </button>
                      <button
                        class="section-control-btn"
                        onclick="editSection(this)"
                        title="Edit"
                      >
                        <i class="fas fa-edit"></i>
                      </button>
                      <button
                        class="section-control-btn"
                        onclick="deleteSection(this)"
                        title="Delete"
                      >
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                  <div class="section-content">
                    <div class="text-content">
                      <p>
                        This report presents the findings of the Q4 2024 ISO
                        27001 audit conducted for ArionComply's Information
                        Security Management System (ISMS). The audit assessed
                        compliance with ISO/IEC 27001:2022 standards and
                        evaluated the effectiveness of implemented security
                        controls.
                      </p>

                      <h4>Key Findings</h4>
                      <ul>
                        <li>
                          <strong>Overall Compliance:</strong> 94% compliance
                          rate achieved across all control domains
                        </li>
                        <li>
                          <strong>Critical Issues:</strong> 2 critical findings
                          requiring immediate attention
                        </li>
                        <li>
                          <strong>Risk Reduction:</strong> 23% improvement in
                          overall risk posture since last audit
                        </li>
                        <li>
                          <strong>Certification Status:</strong> Recommended for
                          continued certification with minor corrective actions
                        </li>
                      </ul>

                      <h4>Recommendations</h4>
                      <p>
                        Priority actions include enhancing incident response
                        procedures, updating access control policies, and
                        implementing additional monitoring for cloud services.
                        Full details are provided in the findings section below.
                      </p>
                    </div>
                  </div>
                </div>

                <!-- Key Metrics Section -->
                <div class="report-section">
                  <div class="section-header">
                    <input
                      type="text"
                      class="section-title-input"
                      value="Key Performance Indicators"
                      placeholder="Section Title"
                    />
                    <div class="section-controls">
                      <button
                        class="section-control-btn"
                        onclick="moveSection(this, 'up')"
                        title="Move Up"
                      >
                        <i class="fas fa-arrow-up"></i>
                      </button>
                      <button
                        class="section-control-btn"
                        onclick="moveSection(this, 'down')"
                        title="Move Down"
                      >
                        <i class="fas fa-arrow-down"></i>
                      </button>
                      <button
                        class="section-control-btn"
                        onclick="editSection(this)"
                        title="Edit"
                      >
                        <i class="fas fa-edit"></i>
                      </button>
                      <button
                        class="section-control-btn"
                        onclick="deleteSection(this)"
                        title="Delete"
                      >
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                  <div class="section-content">
                    <div class="metrics-grid">
                      <div class="metric-card">
                        <div class="metric-value success">94%</div>
                        <div class="metric-label">Compliance Score</div>
                        <div class="metric-change positive">
                          +3% from last audit
                        </div>
                      </div>
                      <div class="metric-card">
                        <div class="metric-value warning">8</div>
                        <div class="metric-label">Open Findings</div>
                        <div class="metric-change negative">
                          +2 new findings
                        </div>
                      </div>
                      <div class="metric-card">
                        <div class="metric-value danger">2</div>
                        <div class="metric-label">Critical Issues</div>
                        <div class="metric-change negative">
                          Requires attention
                        </div>
                      </div>
                      <div class="metric-card">
                        <div class="metric-value primary">23</div>
                        <div class="metric-label">Controls Tested</div>
                        <div class="metric-change positive">100% coverage</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Audit Findings Table -->
                <div class="report-section">
                  <div class="section-header">
                    <input
                      type="text"
                      class="section-title-input"
                      value="Audit Findings Summary"
                      placeholder="Section Title"
                    />
                    <div class="section-controls">
                      <button
                        class="section-control-btn"
                        onclick="moveSection(this, 'up')"
                        title="Move Up"
                      >
                        <i class="fas fa-arrow-up"></i>
                      </button>
                      <button
                        class="section-control-btn"
                        onclick="moveSection(this, 'down')"
                        title="Move Down"
                      >
                        <i class="fas fa-arrow-down"></i>
                      </button>
                      <button
                        class="section-control-btn"
                        onclick="editSection(this)"
                        title="Edit"
                      >
                        <i class="fas fa-edit"></i>
                      </button>
                      <button
                        class="section-control-btn"
                        onclick="deleteSection(this)"
                        title="Delete"
                      >
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                  <div class="section-content">
                    <div class="table-container">
                      <table class="report-table">
                        <thead>
                          <tr>
                            <th>Finding ID</th>
                            <th>Control Area</th>
                            <th>Severity</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Due Date</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td>F-001</td>
                            <td>A.12.6.1</td>
                            <td>
                              <span class="badge badge-danger">Critical</span>
                            </td>
                            <td>Incident response procedures incomplete</td>
                            <td>
                              <span class="badge badge-warning">Open</span>
                            </td>
                            <td>2025-01-15</td>
                          </tr>
                          <tr>
                            <td>F-002</td>
                            <td>A.9.1.2</td>
                            <td>
                              <span class="badge badge-danger">Critical</span>
                            </td>
                            <td>Access review process needs enhancement</td>
                            <td>
                              <span class="badge badge-warning">Open</span>
                            </td>
                            <td>2025-01-30</td>
                          </tr>
                          <tr>
                            <td>F-003</td>
                            <td>A.8.1.3</td>
                            <td>
                              <span class="badge badge-warning">High</span>
                            </td>
                            <td>Asset inventory requires updates</td>
                            <td>
                              <span class="badge badge-info">In Progress</span>
                            </td>
                            <td>2025-02-15</td>
                          </tr>
                          <tr>
                            <td>F-004</td>
                            <td>A.13.1.1</td>
                            <td>
                              <span class="badge badge-warning">High</span>
                            </td>
                            <td>Network security monitoring gaps</td>
                            <td>
                              <span class="badge badge-warning">Open</span>
                            </td>
                            <td>2025-02-28</td>
                          </tr>
                          <tr>
                            <td>F-005</td>
                            <td>A.7.2.2</td>
                            <td>
                              <span class="badge badge-info">Medium</span>
                            </td>
                            <td>Security awareness training completion</td>
                            <td>
                              <span class="badge badge-success">Resolved</span>
                            </td>
                            <td>2024-12-31</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>

                <!-- Chart Section -->
                <div class="report-section">
                  <div class="section-header">
                    <input
                      type="text"
                      class="section-title-input"
                      value="Compliance Trend Analysis"
                      placeholder="Section Title"
                    />
                    <div class="section-controls">
                      <button
                        class="section-control-btn"
                        onclick="moveSection(this, 'up')"
                        title="Move Up"
                      >
                        <i class="fas fa-arrow-up"></i>
                      </button>
                      <button
                        class="section-control-btn"
                        onclick="moveSection(this, 'down')"
                        title="Move Down"
                      >
                        <i class="fas fa-arrow-down"></i>
                      </button>
                      <button
                        class="section-control-btn"
                        onclick="editSection(this)"
                        title="Edit"
                      >
                        <i class="fas fa-edit"></i>
                      </button>
                      <button
                        class="section-control-btn"
                        onclick="deleteSection(this)"
                        title="Delete"
                      >
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                  <div class="section-content">
                    <div
                      class="chart-placeholder"
                      onclick="configureChart(this)"
                    >
                      <i class="fas fa-chart-line"></i>
                      <p>Click to configure chart</p>
                      <p style="font-size: 0.75rem">
                        Data source: Audit findings over time
                      </p>
                    </div>
                  </div>
                </div>

                <!-- Add Section Button -->
                <div class="add-section-btn" onclick="showAddSectionMenu(this)">
                  <i class="fas fa-plus"></i>
                  <p>Add Section</p>
                  <div class="section-add-menu" id="add-section-menu">
                    <div class="menu-item" onclick="addSection('text')">
                      <i class="fas fa-align-left"></i>
                      <span>Text Section</span>
                    </div>
                    <div class="menu-item" onclick="addSection('chart')">
                      <i class="fas fa-chart-bar"></i>
                      <span>Chart</span>
                    </div>
                    <div class="menu-item" onclick="addSection('table')">
                      <i class="fas fa-table"></i>
                      <span>Data Table</span>
                    </div>
                    <div class="menu-item" onclick="addSection('metrics')">
                      <i class="fas fa-tachometer-alt"></i>
                      <span>Metrics Grid</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <!-- AI Chat Popup -->
      <button class="chat-trigger" onclick="toggleChat()">
        <i class="fas fa-robot"></i>
      </button>

      <div class="chat-popup" id="chatPopup">
        <div class="chat-header">
          <div class="chat-title">ArionComply AI Assistant</div>
          <button class="chat-close" onclick="toggleChat()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <iframe
          src="chatInterface.html?context=Report%20Builder&embed=1"
          class="chat-iframe"
        ></iframe>
      </div>
    </div>

    <!-- NEW: Add navigation scripts BEFORE existing scripts -->
    <script src="navigation-config.js"></script>
    <script src="sidebar-component.js"></script>
    <script src="layout-manager.js"></script>

    <!-- THEN existing scripts -->
    <script src="seedData.js"></script>
    <script src="scripts.js"></script>
    <script>
      let selectedTemplate = "iso27001-audit";

      document.addEventListener("DOMContentLoaded", function () {
        // NEW: Initialize layout system
        LayoutManager.initializePage("reportBuilder.html");

        // KEEP: Existing page-specific code
        updateChatContext("Report Builder");
        updateBreadcrumb("Reports > Report Builder");
      });

      function selectTemplate(element, templateId) {
        // Update selected state
        document
          .querySelectorAll(".template-item")
          .forEach((item) => item.classList.remove("selected"));
        element.classList.add("selected");

        selectedTemplate = templateId;
        showNotification(
          `Selected template: ${element.querySelector(".template-name").textContent}`,
          "info",
        );
      }

      function loadTemplate() {
        showNotification("Loading template...", "info");
      }

      function previewReport() {
        showNotification("Generating preview...", "info");
      }

      function generateReport() {
        showNotification("Generating report...", "info");

        // Simulate report generation
        setTimeout(() => {
          showNotification("Report generated successfully!", "success");
        }, 2000);
      }

      function saveReport() {
        showNotification("Report saved", "success");
      }

      function shareReport() {
        showNotification("Opening share options...", "info");
      }

      function moveSection(button, direction) {
        const section = button.closest(".report-section");
        if (direction === "up" && section.previousElementSibling) {
          section.parentNode.insertBefore(
            section,
            section.previousElementSibling,
          );
          showNotification("Section moved up", "info");
        } else if (
          direction === "down" &&
          section.nextElementSibling &&
          !section.nextElementSibling.classList.contains("add-section-btn")
        ) {
          section.parentNode.insertBefore(section.nextElementSibling, section);
          showNotification("Section moved down", "info");
        }
      }

      function editSection(button) {
        showNotification("Opening section editor...", "info");
      }

      function deleteSection(button) {
        if (confirm("Are you sure you want to delete this section?")) {
          const section = button.closest(".report-section");
          section.remove();
          showNotification("Section deleted", "success");
        }
      }

      function configureChart(element) {
        showNotification("Opening chart configuration...", "info");
      }

      function showAddSectionMenu(button) {
        const menu = document.getElementById("add-section-menu");
        menu.classList.toggle("show");
      }

      function addSection(type) {
        const sectionTemplates = {
          text: `
                    <div class="text-content">
                        <p>Click to edit this text section. You can add paragraphs, lists, and formatting here.</p>
                    </div>
                `,
          chart: `
                    <div class="chart-placeholder" onclick="configureChart(this)">
                        <i class="fas fa-chart-bar"></i>
                        <p>Click to configure chart</p>
                        <p style="font-size: 0.75rem;">Select data source and chart type</p>
                    </div>
                `,
          table: `
                    <div class="table-container">
                        <table class="report-table">
                            <thead>
                                <tr>
                                    <th>Column 1</th>
                                    <th>Column 2</th>
                                    <th>Column 3</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Sample data</td>
                                    <td>Sample data</td>
                                    <td>Sample data</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                `,
          metrics: `
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-value primary">--</div>
                            <div class="metric-label">Metric 1</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value primary">--</div>
                            <div class="metric-label">Metric 2</div>
                        </div>
                    </div>
                `,
        };

        const titles = {
          text: "Text Section",
          chart: "Chart Section",
          table: "Data Table",
          metrics: "Metrics Section",
        };

        const newSection = document.createElement("div");
        newSection.className = "report-section";
        newSection.innerHTML = `
                <div class="section-header">
                    <input type="text" class="section-title-input" value="${titles[type]}" placeholder="Section Title">
                    <div class="section-controls">
                        <button class="section-control-btn" onclick="moveSection(this, 'up')" title="Move Up">
                            <i class="fas fa-arrow-up"></i>
                        </button>
                        <button class="section-control-btn" onclick="moveSection(this, 'down')" title="Move Down">
                            <i class="fas fa-arrow-down"></i>
                        </button>
                        <button class="section-control-btn" onclick="editSection(this)" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="section-control-btn" onclick="deleteSection(this)" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="section-content">
                    ${sectionTemplates[type]}
                </div>
            `;

        // Insert before the add section button
        const addButton = document.querySelector(".add-section-btn");
        addButton.parentNode.insertBefore(newSection, addButton);

        // Hide the menu
        document.getElementById("add-section-menu").classList.remove("show");

        showNotification(`Added ${titles[type]}`, "success");
      }

      function exportReport(format) {
        showNotification(
          `Exporting report as ${format.toUpperCase()}...`,
          "info",
        );
      }

      // Data source change handlers
      document.querySelectorAll(".source-checkbox").forEach((checkbox) => {
        checkbox.addEventListener("change", function () {
          const label = this.nextElementSibling.nextElementSibling.textContent;
          const action = this.checked ? "included" : "excluded";
          showNotification(`${label} ${action} in report`, "info");
        });
      });

      // Filter change handlers
      document.querySelectorAll(".filter-select").forEach((select) => {
        select.addEventListener("change", function () {
          showNotification(`Filter updated: ${this.value}`, "info");
        });
      });

      // Close add section menu when clicking outside
      document.addEventListener("click", function (e) {
        if (!e.target.closest(".add-section-btn")) {
          document.getElementById("add-section-menu").classList.remove("show");
        }
      });

      // Auto-save functionality
      let autoSaveTimeout;

      function triggerAutoSave() {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(() => {
          showNotification("Report auto-saved", "success");
        }, 2000);
      }

      // Add auto-save to input changes
      document
        .querySelectorAll(".report-title-input, .section-title-input")
        .forEach((input) => {
          input.addEventListener("input", triggerAutoSave);
        });
    </script>
  </body>
</html>
<!-- File: arioncomply-v1/Mockup/reportBuilder.html -->
