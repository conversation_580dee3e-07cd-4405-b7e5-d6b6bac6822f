# AI Backend Design

Purpose: Centralize decisions and open questions for the AI backend across MVP phases.

Contents
- Architecture: high-level components and flows
- Retrieval Design: hybrid BM25 + vector + graph hops
- Data Compliance: anonymization for GLLMs, retention, logging
- HITL Workflow: post-generation approvals for DB and documents
- VectorDB Tenancy: org_id isolation vs dedicated instances
- Ingestion Pipeline: sources, extraction (PDF/DOCX/Markdown/Locling), forked outputs
- Prompt Management: templates, versioning, safety
- Provider Strategy: SLLMs (INT8) primary, GLLMs fallback
- Session & Traceability: IDs and correlation across layers
- LLM 2.0 Preprocessing: minimizing LLM reasoning

See sibling docs in this folder for details. Each file lists decisions, open questions, and next steps.

Source of truth for deployment
- The deployment topology and operational flows are defined in:
  - `AI-Backend-Design/AI_Backend_Deployment.md`
  - Diagrams included via PlantUML in `AI-Backend-Design/diagrams/`
  - Treat that document as authoritative for API+worker wiring, LLM selection policy, vector store usage, and path/write guards.
