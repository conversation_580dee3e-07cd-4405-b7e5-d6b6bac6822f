{"permissions": {"allow": ["Bash(find:*)", "Bash(for f in /Users/<USER>/LocalProjects/GitHubProjectsDocuments/xLLMArionComply/arioncomply-v1/db/migrations/*.sql)", "Bash(grep:*)", "Bash(done)", "Bash(for f in /Users/<USER>/LocalProjects/GitHubProjectsDocuments/xLLMArionComply/arioncomply-v1/ai-backend/supabase_migrations/schemas/*.sql)", "Bash(tree:*)", "Read:*", "Edit:*", "Write:*", "Glob:*", "Grep:*", "MultiEdit:*", "TodoWrite:*"], "deny": [], "ask": [], "defaultMode": "acceptEdits"}, "hooks": {"PostToolUse": [{"matcher": "TodoWrite", "hooks": [{"type": "command", "command": "echo '📋 REMINDER: Update ActionsToDo.md CURRENT SESSION TODO LIST section to maintain continuity across conversation restarts. File: arioncomply-v1/ProjectManagement/ActionsToDo.md'"}]}]}}