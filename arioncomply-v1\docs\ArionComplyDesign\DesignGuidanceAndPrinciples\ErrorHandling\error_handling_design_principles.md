# Error Handling Design Principles

This document establishes consistent principles for error handling across all system components, with particular focus on resolving inconsistencies between the DataDriveUISchemaAndFunctions architecture and the SubscriptionManagement implementation.

## 1. Error Classification

### Error Categories

**Standard: Classify errors into consistent categories**

| Category | Description | HTTP Status Codes | Example |
|----------|-------------|-------------------|---------|
| Authentication | User identity errors | 401 | Invalid credentials |
| Authorization | Permission errors | 403 | Insufficient permissions |
| Validation | Invalid input data | 400, 422 | Invalid email format |
| Resource | Entity not found | 404 | Subscription not found |
| Conflict | Resource state conflict | 409 | Plan already exists |
| External | Third-party service errors | 502, 503, 504 | Payment provider unavailable |
| Internal | System errors | 500 | Database connection failed |
| Rate Limit | Request throttling | 429 | Too many requests |

**Rationale:**
- Consistent categorization simplifies error handling
- Categories map naturally to HTTP status codes
- Clear separation of client errors vs. system errors

**Rejected Alternatives:**
- Simple error/success flags: Too limited for proper handling
- Custom status codes: Create confusion and inconsistency
- Uncategorized errors: Difficult to handle systematically

### Error Codes

**Standard: Use hierarchical error codes**

Format: `<DOMAIN>_<TYPE>_<DETAIL>`

Examples:
- `SUBSCRIPTION_NOT_FOUND`
- `BILLING_PAYMENT_DECLINED`
- `AUTH_TOKEN_EXPIRED`
- `VALIDATION_REQUIRED_FIELD`

**Rationale:**
- Hierarchical codes enable systematic handling
- Domain prefix identifies the error source
- Consistent format improves error tracking

**Rejected Alternatives:**
- Numeric error codes: Less self-documenting
- Inconsistent naming: Complicates error handling
- Generic error messages: Insufficient for debugging

## 2. Error Response Structure

### API Error Response

**Standard: Use consistent JSON structure for all error responses**

```json
{
  "error": {
    "code": "SUBSCRIPTION_NOT_FOUND",
    "message": "Subscription with ID '123' not found",
    "details": {
      "subscriptionId": "123",
      "organizationId": "456"
    },
    "help": "https://docs.example.com/errors/subscription-not-found"
  },
  "meta": {
    "timestamp": "2025-08-28T12:34:56Z",
    "requestId": "req_abcdef123456"
  }
}
```

**Rationale:**
- Consistent structure simplifies client-side parsing
- Error code enables programmatic handling
- Human-readable message improves user experience
- Details provide context for debugging
- Help link offers resolution guidance

**Rejected Alternatives:**
- Varying formats per endpoint: Complicates client implementation
- Multiple error arrays: Creates ambiguity about primary error
- Minimal error responses: Insufficient for proper debugging

### Database Error Handling

**Standard: Transform database errors into application errors**

```javascript
try {
  const { data, error } = await supabase
    .from('subscriptions')
    .select('*')
    .eq('id', subscriptionId)
    .single();
    
  if (error) {
    if (error.code === 'PGRST116') {
      throw new ResourceNotFoundError(
        'SUBSCRIPTION_NOT_FOUND',
        `Subscription with ID '${subscriptionId}' not found`,
        { subscriptionId }
      );
    }
    
    throw new DatabaseError(
      'DATABASE_ERROR',
      'A database error occurred',
      { originalError: error.message }
    );
  }
  
  return data;
} catch (error) {
  if (error instanceof ApplicationError) {
    throw error;
  }
  
  throw new InternalError(
    'INTERNAL_SERVER_ERROR',
    'An unexpected error occurred',
    { originalError: error.message }
  );
}
```

**Rationale:**
- Transforms low-level errors into meaningful application errors
- Preserves original error information for debugging
- Prevents leaking internal details to clients

## 3. Error Handling Implementation

### Application Error Classes

**Standard: Use a hierarchy of error classes**

```javascript
// Base error class
class ApplicationError extends Error {
  constructor(code, message, details = {}, statusCode = 500) {
    super(message);
    this.code = code;
    this.details = details;
    this.statusCode = statusCode;
    this.timestamp = new Date().toISOString();
  }
  
  toResponse() {
    return {
      statusCode: this.statusCode,
      body: JSON.stringify({
        error: {
          code: this.code,
          message: this.message,
          details: this.details
        },
        meta: {
          timestamp: this.timestamp,
          requestId: getCurrentRequestId()
        }
      })
    };
  }
}

// Specific error types
class ValidationError extends ApplicationError {
  constructor(code, message, details) {
    super(code, message, details, 400);
  }
}

class ResourceNotFoundError extends ApplicationError {
  constructor(code, message, details) {
    super(code, message, details, 404);
  }
}

// More error classes...
```

**Rationale:**
- Class hierarchy enables consistent error handling
- Error instances contain all necessary response information
- Simplifies error propagation and handling

**Rejected Alternatives:**
- Ad-hoc error objects: Inconsistent structure and handling
- Status code only: Insufficient error information
- String-only errors: Limited context for debugging

### Frontend Error Handling

**Standard: Implement consistent error handling in UI components**

```javascript
// React error handling hook
function useApiCall(apiFunction) {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  
  const execute = async (...args) => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiFunction(...args);
      setData(result);
      return result;
    } catch (err) {
      const errorData = err.response?.data?.error || {
        code: 'UNKNOWN_ERROR',
        message: err.message
      };
      
      setError(errorData);
      
      // Handle specific error types
      if (errorData.code === 'AUTH_SESSION_EXPIRED') {
        AuthService.redirectToLogin();
      }
      
      throw errorData;
    } finally {
      setLoading(false);
    }
  };
  
  return { data, error, loading, execute };
}
```

**Rationale:**
- Consistent error handling across components
- Centralized handling of common errors (e.g., expired sessions)
- Error state management for UI feedback

## 4. Error Logging and Monitoring

### Error Logging

**Standard: Log errors with consistent structure and severity**

```javascript
function logError(error, context = {}) {
  const errorData = {
    code: error.code || 'UNKNOWN_ERROR',
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    requestId: getCurrentRequestId(),
    userId: getCurrentUser()?.id,
    organizationId: getCurrentOrganization()?.id,
    ...context
  };
  
  // Determine severity
  let severity = 'ERROR';
  if (error instanceof ValidationError) severity = 'WARN';
  if (error instanceof ResourceNotFoundError) severity = 'INFO';
  
  // Log to appropriate destination
  Logger.log(severity, 'API_ERROR', errorData);
  
  // For critical errors, send alerts
  if (severity === 'ERROR') {
    AlertService.sendAlert('API_ERROR', errorData);
  }
}
```

**Rationale:**
- Structured logging enables better search and analysis
- Severity classification improves monitoring
- Consistent format across all system components

### Error Metrics

**Standard: Track error metrics for monitoring and alerting**

Metrics to track:
- Error rate by category
- Error rate by endpoint
- Error rate by organization
- Error resolution time
- Recurring error patterns

**Implementation:**
```javascript
function recordErrorMetrics(error) {
  const labels = {
    errorCode: error.code,
    errorCategory: getErrorCategory(error),
    endpoint: getCurrentEndpoint(),
    organizationId: getCurrentOrganization()?.id
  };
  
  Metrics.increment('api_errors_total', 1, labels);
  Metrics.observe('api_error_handling_seconds', getErrorHandlingTime(), labels);
}
```

**Rationale:**
- Error metrics enable proactive monitoring
- Pattern detection helps identify systemic issues
- Organization-specific metrics help prioritize fixes

## 5. User-Facing Error Messages

### Message Guidelines

**Standard: Create user-friendly error messages**

Guidelines:
- Be specific about what went wrong
- Suggest corrective action when possible
- Avoid technical jargon
- Don't blame the user
- Include support reference when appropriate

Examples:
- Instead of: "Validation error: plan_id is null"
- Use: "Please select a subscription plan to continue"

- Instead of: "Database constraint violation"
- Use: "This email address is already registered. Please sign in or use a different email"

**Rationale:**
- User-friendly messages improve experience
- Actionable suggestions help resolve issues
- Consistent tone maintains brand voice

### Internationalization

**Standard: Support translated error messages**

Implementation:
```javascript
// Error message translation
function getErrorMessage(error, locale = 'en') {
  const key = `errors.${error.code}`;
  return i18n.translate(key, locale, {
    defaultValue: error.message,
    ...error.details
  });
}
```

Translation file example:
```json
{
  "errors": {
    "SUBSCRIPTION_NOT_FOUND": "We couldn't find this subscription",
    "BILLING_PAYMENT_DECLINED": "Your payment was declined: {{reason}}",
    "VALIDATION_REQUIRED_FIELD": "{{field}} is required"
  }
}
```

**Rationale:**
- Localized error messages improve global user experience
- Separation of error codes from messages enables translation
- Variable substitution maintains context in translations

## 6. Database Constraint Errors

### Constraint Naming

**Standard: Use consistent naming for database constraints**

Format: `<table>_<field>_<type>_<detail>`

Examples:
- `subscriptions_plan_id_fkey` - Foreign key constraint
- `users_email_unique` - Unique constraint
- `invoices_amount_check_positive` - Check constraint

**Rationale:**
- Consistent naming enables programmatic error handling
- Table and field identification simplifies error mapping
- Constraint type indicates error category

**Implementation:**
```sql
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY,
  organization_id UUID NOT NULL,
  plan_id UUID NOT NULL,
  status TEXT NOT NULL,
  
  CONSTRAINT subscriptions_organization_id_fkey 
    FOREIGN KEY (organization_id) REFERENCES organizations(id),
    
  CONSTRAINT subscriptions_plan_id_fkey 
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id),
    
  CONSTRAINT subscriptions_status_check_valid 
    CHECK (status IN ('active', 'cancelled', 'past_due', 'pending'))
);
```

### Constraint Error Mapping

**Standard: Map database constraint errors to application errors**

```javascript
function mapDatabaseError(error) {
  // PostgreSQL unique violation
  if (error.code === '23505') {
    const match = error.detail.match(/Key \((.+)\)=\((.+)\) already exists/);
    if (match) {
      const [, field, value] = match;
      return new ConflictError(
        'RESOURCE_ALREADY_EXISTS',
        `A resource with this ${field} already exists`,
        { field, value }
      );
    }
  }
  
  // PostgreSQL foreign key violation
  if (error.code === '23503') {
    const match = error.detail.match(/Key \((.+)\)=\((.+)\) is not present in table "(.+)"/);
    if (match) {
      const [, field, value, table] = match;
      return new ValidationError(
        'INVALID_REFERENCE',
        `The referenced ${field} does not exist`,
        { field, value, table }
      );
    }
  }
  
  // PostgreSQL check constraint violation
  if (error.code === '23514') {
    const match = error.constraint.match(/^(.+)_check_(.+)$/);
    if (match) {
      const [, field, check] = match;
      return new ValidationError(
        'CONSTRAINT_VIOLATION',
        `The value for ${field} is invalid (${check})`,
        { field, constraint: check }
      );
    }
  }
  
  // Default error for unhandled database errors
  return new DatabaseError(
    'DATABASE_ERROR',
    'A database error occurred',
    { originalError: error.message, code: error.code }
  );
}
```

**Rationale:**
- Transforms cryptic database errors into meaningful application errors
- Preserves error context for debugging
- Consistent handling across all database operations

## Implementation Checklist

- [ ] Define error class hierarchy
- [ ] Implement error mapping for database constraints
- [ ] Create standard error response formatter
- [ ] Update edge functions to use error classes
- [ ] Implement frontend error handling components
- [ ] Create error logging and monitoring service
- [ ] Document error codes and categories

## Related Documents

- API_design_principles.md - For API response standards
- database_schema_design_principles.md - For constraint naming
- ui_design_principles.md - For error display guidelines