"""
File: arioncomply-v1/ai-backend/python-backend/services/retrieval/confidence_scoring_system.py
File Description: Advanced confidence scoring system for dual-vector hybrid search results
Purpose: Calculate and track confidence scores for multi-tier escalation decisions and system optimization
Inputs: search_results, intent_classification, fusion_metadata, historical_data
Outputs: confidence_scores, tier_recommendations, escalation_decisions, system_feedback
Security: Performance tracking without exposing sensitive content
Notes: Core component for multi-tier retrieval system with adaptive thresholds
"""

import logging
import asyncio
import statistics
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import json
import math
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class ConfidenceLevel(str, Enum):
    """Confidence levels for tier routing decisions."""
    VERY_HIGH = "very_high"      # 0.90+ - Deterministic/direct response
    HIGH = "high"                # 0.75-0.89 - Dual vector sufficient  
    MEDIUM = "medium"            # 0.60-0.74 - Cloud LLM needed
    LOW = "low"                  # 0.40-0.59 - Clarifying questions
    VERY_LOW = "very_low"        # <0.40 - System failure


class ConfidenceSignal(str, Enum):
    """Individual confidence signals."""
    SEMANTIC_SIMILARITY = "semantic_similarity"
    INTENT_MATCH = "intent_match"
    SOURCE_AUTHORITY = "source_authority"
    RESULT_DIVERSITY = "result_diversity"
    RESULT_COMPLETENESS = "result_completeness"
    HISTORICAL_SUCCESS = "historical_success"
    CONTEXT_RELEVANCE = "context_relevance"
    KNOWLEDGE_COVERAGE = "knowledge_coverage"


@dataclass
class ConfidenceScore:
    """Detailed confidence score with component breakdown."""
    overall_score: float
    confidence_level: ConfidenceLevel
    component_scores: Dict[ConfidenceSignal, float]
    tier_recommendation: str
    escalation_reason: Optional[str]
    system_feedback: Dict[str, Any]
    calculation_metadata: Dict[str, Any]


@dataclass
class ConfidenceHistoryEntry:
    """Historical confidence tracking entry."""
    timestamp: str
    query_hash: str
    org_id: str
    confidence_score: float
    tier_used: str
    actual_success: Optional[bool]
    user_satisfaction: Optional[float]
    escalation_path: List[str]


class AdaptiveConfidenceScorer:
    """Advanced confidence scoring system with adaptive thresholds."""
    
    def __init__(self):
        """Configure scoring parameters and initialize model weights, if any."""
        # Base confidence thresholds (will be adapted based on performance)
        self.base_thresholds = {
            "tier_0_deterministic": 0.95,
            "tier_1_dual_vector": 0.75,
            "tier_2_cloud_llm": 0.60,
            "tier_3_clarify": 0.0
        }
        
        # Component weight configuration
        self.component_weights = {
            ConfidenceSignal.SEMANTIC_SIMILARITY: 0.25,
            ConfidenceSignal.INTENT_MATCH: 0.15,
            ConfidenceSignal.SOURCE_AUTHORITY: 0.15,
            ConfidenceSignal.RESULT_DIVERSITY: 0.10,
            ConfidenceSignal.RESULT_COMPLETENESS: 0.10,
            ConfidenceSignal.HISTORICAL_SUCCESS: 0.10,
            ConfidenceSignal.CONTEXT_RELEVANCE: 0.10,
            ConfidenceSignal.KNOWLEDGE_COVERAGE: 0.05
        }
        
        # Historical tracking
        self.confidence_history: List[ConfidenceHistoryEntry] = []
        self.adapted_thresholds = self.base_thresholds.copy()
        self.last_adaptation = datetime.utcnow()
        
        logger.info("AdaptiveConfidenceScorer initialized with base thresholds")
    
    async def calculate_confidence(
        self,
        search_results: List[Dict[str, Any]],
        intent_classification: Dict[str, Any],
        fusion_metadata: Dict[str, Any],
        query_context: Dict[str, Any]
    ) -> ConfidenceScore:
        """
        Calculate comprehensive confidence score for search results.
        
        Args:
            search_results: List of search results from hybrid search
            intent_classification: Intent classification metadata
            fusion_metadata: Result fusion metadata
            query_context: Additional query context
            
        Returns:
            ConfidenceScore with detailed component breakdown
        """
        calculation_start = datetime.utcnow()
        
        try:
            # Calculate individual confidence components
            component_scores = {}
            
            # 1. Semantic Similarity Score
            component_scores[ConfidenceSignal.SEMANTIC_SIMILARITY] = self._calculate_semantic_similarity_score(search_results)
            
            # 2. Intent Match Score  
            component_scores[ConfidenceSignal.INTENT_MATCH] = self._calculate_intent_match_score(
                search_results, intent_classification
            )
            
            # 3. Source Authority Score
            component_scores[ConfidenceSignal.SOURCE_AUTHORITY] = self._calculate_source_authority_score(
                search_results, fusion_metadata
            )
            
            # 4. Result Diversity Score
            component_scores[ConfidenceSignal.RESULT_DIVERSITY] = self._calculate_diversity_score(
                search_results, fusion_metadata
            )
            
            # 5. Result Completeness Score
            component_scores[ConfidenceSignal.RESULT_COMPLETENESS] = self._calculate_completeness_score(
                search_results, intent_classification
            )
            
            # 6. Historical Success Score
            component_scores[ConfidenceSignal.HISTORICAL_SUCCESS] = await self._calculate_historical_success_score(
                query_context
            )
            
            # 7. Context Relevance Score
            component_scores[ConfidenceSignal.CONTEXT_RELEVANCE] = self._calculate_context_relevance_score(
                search_results, query_context
            )
            
            # 8. Knowledge Coverage Score
            component_scores[ConfidenceSignal.KNOWLEDGE_COVERAGE] = self._calculate_knowledge_coverage_score(
                search_results, intent_classification
            )
            
            # Calculate weighted overall score
            overall_score = self._calculate_weighted_score(component_scores)
            
            # Determine confidence level and tier recommendation
            confidence_level = self._determine_confidence_level(overall_score)
            tier_recommendation, escalation_reason = self._recommend_tier(overall_score, component_scores)
            
            # Generate system feedback
            system_feedback = self._generate_system_feedback(component_scores, fusion_metadata)
            
            # Calculation metadata
            calculation_metadata = {
                "calculation_time_ms": (datetime.utcnow() - calculation_start).total_seconds() * 1000,
                "component_count": len(component_scores),
                "threshold_version": "adaptive_v1",
                "adapted_thresholds": self.adapted_thresholds,
                "base_thresholds": self.base_thresholds
            }
            
            confidence_score = ConfidenceScore(
                overall_score=overall_score,
                confidence_level=confidence_level,
                component_scores=component_scores,
                tier_recommendation=tier_recommendation,
                escalation_reason=escalation_reason,
                system_feedback=system_feedback,
                calculation_metadata=calculation_metadata
            )
            
            logger.debug(f"Confidence calculated: {overall_score:.3f} ({confidence_level.value}) -> {tier_recommendation}")
            return confidence_score
            
        except Exception as e:
            logger.error(f"Confidence calculation failed: {e}", exc_info=True)
            
            # Return fallback confidence
            return self._create_fallback_confidence(str(e))
    
    def _calculate_semantic_similarity_score(self, search_results: List[Dict[str, Any]]) -> float:
        """Calculate confidence based on semantic similarity scores."""
        
        if not search_results:
            return 0.0
        
        # Extract similarity scores (lower is better for cosine distance)
        similarity_scores = []
        for result in search_results:
            score = result.get("score", 1.0)  # Default to poor score if missing
            # Convert distance to similarity (0 = perfect match, 2 = opposite)
            similarity = max(0.0, 1.0 - (score / 2.0))
            similarity_scores.append(similarity)
        
        # Calculate confidence based on top results
        if similarity_scores:
            # Average of top 3 results (or all if less than 3)
            top_scores = sorted(similarity_scores, reverse=True)[:3]
            avg_similarity = sum(top_scores) / len(top_scores)
            
            # Apply diminishing returns for very high similarity
            confidence = min(1.0, avg_similarity * 1.2)
            return confidence
        
        return 0.0
    
    def _calculate_intent_match_score(
        self, 
        search_results: List[Dict[str, Any]], 
        intent_classification: Dict[str, Any]
    ) -> float:
        """Calculate confidence based on intent classification match."""
        
        intent_confidence = intent_classification.get("confidence_score", 0.5)
        intent_category = intent_classification.get("category", "general_inquiry")
        
        # Base score from intent classification confidence
        base_score = intent_confidence
        
        # Boost for specific intents with good results
        if search_results and intent_category in ["policy_inquiry", "compliance_inquiry", "standards_inquiry"]:
            # Check if results contain relevant metadata
            relevant_results = 0
            for result in search_results:
                metadata = result.get("metadata", {})
                content_type = metadata.get("content_type", "").lower()
                
                if intent_category == "policy_inquiry" and "policy" in content_type:
                    relevant_results += 1
                elif intent_category == "compliance_inquiry" and any(term in content_type for term in ["compliance", "regulation"]):
                    relevant_results += 1
                elif intent_category == "standards_inquiry" and any(term in content_type for term in ["standard", "framework"]):
                    relevant_results += 1
            
            # Boost confidence if we have relevant results
            if relevant_results > 0:
                relevance_boost = min(0.3, relevant_results * 0.1)
                base_score = min(1.0, base_score + relevance_boost)
        
        return base_score
    
    def _calculate_source_authority_score(
        self, 
        search_results: List[Dict[str, Any]], 
        fusion_metadata: Dict[str, Any]
    ) -> float:
        """Calculate confidence based on source authority and credibility."""
        
        if not search_results:
            return 0.0
        
        authority_score = 0.0
        
        # Source distribution analysis
        source_distribution = fusion_metadata.get("source_distribution", {})
        chromadb_results = source_distribution.get("chromadb", 0)
        supabase_results = source_distribution.get("supabase_vector", 0)
        total_results = chromadb_results + supabase_results
        
        if total_results > 0:
            # Boost for having both public and private sources
            if chromadb_results > 0 and supabase_results > 0:
                authority_score += 0.3  # Cross-source validation boost
            
            # Individual source authority
            for result in search_results:
                metadata = result.get("metadata", {})
                source_store = result.get("source_store", "")
                
                # ChromaDB (public standards) authority scoring
                if source_store == "chromadb":
                    content_type = metadata.get("content_type", "").lower()
                    standards_type = metadata.get("standards_type", "").lower()
                    
                    if any(standard in standards_type for standard in ["iso_27001", "gdpr", "nist_csf"]):
                        authority_score += 0.15  # Major standards boost
                    elif "regulation" in content_type or "standard" in content_type:
                        authority_score += 0.1   # General regulatory boost
                
                # Supabase Vector (private org data) authority scoring
                elif source_store == "supabase_vector":
                    document_type = metadata.get("document_type", "").lower()
                    sensitivity = metadata.get("sensitivity_level", "").lower()
                    
                    if document_type == "company_policy":
                        authority_score += 0.12  # Company policy boost
                    elif document_type in ["compliance_assessment", "audit_report"]:
                        authority_score += 0.1   # Assessment/audit boost
                    
                    # Sensitivity level consideration
                    if sensitivity in ["confidential", "restricted"]:
                        authority_score += 0.05  # High-value internal data boost
        
        return min(1.0, authority_score)
    
    def _calculate_diversity_score(
        self, 
        search_results: List[Dict[str, Any]], 
        fusion_metadata: Dict[str, Any]
    ) -> float:
        """Calculate confidence based on result diversity and coverage."""
        
        if not search_results:
            return 0.0
        
        diversity_score = 0.0
        
        # Source diversity (dual vector store coverage)
        source_distribution = fusion_metadata.get("source_distribution", {})
        chromadb_count = source_distribution.get("chromadb", 0)
        supabase_count = source_distribution.get("supabase_vector", 0)
        total_count = chromadb_count + supabase_count
        
        if total_count > 0:
            # Perfect balance gets max score, imbalance reduces score
            balance = min(chromadb_count, supabase_count) / max(chromadb_count, supabase_count, 1)
            diversity_score += balance * 0.4
            
            # Bonus for having both sources
            if chromadb_count > 0 and supabase_count > 0:
                diversity_score += 0.3
        
        # Content type diversity
        content_types = set()
        document_types = set()
        
        for result in search_results:
            metadata = result.get("metadata", {})
            content_types.add(metadata.get("content_type", "unknown"))
            document_types.add(metadata.get("document_type", "unknown"))
        
        # Boost for content diversity
        content_diversity = min(1.0, len(content_types) / 3.0)  # Max boost at 3+ types
        diversity_score += content_diversity * 0.2
        
        # Boost for document type diversity  
        doc_diversity = min(1.0, len(document_types) / 3.0)
        diversity_score += doc_diversity * 0.1
        
        return min(1.0, diversity_score)
    
    def _calculate_completeness_score(
        self, 
        search_results: List[Dict[str, Any]], 
        intent_classification: Dict[str, Any]
    ) -> float:
        """Calculate confidence based on result completeness for the given intent."""
        
        if not search_results:
            return 0.0
        
        intent_category = intent_classification.get("category", "general_inquiry")
        result_count = len(search_results)
        
        # Base completeness based on result count
        base_completeness = min(1.0, result_count / 5.0)  # Max score at 5+ results
        
        # Intent-specific completeness requirements
        completeness_bonus = 0.0
        
        if intent_category == "policy_inquiry":
            # Look for policy-related content
            policy_results = sum(1 for r in search_results 
                               if "policy" in r.get("metadata", {}).get("document_type", "").lower())
            if policy_results > 0:
                completeness_bonus += min(0.3, policy_results * 0.1)
        
        elif intent_category == "compliance_inquiry":
            # Look for compliance and regulatory content
            compliance_results = sum(1 for r in search_results 
                                   if any(term in r.get("metadata", {}).get("content_type", "").lower() 
                                         for term in ["compliance", "regulation", "standard"]))
            if compliance_results > 0:
                completeness_bonus += min(0.3, compliance_results * 0.1)
        
        elif intent_category == "assessment_inquiry":
            # Look for assessment-related content
            assessment_results = sum(1 for r in search_results 
                                   if "assessment" in r.get("metadata", {}).get("content_type", "").lower())
            if assessment_results > 0:
                completeness_bonus += min(0.3, assessment_results * 0.1)
        
        # Coverage breadth (different sources/documents)
        unique_docs = len(set(r.get("doc_id", "") for r in search_results))
        coverage_bonus = min(0.2, unique_docs * 0.05)
        
        return min(1.0, base_completeness + completeness_bonus + coverage_bonus)
    
    async def _calculate_historical_success_score(self, query_context: Dict[str, Any]) -> float:
        """Calculate confidence based on historical success patterns."""
        
        # For MVP, return neutral score
        # In production, this would analyze historical performance for similar queries
        return 0.5
    
    def _calculate_context_relevance_score(
        self, 
        search_results: List[Dict[str, Any]], 
        query_context: Dict[str, Any]
    ) -> float:
        """Calculate confidence based on contextual relevance."""
        
        if not search_results:
            return 0.0
        
        org_id = query_context.get("org_id")
        query_text = query_context.get("query_text", "").lower()
        
        relevance_score = 0.0
        
        # Organization context relevance
        if org_id:
            org_specific_results = sum(1 for r in search_results 
                                     if r.get("metadata", {}).get("org_id") == org_id)
            if org_specific_results > 0:
                relevance_score += min(0.4, org_specific_results * 0.1)
        
        # Query-content alignment
        query_terms = set(query_text.split())
        alignment_scores = []
        
        for result in search_results:
            text = result.get("text", "").lower()
            text_terms = set(text.split())
            
            # Calculate term overlap
            if query_terms and text_terms:
                overlap = len(query_terms.intersection(text_terms))
                alignment = overlap / max(len(query_terms), 1)
                alignment_scores.append(alignment)
        
        if alignment_scores:
            avg_alignment = sum(alignment_scores) / len(alignment_scores)
            relevance_score += avg_alignment * 0.4
        
        # Recency relevance (recent documents may be more relevant)
        recent_results = 0
        for result in search_results:
            created_at = result.get("metadata", {}).get("created_at", "")
            if created_at:
                try:
                    created_date = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    days_old = (datetime.utcnow().replace(tzinfo=None) - created_date.replace(tzinfo=None)).days
                    if days_old < 180:  # Less than 6 months old
                        recent_results += 1
                except:
                    pass
        
        if recent_results > 0:
            recency_boost = min(0.2, recent_results * 0.05)
            relevance_score += recency_boost
        
        return min(1.0, relevance_score)
    
    def _calculate_knowledge_coverage_score(
        self, 
        search_results: List[Dict[str, Any]], 
        intent_classification: Dict[str, Any]
    ) -> float:
        """Calculate confidence based on knowledge domain coverage."""
        
        if not search_results:
            return 0.0
        
        intent_category = intent_classification.get("category", "general_inquiry")
        
        # Analyze knowledge domain coverage
        domains_covered = set()
        frameworks_covered = set()
        
        for result in search_results:
            metadata = result.get("metadata", {})
            
            # Standards/frameworks coverage
            standards_type = metadata.get("standards_type", "")
            if standards_type:
                frameworks_covered.add(standards_type)
            
            # Compliance frameworks
            compliance_frameworks = metadata.get("compliance_frameworks", [])
            if isinstance(compliance_frameworks, list):
                frameworks_covered.update(compliance_frameworks)
            
            # Content domains
            content_type = metadata.get("content_type", "")
            document_type = metadata.get("document_type", "")
            domains_covered.add(content_type)
            domains_covered.add(document_type)
        
        # Score based on domain coverage
        coverage_score = 0.0
        
        # Framework coverage boost
        framework_count = len(frameworks_covered)
        if framework_count > 0:
            coverage_score += min(0.5, framework_count * 0.15)
        
        # Domain coverage boost
        domain_count = len(domains_covered) 
        if domain_count > 0:
            coverage_score += min(0.3, domain_count * 0.1)
        
        # Intent-specific coverage requirements
        if intent_category == "compliance_inquiry" and framework_count >= 2:
            coverage_score += 0.2  # Multiple compliance frameworks bonus
        elif intent_category == "standards_inquiry" and any(fw in str(frameworks_covered).lower() 
                                                           for fw in ["iso", "nist", "gdpr"]):
            coverage_score += 0.2  # Major standards bonus
        
        return min(1.0, coverage_score)
    
    def _calculate_weighted_score(self, component_scores: Dict[ConfidenceSignal, float]) -> float:
        """Calculate weighted overall confidence score."""
        
        weighted_sum = 0.0
        total_weight = 0.0
        
        for signal, score in component_scores.items():
            weight = self.component_weights.get(signal, 0.0)
            weighted_sum += score * weight
            total_weight += weight
        
        if total_weight > 0:
            return weighted_sum / total_weight
        
        return 0.0
    
    def _determine_confidence_level(self, overall_score: float) -> ConfidenceLevel:
        """Determine confidence level from overall score."""
        
        if overall_score >= 0.90:
            return ConfidenceLevel.VERY_HIGH
        elif overall_score >= 0.75:
            return ConfidenceLevel.HIGH
        elif overall_score >= 0.60:
            return ConfidenceLevel.MEDIUM
        elif overall_score >= 0.40:
            return ConfidenceLevel.LOW
        else:
            return ConfidenceLevel.VERY_LOW
    
    def _recommend_tier(
        self, 
        overall_score: float, 
        component_scores: Dict[ConfidenceSignal, float]
    ) -> Tuple[str, Optional[str]]:
        """Recommend tier routing and escalation reason."""
        
        escalation_reason = None
        
        # Use adapted thresholds for tier decisions
        if overall_score >= self.adapted_thresholds["tier_0_deterministic"]:
            return "tier_0_deterministic", escalation_reason
        elif overall_score >= self.adapted_thresholds["tier_1_dual_vector"]:
            return "tier_1_dual_vector", escalation_reason
        elif overall_score >= self.adapted_thresholds["tier_2_cloud_llm"]:
            escalation_reason = self._identify_escalation_reason(component_scores, "tier_2")
            return "tier_2_cloud_llm", escalation_reason
        else:
            escalation_reason = self._identify_escalation_reason(component_scores, "tier_3")
            return "tier_3_clarify", escalation_reason
    
    def _identify_escalation_reason(
        self, 
        component_scores: Dict[ConfidenceSignal, float], 
        target_tier: str
    ) -> str:
        """Identify the primary reason for tier escalation."""
        
        # Find the weakest confidence components
        weak_components = [
            signal for signal, score in component_scores.items() 
            if score < 0.5
        ]
        
        if not weak_components:
            return "overall_low_confidence"
        
        # Map weak components to escalation reasons
        reason_mapping = {
            ConfidenceSignal.SEMANTIC_SIMILARITY: "poor_semantic_match",
            ConfidenceSignal.INTENT_MATCH: "unclear_intent",
            ConfidenceSignal.SOURCE_AUTHORITY: "insufficient_authoritative_sources",
            ConfidenceSignal.RESULT_DIVERSITY: "limited_source_coverage",
            ConfidenceSignal.RESULT_COMPLETENESS: "incomplete_results",
            ConfidenceSignal.CONTEXT_RELEVANCE: "poor_context_match",
            ConfidenceSignal.KNOWLEDGE_COVERAGE: "insufficient_domain_coverage"
        }
        
        # Return reason for the weakest component
        weakest_component = min(weak_components, key=lambda x: component_scores[x])
        return reason_mapping.get(weakest_component, "multiple_factors")
    
    def _generate_system_feedback(
        self, 
        component_scores: Dict[ConfidenceSignal, float], 
        fusion_metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate system feedback for optimization."""
        
        feedback = {
            "strengths": [],
            "weaknesses": [],
            "recommendations": [],
            "threshold_adjustments": {}
        }
        
        # Identify strengths (scores > 0.75)
        for signal, score in component_scores.items():
            if score > 0.75:
                feedback["strengths"].append({
                    "component": signal.value,
                    "score": score,
                    "description": self._get_strength_description(signal, score)
                })
        
        # Identify weaknesses (scores < 0.5)
        for signal, score in component_scores.items():
            if score < 0.5:
                feedback["weaknesses"].append({
                    "component": signal.value,
                    "score": score,
                    "description": self._get_weakness_description(signal, score),
                    "recommendation": self._get_improvement_recommendation(signal)
                })
        
        # Overall recommendations
        if len(feedback["weaknesses"]) > len(feedback["strengths"]):
            feedback["recommendations"].append("Consider improving data quality or expanding knowledge base")
        
        source_distribution = fusion_metadata.get("source_distribution", {})
        if source_distribution.get("chromadb", 0) == 0:
            feedback["recommendations"].append("Consider adding more public standards knowledge")
        elif source_distribution.get("supabase_vector", 0) == 0:
            feedback["recommendations"].append("Consider adding more organization-specific content")
        
        return feedback
    
    def _get_strength_description(self, signal: ConfidenceSignal, score: float) -> str:
        """Get description for confidence strength."""
        descriptions = {
            ConfidenceSignal.SEMANTIC_SIMILARITY: f"Excellent semantic match (score: {score:.2f})",
            ConfidenceSignal.SOURCE_AUTHORITY: f"Strong authoritative sources (score: {score:.2f})",
            ConfidenceSignal.RESULT_DIVERSITY: f"Good source diversity (score: {score:.2f})",
            ConfidenceSignal.INTENT_MATCH: f"Clear intent classification (score: {score:.2f})"
        }
        return descriptions.get(signal, f"Strong {signal.value} (score: {score:.2f})")
    
    def _get_weakness_description(self, signal: ConfidenceSignal, score: float) -> str:
        """Get description for confidence weakness."""
        descriptions = {
            ConfidenceSignal.SEMANTIC_SIMILARITY: f"Poor semantic similarity (score: {score:.2f})",
            ConfidenceSignal.SOURCE_AUTHORITY: f"Limited authoritative sources (score: {score:.2f})",
            ConfidenceSignal.RESULT_DIVERSITY: f"Poor source diversity (score: {score:.2f})",
            ConfidenceSignal.INTENT_MATCH: f"Unclear intent classification (score: {score:.2f})"
        }
        return descriptions.get(signal, f"Weak {signal.value} (score: {score:.2f})")
    
    def _get_improvement_recommendation(self, signal: ConfidenceSignal) -> str:
        """Get improvement recommendation for weak signal."""
        recommendations = {
            ConfidenceSignal.SEMANTIC_SIMILARITY: "Improve embedding quality or add more relevant content",
            ConfidenceSignal.SOURCE_AUTHORITY: "Add more authoritative sources or validate existing ones",
            ConfidenceSignal.RESULT_DIVERSITY: "Expand knowledge base coverage across vector stores",
            ConfidenceSignal.INTENT_MATCH: "Improve intent classification model or add training data",
            ConfidenceSignal.RESULT_COMPLETENESS: "Increase result limit or expand search parameters",
            ConfidenceSignal.CONTEXT_RELEVANCE: "Improve query preprocessing or context extraction",
            ConfidenceSignal.KNOWLEDGE_COVERAGE: "Expand domain coverage in knowledge base"
        }
        return recommendations.get(signal, f"Improve {signal.value} component")
    
    def _create_fallback_confidence(self, error_message: str) -> ConfidenceScore:
        """Create fallback confidence score for error cases."""
        
        return ConfidenceScore(
            overall_score=0.0,
            confidence_level=ConfidenceLevel.VERY_LOW,
            component_scores={},
            tier_recommendation="tier_3_clarify",
            escalation_reason="confidence_calculation_error",
            system_feedback={
                "error": error_message,
                "recommendations": ["Check system health and retry"]
            },
            calculation_metadata={
                "error": True,
                "error_message": error_message
            }
        )
    
    async def record_confidence_outcome(
        self, 
        confidence_score: ConfidenceScore,
        actual_outcome: Dict[str, Any]
    ):
        """Record confidence score outcome for adaptive learning."""
        
        try:
            # Extract outcome data
            success = actual_outcome.get("success", False)
            user_satisfaction = actual_outcome.get("user_satisfaction")
            tier_used = actual_outcome.get("tier_used", "unknown")
            
            # Create history entry
            history_entry = ConfidenceHistoryEntry(
                timestamp=datetime.utcnow().isoformat() + "Z",
                query_hash=actual_outcome.get("query_hash", ""),
                org_id=actual_outcome.get("org_id", ""),
                confidence_score=confidence_score.overall_score,
                tier_used=tier_used,
                actual_success=success,
                user_satisfaction=user_satisfaction,
                escalation_path=actual_outcome.get("escalation_path", [])
            )
            
            self.confidence_history.append(history_entry)
            
            # Trigger threshold adaptation if enough data
            if len(self.confidence_history) % 50 == 0:  # Adapt every 50 outcomes
                await self._adapt_thresholds()
            
            logger.debug(f"Recorded confidence outcome: score={confidence_score.overall_score:.3f}, success={success}")
            
        except Exception as e:
            logger.error(f"Failed to record confidence outcome: {e}")
    
    async def _adapt_thresholds(self):
        """Adapt confidence thresholds based on historical performance."""
        
        try:
            if len(self.confidence_history) < 10:
                return  # Need minimum data for adaptation
            
            # Analyze recent performance (last 100 entries)
            recent_history = self.confidence_history[-100:]
            
            # Calculate success rates by confidence ranges
            success_by_range = {}
            for entry in recent_history:
                if entry.actual_success is not None:
                    confidence_range = self._get_confidence_range(entry.confidence_score)
                    if confidence_range not in success_by_range:
                        success_by_range[confidence_range] = []
                    success_by_range[confidence_range].append(entry.actual_success)
            
            # Calculate success rates
            range_success_rates = {
                range_name: sum(successes) / len(successes)
                for range_name, successes in success_by_range.items()
                if len(successes) > 0
            }
            
            # Adjust thresholds based on success rates
            adjustments = {}
            
            # If high confidence range has low success rate, increase threshold
            if "high" in range_success_rates and range_success_rates["high"] < 0.8:
                adjustments["tier_1_dual_vector"] = min(0.85, self.adapted_thresholds["tier_1_dual_vector"] + 0.05)
            
            # If medium confidence range has good success rate, decrease threshold
            if "medium" in range_success_rates and range_success_rates["medium"] > 0.7:
                adjustments["tier_2_cloud_llm"] = max(0.5, self.adapted_thresholds["tier_2_cloud_llm"] - 0.05)
            
            # Apply adjustments
            for threshold_name, new_value in adjustments.items():
                old_value = self.adapted_thresholds[threshold_name]
                self.adapted_thresholds[threshold_name] = new_value
                logger.info(f"Adapted threshold {threshold_name}: {old_value:.3f} -> {new_value:.3f}")
            
            self.last_adaptation = datetime.utcnow()
            
        except Exception as e:
            logger.error(f"Threshold adaptation failed: {e}")
    
    def _get_confidence_range(self, confidence_score: float) -> str:
        """Get confidence range category for analysis."""
        if confidence_score >= 0.75:
            return "high"
        elif confidence_score >= 0.60:
            return "medium"
        elif confidence_score >= 0.40:
            return "low"
        else:
            return "very_low"
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get confidence scoring system status."""
        
        recent_count = len([h for h in self.confidence_history 
                          if datetime.fromisoformat(h.timestamp.replace('Z', '+00:00')).replace(tzinfo=None) > 
                             datetime.utcnow() - timedelta(hours=24)])
        
        return {
            "status": "operational",
            "total_confidence_calculations": len(self.confidence_history),
            "recent_24h_calculations": recent_count,
            "threshold_adaptation_enabled": True,
            "last_adaptation": self.last_adaptation.isoformat() + "Z",
            "current_thresholds": self.adapted_thresholds,
            "base_thresholds": self.base_thresholds,
            "component_weights": {k.value: v for k, v in self.component_weights.items()}
        }


# Global instance
confidence_scorer = AdaptiveConfidenceScorer()


# Convenience functions
async def calculate_search_confidence(
    search_results: List[Dict[str, Any]],
    intent_classification: Dict[str, Any],
    fusion_metadata: Dict[str, Any],
    query_context: Dict[str, Any]
) -> ConfidenceScore:
    """Convenience function for confidence calculation."""
    return await confidence_scorer.calculate_confidence(
        search_results, intent_classification, fusion_metadata, query_context
    )


async def record_search_outcome(
    confidence_score: ConfidenceScore,
    actual_outcome: Dict[str, Any]
):
    """Convenience function for outcome recording."""
    return await confidence_scorer.record_confidence_outcome(confidence_score, actual_outcome)
