id: Q165
query: >-
  How do we handle international employee data?
packs:
  - "GDPR:2016"
  - "ISO27701:2019"
primary_ids:
  - "GDPR:2016/Art.3"
  - "ISO27701:2019/8.2"
overlap_ids: []
capability_tags:
  - "Register"
  - "Workflow"
flags:
  - "LOCAL LAW_CHECK"
sources:
  - title: "GDPR — Territorial Scope"
    id: "GDPR:2016/Art.3"
    locator: "Article 3"
  - title: "ISO/IEC 27701:2019 — PII Processing Records"
    id: "ISO27701:2019/8.2"
    locator: "Section 8.2"
ui:
  cards_hint:
    - "Employee data register"
  actions:
    - type: "start_workflow"
      target: "employee_data_map"
      label: "Map Employee Data"
    - type: "open_register"
      target: "ropa"
      label: "View RoPA"
output_mode: "both"
graph_required: false
notes: "Local labor laws may add extra rules beyond GDPR"
---
### 165) How do we handle international employee data?

**Standard terms)**  
- **Territorial scope (GDPR Art. 3):** EU staff = EU data subjects.  
- **RoPA (ISO 27701 § 8.2):** log all processing activities.

**Plain-English answer**  
Apply GDPR (and local labor-privacy laws) wherever employees reside. Document processing in your **Record of Processing Activities (RoPA)** and apply appropriate transfer safeguards for HR systems hosted abroad.

**Applies to**  
- **Primary:** GDPR Article 3; ISO/IEC 27701:2019 § 8.2

**Why it matters**  
Employee data carries high sensitivity and legal penalties.

**Do next in our platform**  
- Run **Employee Data Map** workflow to capture systems, purposes, and locations.  
- Add transfer safeguards for non-EEA HR tools.

**How our platform will help**  
- **[Register]** Pre-built HR data categories, retention defaults.  
- **[Workflow]** Guides consent forms and privacy notices per jurisdiction.

**Likely follow-ups**  
- Do we need works-council approval in the EU?

**Sources**  
- GDPR Article 3; ISO/IEC 27701:2019 § 8.2

**Legal note:** Consult local counsel for country-specific HR rules.  
