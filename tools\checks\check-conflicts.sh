#!/usr/bin/env bash
# File: tools/checks/check-conflicts.sh
# File Description: Fails if Git conflict markers exist in the repo
# Purpose: Prevent committing unresolved merge conflicts
# Notes: Scans tracked files for <<<<<<<, =======, >>>>>>> markers

set -euo pipefail

cd "$(git rev-parse --show-toplevel)" >/dev/null 2>&1 || true

if rg -n "^(<<<<<<<|=======|>>>>>>>)" --hidden --no-ignore-vcs -g '!**/*.png' -g '!**/*.jpg' -g '!**/*.jpeg' -g '!**/*.gif' -g '!**/*.pdf' -g '!**/*.svg' -g '!**/*.ico' -g '!**/node_modules/**' -g '!**/.git/**' .; then
  echo "Error: Conflict markers found above. Resolve them before commit." >&2
  exit 1
fi

echo "No conflict markers found."
