id: Q122
query: >-
  How do we handle compliance for Software-as-a-Service providers?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/A.15.1.2"
  - "GDPR:2016/Art.28"
overlap_ids: []
capability_tags:
  - "Register"
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Annex A.15.1.2 Supplier Service Delivery"
    id: "ISO27001:2022/A.15.1.2"
    locator: "Annex A.15.1.2"
  - title: "GDPR — Processor Contracts"
    id: "GDPR:2016/Art.28"
    locator: "Article 28"
ui:
  cards_hint:
    - "SaaS compliance register"
  actions:
    - type: "open_register"
      target: "saas_compliance"
      label: "View SaaS Register"
    - type: "start_workflow"
      target: "saas_onboarding"
      label: "Onboard SaaS Provider"
output_mode: "both"
graph_required: false
notes: "Ensure SLAs cover availability, privacy, and audit rights"
---
### 122) How do we handle compliance for Software-as-a-Service providers?

**Standard terms)**  
- **Supplier service delivery (A.15.1.2):** ensure provider meets contractual obligations.  
- **Processor contracts (GDPR Art. 28):** mandatory data processor terms.

**Plain-English answer**  
Maintain a **SaaS compliance register**, embed availability, privacy, and audit clauses in contracts, verify provider certifications, and schedule periodic compliance reviews.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.15.1.2; GDPR Article 28

**Why it matters**  
Addresses shared-responsibility and data protection when using SaaS.

**Do next in our platform**  
- Populate **SaaS Compliance** register.  
- Launch **SaaS Onboarding** workflow.

**How our platform will help**  
- **[Register]** Tracks provider controls and SLAs.  
- **[Workflow]** Automated review reminders.

**Likely follow-ups**  
- “What certifications should SaaS vendors have?” (ISO 27001, SOC 2)

**Sources**  
- ISO/IEC 27001:2022 Annex A.15.1.2; GDPR Article 28
