"""
File: arioncomply-v1/ai-backend/python-backend/services/preprocessing/query_preprocessor.py
File Description: Deterministic query preprocessing pipeline
Purpose: Resolve queries through synonym/paraphrase/E-PMI matching before RAG/LLM
Inputs: User query, org context, framework hints
Outputs: Deterministic match results or preprocessed query for RAG
Dependencies: synonym_index, multi_index, e_pmi_associations
Security/RLS: Org-scoped synonym and index access
Notes: Returns early if deterministic match found; enriches query otherwise
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Any
import re
import time
from enum import Enum

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from logging.events import log_event


class MatchType(Enum):
    EXACT_CANONICAL_ID = "exact_canonical_id"
    SYNONYM_MATCH = "synonym_match"
    PARAPHRASE_MATCH = "paraphrase_match"
    E_PMI_ASSOCIATION = "e_pmi_association"
    GRAPH_CRAWL_MATCH = "graph_crawl_match"
    BM25_FALLBACK = "bm25_fallback"
    NO_DETERMINISTIC_MATCH = "no_deterministic_match"


@dataclass
class DeterministicMatch:
    match_type: MatchType
    confidence: float
    content_id: str
    canonical_ids: List[str]
    explanation: str
    source_terms: List[str]
    metadata: Dict[str, Any]


@dataclass
class PreprocessingResult:
    deterministic_match: Optional[DeterministicMatch]
    enriched_query: str
    query_expansions: List[str]
    framework_filters: List[str]
    processing_time_ms: int
    cache_hit: bool


class QueryPreprocessor:
    """
    Deterministic query preprocessing pipeline that attempts to resolve
    queries without hitting RAG/LLM through multiple matching strategies.
    """
    
    def __init__(self, synonym_index, multi_index, e_pmi_associations, graph_service, org_id: str):
        """Initialize the preprocessing pipeline with indices and org context.

        Args:
            synonym_index: Lookup for term → synonyms per org
            multi_index: Access to canonical, synonym, paraphrase indexes
            e_pmi_associations: Precomputed PMI associations
            graph_service: Graph traversal service
            org_id: Organization id for scoping
        """
        self.synonym_index = synonym_index
        self.multi_index = multi_index
        self.e_pmi_associations = e_pmi_associations
        self.graph_service = graph_service
        self.org_id = org_id
        
        # Canonical ID patterns for exact matching
        self.canonical_patterns = [
            r'ISO\s*27001:?2022?[/\s]*A?\.?(\d+\.?\d*)',  # ISO27001 variations
            r'ISO\s*27701:?2019?[/\s]*(\d+\.?\d*)',       # ISO27701 variations
            r'GDPR:?2016?[/\s]*Art\.?(\d+)',              # GDPR articles
            r'A\.?(\d+\.?\d*)',                           # Shorthand Annex references
        ]
    
    async def preprocess_query(
        self, 
        query: str, 
        framework_hint: Optional[str] = None,
        request_id: str = None
    ) -> PreprocessingResult:
        """
        Main preprocessing pipeline that attempts deterministic resolution.
        
        Pipeline stages:
        1. Exact canonical ID matching
        2. Synonym expansion and matching
        3. Paraphrase detection
        4. E-PMI association lookup
        5. BM25 fallback preparation
        """
        start_time = time.time()
        
        # Stage 1: Exact canonical ID matching
        canonical_match = self._check_canonical_id_match(query, framework_hint)
        if canonical_match:
            result = PreprocessingResult(
                deterministic_match=canonical_match,
                enriched_query=query,
                query_expansions=[],
                framework_filters=[framework_hint] if framework_hint else [],
                processing_time_ms=int((time.time() - start_time) * 1000),
                cache_hit=False
            )
            await self._log_preprocessing_event(request_id, "canonical_id_match", result)
            return result
        
        # Stage 2: Synonym expansion and matching
        synonym_expansions = self._expand_synonyms(query)
        synonym_match = await self._check_synonym_match(query, synonym_expansions, framework_hint)
        if synonym_match:
            result = PreprocessingResult(
                deterministic_match=synonym_match,
                enriched_query=query,
                query_expansions=synonym_expansions,
                framework_filters=[framework_hint] if framework_hint else [],
                processing_time_ms=int((time.time() - start_time) * 1000),
                cache_hit=False
            )
            await self._log_preprocessing_event(request_id, "synonym_match", result)
            return result
        
        # Stage 3: Paraphrase detection
        paraphrase_match = await self._check_paraphrase_match(query, framework_hint)
        if paraphrase_match:
            result = PreprocessingResult(
                deterministic_match=paraphrase_match,
                enriched_query=query,
                query_expansions=synonym_expansions,
                framework_filters=[framework_hint] if framework_hint else [],
                processing_time_ms=int((time.time() - start_time) * 1000),
                cache_hit=False
            )
            await self._log_preprocessing_event(request_id, "paraphrase_match", result)
            return result
        
        # Stage 4: E-PMI association lookup
        e_pmi_match = await self._check_e_pmi_associations(query, framework_hint)
        if e_pmi_match:
            result = PreprocessingResult(
                deterministic_match=e_pmi_match,
                enriched_query=query,
                query_expansions=synonym_expansions,
                framework_filters=[framework_hint] if framework_hint else [],
                processing_time_ms=int((time.time() - start_time) * 1000),
                cache_hit=False
            )
            await self._log_preprocessing_event(request_id, "e_pmi_match", result)
            return result
        
        # Stage 5: Graph crawl from any partial matches found in previous stages
        graph_match = await self._perform_graph_crawl(query, framework_hint, request_id)
        if graph_match:
            result = PreprocessingResult(
                deterministic_match=graph_match,
                enriched_query=query,
                query_expansions=synonym_expansions,
                framework_filters=[framework_hint] if framework_hint else [],
                processing_time_ms=int((time.time() - start_time) * 1000),
                cache_hit=False
            )
            await self._log_preprocessing_event(request_id, "graph_crawl_match", result)
            return result
        
        # Stage 6: No deterministic match - prepare enriched query for RAG
        enriched_query = self._enrich_query_for_rag(query, synonym_expansions)
        framework_filters = self._detect_framework_intent(query, framework_hint)
        
        result = PreprocessingResult(
            deterministic_match=None,
            enriched_query=enriched_query,
            query_expansions=synonym_expansions,
            framework_filters=framework_filters,
            processing_time_ms=int((time.time() - start_time) * 1000),
            cache_hit=False
        )
        
        await self._log_preprocessing_event(request_id, "no_deterministic_match", result)
        return result
    
    def _check_canonical_id_match(self, query: str, framework_hint: Optional[str]) -> Optional[DeterministicMatch]:
        """
        Check for exact canonical ID matches (e.g., "A.8.12", "ISO27001:2022/A.8.12").
        This is the highest confidence deterministic match.
        """
        for pattern in self.canonical_patterns:
            matches = re.finditer(pattern, query, re.IGNORECASE)
            for match in matches:
                canonical_id = self._normalize_canonical_id(match.group(), framework_hint)
                content = self.multi_index.get_by_canonical_id(canonical_id, self.org_id)
                
                if content:
                    return DeterministicMatch(
                        match_type=MatchType.EXACT_CANONICAL_ID,
                        confidence=0.95,
                        content_id=content.get('id'),
                        canonical_ids=[canonical_id],
                        explanation=f"Exact match for canonical ID: {canonical_id}",
                        source_terms=[match.group()],
                        metadata=content
                    )
        return None
    
    def _expand_synonyms(self, query: str) -> List[str]:
        """
        Expand query terms using org-specific synonym mappings.
        These come from the v2 JSON content authoring.
        """
        query_terms = re.findall(r'\b\w+\b', query.lower())
        expansions = []
        
        for term in query_terms:
            # Look up synonyms in org-scoped synonym index
            synonyms = self.synonym_index.get_synonyms(term, self.org_id)
            expansions.extend(synonyms)
        
        return list(set(expansions))
    
    async def _check_synonym_match(
        self, 
        query: str, 
        expansions: List[str], 
        framework_hint: Optional[str]
    ) -> Optional[DeterministicMatch]:
        """
        Check if expanded synonyms match indexed content.
        """
        all_terms = [query] + expansions
        
        for term in all_terms:
            matches = self.multi_index.search_synonyms(term, self.org_id, framework_hint)
            if matches:
                best_match = max(matches, key=lambda x: x.get('confidence', 0))
                if best_match.get('confidence', 0) >= 0.8:
                    return DeterministicMatch(
                        match_type=MatchType.SYNONYM_MATCH,
                        confidence=best_match.get('confidence'),
                        content_id=best_match.get('id'),
                        canonical_ids=best_match.get('canonical_ids', []),
                        explanation=f"Synonym match for: {term}",
                        source_terms=[term],
                        metadata=best_match
                    )
        return None
    
    async def _check_paraphrase_match(
        self, 
        query: str, 
        framework_hint: Optional[str]
    ) -> Optional[DeterministicMatch]:
        """
        Check for paraphrase matches using pre-computed paraphrase index.
        """
        # This would use the paraphrase mappings generated from v2 JSON content
        paraphrase_matches = self.multi_index.search_paraphrases(query, self.org_id, framework_hint)
        
        if paraphrase_matches:
            best_match = max(paraphrase_matches, key=lambda x: x.get('confidence', 0))
            if best_match.get('confidence', 0) >= 0.75:
                return DeterministicMatch(
                    match_type=MatchType.PARAPHRASE_MATCH,
                    confidence=best_match.get('confidence'),
                    content_id=best_match.get('id'),
                    canonical_ids=best_match.get('canonical_ids', []),
                    explanation=f"Paraphrase match for query pattern",
                    source_terms=[query],
                    metadata=best_match
                )
        return None
    
    async def _check_e_pmi_associations(
        self, 
        query: str, 
        framework_hint: Optional[str]
    ) -> Optional[DeterministicMatch]:
        """
        Use Enhanced PMI associations to find related content.
        """
        query_terms = re.findall(r'\b\w+\b', query.lower())
        
        for term in query_terms:
            associations = self.e_pmi_associations.get_associations(term, self.org_id, framework_hint)
            if associations:
                best_association = max(associations, key=lambda x: x.get('pmi_score', 0))
                if best_association.get('pmi_score', 0) >= 0.6:
                    return DeterministicMatch(
                        match_type=MatchType.E_PMI_ASSOCIATION,
                        confidence=best_association.get('pmi_score'),
                        content_id=best_association.get('id'),
                        canonical_ids=best_association.get('canonical_ids', []),
                        explanation=f"E-PMI association for: {term}",
                        source_terms=[term],
                        metadata=best_association
                    )
        return None
    
    async def _perform_graph_crawl(
        self, 
        query: str, 
        framework_hint: Optional[str],
        request_id: Optional[str]
    ) -> Optional[DeterministicMatch]:
        """
        Perform multi-hop graph crawl to find relevant content through relationship traversal.
        
        This stage is triggered when direct matching methods (canonical ID, synonym, 
        paraphrase, E-PMI) don't find high-confidence results. The graph crawl uses
        any partial matches as seed nodes and traverses the knowledge graph to find
        related content through weighted relationships.
        
        Algorithm:
        1. Collect seed nodes from weaker matches in previous stages
        2. Use GraphCrawler to perform multi-hop traversal
        3. Return highest-scoring result if above confidence threshold
        
        Args:
            query: User query string
            framework_hint: Optional framework context
            request_id: For logging and traceability
            
        Returns:
            DeterministicMatch if graph traversal finds high-confidence content, None otherwise
        """
        # Import graph crawler (lazy import to avoid circular dependencies)
        from preprocessing.graph_crawler import create_graph_crawler, GraphNode
        
        # Create graph crawler instance for this organization
        crawler = create_graph_crawler(self.org_id, self.graph_service)
        
        # Collect seed nodes from weaker matches that didn't meet thresholds in previous stages
        seed_nodes = []
        
        # Check for low-confidence canonical ID matches that could be expanded
        canonical_seeds = self._find_weak_canonical_matches(query, framework_hint)
        seed_nodes.extend(canonical_seeds)
        
        # Check for partial synonym matches below the confidence threshold
        synonym_seeds = await self._find_weak_synonym_matches(query, framework_hint)
        seed_nodes.extend(synonym_seeds)
        
        # Check for low-scoring E-PMI associations
        pmi_seeds = await self._find_weak_pmi_matches(query, framework_hint)
        seed_nodes.extend(pmi_seeds)
        
        # If we have no seed nodes, can't perform graph crawl
        if not seed_nodes:
            return None
        
        # Perform graph crawl from seed nodes
        try:
            crawl_result = await crawler.crawl_from_seeds(query, seed_nodes, request_id)
            
            # Check if we found high-confidence content through graph traversal
            if (crawl_result.discovered_content and 
                crawl_result.confidence >= 0.7 and 
                crawl_result.aggregated_scores):
                
                # Get the highest-scoring discovered content
                best_content_id = max(
                    crawl_result.aggregated_scores.keys(),
                    key=lambda x: crawl_result.aggregated_scores[x]
                )
                best_score = crawl_result.aggregated_scores[best_content_id]
                
                # Find the corresponding content node
                best_content = next(
                    (node for node in crawl_result.discovered_content 
                     if node.node_id == best_content_id), None
                )
                
                if best_content and best_score >= 0.7:
                    return DeterministicMatch(
                        match_type=MatchType.GRAPH_CRAWL_MATCH,
                        confidence=best_score,
                        content_id=best_content_id,
                        canonical_ids=best_content.canonical_ids,
                        explanation=f"Graph traversal found related content: {crawl_result.explanation}",
                        source_terms=[query],
                        metadata={
                            "crawl_result": crawl_result,
                            "seed_count": len(seed_nodes),
                            "paths_explored": len(crawl_result.traversal_paths),
                            "processing_time_ms": crawl_result.processing_time_ms
                        }
                    )
                    
        except Exception as e:
            # Log graph crawl error but don't fail the entire preprocessing
            if request_id:
                await log_event(
                    request_id=request_id,
                    org_id=self.org_id,
                    user_id=None,
                    event_type="graph_crawl_error",
                    direction="internal",
                    details={"error": str(e), "query": query}
                )
        
        return None
    
    def _find_weak_canonical_matches(self, query: str, framework_hint: Optional[str]) -> List['GraphNode']:
        """
        Find canonical ID matches that were below the confidence threshold for direct return
        but could serve as seeds for graph crawl.
        
        Args:
            query: User query string
            framework_hint: Optional framework context
            
        Returns:
            List of GraphNode objects to use as graph crawl seeds
        """
        from preprocessing.graph_crawler import GraphNode
        
        seeds = []
        for pattern in self.canonical_patterns:
            matches = re.finditer(pattern, query, re.IGNORECASE)
            for match in matches:
                canonical_id = self._normalize_canonical_id(match.group(), framework_hint)
                content = self.multi_index.get_by_canonical_id(canonical_id, self.org_id)
                
                if content:
                    # Convert to GraphNode format
                    node = GraphNode(
                        node_id=content.get('id'),
                        content_type=content.get('type', 'unknown'),
                        canonical_ids=[canonical_id],
                        title=content.get('title', canonical_id),
                        content_snippet=content.get('snippet', '')[:200],
                        metadata=content,
                        org_id=self.org_id
                    )
                    seeds.append(node)
        
        return seeds
    
    async def _find_weak_synonym_matches(self, query: str, framework_hint: Optional[str]) -> List['GraphNode']:
        """
        Find synonym matches below the confidence threshold that could be graph seeds.
        
        Args:
            query: User query string
            framework_hint: Optional framework context
            
        Returns:
            List of GraphNode objects to use as graph crawl seeds
        """
        from preprocessing.graph_crawler import GraphNode
        
        seeds = []
        synonym_expansions = self._expand_synonyms(query)
        all_terms = [query] + synonym_expansions
        
        for term in all_terms:
            matches = self.multi_index.search_synonyms(term, self.org_id, framework_hint)
            for match in matches:
                # Use matches with confidence 0.5-0.8 as seeds (below direct match threshold)
                confidence = match.get('confidence', 0)
                if 0.5 <= confidence < 0.8:
                    node = GraphNode(
                        node_id=match.get('id'),
                        content_type=match.get('type', 'unknown'),
                        canonical_ids=match.get('canonical_ids', []),
                        title=match.get('title', ''),
                        content_snippet=match.get('snippet', '')[:200],
                        metadata=match,
                        org_id=self.org_id
                    )
                    seeds.append(node)
        
        return seeds
    
    async def _find_weak_pmi_matches(self, query: str, framework_hint: Optional[str]) -> List['GraphNode']:
        """
        Find E-PMI association matches below threshold that could be graph seeds.
        
        Args:
            query: User query string  
            framework_hint: Optional framework context
            
        Returns:
            List of GraphNode objects to use as graph crawl seeds
        """
        from preprocessing.graph_crawler import GraphNode
        
        seeds = []
        query_terms = re.findall(r'\b\w+\b', query.lower())
        
        for term in query_terms:
            associations = self.e_pmi_associations.get_associations(term, self.org_id, framework_hint)
            for assoc in associations:
                # Use associations with PMI score 0.4-0.6 as seeds (below direct match threshold)
                pmi_score = assoc.get('pmi_score', 0)
                if 0.4 <= pmi_score < 0.6:
                    node = GraphNode(
                        node_id=assoc.get('id'),
                        content_type=assoc.get('type', 'unknown'),
                        canonical_ids=assoc.get('canonical_ids', []),
                        title=assoc.get('title', ''),
                        content_snippet=assoc.get('snippet', '')[:200],
                        metadata=assoc,
                        org_id=self.org_id
                    )
                    seeds.append(node)
        
        return seeds
    
    def _normalize_canonical_id(self, raw_id: str, framework_hint: Optional[str]) -> str:
        """
        Normalize various ID formats to canonical format.
        """
        # Remove whitespace and normalize
        normalized = re.sub(r'\s+', '', raw_id)
        
        # If just A.X.Y format and we have framework hint, expand it
        if re.match(r'^A\.\d+', normalized) and framework_hint:
            if framework_hint.startswith('ISO27001'):
                return f"ISO27001:2022/{normalized}"
            elif framework_hint.startswith('ISO27701'):
                return f"ISO27701:2019/{normalized}"
        
        return normalized
    
    def _enrich_query_for_rag(self, query: str, expansions: List[str]) -> str:
        """
        Enrich the query with synonyms and context for better RAG retrieval.
        """
        if not expansions:
            return query
        
        # Add top 3 most relevant expansions
        top_expansions = expansions[:3]
        return f"{query} ({' '.join(top_expansions)})"
    
    def _detect_framework_intent(self, query: str, framework_hint: Optional[str]) -> List[str]:
        """
        Detect which frameworks the query is likely about.
        """
        frameworks = []
        
        if framework_hint:
            frameworks.append(framework_hint)
        
        # Pattern-based framework detection
        if re.search(r'ISO\s*27001|ISMS|information security management', query, re.IGNORECASE):
            frameworks.append('ISO27001:2022')
        
        if re.search(r'ISO\s*27701|privacy|PII|personal data', query, re.IGNORECASE):
            frameworks.append('ISO27701:2019')
        
        if re.search(r'GDPR|data protection|consent|lawful basis', query, re.IGNORECASE):
            frameworks.append('GDPR:2016')
        
        return list(set(frameworks))
    
    async def _log_preprocessing_event(self, request_id: str, stage: str, result: PreprocessingResult):
        """
        Log preprocessing events for monitoring and optimization.
        """
        if not request_id:
            return
        
        details = {
            "stage": stage,
            "processing_time_ms": result.processing_time_ms,
            "query_expansions_count": len(result.query_expansions),
            "framework_filters": result.framework_filters,
            "deterministic_match": result.deterministic_match is not None,
            "cache_hit": result.cache_hit
        }
        
        if result.deterministic_match:
            details.update({
                "match_type": result.deterministic_match.match_type.value,
                "confidence": result.deterministic_match.confidence,
                "content_id": result.deterministic_match.content_id
            })
        
        await log_event(
            request_id=request_id,
            org_id=self.org_id,
            user_id=None,
            event_type="query_preprocessing",
            direction="internal",
            details=details
        )


# Factory function for creating preprocessor instances
def create_query_preprocessor(org_id: str, synonym_index=None, multi_index=None, e_pmi_associations=None):
    """
    Factory function to create org-scoped query preprocessor instances.
    """
    # These would be injected or loaded based on org_id
    if not synonym_index:
        from ..indexing.synonym_index import SynonymIndex
        synonym_index = SynonymIndex(org_id)
    
    if not multi_index:
        from ..indexing.multi_index import MultiIndex
        multi_index = MultiIndex(org_id)
    
    if not e_pmi_associations:
        from ..indexing.e_pmi_associations import EPMIAssociations
        e_pmi_associations = EPMIAssociations(org_id)
    
    return QueryPreprocessor(synonym_index, multi_index, e_pmi_associations, org_id)
