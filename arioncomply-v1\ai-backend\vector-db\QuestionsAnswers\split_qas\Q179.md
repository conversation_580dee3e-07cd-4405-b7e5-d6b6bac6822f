id: Q179
query: >-
  How do we handle data subject rights like deletion when AI has learned from the data?
packs:
  - "GDPR:2016"
  - "EUAI:2024"
primary_ids:
  - "GDPR:2016/Art.17"
  - "EUAI:2024/Art.22"
overlap_ids: []
capability_tags:
  - "Report"
  - "Workflow"
flags:
  - "LOCAL LAW_CHECK"
sources:
  - title: "GDPR — Right to Erasure"
    id: "GDPR:2016/Art.17"
    locator: "Article 17"
  - title: "EU AI Act — Data Governance"
    id: "EUAI:2024/Art.22"
    locator: "Article 22"
ui:
  cards_hint:
    - "AI retraining guide"
  actions:
    - type: "start_workflow"
      target: "ai_dsar_handling"
      label: "Manage AI DSARs"
output_mode: "both"
graph_required: false
notes: "May require retraining, differential privacy, or model pruning"
---
### 179) How do we handle data subject rights like deletion when AI has learned from the data?

**Standard term(s)**  
- **Right to erasure (GDPR Art. 17):** an individual’s right to have personal data deleted without undue delay, subject to certain exceptions.  
- **Data governance (EU AI Act Art. 22):** requirements for managing datasets used in training, validation, and testing of AI systems.

**Plain-English answer**  
When a data subject requests deletion, GDPR requires removing their personal data from systems where it is stored or processed. For AI, this is more complex because training data may be embedded into the model’s parameters. Depending on your design, you may need to retrain the model without the deleted data, apply machine unlearning techniques, or ensure the model cannot output information related to that individual. The EU AI Act reinforces the need for strong data governance, meaning you should have processes in place to trace and manage training data sources.

**Applies to**  
- **Primary:** GDPR Article 17; EU AI Act Article 22

**Why it matters**  
Failure to respect erasure rights in AI contexts can result in both GDPR and AI Act noncompliance, with significant technical and legal consequences.

**Do next in our platform**  
- Launch the **AI DSAR Handling** workflow to evaluate the feasibility and method for removing personal data from AI models.  
- Document any retraining or mitigation steps in compliance reports.

**How our platform will help**  
- **[Workflow]** Structured process to assess and respond to AI-related data subject requests.  
- **[Report]** Record of actions taken and justification for chosen approach.

**Likely follow-ups**  
- “What technical methods can remove learned data from models?” (Machine unlearning, model pruning, differential privacy) [LOCAL LAW CHECK]

**Sources**  
- GDPR Article 17  
- EU AI Act Article 22
