# SLLM Quantization and Inference (Placeholder)

Decisions
- INT8 quantization for on-prem SLLMs to reduce latency/cost.

Design Points
- Model family candidates and tokenizer compatibility with prompts.
- Inference stack: runtime, batching, streaming, and hardware utilization.
- Evaluation: quality vs speed trade-offs vs GLLMs.

Open Questions
- Exact hardware targets and model choices; quantization toolchain.

Next Steps
- Prototype inference path; measure latency/throughput; define selection policy.

