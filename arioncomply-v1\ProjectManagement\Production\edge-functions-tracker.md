# ArionComply Edge Functions Technical Task Tracker

**Document Version:** 1.0  
**Date:** August 27, 2025  
**Status:** Active  
**Reference Documents:** 
- arioncomply-v1/docs/Detailed-Plan.md
- arioncomply-v1/docs/Architecture.md
- arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js

---

## Phase Alignment Update (2025-09-08)

- Edge acts as a thin gateway for AI: forward-only to backend with serverless-safe timeout. Provider routing, retrieval, and anonymization moved to AI Backend. (MVP-Assessment)
- Mandatory logging at Edge (`LOGGING_REQUIRED=true`); `api_request_logs` and `api_event_logs` writes with minimal retry. (MVP-Assessment)
- W3C Trace Context supported: parse/generate `traceparent`, echo in responses, enrich event details with trace IDs (JSON). (MVP-Assessment)
- Events coverage: `response_sent` and `stream_finished` emitted from conversation endpoints. (MVP-Assessment)
- Demo-Light: continue logging; backend will emit `retrieval_run` and `ai_call`.
- Pilot: plan adding explicit columns for trace/session/process per Traceability IDs doc.

## MVP-Assessment-App Priority Scope (Edge)

- [ ] Deploy conversation endpoints: `conversation-start`, `conversation-send`, `conversation-stream` with logging middleware.
- [ ] Implement assessment endpoints (MVP): `assessments.create`, `assessments.answer`, `assessments.score/get`.

### Audit & Logging (MVP-Assessment-App)
- [ ] Derive `orgId`/`userId` from JWT claims; fallback to headers only in local testing — Dependencies: Auth parsing helper
- [x] Emit `response_sent` just before returning; ensure `logRequestEnd` duration covers full handling
- [x] For SSE: emit `stream_finished` on close; record total duration
- [ ] Add `db_read`/`db_write` wrappers to emit events with table and row counts
- [ ] Around outbound AI calls, emit `ai_call` events with target, status, timing, and error details (handled in Backend for AI calls)

## MVP-Demo-Light-App Priority Scope (Edge)

- [ ] Provide `documents.generate` endpoint for SoA/policy (stub acceptable for demo).
- [ ] Scenario bootstrap/reset endpoints for demo flows.
- [ ] Basic registration endpoints for business email/org profile capture.
- [ ] assistant_router (MVP skeleton): dispatch + validation + logging for core routes (conversation, documents, scenarios).
- [ ] ListView Data API (MVP): metadata-driven list retrieval (filters/sort/pagination) from registry.
- [ ] Form submit (MVP): schema-driven writes with runtime validation.
- [ ] Runtime validation (Zod/JSON Schema) for MVP endpoints (ListView/Form/Documents/Scenarios).
- [ ] FieldMapper MVP: override map for snake↔camel exceptions; integrate into casing helpers.

## Production Scope (Later Phases)

- [ ] Single `assistant_router` with route spec + middleware (auth, rate limiting, validation).
- [ ] Full metadata-driven handlers (listview/forms) and broader API coverage.
- [ ] Advanced security middleware and observability.

## MVP-Pilot Scope (Edge)

- [ ] Endpoints to manage and update required registers (assets, risks, processing activities) and generate certification document set.
- [ ] Task/plan endpoints to create and track ongoing maintenance and evidence tasks.

## Testing Readiness Deployment Checklist (Edge Functions)

- [ ] Install Supabase CLI and authenticate (`supabase login`), link the target project (`supabase link`).
- [ ] Configure required env for edge runtime: `SUPABASE_URL`, `SUPABASE_SERVICE_ROLE_KEY` (used by `_shared/supabase.ts`).
- [ ] Integrate conversation endpoints into deployable structure:
  - [ ] Copy or mirror `ai-backend/edge-functions/conversation/start` → `supabase/functions/conversation-start`.
  - [ ] Copy or mirror `ai-backend/edge-functions/conversation/send` → `supabase/functions/conversation-send`.
  - [ ] Copy or mirror `ai-backend/edge-functions/conversation/stream` → `supabase/functions/conversation-stream`.
  - [ ] Include shared modules under `supabase/functions/_shared` (utils.ts, errors.ts, logger.ts, supabase.ts).
- [ ] Ensure CORS and auth headers are accepted for the testing UI (anon key or bearer as configured).
- [ ] Deploy functions: `supabase functions deploy conversation-start conversation-send conversation-stream`.
- [ ] Sanity test each endpoint with curl/Postman: confirm 200/4xx envelopes and that `api_request_logs`/`api_event_logs` receive entries.
- [ ] Record final function URLs for the testing harness configuration.

---

## 1. Core Edge Function Infrastructure

### 1.1 Edge Function Router
- [ ] **Task:** Implement assistant_router edge function
  - **Dependencies:** None (foundation component)
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Request validation
    - [ ] Route determination
    - [ ] Handler dispatching
    - [ ] Error handling
    - [ ] Logging and metrics

### 1.2 Authentication Function
- [ ] **Task:** Implement authentication and session management
  - **Dependencies:** Edge Function Router
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] JWT validation
    - [ ] Permission checking
    - [ ] Session management
    - [ ] MFA verification
    - [ ] Rate limiting integration

### 1.3 Security Middleware
- [ ] **Task:** Implement security middleware
  - **Dependencies:** Edge Function Router
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Request sanitization
    - [ ] Turnstile integration
    - [ ] Rate limiting
    - [ ] IP blacklisting
    - [ ] Abuse detection

---

## 2. Data Processing Functions

### 2.1 Query Pre-Processing
- [ ] **Task:** Implement query pre-processing function
  - **Dependencies:** Authentication Function, Security Middleware
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Query normalization
    - [ ] Intent classification
    - [ ] Entity extraction
    - [ ] Query enrichment
    - [ ] Context injection

### 2.2 Database Writing/Amendment
- [ ] **Task:** Implement database writing function
  - **Dependencies:** Authentication Function
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Transaction management
    - [ ] RLS policy compliance
    - [ ] Audit logging
    - [ ] Schema validation
    - [ ] Conflict resolution

### 2.3 Database Retrieval
- [ ] **Task:** Implement database retrieval function
  - **Dependencies:** Authentication Function
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Query optimization
    - [ ] RLS policy enforcement
    - [ ] Result formatting
    - [ ] Pagination handling
    - [ ] Read-only transaction management

### 2.4 Document Generation
- [ ] **Task:** Implement document generation function
  - **Dependencies:** Database Retrieval
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Template retrieval
    - [ ] Content population
    - [ ] Format conversion
    - [ ] Signature handling
    - [ ] Output delivery

---

## 3. AI Integration Functions

### 3.1 AI Backend Integration
- [ ] **Task:** Implement AI backend integration function
  - **Dependencies:** Query Pre-Processing
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] LLM service connection
    - [ ] Prompt formatting
    - [ ] Response parsing
    - [ ] Error handling
    - [ ] Fallback mechanisms

### 3.2 Knowledge Base Query Function
- [ ] **Task:** Implement knowledge base query function
  - **Dependencies:** Database Retrieval
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] FTS query construction
    - [ ] Result ranking
    - [ ] Relevance scoring
    - [ ] Context assembly
    - [ ] Evidence collection

### 3.3 Response Formatting Function
- [ ] **Task:** Implement response formatting function
  - **Dependencies:** AI Backend Integration, Knowledge Base Query Function
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Card format construction
    - [ ] Prose format construction
    - [ ] Citation linking
    - [ ] Action button generation
    - [ ] Follow-up suggestion creation

---

## 4. User Interface API Functions

### 4.1 Dashboard Data API
- [ ] **Task:** Implement dashboard data API
  - **Dependencies:** Database Retrieval
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] Metrics aggregation
    - [ ] Visualization data formatting
    - [ ] Timeline generation
    - [ ] Activity feed assembly
    - [ ] Status summary creation

### 4.2 ListView Data API
- [ ] **Task:** Implement listview data API
  - **Dependencies:** Database Retrieval
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] Dynamic query construction
    - [ ] Filter application
    - [ ] Sort implementation
    - [ ] Pagination handling
    - [ ] Related data retrieval

### 4.3 Record Editor API
- [ ] **Task:** Implement record editor API
  - **Dependencies:** Database Writing/Amendment, Database Retrieval
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] Record retrieval
    - [ ] Validation implementation
    - [ ] Save operation
    - [ ] Audit trail creation
    - [ ] Relationship management

### 4.4 File Management API
- [ ] **Task:** Implement file management API
  - **Dependencies:** Database Writing/Amendment, Database Retrieval
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] File upload handling
    - [ ] Storage integration
    - [ ] Metadata management
    - [ ] Versioning implementation
    - [ ] Permission enforcement

---

## 5. Workflow Engine Functions

### 5.1 Workflow Execution Function
- [ ] **Task:** Implement workflow execution function
  - **Dependencies:** Database Writing/Amendment, Database Retrieval
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/EventWorkflow Design/Workflows/CustomerWorkflows/screens-list.md
  - **Components:**
    - [ ] Workflow template retrieval
    - [ ] State management
    - [ ] Transition validation
    - [ ] Action triggering
    - [ ] Notification dispatch

### 5.2 Approval Flow Function
- [ ] **Task:** Implement approval flow function
  - **Dependencies:** Workflow Execution Function
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/EventWorkflow Design/Workflows/CustomerWorkflows/screens-list.md
  - **Components:**
    - [ ] Approver determination
    - [ ] Approval request creation
    - [ ] Response processing
    - [ ] Escalation handling
    - [ ] Audit recording

### 5.3 Task Management Function
- [ ] **Task:** Implement task management function
  - **Dependencies:** Workflow Execution Function
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/EventWorkflow Design/Workflows/CustomerWorkflows/integrated-planning-workflow.md
  - **Components:**
    - [ ] Task creation
    - [ ] Assignment management
    - [ ] Due date calculation
    - [ ] Dependency tracking
    - [ ] Completion processing

---

## 6. Integration Functions

### 6.1 External API Gateway
- [ ] **Task:** Implement external API gateway
  - **Dependencies:** Authentication Function, Security Middleware
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] API key validation
    - [ ] Request transformation
    - [ ] Response formatting
    - [ ] Rate limiting
    - [ ] Usage tracking

### 6.2 Webhook Delivery Function
- [ ] **Task:** Implement webhook delivery function
  - **Dependencies:** Database Writing/Amendment
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] Event filtering
    - [ ] Payload construction
    - [ ] Delivery attempts
    - [ ] Retry management
    - [ ] Delivery confirmation

### 6.3 External System Integration
- [ ] **Task:** Implement external system integration functions
  - **Dependencies:** Authentication Function
  - **Verification Document:** arioncomply-v1/docs/functional-definitions/integration/README.md
  - **Components:**
    - [ ] OAuth flow handling
    - [ ] API connection management
    - [ ] Data transformation
    - [ ] Synchronization
    - [ ] Error reconciliation

---

## 7. Scheduled and Background Functions

### 7.1 Notification Dispatcher
- [ ] **Task:** Implement notification dispatcher
  - **Dependencies:** Database Retrieval
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Notification queue processing
    - [ ] Channel determination
    - [ ] Template application
    - [ ] Delivery management
    - [ ] Status tracking

### 7.2 Retention Policy Enforcer
- [ ] **Task:** Implement retention policy enforcer
  - **Dependencies:** Database Writing/Amendment
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Policy evaluation
    - [ ] Data identification
    - [ ] Archive process
    - [ ] Deletion execution
    - [ ] Audit recording

### 7.3 Scheduled Report Generator
- [ ] **Task:** Implement scheduled report generator
  - **Dependencies:** Document Generation
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Schedule evaluation
    - [ ] Report generation
    - [ ] Distribution handling
    - [ ] Status notification
    - [ ] Error recovery
