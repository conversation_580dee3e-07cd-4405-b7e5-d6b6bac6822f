# API Design Principles

## Overview
This document establishes architectural principles for ArionComply's metadata-driven API design, addressing endpoint structure, request/response patterns, and integration strategies. **This document builds on and MUST comply with the Database Schema Design Principles v3.**

## Compliance with Database Schema Design Principles

This document leverages the following systems established in the foundation document:
- ✅ **FieldMapper utility** - All APIs use the established field mapping system
- ✅ **Type definitions system** - All validation uses the established type_definitions table
- ✅ **Permission system** - All authorization uses the established `action:resource` format
- ✅ **Multi-tenant isolation** - All APIs respect the established RLS and organization context
- ✅ **Field mapping table** - All APIs use the established field_mappings for snake_case ↔ camelCase conversion

## API Architectural Principles (Mandatory)

### Metadata-Driven Architecture Requirements
- **MUST** use a single generic API handler for all entities
- **MUST** derive API behavior from database metadata rather than hardcoded logic
- **MUST** leverage existing FieldMapper, type system, and permission system from Database Schema
- **MUST** support dynamic API endpoint generation based on table definitions
- **MUST** integrate with established validation and authorization systems

### Response Consistency Requirements  
- **MUST** return camelCase responses using established FieldMapper system
- **MUST** use consistent response envelope across all endpoints
- **MUST** integrate error responses with established constraint validation
- **MUST** maintain request traceability for debugging

### Integration Requirements
- **MUST** build on Database Schema foundations without duplication
- **MUST** support both generic CRUD and entity-specific business operations
- **MUST** maintain compatibility with established type and permission systems

---

## 1. Metadata-Driven API Architecture

### Core Architectural Pattern
**PRINCIPLE:** All API behavior is driven by database metadata, not hardcoded implementations.

The API architecture leverages a **single generic handler** that:
- Reads entity configuration from database metadata
- Uses established FieldMapper for field conversions (from Database Schema)  
- Applies established type validation (from Database Schema)
- Enforces established permission patterns (from Database Schema)
- Respects established tenant isolation (from Database Schema)

### Metadata Registry Integration  
**PRINCIPLE:** Extend the established database schema with API behavior metadata.

```sql
-- Extends Database Schema foundations with API-specific metadata
ALTER TABLE metadata_registry ADD COLUMN api_config JSONB DEFAULT '{}';
ALTER TABLE metadata_registry ADD COLUMN business_rules JSONB DEFAULT '{}';

-- Example API configuration
UPDATE metadata_registry SET api_config = '{
  "enabled": true,
  "allowed_operations": ["GET", "POST", "PUT", "DELETE"],
  "custom_actions": ["cancel", "upgrade"],
  "pagination": {"default_limit": 50, "max_limit": 1000},
  "filtering": {"allowed_fields": ["status", "created_at"]},
  "sorting": {"default_field": "created_at", "default_order": "desc"}
}' WHERE table_name = 'org_subscriptions';
```

### Generic Handler Pattern
**PRINCIPLE:** One handler serves all entities through metadata configuration.

The architecture uses a **single MetadataApiHandler** that:
1. **Loads entity metadata** from the registry
2. **Validates operations** against metadata configuration  
3. **Applies established systems** (FieldMapper, type validation, permissions)
4. **Executes generic CRUD** or routes to metadata-defined business logic
5. **Returns consistent responses** using established patterns

## 2. Endpoint Structure Principles  

### RESTful Resource Patterns
**PRINCIPLE:** API endpoints follow consistent patterns derived from database table names.

```
Pattern: /api/v1/{table_name}[/{id}][/{action}]

Examples:
- GET  /api/v1/org_subscriptions           → List (using established FieldMapper)
- POST /api/v1/org_subscriptions           → Create (using established validation)  
- GET  /api/v1/org_subscriptions/{id}      → Read (using established permissions)
- PUT  /api/v1/org_subscriptions/{id}      → Update (using established RLS)
- POST /api/v1/org_subscriptions/{id}/cancel → Business action (metadata-defined)
```

### Metadata-Driven Endpoint Generation
**PRINCIPLE:** Available endpoints are determined by metadata configuration, not code.

- **Table Registration:** Only tables registered in metadata_registry get API endpoints
- **Operation Control:** Allowed HTTP methods defined in api_config metadata
- **Custom Actions:** Business operations defined in business_rules metadata  
- **Field Access:** Available fields controlled by established permission system

### Dynamic Routing Strategy
**PRINCIPLE:** URL routing is generated from database schema metadata.

The routing system:
- **Discovers entities** from metadata_registry table
- **Validates operations** against api_config for each entity
- **Applies permissions** using established `action:resource` format
- **Routes requests** to generic handler with metadata context

## 3. Request/Response Integration Principles

### Field Conversion Integration
**PRINCIPLE:** All APIs use the established FieldMapper system for consistent field naming.

- **Request Processing:** camelCase → snake_case using established field_mappings table
- **Response Processing:** snake_case → camelCase using established FieldMapper utility
- **Consistency:** All endpoints return camelCase responses per Database Schema requirements
- **Transparency:** Field conversion is handled by established utility, invisible to business logic

### Validation System Integration  
**PRINCIPLE:** All input validation uses the established type_definitions system.

- **Type Validation:** Uses established `validate_field_type()` function
- **Schema Validation:** Leverages established JSON schema definitions
- **Error Mapping:** Converts database constraint errors to user-friendly messages
- **Consistency:** Same validation rules across database, API, and UI layers

### Permission System Integration
**PRINCIPLE:** All authorization uses the established permission_definitions system.

- **Permission Format:** Uses established `action:resource` format (e.g., `read:org_subscriptions`)
- **Permission Checks:** Leverages established permission checking functions
- **Organization Context:** Uses established organization_id context for tenant isolation
- **Role Integration:** Works with established user roles and permission assignments

## 4. Business Logic Integration Patterns

### Generic CRUD Operations
**PRINCIPLE:** Standard operations use metadata-driven generic implementation.

- **List/Read/Create/Update/Delete** handled generically using metadata configuration
- **Field mapping** applied automatically using established FieldMapper
- **Validation** applied automatically using established type system
- **Permissions** enforced automatically using established permission system
- **Tenant isolation** enforced automatically using established RLS policies

### Custom Business Actions
**PRINCIPLE:** Entity-specific business logic integrates with metadata framework.

Business actions (like subscription cancellation, plan upgrades) are:
- **Defined in metadata:** Custom actions specified in business_rules configuration
- **Permission controlled:** Use established permission system with action-specific permissions
- **Validation integrated:** Use established type system for action parameters
- **Response consistent:** Return responses using established FieldMapper for consistency

### Workflow Integration Points
**PRINCIPLE:** API actions can trigger metadata-defined workflows and events.

- **Event Triggers:** API operations can trigger events based on metadata configuration
- **Workflow Hooks:** Business actions can invoke workflows defined in metadata
- **Audit Integration:** All operations use established audit fields and tracking
- **State Management:** Status changes respect metadata-defined valid transitions

## 5. Error Handling Integration Principles

### Database Error Integration
**PRINCIPLE:** Database constraint errors are mapped to consistent API error responses.

- **Constraint Mapping:** Database constraint names map to API error codes
- **Validation Errors:** Type validation failures become structured API errors
- **Permission Errors:** Authorization failures use consistent error format
- **Tenant Errors:** RLS policy violations become appropriate API errors

### Response Format Consistency
**PRINCIPLE:** All error responses use established patterns and camelCase field names.

Error responses:
- **Use camelCase** fields in error details (consistent with established FieldMapper)
- **Include field mappings** to show both database and API field names in errors
- **Reference type definitions** to provide validation guidance to clients
- **Maintain traceability** with request IDs for debugging

## 6. Performance and Caching Principles

### Metadata Caching Strategy  
**PRINCIPLE:** Metadata is cached to minimize database queries for API configuration.

- **Entity Metadata:** Cache metadata_registry entries for API configuration
- **Field Mappings:** Cache established field_mappings for conversion performance
- **Type Definitions:** Cache established type_definitions for validation performance
- **Permission Rules:** Cache permission definitions for authorization performance

### Query Optimization Integration
**PRINCIPLE:** Generic queries are optimized using database schema information.

- **Index Utilization:** Use database indexes established in Database Schema document
- **Field Selection:** Only query fields specified in metadata configuration
- **Relationship Loading:** Use metadata to determine optimal join strategies  
- **Pagination:** Apply metadata-configured pagination limits and defaults

## Implementation Strategy

### Phase 1: Metadata Extension
- Extend existing metadata_registry with api_config and business_rules columns
- Define API configuration for core entities (users, org_subscriptions, etc.)
- Establish business action definitions in metadata

### Phase 2: Generic Handler Implementation  
- Implement single MetadataApiHandler using established Database Schema systems
- Integrate with existing FieldMapper, type validation, and permission systems
- Create dynamic routing based on metadata_registry entries

### Phase 3: Business Logic Integration
- Implement metadata-driven custom actions
- Integrate with workflow and event systems
- Add performance optimizations and caching

### Phase 4: Testing and Documentation
- Validate integration with established Database Schema systems  
- Test field mapping consistency across all endpoints
- Generate API documentation from metadata definitions

## Cross-Document Integration

**This document establishes principles that will be used by:**
- **Error Handling Design Principles** - Will use these response patterns and error integration
- **Event and Workflow Design Principles** - Will use these API trigger points and business actions
- **UI Design Principles** - Will consume these camelCase APIs and integrate with metadata
- **Tenant Isolation Design Principles** - Will extend these API-level isolation patterns  
- **Database Function Integration** - Will integrate these API patterns with database functions

**This document builds on and leverages:**
- **Database Schema Design Principles v3** - Uses FieldMapper, type system, permissions, and RLS
- **Design Improvements document** - Addresses Point 2 (API Endpoint Structure) with metadata-driven approach
