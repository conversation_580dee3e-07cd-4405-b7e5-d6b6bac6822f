<!--
  File: arioncomply-v1/testing/workflow-gui/index.html
  File Description: Interactive test harness UI for ArionComply Edge-to-Backend workflow testing with trace visualization
  Purpose: Exercise request logging, trace IDs, audit filtering, and AI workflow flows with Cytoscape.js graph visualization
  Inputs: User interactions (buttons, forms), API responses from Edge Functions and Backend services
  Outputs: Real-time audit logs, interactive trace graphs, request/response visualization, workflow debugging interface
  Dependencies: Cytoscape.js, dagre layout, modern browser with fetch API, ArionComply Edge Functions and Backend APIs
  Security/RLS: Client-side testing tool, no sensitive data persistence, org-scoped API calls through Edge Functions
  Notes: Enhanced with requestId/sessionId filtering, interactive trace graph, real-time audit log streaming, and workflow debugging
-->
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>ArionComply Workflow GUI</title>
    <!-- Cytoscape.js for trace graph visualization -->
    <script src="https://unpkg.com/cytoscape@3.26.0/dist/cytoscape.min.js"></script>
    <script src="https://unpkg.com/cytoscape-dagre@2.5.0/cytoscape-dagre.js"></script>
    <script src="https://unpkg.com/dagre@0.8.5/dist/dagre.min.js"></script>
    <style>
      *, *::before, *::after { box-sizing: border-box; }
      :root {
        --bg: #f7f8fa;
        --text: #0b1220;
        --muted: #606a7a;
        --card-bg: #ffffff;
        --card-border: #e6eaf0;
        --input-border: #d8dee9;
        --input-bg: #ffffff;
        --primary: #1a73e8;
        --primary-contrast: #ffffff;
        --log-bg: #f5f7fb;
        --log-text: #0b1220;
        --log-border: #e6eaf0;
        --shadow: 0 1px 2px rgba(0,0,0,.06), 0 8px 24px rgba(0,0,0,.06);
        --radius: 10px;
        --container: 1180px;
        --space: 12px;
      }
      body { font-family: system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif; margin: 24px; background: var(--bg); color: var(--text); }
      body > * { max-width: var(--container); margin-left: auto; margin-right: auto; }
      h1 { margin: 0 0 12px; letter-spacing: -0.2px; }
      .row { display: flex; gap: 16px; align-items: flex-start; }
      .col { flex: 1; min-width: 320px; }
      .card { border: 1px solid var(--card-border); border-radius: var(--radius); padding: 16px; background: var(--card-bg); box-shadow: var(--shadow); }
      label { display: block; font-weight: 600; margin: 8px 0 4px; }
      input, textarea, select { width: 100%; padding: 10px; border: 1px solid var(--input-border); border-radius: 8px; background: var(--input-bg); color: inherit; margin-bottom: 10px; }
      input::placeholder, textarea::placeholder { color: #8a94a6; }
      textarea { min-height: 88px; resize: none; }
      input:focus, textarea:focus, select:focus { outline: none; border-color: var(--primary); box-shadow: 0 0 0 3px rgba(26,115,232,0.15); }
      button { padding: 8px 12px; border: 1px solid #444; background: #fff; border-radius: 8px; cursor: pointer; transition: transform .02s ease, background .15s ease, border-color .15s ease; }
      button:hover { transform: translateY(-1px); }
      button.primary { background: var(--primary); color: var(--primary-contrast); border-color: var(--primary); }
      .flex { display: flex; gap: 8px; align-items: center; }
      .muted { color: var(--muted); font-size: 12px; }
      .mono { font-family: ui-monospace, SFMono-Regular, Menlo, monospace; }
      .log { white-space: pre-wrap; background: var(--log-bg); color: var(--log-text); padding: 10px; border-radius: 8px; min-height: 44px; border: 1px solid var(--log-border); }
      .pill { font-size: 12px; background: #eef3ff; color: #113; padding: 2px 8px; border-radius: 999px; display: inline-block; }
      .split { display: grid; grid-template-columns: 1fr 1fr; gap: 12px; }
      .right { margin-left: auto; }
      .card > * + * { margin-top: 12px; }
      .row + .row { margin-top: 12px; }
      .badge { font-size: 12px; padding: 2px 8px; border-radius: 999px; display: inline-block; line-height: 18px; border: 1px solid transparent; }
      .badge-A { background: #e9f8f0; color: #0d6d3d; border-color: #b9e7cf; }
      .badge-B { background: #efe9fb; color: #5a2ea6; border-color: #dac5ff; }
      /* Tabs */
      .tabs { display: flex; gap: 8px; margin: 10px auto 14px; border-bottom: 1px solid var(--card-border); max-width: var(--container); }
      .tab { padding: 8px 12px; border: 1px solid var(--card-border); border-bottom: none; border-top-left-radius: 8px; border-top-right-radius: 8px; background: var(--card-bg); cursor: pointer; color: var(--muted); }
      .tab.active { color: var(--text); font-weight: 600; box-shadow: var(--shadow); }
      .panel { display: none; }
      .panel.active { display: block; }
      /* Dark mode (opt-in via data-theme="dark"): redefine variables */
      body[data-theme="dark"] {
        --bg: #0b0f14;
        --text: #e6e6e6;
        --muted: #9aa9bf;
        --card-bg: #0f141b;
        --card-border: #273142;
        --input-border: #2b3443;
        --input-bg: #0f141b;
        --log-bg: #0a0f14;
        --log-text: #dcdfe4;
        --log-border: #1b2633;
        --shadow: 0 0 0 rgba(0,0,0,0);
      }
      /* NEW: Styling for backend features testing */
      .cards-container { border: 1px solid var(--card-border); border-radius: var(--radius); padding: 8px; background: var(--bg); }
      .cards-hint { margin-bottom: 8px; font-size: 0.9em; }
      .hint-chip { background: var(--accent); color: white; padding: 2px 6px; border-radius: 3px; margin-right: 4px; font-size: 0.8em; }
      .evidence-card { border: 1px solid var(--log-border); border-radius: 4px; padding: 8px; margin-bottom: 8px; background: var(--card-bg); }
      .evidence-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px; }
      .confidence { font-size: 0.8em; color: var(--muted); }
      .evidence-content { font-size: 0.9em; }
      .graph-paths { font-size: 0.8em; color: var(--muted); margin-top: 4px; }
      .prose-container { border: 1px solid var(--card-border); border-radius: var(--radius); padding: 8px; background: var(--bg); }
      .prose-content { margin-bottom: 8px; }
      .typing-indicator { font-size: 0.8em; color: var(--success); }
      .preprocessing-container { border: 1px solid var(--card-border); border-radius: var(--radius); padding: 8px; background: var(--bg); }
      .deterministic-match { background: var(--success); color: white; padding: 8px; border-radius: 4px; margin-bottom: 8px; }
      .deterministic-match strong { display: block; margin-bottom: 4px; }
      .deterministic-match div { font-size: 0.9em; margin-bottom: 2px; }
      .pipeline-stages { margin-bottom: 8px; }
      .pipeline-stages strong { display: block; margin-bottom: 4px; }
      .query-enhanced { color: var(--success); font-size: 0.9em; }
      .evidence-item { padding: 4px 8px; margin: 2px 0; background: var(--card-bg); border-radius: 3px; font-size: 0.9em; }

      /* Trace graph styling */
      .trace-node-intent { background-color: #4CAF50; }
      .trace-node-routing { background-color: #2196F3; }
      .trace-node-retrieval { background-color: #FF9800; }
      .trace-node-ai_call { background-color: #9C27B0; }
      .trace-node-response { background-color: #607D8B; }
      .trace-edge-success { line-color: #4CAF50; }
      .trace-edge-error { line-color: #F44336; }

      @media (max-width: 900px) {
        .row { flex-direction: column; }
        .col { min-width: 100%; }
        .split { grid-template-columns: 1fr; }
      }
    </style>
  </head>
  <body>
    <div class="topbar" style="display:flex; align-items:center; gap:12px; justify-content: space-between; margin-bottom: 12px;">
      <div class="brand" style="display:flex; align-items:center; gap:10px;">
        <div style="width:10px; height:10px; border-radius:50%; background: var(--primary);"></div>
        <div>
          <div style="font-weight:700; font-size:20px; letter-spacing:-.2px;">ArionComply Workflow GUI</div>
          <div class="muted">Parallel test harness for Edge functions, backend routing, and provider comparison.</div>
        </div>
      </div>
      <div class="top-actions">
        <button class="btn" onclick="toggleTheme()">Toggle Theme</button>
      </div>
    </div>

    <div class="tabs">
      <div class="tab active" data-panel="panel-config" onclick="switchTab(event)">Config</div>
      <div class="tab" data-panel="panel-conv" onclick="switchTab(event)">Conversation</div>
      <div class="tab" data-panel="panel-compare" onclick="switchTab(event)">Compare</div>
      <div class="tab" data-panel="panel-arena" onclick="switchTab(event)">Arena</div>
      <div class="tab" data-panel="panel-audit" onclick="switchTab(event)">Audit</div>
      <div class="tab" data-panel="panel-output-modes" onclick="switchTab(event)">Backend Features</div>
      <div class="tab" data-panel="panel-preprocessing" onclick="switchTab(event)">Debug Pipeline</div>
    </div>

    <div id="panel-config" class="card panel active">
      <div class="row">
        <div class="col">
          <label for="supabaseUrl">Supabase URL</label>
          <input id="supabaseUrl" placeholder="http://localhost:54321" />
        </div>
        <div class="col">
          <label for="anonKey">Anon Key (optional)</label>
          <input id="anonKey" placeholder="Paste anon key if required" />
        </div>
      </div>
      <div class="row" style="margin-top: 8px;">
        <div class="col">
          <label for="providerOrder">x-provider-order (optional)</label>
          <input id="providerOrder" placeholder="sllm,openai,anthropic" />
        </div>
        <div class="col">
          <label for="allowGllm">x-allow-gllm (optional)</label>
          <select id="allowGllm">
            <option value="">(not set)</option>
            <option value="true">true</option>
            <option value="false">false</option>
          </select>
        </div>
      </div>
      <div class="row" style="margin-top: 8px;">
        <div class="col">
          <label for="chainId">compliance-chain-id (optional)</label>
          <input id="chainId" placeholder="chain-123" />
        </div>
        <div class="col">
          <label for="decisionContext">decision-context (optional)</label>
          <input id="decisionContext" placeholder="workflow:assessment" />
        </div>
      </div>
      <div class="flex" style="margin-top: 10px;">
        <button class="primary" onclick="saveConfig()">Save Config</button>
        <span class="muted">Stored in localStorage</span>
      </div>
    </div>

    <div id="panel-conv" class="row panel" style="margin-top: 16px;">
      <div class="col card">
        <h3>Conversation</h3>
        <div class="muted">Uses ai-conversation-start / ai-conversation-send</div>
        <label for="title">Title (optional)</label>
        <input id="title" placeholder="New Conversation" />
        <label for="context">Context JSON (optional)</label>
        <textarea id="context" placeholder='{"project":"demo"}'></textarea>
        <div class="flex" style="margin-top: 8px;">
          <button class="primary" onclick="startConversation()">Start Session</button>
          <span>Session: <span id="sessionId" class="pill mono">(none)</span></span>
        </div>
        <hr />
        <label for="message">Message</label>
        <textarea id="message" placeholder="Type your message"></textarea>
        <div class="flex" style="margin-top: 8px;">
          <button onclick="sendMessage()">Send (edge)</button>
          <button onclick="streamMessage()">Stream (SSE)</button>
        </div>
        <div style="margin-top: 8px;" class="muted">
          Trace: requestId=<span id="reqId" class="mono"></span> • traceparent=<span id="traceparent" class="mono"></span>
        </div>
        <div class="split" style="margin-top: 12px;">
          <div>
            <div class="muted">Last User Message</div>
            <div id="userOut" class="log" style="max-width: 100%;"></div>
          </div>
          <div>
            <div class="muted">Assistant Reply</div>
            <div id="assistantOut" class="log" style="max-width: 100%;"></div>
            <div id="suggestionChips" style="margin-top:8px;"></div>
            <div id="feedbackBar" class="muted" style="margin-top:6px;">
              <button id="thumbUpBtn" title="Helpful">👍</button>
              <button id="thumbDownBtn" title="Not helpful">👎</button>
            </div>
          </div>
        </div>
        <div style="margin-top: 12px;">
          <div class="muted">Suggestions</div>
          <div id="suggestions" class="log"></div>
        </div>
      </div>
    </div>

    <!-- Feedback modal -->
    <div id="feedbackModal" style="display:none; position:fixed; inset:0; background:rgba(0,0,0,.35); align-items:center; justify-content:center;">
      <div style="background:#fff; padding:12px; border-radius:8px; width: min(480px, 90vw);">
        <div style="font-weight:600; margin-bottom:6px;">Share more details (optional)</div>
        <textarea id="feedbackComment" placeholder="What worked well or what was missing?" style="width:100%; min-height:80px;"></textarea>
        <div style="display:flex; gap:8px; justify-content:flex-end; margin-top:8px;">
          <button id="feedbackCancel">Cancel</button>
          <button id="feedbackSubmit" class="primary">Submit</button>
        </div>
      </div>
    </div>

    <div id="panel-compare" class="row panel" style="margin-top: 16px;">
      <div class="col card">
        <h3>Compare Providers</h3>
        <div class="muted">Calls existing compliance-proxy (OpenAI/Anthropic)</div>
        <label for="systemPrompt">System Prompt (optional)</label>
        <textarea id="systemPrompt" placeholder="You are a helpful assistant."></textarea>
        <label for="compareQuestion">Question</label>
        <textarea id="compareQuestion" placeholder="Ask a question to both providers"></textarea>
        <div class="row" style="align-items: end;">
          <div class="col">
            <label for="openaiTemp">OpenAI Temperature</label>
            <input id="openaiTemp" type="number" min="0" max="1" step="0.1" value="0.7" />
          </div>
          <div class="col">
            <label for="claudeTemp">Claude Temperature</label>
            <input id="claudeTemp" type="number" min="0" max="1" step="0.1" value="0.7" />
          </div>
        </div>
        <div class="row" style="margin-top: 8px; align-items: end;">
          <div class="col">
            <label for="maxTokens">Max Tokens</label>
            <input id="maxTokens" type="number" min="100" max="4000" value="1024" />
          </div>
          <div class="col" style="display:flex; align-items:flex-end; gap:8px;">
            <button onclick="compare('openai')">Ask OpenAI</button>
            <button onclick="compare('claude')">Ask Claude</button>
            <button class="primary" onclick="compare('both')">Ask Both</button>
          </div>
        </div>
        <div class="split" style="margin-top: 12px;">
          <div>
            <div class="muted">OpenAI</div>
            <div id="gptOut" class="log" style="max-width: 100%;"></div>
          </div>
          <div>
            <div class="muted">Claude</div>
            <div id="claudeOut" class="log" style="max-width: 100%;"></div>
          </div>
        </div>
      </div>
    </div>

    <div id="panel-audit" class="card panel" style="margin-top: 16px;">
      <h3>Audit (Org‑Scoped Logs)</h3>
      <div class="row">
        <div class="col">
          <label for="auditFrom">From</label>
          <input id="auditFrom" type="datetime-local" />
        </div>
        <div class="col">
          <label for="auditTo">To</label>
          <input id="auditTo" type="datetime-local" />
        </div>
      </div>
      <div class="row">
        <div class="col">
          <label for="auditRoute">Route</label>
          <input id="auditRoute" placeholder="conversation.send" />
        </div>
        <div class="col">
          <label for="auditStatus">Status</label>
          <input id="auditStatus" type="number" min="100" max="599" />
        </div>
        <div class="col">
          <label for="auditEventType">Event Type</label>
          <input id="auditEventType" placeholder="ai_call" />
        </div>
      </div>
      <div class="row">
        <div class="col">
          <label for="auditRequestId">Request ID</label>
          <input id="auditRequestId" placeholder="Filter by request ID" />
        </div>
        <div class="col">
          <label for="auditSessionId">Session ID</label>
          <input id="auditSessionId" placeholder="Filter by session ID" />
        </div>
        <div class="col">
          <!-- Empty column for alignment -->
        </div>
      </div>
      <div class="row">
        <div class="col">
          <label for="auditPage">Page</label>
          <input id="auditPage" type="number" value="1" min="1" />
        </div>
        <div class="col">
          <label for="auditPageSize">Page Size</label>
          <input id="auditPageSize" type="number" value="50" min="1" max="200" />
        </div>
        <div class="col" style="display:flex; align-items:flex-end; gap:8px;">
          <button class="primary" onclick="runAuditQuery()">Search</button>
          <button onclick="auditPrev()">Prev</button>
          <button onclick="auditNext()">Next</button>
          <span id="auditPageInfo" class="muted"></span>
        </div>
      </div>
      <div style="margin-top: 8px;">
        <table style="width:100%; border-collapse: collapse;">
          <thead>
            <tr style="text-align:left; border-bottom:1px solid var(--card-border);">
              <th>Time</th><th>Route</th><th>Status</th><th>Event</th><th>Dir</th><th>RequestId</th><th>TraceId</th><th>SessionId</th>
            </tr>
          </thead>
          <tbody id="auditRows"></tbody>
        </table>
      </div>

      <!-- Trace Graph Section -->
      <div style="margin-top: 24px; border-top: 1px solid var(--card-border); padding-top: 16px;">
        <h4>Trace Graph</h4>
        <div class="muted">Interactive timeline visualization of request flow: intent → routing → retrieval → AI call → response</div>

        <div class="row" style="margin-top: 8px;">
          <div class="col">
            <label for="traceRequestId">Request ID for Tracing</label>
            <input id="traceRequestId" placeholder="Enter request ID to visualize trace flow" />
          </div>
          <div class="col">
            <label for="traceSessionId">Or Session ID</label>
            <input id="traceSessionId" placeholder="Enter session ID to see all requests" />
          </div>
          <div class="col" style="display:flex; align-items:flex-end; gap:8px;">
            <button class="primary" onclick="buildTraceGraph()">Build Graph</button>
            <button onclick="clearTraceGraph()">Clear</button>
          </div>
        </div>

        <div style="margin-top: 12px;">
          <div id="traceGraph" style="width: 100%; height: 400px; border: 1px solid var(--card-border); border-radius: 8px; background: var(--card-bg);"></div>
        </div>

        <!-- Trace Details Panel -->
        <div style="margin-top: 12px;">
          <div class="muted">Trace Details</div>
          <div id="traceDetails" class="log" style="min-height: 60px;">Click on nodes or edges in the graph to see detailed event information</div>
        </div>
      </div>
    </div>

    <div id="panel-arena" class="card panel" style="margin-top: 16px;">
      <h3>Arena Compare (blind A/B)</h3>
      <div class="muted">Inspired by LLM Arena: run blind A/B, vote, and reveal providers.</div>
      <label for="arenaSystemPrompt">System Prompt (optional)</label>
      <textarea id="arenaSystemPrompt" placeholder="You are a helpful assistant."></textarea>
      <label for="arenaQuestion">Question</label>
      <textarea id="arenaQuestion" placeholder="Ask a question to compare models"></textarea>
      <div class="row">
        <div class="col">
          <label for="arenaOpenaiTemp">OpenAI Temperature</label>
          <input id="arenaOpenaiTemp" type="number" min="0" max="1" step="0.1" value="0.7" />
        </div>
        <div class="col">
          <label for="arenaClaudeTemp">Claude Temperature</label>
          <input id="arenaClaudeTemp" type="number" min="0" max="1" step="0.1" value="0.7" />
        </div>
      </div>
      <div class="row" style="margin-top: 8px; align-items: end;">
        <div class="col">
          <label for="arenaMaxTokens">Max Tokens</label>
          <input id="arenaMaxTokens" type="number" min="100" max="4000" value="1024" />
        </div>
        <div class="col" style="display:flex; align-items:flex-end; gap:8px;">
          <button class="primary" onclick="runArena()">Run Arena</button>
          <button onclick="arenaReveal()">Reveal Models</button>
        </div>
      </div>
      <div class="split" style="margin-top: 12px;">
        <div>
          <div class="muted">Model <span class="badge badge-A">A</span></div>
          <div id="arenaAOut" class="log" style="max-width: 100%;"></div>
          <div class="flex" style="margin-top: 6px;">
            <button onclick="arenaVote('A')">Vote A</button>
            <button onclick="copyText('arenaAOut')">Copy A</button>
          </div>
        </div>
        <div>
          <div class="muted">Model <span class="badge badge-B">B</span></div>
          <div id="arenaBOut" class="log" style="max-width: 100%;"></div>
          <div class="flex" style="margin-top: 6px;">
            <button onclick="arenaVote('B')">Vote B</button>
            <button onclick="copyText('arenaBOut')">Copy B</button>
          </div>
        </div>
      </div>
      <div class="flex" style="margin-top: 8px;">
        <button onclick="arenaVote('tie')">Tie / Skip</button>
        <span class="muted">Mapping: <span id="arenaMapping" class="mono">(hidden)</span></span>
        <div class="right muted">Stats: <span id="arenaStats" class="mono">A=0 • B=0 • tie=0 • OpenAI=0 • Claude=0</span></div>
      </div>
    </div>

    <!-- NEW: Output Mode Testing Panel -->
    <div id="panel-output-modes" class="card panel" style="margin-top: 16px;">
      <h3>Backend Features Testing</h3>
      <div class="muted">Test Cards/Prose output modes and preprocessing pipeline visibility</div>
      
      <div class="row">
        <div class="col">
          <label for="outputMode">Output Mode</label>
          <select id="outputMode">
            <option value="both">Both (Cards + Prose)</option>
            <option value="cards">Cards Only</option>
            <option value="prose">Prose Only</option>
          </select>
        </div>
        <div class="col">
          <label for="testPipelineMode">Pipeline Mode</label>
          <select id="testPipelineMode">
            <option value="retrieval">Full Pipeline</option>
            <option value="preprocessing">Preprocessing Only</option>
          </select>
        </div>
        <div class="col">
          <label for="testFrameworkHint">Framework</label>
          <select id="testFrameworkHint">
            <option value="">Auto-detect</option>
            <option value="ISO27001">ISO 27001</option>
            <option value="ISO27701">ISO 27701</option>
            <option value="GDPR">GDPR</option>
          </select>
        </div>
      </div>
      
      <label for="outputTestQuestion">Test Question</label>
      <textarea id="outputTestQuestion" placeholder="What is ISO 27001?">What is ISO 27001?</textarea>
      
      <div class="row" style="margin-top: 8px;">
        <button class="primary" onclick="testOutputModes()">Test Backend Features</button>
        <button onclick="clearOutputResults()">Clear Results</button>
      </div>
      
      <!-- Results Display -->
      <div class="split" style="margin-top: 12px;">
        <div>
          <div class="muted">Cards Format</div>
          <div id="cardsOut" class="log" style="max-width: 100%; min-height: 100px;"></div>
        </div>
        <div>
          <div class="muted">Prose Format</div>
          <div id="proseOut" class="log" style="max-width: 100%; min-height: 100px;"></div>
        </div>
      </div>
      
      <!-- Preprocessing Pipeline Visibility -->
      <div style="margin-top: 12px;">
        <div class="muted">Preprocessing Pipeline Results</div>
        <div id="preprocessingOut" class="log"></div>
      </div>
    </div>

    <!-- NEW: Preprocessing Debug Panel -->
    <div id="panel-preprocessing" class="card panel" style="margin-top: 16px;">
      <h3>Preprocessing Pipeline Debug</h3>
      <div class="muted">Test individual preprocessing stages and inspect pipeline execution</div>
      
      <div class="row">
        <div class="col">
          <label for="debugQuery">Debug Query</label>
          <input id="debugQuery" type="text" placeholder="ISO27001:2022/A.5.1 or natural language" />
        </div>
        <div class="col">
          <label for="debugFramework">Framework Hint</label>
          <select id="debugFramework">
            <option value="">Auto-detect</option>
            <option value="ISO27001">ISO 27001</option>
            <option value="ISO27701">ISO 27701</option>
          </select>
        </div>
      </div>
      
      <div class="row" style="margin-top: 8px;">
        <button onclick="testCanonicalId()">Test Canonical ID</button>
        <button onclick="testFullPipeline()">Test Full Pipeline</button>
        <button onclick="clearDebugResults()">Clear Debug</button>
      </div>
      
      <div id="preprocessingDebugOut" class="log" style="margin-top: 12px;"></div>
    </div>

    <script src="./app.js"></script>
  </body>
  </html>
<!-- File: arioncomply-v1/testing/workflow-gui/index.html -->
