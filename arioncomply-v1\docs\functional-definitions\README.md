# Functional Definitions

This directory contains functional specifications for the ArionComply platform, detailing workflows, data schemas, and integration patterns.

## Structure

- **workflows/** - Business process definitions and workflow specifications
- **schemas/** - Data structure and framework definitions
- **integration/** - System integration and architecture patterns

## Cross-References

- [Technical Architecture](../Architecture.md)
- [Data Model](../DataModelingSupabase.md)
- [Functional Requirements](../FunctionalNonFunctionalReq.md)

## Status

All functional definitions are in **Draft** status unless marked otherwise.
