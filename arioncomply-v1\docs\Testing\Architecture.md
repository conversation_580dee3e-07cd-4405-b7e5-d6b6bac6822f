<!-- File: arioncomply-v1/docs/Testing/Architecture.md -->
# Testing Architecture & Design (Phase 1)

Overview
- Test client: `testing/workflow-gui` (tabs for Config, Conversation, Compare, Arena, Audit).
- Edge: Supabase functions (`ai-conversation-*`, `compliance-proxy`, `app-audit-read`).
- Backend: Python `/ai/chat` (router skeleton emits events; retrieval optional).
- DB: `api_request_logs`, `api_event_logs`, view `app_audit_logs_v1`.

Data Flow (Audit)
1) GUI → `app-audit-read` with filters (JWT provides org scope).
2) Edge → Supabase admin client → `app_audit_logs_v1` (filtered by `org_id`).
3) Response: items + pagination metadata; no raw content.

Observability
- requestId + traceparent visible in GUI; top-level columns `org_id`, `request_id`, `trace_id`, `session_id` for joins.

Environments
- Cloud-only OK (no local stack required). Ensure migrations 0011, 0012, 0014, 0015 applied.

