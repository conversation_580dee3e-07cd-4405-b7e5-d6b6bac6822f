"""
File: arioncomply-v1/ai-backend/python-backend/services/prompts/registry.py
File Description: Prompt registry and compiler (MVP)
Purpose: Provide versioned prompt templates and simple compiler hooks
Notes: Hard-coded minimal registry for MVP-Assessment; extend to file/DB later
"""

from typing import Dict, Optional


_REGISTRY: Dict[str, Dict] = {
    "qna": {
        "id": "qna",
        "version": "v0",
        "system": (
            "You are an ISO 27001 and GDPR compliance assistant.\n"
            "- Provide clear, concise guidance based on ISO/IEC 27001:2022 and GDPR.\n"
            "- Ask clarifying questions before answering.\n"
            "- Use bullet points and avoid speculation.\n"
            "Restrictions:\n- Do NOT provide legal advice.\n- Do NOT guess or hallucinate.\n"
        ),
    }
}


def get_template(template_id: str = "qna") -> Dict:
    """Return a copy of the prompt template by id (defaults to 'qna')."""
    return _REGISTRY.get(template_id, _REGISTRY["qna"]).copy()


def template_meta(template_id: str = "qna") -> Dict[str, str]:
    """Return minimal template metadata suitable for logging/auditing."""
    t = get_template(template_id)
    return {"id": t["id"], "version": t["version"]}


def compile_messages(*, template_id: str = "qna", retrieval_context: Optional[str] = None, user_text: str = "") -> list:
    """Build chat messages for the provider using the selected template.

    Includes system prompt, optional retrieval context, and user message.
    """
    t = get_template(template_id)
    messages = [{"role": "system", "content": t["system"]}]
    if retrieval_context:
        messages.append({"role": "system", "content": f"Context (retrieved):\n{retrieval_context}\nUse this when answering. Cite IDs in brackets."})
    messages.append({"role": "user", "content": user_text})
    return messages
