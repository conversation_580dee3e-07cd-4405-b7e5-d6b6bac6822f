#!/usr/bin/env bash
# File: arioncomply-v1/scripts/migrate_edge_to_supabase.sh
set -euo pipefail

SRC="arioncomply-v1/ai-backend/edge-functions"
DEST="arioncomply-v1/supabase/functions"
APPLY=0

if [[ "${1:-}" == "--apply" ]]; then
  APPLY=1
fi

say() { printf "%b\n" "• $*"; }
doit() { if [[ $APPLY -eq 1 ]]; then eval "$*"; else echo "[dry-run] $*"; fi; }

if [[ ! -d "$SRC/conversation/start" || ! -d "$SRC/conversation/send" || ! -d "$SRC/conversation/stream" ]]; then
  echo "Error: Missing conversation handlers under $SRC" >&2
  exit 1
fi

say "Preparing destination directories under $DEST ..."
doit "mkdir -p '$DEST/_shared' '$DEST/ai-conversation-start' '$DEST/ai-conversation-send' '$DEST/ai-conversation-stream'"

SHARED_FILES=(
  "errors.ts"
  "utils.ts"
  "logger.ts"
  "supabase.ts"
  "jwt.ts"
  "trace.ts"
  "schemas.ts"
  "assistant_router.ts"
  "config.ts"
)

say "Copying shared helpers to $DEST/_shared ..."
for f in "${SHARED_FILES[@]}"; do
  if [[ -f "$SRC/_shared/$f" ]]; then
    doit "cp -f '$SRC/_shared/$f' '$DEST/_shared/$f'"
  else
    echo "  [warn] missing: $SRC/_shared/$f"
  fi
done

say "Copying conversation handlers to supabase/functions ..."
doit "cp -f '$SRC/conversation/start/index.ts'  '$DEST/ai-conversation-start/index.ts'"
doit "cp -f '$SRC/conversation/send/index.ts'   '$DEST/ai-conversation-send/index.ts'"
doit "cp -f '$SRC/conversation/stream/index.ts' '$DEST/ai-conversation-stream/index.ts'"

say "Fixing relative imports in conversation handlers ..."
for fn in "$DEST/ai-conversation-start/index.ts" "$DEST/ai-conversation-send/index.ts" "$DEST/ai-conversation-stream/index.ts"; do
  if command -v gsed >/dev/null 2>&1; then
    doit "gsed -i 's#\../../_shared/#../_shared/#g' '$fn'"
  else
    doit "sed -i '' 's#\../../_shared/#../_shared/#g' '$fn'"
  fi
done

say "Writing deprecation README to $SRC/README.md ..."
doit "cat > '$SRC/README.md' << 'EOF'
# Edge Functions moved

Source-of-truth for Edge Functions now lives under:
- supabase/functions/ai-conversation-start
- supabase/functions/ai-conversation-send
- supabase/functions/ai-conversation-stream
- supabase/functions/_shared

This folder is deprecated and kept temporarily for reference.
EOF"

say "DONE. Mode: $([[ $APPLY -eq 1 ]] && echo 'APPLY' || echo 'DRY RUN')"
echo "Next steps:"
echo "  1) Inspect ${DEST}/ai-conversation-*/index.ts and ${DEST}/_shared/*"
echo "  2) supabase functions serve ai-conversation-start ai-conversation-send ai-conversation-stream"
echo "  3) supabase functions deploy ai-conversation-start ai-conversation-send ai-conversation-stream"
