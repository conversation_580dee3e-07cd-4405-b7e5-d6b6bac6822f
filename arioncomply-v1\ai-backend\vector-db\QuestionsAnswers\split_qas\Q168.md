id: Q168
query: >-
  How do we manage different breach-notification deadlines across countries?
packs:
  - "GDPR:2016"
  - "NIS2:2023"
primary_ids:
  - "GDPR:2016/Art.33"
  - "NIS2:2023/Art.22"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Tracker"
flags:
  - "LOCAL LAW_CHECK"
sources:
  - title: "GDPR — 72-Hour Breach Notification"
    id: "GDPR:2016/Art.33"
    locator: "Article 33"
  - title: "NIS2 — 24-Hour Incident Notification"
    id: "NIS2:2023/Art.22"
    locator: "Article 22"
ui:
  cards_hint:
    - "Breach SLA matrix"
  actions:
    - type: "start_workflow"
      target: "breach_notification"
      label: "Notify Authorities"
    - type: "open_register"
      target: "breach_requirements"
      label: "View Deadlines"
output_mode: "both"
graph_required: false
notes: "Timelines differ: GDPR 72 h vs NIS2 24 h; some APAC laws < 72 h"
---
### 168) How do we manage different breach-notification deadlines across countries?

**Standard terms)**  
- **GDPR breach (Art. 33):** 72-hour limit to notify supervisory authority.  
- **NIS2 incident (Art. 22):** 24-hour “early warning” to CSIRT.

**Plain-English answer**  
Build a **Breach SLA Matrix** listing each jurisdiction’s timeline (e.g., UAE 6 h, Australia 30 d). Incident workflow auto-selects deadlines based on affected data subjects and sector.

**Applies to**  
- **Primary:** GDPR Art. 33; NIS2 Art. 22

**Why it matters**  
Missing a deadline is a strict-liability offence in many regions.

**Do next in our platform**  
- Maintain the **Breach Requirements Register**.  
- Run **Breach Notification** workflow on every incident.

**How our platform will help**  
- **[Tracker]** Dynamic countdown timers per jurisdiction.  
- **[Workflow]** Pre-filled regulator forms and contact lists.

**Likely follow-ups**  
- Do we notify customers as well? (Depends on risk and local laws)

**Sources**  
- GDPR Article 33; NIS2 Article 22

**Legal note:** Always confirm local breach rules with counsel.  
