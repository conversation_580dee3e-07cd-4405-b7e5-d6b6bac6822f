<!--
File: arioncomply-v1/docs/ArionComplyDesign/ApplicationDatabase/SeedDataDesign.md
File Description: Comprehensive seed data design for MVP-Assessment and MVP-Demo-Light phases
Purpose: Define demo organization profiles, data structures, and implementation strategy with RLS compliance
Inputs: Company profiles, compliance frameworks, MVP phase requirements, existing database schema
Outputs: Complete seed data specifications, SQL migration files, quality assurance procedures
Dependencies: Database migrations 0001-0013, RLS policies, multi-tenant architecture, File Header Style Guide
Security/RLS: Full org_id isolation, proper audit fields, compliance with existing access patterns
Notes: Supports 5 demo organizations across industries, geographies, and compliance maturity levels
-->

# ArionComply Seed Data Design Document

## Executive Summary

This document defines the complete seed data strategy for ArionComply's MVP-Assessment and MVP-Demo-Light phases. It establishes 5 realistic demo organizations across different industries, sizes, and compliance frameworks while maintaining strict multi-tenant isolation and security principles consistent with production customer data.

## Business Requirements & Success Criteria

### Demo Objectives
- **Sales Demonstrations**: Compelling scenarios across industry verticals
- **User Onboarding**: Comprehensive training examples with realistic complexity
- **Development Testing**: Rich data sets for feature validation and performance testing
- **Compliance Showcase**: Framework-specific implementations demonstrating platform depth
- **Scalability Proof**: Organizations at different maturity levels showing growth path

### Framework Coverage Strategy
- **Core Frameworks**: ISO 27001, GDPR, SOC 2 (foundational for most demos)
- **Emerging Frameworks**: EU AI Act, NIS 2 (competitive differentiation)
- **Industry-Specific**: HIPAA-like protections, FTC Privacy, ISO 14001
- **Cross-Framework**: Overlap scenarios (GDPR + AI Act, ISO 27001 + NIS 2)

## Security & Compliance Architecture

### RLS Compliance Requirements
```sql
-- All seed data MUST respect existing RLS policies
-- Example from migration 0002:
CREATE POLICY org_select ON organizations
  FOR SELECT USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  );
```

### Data Isolation Principles
- **Complete Tenant Separation**: Each demo org isolated via `org_id` as production tenants
- **No Cross-Contamination**: Zero shared data between demo organizations
- **Audit Trail Integrity**: All `created_by`, `updated_by`, `deleted_at` fields properly populated
- **Access Pattern Consistency**: Demo data accessible only through same mechanisms as customer data

### Security Controls
- **Demo Data Marking**: Clear metadata indicating test/demo status
- **Email Domains**: All demo emails use `.demo` or clearly fictional domains
- **Realistic But Fictional**: No real PII, company names, or sensitive information
- **Role-Based Access**: Demo users respect RBAC patterns established in migrations 0006-0008

## MVP Phase Data Architecture

### MVP-Assessment Phase (Immediate Implementation)
**Scope**: Single organization for chat and assessment testing
**Primary Organization**: TechForge Solutions (Berlin-based software development)

**Data Requirements**:
```sql
-- Core tables for MVP-Assessment
- organizations (1 demo org)
- profiles (8-12 user personas)
- conversation_sessions (15-20 sample sessions)
- conversation_messages (150-300 messages)
- assessment_conversations (10-15 assessment sessions)
- assessment_insights (40-60 AI-extracted insights)
- questionnaire_templates (5-8 framework templates)
- questionnaire_responses (25-40 sample responses)
```

**Rationale for TechForge Selection**:
- Medium complexity (not overwhelming for initial demos)
- AI integration showcases platform AI capabilities
- EU-based demonstrates GDPR compliance depth
- Software industry relatable to many prospects
- Remote workforce scenarios address modern business challenges

### MVP-Demo-Light Phase (Full Implementation)
**Scope**: All 5 organizations with complete feature demonstration
**Organizations**: ConnectEU, SmartHome, TechForge, Global Health Alliance, GreenTech

**Additional Data Requirements**:
```sql
-- Extended tables for MVP-Demo-Light
- documents (100-150 per org)
- document_versions (200-400 total)
- document_relationships (50-100 per org)
- workflow_definitions (8-15 per org)
- workflow_instances (20-40 active per org)
- risk_assessments (30-75 per org)
- control_implementations (100-250 per org)
- audit_logs (500-1000 entries per org)
- metrics_data (dashboard and reporting data)
- framework_implementations (compliance status per org)
```

## Demo Organization Specifications

### Organization 1: ConnectEU Communications
**Database Profile**:
```sql
INSERT INTO organizations (org_id, org_name, org_domain, settings) VALUES (
  '550e8400-e29b-41d4-a716-446655440001',
  'ConnectEU Communications',
  'connecteu.demo',
  '{
    "industry": "communications_services",
    "headquarters": "Amsterdam, Netherlands",
    "employee_count": 350,
    "regions": ["EU", "US", "APAC"],
    "primary_frameworks": ["gdpr", "nis2", "iso27001", "ai-act"],
    "compliance_maturity": "advanced",
    "demo_scenario_focus": "telecom_regulations"
  }'::jsonb
);
```

**Key User Personas**:
- **CISO**: Maria van der Berg (<EMAIL>) - NIS 2 compliance lead
- **DPO**: Klaus Richter (<EMAIL>) - GDPR expertise
- **CTO**: Priya Nakamura (<EMAIL>) - Technical infrastructure
- **Compliance Manager**: Jean-Luc Dubois (<EMAIL>) - Framework coordination

**Sample Data Characteristics**:
- High volume of real-time communications compliance data
- Cross-border data flow scenarios (EU-US-APAC)
- Critical infrastructure protection requirements
- Customer confidentiality vs law enforcement access
- Telecom-specific incident response procedures

### Organization 2: SmartHome Industries
**Database Profile**:
```sql
INSERT INTO organizations (org_id, org_name, org_domain, settings) VALUES (
  '550e8400-e29b-41d4-a716-446655440002',
  'SmartHome Industries',
  'smarthome.demo',
  '{
    "industry": "consumer_manufacturing",
    "headquarters": "Austin, Texas, USA",
    "employee_count": 850,
    "regions": ["US", "EU", "APAC"],
    "primary_frameworks": ["iso27001", "gdpr", "soc2", "ftc_privacy"],
    "compliance_maturity": "mature",
    "demo_scenario_focus": "iot_device_security"
  }'::jsonb
);
```

**Key User Personas**:
- **Chief Security Officer**: David Chen (<EMAIL>) - Product security
- **Privacy Officer**: Sarah Williams (<EMAIL>) - Consumer privacy
- **IoT Security Lead**: Ahmed Hassan (<EMAIL>) - Device lifecycle
- **Compliance Director**: Lisa Rodriguez (<EMAIL>) - Multi-framework

**Sample Data Characteristics**:
- IoT device security lifecycle management
- Consumer privacy by design workflows
- Supply chain risk assessments
- Product recall and incident response procedures
- International market compliance variations

### Organization 3: TechForge Solutions
**Database Profile**:
```sql
INSERT INTO organizations (org_id, org_name, org_domain, settings) VALUES (
  '550e8400-e29b-41d4-a716-446655440003',
  'TechForge Solutions',
  'techforge.demo',
  '{
    "industry": "software_development",
    "headquarters": "Berlin, Germany",
    "employee_count": 180,
    "regions": ["EU", "US"],
    "primary_frameworks": ["iso27001", "gdpr", "ai-act", "iso27701"],
    "compliance_maturity": "implementing",
    "demo_scenario_focus": "ai_governance"
  }'::jsonb
);
```

**Key User Personas**:
- **CISO**: Dr. Anna Kowalski (<EMAIL>) - Security architecture
- **DPO**: Marcus Weber (<EMAIL>) - Data protection
- **AI Ethics Lead**: Dr. Rajesh Patel (<EMAIL>) - AI Act compliance
- **DevSecOps Manager**: Elena Volkov (<EMAIL>) - Secure development

**Sample Data Characteristics**:
- AI development governance workflows
- Remote workforce security management
- Client data processing agreements
- Software supply chain security assessments
- Code review and vulnerability management

### Organization 4: Global Health Alliance
**Database Profile**:
```sql
INSERT INTO organizations (org_id, org_name, org_domain, settings) VALUES (
  '550e8400-e29b-41d4-a716-446655440004',
  'Global Health Alliance',
  'globalhealth.demo',
  '{
    "industry": "nonprofit_health",
    "headquarters": "Washington, D.C., USA",
    "employee_count": 275,
    "regions": ["Americas", "EU", "Africa", "Asia"],
    "primary_frameworks": ["gdpr", "soc2", "iso27001", "hipaa_like"],
    "compliance_maturity": "developing",
    "demo_scenario_focus": "humanitarian_data_protection"
  }'::jsonb
);
```

**Key User Personas**:
- **Chief Privacy Officer**: Dr. Amara Johnson (<EMAIL>) - Beneficiary data protection
- **Compliance Director**: Roberto Silva (<EMAIL>) - Grant compliance
- **IT Security Manager**: Fatima Al-Zahra (<EMAIL>) - Multi-country operations
- **Program Director**: Dr. James Ochieng (<EMAIL>) - Field operations

**Sample Data Characteristics**:
- Vulnerable population data protection workflows
- Multi-stakeholder governance (donors, beneficiaries, partners)
- Cross-border humanitarian operations
- Grant compliance and financial transparency
- Volunteer and partner access management

### Organization 5: GreenTech Analytics
**Database Profile**:
```sql
INSERT INTO organizations (org_id, org_name, org_domain, settings) VALUES (
  '550e8400-e29b-41d4-a716-446655440005',
  'GreenTech Analytics',
  'greentech.demo',
  '{
    "industry": "environmental_technology",
    "headquarters": "San Francisco, California, USA",
    "employee_count": 35,
    "regions": ["US", "EU"],
    "primary_frameworks": ["soc2", "gdpr", "iso14001"],
    "compliance_maturity": "early_stage",
    "demo_scenario_focus": "startup_scaling"
  }'::jsonb
);
```

**Key User Personas**:
- **CEO/CISO**: Jordan Kim (<EMAIL>) - Wearing multiple hats
- **Head of Engineering**: Alex Thompson (<EMAIL>) - Security implementation
- **Compliance Lead**: Maria Santos (<EMAIL>) - Framework setup
- **Customer Success**: Taylor Chen (<EMAIL>) - Client requirements

**Sample Data Characteristics**:
- Early-stage compliance framework setup
- Rapid scaling security challenges
- International expansion planning
- Investor due diligence preparation
- Environmental impact measurement workflows

## Data Volume & Complexity Strategy

### Realistic Scale Per Organization
```
Organization Size    Users  Documents  Risks  Controls  Tasks  Conversations
ConnectEU (350)      15     60         45     180       60     25
SmartHome (850)      18     75         60     220       80     30
TechForge (180)      12     45         35     150       50     20
Global Health (275)  14     55         40     160       55     22
GreenTech (35)       8      25         20     80        30     15

Total:              67     260        200    790       275    112
```

### Content Depth Strategy
- **Framework-Specific**: Controls and policies appropriate to each framework
- **Industry Terminology**: Sector-specific language and examples
- **Maturity Appropriate**: Content complexity matching organizational maturity
- **Regional Variations**: EU vs US regulatory difference examples
- **Cross-Framework**: Overlap scenarios showing platform integration capabilities

## Technical Implementation Strategy

### Phase 1: Foundation (MVP-Assessment)
**Target**: Single organization (TechForge) operational for chat testing

**Migration Files Required**:
```sql
-- 0014_seed_mvp_assessment.sql
- INSERT organizations (TechForge only)
- INSERT profiles (12 TechForge users)
- INSERT questionnaire_templates (5 core templates)
- INSERT conversation_sessions (20 sample sessions)
- INSERT conversation_messages (200 messages)
- INSERT assessment_conversations (15 assessments)
- INSERT assessment_insights (50 insights)
```

**Implementation Dependencies**:
1. Core organization and user setup
2. Conversation and assessment data
3. Questionnaire templates and responses
4. Basic framework implementation records
5. Sample workflow instances

### Phase 2: Full Demo (MVP-Demo-Light)
**Target**: All 5 organizations with complete feature demonstration

**Migration Files Required**:
```sql
-- 0015_seed_mvp_demo_light_orgs.sql (remaining 4 organizations)
-- 0016_seed_mvp_demo_light_content.sql (documents, policies, procedures)
-- 0017_seed_mvp_demo_light_workflows.sql (workflow definitions and instances)
-- 0018_seed_mvp_demo_light_metrics.sql (dashboard and analytics data)
-- 0019_seed_mvp_demo_light_scenarios.sql (preset demo scenarios)
```

**Implementation Dependencies**:
1. Remaining organizations and users
2. Document management system content
3. Workflow definitions and active instances  
4. Risk assessments and control implementations
5. Metrics data for dashboards
6. Preset scenarios for guided demos

### Data Generation Approach
**Manual Curation**: High-value content (policies, procedures, user personas)
**Template-Based**: Repetitive content (conversation messages, audit logs)
**Realistic Variation**: Date ranges, status distributions, complexity levels
**Cross-References**: Proper foreign key relationships and data consistency

## Quality Assurance & Validation

### Data Integrity Checks
```sql
-- Verify RLS compliance
SELECT COUNT(*) FROM organizations WHERE org_id NOT LIKE '550e8400-e29b-41d4-a716-4466554400%';

-- Ensure proper audit fields
SELECT org_name FROM organizations WHERE created_by IS NULL OR created_at IS NULL;

-- Validate cross-references
SELECT COUNT(*) FROM profiles p 
LEFT JOIN organizations o ON p.org_id = o.org_id 
WHERE o.org_id IS NULL;

-- Check framework consistency
SELECT org_name, settings->'primary_frameworks' as frameworks
FROM organizations 
WHERE jsonb_array_length(settings->'primary_frameworks') = 0;
```

### Demo Scenario Validation
- **User Journey Testing**: Complete workflows from login to task completion
- **Framework Coverage**: Each framework demonstrates key features
- **Performance Testing**: Acceptable response times with full seed data
- **Cross-Organization**: No data leakage between demo tenants

### Content Quality Standards
- **Professional Language**: Business-appropriate terminology and examples
- **Technical Accuracy**: Correct compliance framework interpretations
- **Realistic Scenarios**: Based on actual industry challenges and requirements
- **Consistent Branding**: Professional presentation suitable for client demos

## Maintenance & Evolution Strategy

### Regular Updates
- **Framework Reviews**: Update when regulations evolve
- **Content Refresh**: Maintain relevance to current market conditions
- **User Feedback Integration**: Incorporate demo improvement suggestions
- **Performance Optimization**: Monitor and improve query performance

### Version Control Strategy
```
/db/migrations/
  0014_seed_mvp_assessment.sql
  0015_seed_mvp_demo_light_orgs.sql
  0016_seed_mvp_demo_light_content.sql
  0017_seed_mvp_demo_light_workflows.sql
  0018_seed_mvp_demo_light_metrics.sql
  0019_seed_mvp_demo_light_scenarios.sql

/db/seed_data/
  README.md (this document summary)
  validation_queries.sql
  demo_scenarios.md
  user_personas.md
```

### Rollback & Recovery
- **Migration Dependencies**: Clear dependencies between seed migrations
- **Backup Procedures**: Regular backups before seed data updates
- **Rollback Scripts**: Ability to remove seed data cleanly
- **Environment Isolation**: Separate seed data for dev/staging/demo environments

## Security & Privacy Considerations

### Data Protection Measures
- **No Real PII**: All personal data is clearly fictional
- **Email Isolation**: Demo emails use `.demo` domains exclusively  
- **Tenant Isolation**: Complete separation via RLS policies
- **Access Logging**: All demo access logged same as production

### Compliance Alignment
- **Framework Accuracy**: Seed data reflects genuine regulatory requirements
- **Control Mappings**: Technically correct control relationships and implementations
- **Risk Scenarios**: Realistic but not based on actual security incidents
- **Audit Readiness**: Seed data suitable for compliance demonstration purposes

## Success Metrics & KPIs

### Demo Effectiveness Metrics
- **Prospect Engagement**: Time spent exploring different demo scenarios
- **Feature Discovery**: Coverage of platform capabilities during demos
- **Conversion Rates**: Demo session to trial/purchase conversion
- **User Comprehension**: Training completion rates and assessment scores

### Technical Performance Metrics
- **Query Performance**: Sub-second response times for all demo queries
- **Data Consistency**: Zero foreign key violations or orphaned records
- **Storage Efficiency**: Optimal data size vs demonstration value ratio
- **Maintenance Overhead**: Time required for updates and quality assurance

---

*This document serves as the authoritative specification for ArionComply's demo seed data implementation, ensuring consistent, secure, and effective demonstration capabilities across all customer touchpoints.*