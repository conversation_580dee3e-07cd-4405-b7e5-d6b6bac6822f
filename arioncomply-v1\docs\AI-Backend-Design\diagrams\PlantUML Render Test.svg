<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentScriptType="application/ecmascript" contentStyleType="text/css" height="278px" preserveAspectRatio="none" style="width:421px;height:278px;" version="1.1" viewBox="0 0 421 278" width="421px" zoomAndPan="magnify"><defs/><g><text fill="#000000" font-family="sans-serif" font-size="18" lengthAdjust="spacingAndGlyphs" textLength="241" x="93" y="16.708">PlantUML Render Test (v3)</text><!--MD5=[ee52d133ea2372ae680bfb4fb8d133c7]
cluster SYS--><rect fill="#FFFFFF" height="82" style="stroke: #000000; stroke-width: 1.5;" width="303" x="22" y="44.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="98" x="124.5" y="59.9482">Test System</text><ellipse cx="90.9975" cy="95.4768" fill="#F8F8F8" rx="52.4975" ry="14.5236" style="stroke: #383838; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="70" x="55.9975" y="100.1252">Open Doc</text><ellipse cx="243.8237" cy="95.3179" fill="#F8F8F8" rx="64.8237" ry="15.3647" style="stroke: #383838; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="93" x="197.3237" y="99.9663">View Diagram</text><!--MD5=[fee662a292a0353a69bd162820bae93b]
entity User--><ellipse cx="186" cy="199.4531" fill="#F8F8F8" rx="8" ry="8" style="stroke: #383838; stroke-width: 1.5;"/><path d="M186,207.4531 L186,234.4531 M173,215.4531 L199,215.4531 M186,234.4531 L173,249.4531 M186,234.4531 L199,249.4531 " fill="none" style="stroke: #383838; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="32" x="170" y="264.9482">User</text><!--MD5=[f9ba5bf973e7832d2ce5ed59077b93f7]
entity DB--><path d="M29,216.4531 C29,206.4531 67,206.4531 67,206.4531 C67,206.4531 105,206.4531 105,216.4531 L105,241.75 C105,251.75 67,251.75 67,251.75 C67,251.75 29,251.75 29,241.75 L29,216.4531 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M29,216.4531 C29,226.4531 67,226.4531 67,226.4531 C67,226.4531 105,226.4531 105,216.4531 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="56" x="39" y="243.4482">Test DB</text><path d="M237,208.9531 L237,249.2188 L409,249.2188 L409,218.9531 L399,208.9531 L237,208.9531 " fill="#ECECEC" style="stroke: #383838; stroke-width: 1.0;"/><path d="M399,208.9531 L399,218.9531 L409,218.9531 L399,208.9531 " fill="#ECECEC" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="151" x="243" y="226.02">Simple note to exercise</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="150" x="243" y="241.1528">rendering with Graphviz</text><!--MD5=[ad34ad057a410de18d2c20f3bf300a0c]
link User to Open--><path d="M182.31,189.7831 C179.31,174.1231 173.86,156.5131 164,142.9531 C153.76,128.8631 137.83,118.0031 123.46,110.3331 " fill="none" id="User-&gt;Open" style="stroke: #383838; stroke-width: 1.0;"/><polygon fill="#383838" points="118.66,107.8531,124.8331,115.5273,123.1061,110.1405,128.4929,108.4135,118.66,107.8531" style="stroke: #383838; stroke-width: 1.0;"/><!--MD5=[0419d6639f6d4acd97b1084b7f0a2136]
link Open to View--><path d="M143.59,95.4531 C153.61,95.4531 163.63,95.4531 173.65,95.4531 " fill="none" id="Open-&gt;View" style="stroke: #383838; stroke-width: 1.0;"/><polygon fill="#383838" points="178.89,95.4531,169.89,91.4531,173.89,95.4531,169.89,99.4531,178.89,95.4531" style="stroke: #383838; stroke-width: 1.0;"/><!--MD5=[b77c97dbb21d08a303492fc9cddebc87]
link View to User--><path d="M237.57,111.0331 C229.57,129.1631 215.59,160.8731 204.19,186.7031 " fill="none" id="View-&gt;User" style="stroke: #383838; stroke-width: 1.0;"/><polygon fill="#383838" points="202.1,191.4531,209.3894,184.8301,204.1161,186.8776,202.0685,181.6043,202.1,191.4531" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="174" x="222" y="156.02">Rendered OK (SVG update)</text><!--MD5=[2f16fe1f1ef8f6b004a3f116793f8960]
link Open to DB--><path d="M67.36,108.5331 C54.65,116.4331 40.15,128.0631 33,142.9531 C23.68,162.3731 33.75,185.0531 45.3,202.0931 " fill="none" id="Open-&gt;DB" style="stroke: #383838; stroke-width: 1.0;"/><polygon fill="#383838" points="48.26,206.3031,46.3735,196.6366,45.3917,202.2076,39.8207,201.2259,48.26,206.3031" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="125" x="34" y="156.02">read metadata (v3)</text><!--MD5=[edfd690f22c1187c44b3340389246e14]
link DB to GMN11--><path d="M92.69,206.2231 C112.24,190.5231 140.62,171.0031 169.5,162.4531 C218.23,148.0331 272.07,184.4031 301.26,208.8631 " fill="none" id="DB-GMN11" style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 7.0,7.0;"/><!--MD5=[635eb4a46338c5985c933015d44ba6a4]
@startuml PlantUML Render Test
title PlantUML Render Test (v3)
skinparam shadowing false
skinparam monochrome true

actor User
database DB as "Test DB"
rectangle "Test System" as SYS {
  (Open Doc) as Open
  (View Diagram) as View
}

User -> Open
Open -> View
View - -> User : Rendered OK (SVG update)

Open - -> DB : read metadata (v3)
note right of DB
  Simple note to exercise
  rendering with Graphviz
end note

@enduml

PlantUML version 1.2020.02(Sun Mar 01 10:22:07 UTC 2020)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Java Version: 17.0.16+8
Operating System: Linux
Default Encoding: UTF-8
Language: en
Country: null
--></g></svg>