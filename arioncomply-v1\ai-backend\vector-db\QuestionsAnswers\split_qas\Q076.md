id: Q076
query: >-
  How do we prioritize which requirements to implement first?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/6.1"
  - "GDPR:2016/Art.6"
overlap_ids:
  - "ISO27701:2019/6.1"
capability_tags:
  - "Planner"
  - "Dashboard"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Risk Treatment"
    id: "ISO27001:2022/6.1"
    locator: "Clause 6.1"
  - title: "GDPR — Lawfulness of Processing"
    id: "GDPR:2016/Art.6"
    locator: "Article 6"
  - title: "ISO/IEC 27701:2019 — PIMS Planning"
    id: "ISO27701:2019/6.1"
    locator: "Clause 6.1"
ui:
  cards_hint:
    - "Priority heatmap"
  actions:
    - type: "start_workflow"
      target: "prioritization"
      label: "Run Prioritization"
output_mode: "both"
graph_required: false
notes: "Use risk and impact to rank requirements. @product-taxonomy: proposing 'prioritization' as a new workflow for planning implementation order based on risk."

---
### 76) How do we prioritize which requirements to implement first?

**Standard terms**  
- **Risk treatment (ISO 27001 Cl. 6.1):** select controls based on risk levels.  
- **Lawfulness (GDPR Art. 6):** mandatory legal bases.  

**Plain-English answer**  
Rank by **risk** (likelihood × impact) and **legal obligation**. High-risk gaps and required controls (e.g., incident reporting, data subject rights) come first.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 6.1; GDPR Article 6  
- **Also relevant/Overlaps:** ISO/IEC 27701:2019 Clause 6.1

**Why it matters**  
Effective prioritization accelerates risk reduction.

**Do next in our platform**  
- Generate **Priority Heatmap**.  
- Review top 10 controls.

**How our platform will help**  
- **[Dashboard]** Risk vs priority matrix.  
- **[Planner]** Top-control task list.

**Likely follow-ups**  
- “Can we automate high-priority controls?” (Yes—via integrations)

**Sources**  
- ISO/IEC 27001:2022 Clause 6.1  
- GDPR Article 6  
- ISO/IEC 27701:2019 Clause 6.1
