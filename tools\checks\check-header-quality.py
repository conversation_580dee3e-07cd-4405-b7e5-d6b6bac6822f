#!/usr/bin/env python3
# File: tools/checks/check-header-quality.py
# File Description: Validates header quality and basic function documentation
# Purpose: Ensure files include granular headers (File Description, Purpose, etc.) and that functions have docstrings/JSDoc
# Notes: Heuristic checks; reports missing sections and functions lacking docs

import re
import sys
import ast
from pathlib import Path
from typing import List, Tuple, Dict

ROOT = Path(__file__).resolve().parents[2]

INCLUDE_EXTS = { 'ts', 'tsx', 'js', 'jsx', 'py', 'sh', 'sql', 'md', 'html', 'css', 'json', 'yaml', 'yml', 'dart' }
SKIP_DIRS = { '.git', 'node_modules', 'dist', 'build', '.venv', '.mypy_cache', '.pytest_cache' }

# Scope prefixes to include in checks
SCOPE_PREFIXES = [
    'arioncomply-v1/supabase/functions/',
    'arioncomply-v1/ai-backend/python-backend/',
    'arioncomply-v1/db/migrations/',
    'arioncomply-v1/ai-backend/supabase_migrations/',
    'arioncomply-v1/config/',
    'arioncomply-v1/schemas/',
    'arioncomply-v1/testing/workflow-gui/',
    'arioncomply-v1/frontend-flutter/',
    'arioncomply-v1/.kiro/specs/',
    'arioncomply-v1/ai-backend/vector-db/',
]

# Exclusions for assets and vector-db corpora-like content
SKIP_EXTS = {
    'png', 'jpg', 'jpeg', 'gif', 'svg', 'ico', 'zip', 'gz', 'bz2', 'xz', '7z', 'rar',
    'bmp', 'tiff', 'webp', 'mp3', 'mp4', 'mov', 'avi', 'woff', 'woff2', 'eot', 'ttf', 'otf', 'wasm',
    'class', 'jar', 'war', 'iso', 'bin', 'dylib', 'so', 'dll', 'parquet', 'csv', 'tsv', 'doc', 'docx',
    'xlsx'
}
VECTOR_CONTENT_EXTS = {'pdf', 'doc', 'docx', 'csv', 'tsv', 'parquet', 'txt'}

# Vector DB subpaths to exclude (content corpora)
VECTOR_EXCLUDE_DIRS = [
    'arioncomply-v1/ai-backend/vector-db/QuestionsAnswers/',
    'arioncomply-v1/ai-backend/vector-db/inputs/',
]

HEADER_NEEDS = [
    'File Description',
    'Purpose',
]

SQL_HINTS = ['Migration', 'Policies', 'RLS', 'Security']
SHELL_HINTS = ['Usage', 'Env', 'Purpose']

def list_files() -> List[Path]:
    """Collect candidate files for header/doc checks (code + JSON/YAML)."""
    res = []
    for p in ROOT.rglob('*'):
        if not p.is_file():
            continue
        if any(seg in SKIP_DIRS for seg in p.parts):
            continue
        ext = p.suffix.lstrip('.')
        rel = str(p.relative_to(ROOT)).replace('\\', '/')
        if not any(rel.startswith(prefix) for prefix in SCOPE_PREFIXES):
            continue
        # Exclude vector-db content directories except utility scripts
        if any(rel.startswith(d) for d in VECTOR_EXCLUDE_DIRS):
            if ext not in {'py', 'sh'}:
                continue
        if ext in SKIP_EXTS:
            continue
        if rel.startswith('arioncomply-v1/ai-backend/vector-db/') and ext in VECTOR_CONTENT_EXTS:
            continue
        if ext in INCLUDE_EXTS:
            res.append(p)
    return res

def read_head(p: Path, lines: int = 30) -> str:
    """Return the first N lines of a file as a single string."""
    try:
        with p.open('r', encoding='utf-8', errors='ignore') as f:
            return ''.join(f.readline() for _ in range(lines))
    except Exception:
        return ''

def _has_companion_readme(p: Path) -> bool:
    """Check if a file has a companion README in the same directory."""
    d = p.parent
    base = p.stem
    for cand in (f"{base}.README.md", f"README.{base}.md", f"{base}-README.md", f"{base}_README.md"):
        if (d / cand).exists():
            return True
    return False

def check_header_quality(p: Path) -> Tuple[bool, List[str]]:
    """Apply header-quality policy based on file type (code/JSON/YAML/MD)."""
    head = read_head(p)
    problems = []
    ext = p.suffix.lstrip('.')

    # Skip Markdown header checks per policy
    if ext == 'md':
        return (True, [])

    # JSON: require companion README
    if ext == 'json':
        if not _has_companion_readme(p):
            problems.append('json missing companion README (e.g., <name>.README.md)')
        return (len(problems) == 0, problems)

    # YAML: allow either header or companion README
    if ext in ('yaml','yml'):
        has_header = ('File:' in head) or head.strip().startswith('#')
        if not has_header and not _has_companion_readme(p):
            problems.append('yaml missing header or companion README')
        return (len(problems) == 0, problems)

    # Code files: need File header + Description/Purpose
    if 'File:' not in head:
        problems.append('missing File: header')
    if not any(k in head for k in HEADER_NEEDS):
        problems.append('missing File Description/Purpose')

    # SQL: look for purpose or migration notes
    if ext == 'sql':
        if not any(h in head for h in SQL_HINTS) and 'Purpose' not in head:
            problems.append('sql header lacks Migration/Purpose/Security notes')
    # Shell: shebang and usage/env/purpose
    if ext == 'sh':
        if not head.startswith('#!'):
            problems.append('missing shebang')
        if not any(h in head for h in SHELL_HINTS):
            problems.append('shell header lacks Usage/Env/Purpose')
    # HTML: ensure comment block has Purpose guidance
    if ext == 'html':
        if '<!--' not in head:
            problems.append('missing HTML header comment block')
    # CSS/MD: less strict; only require File: or a leading comment
    return (len(problems) == 0, problems)

def check_python_functions(p: Path) -> List[str]:
    """Flag Python functions missing docstrings using AST parsing."""
    try:
        src = p.read_text(encoding='utf-8', errors='ignore')
        tree = ast.parse(src)
    except Exception:
        return ['python parse error']
    problems = []
    for node in ast.walk(tree):
        if isinstance(node, ast.FunctionDef):
            doc = ast.get_docstring(node, clean=False)
            if not doc:
                problems.append(f'missing docstring: def {node.name}()')
    return problems

JS_FUNC_RE = re.compile(r'export\s+(async\s+)?function\s+(\w+)\s*\(', re.MULTILINE)
JS_CONST_FUNC_RE = re.compile(r'export\s+const\s+(\w+)\s*=\s*(async\s*)?\([^)]*\)\s*=>', re.MULTILINE)

def check_js_functions(p: Path) -> List[str]:
    """Flag exported TS/JS functions missing an immediately preceding JSDoc block."""
    src = p.read_text(encoding='utf-8', errors='ignore')
    problems = []
    for m in JS_FUNC_RE.finditer(src):
        name = m.group(2)
        start = m.start()
        prev = src[max(0, start-600):start]
        if '/**' not in prev:
            problems.append(f'missing JSDoc: function {name}()')
    for m in JS_CONST_FUNC_RE.finditer(src):
        name = m.group(1)
        start = m.start()
        prev = src[max(0, start-600):start]
        if '/**' not in prev:
            problems.append(f'missing JSDoc: const {name} = () =>')
    return problems

def main():
    """Run header and doc checks; print summary and exit non-zero on failures."""
    files = list_files()
    header_failures: Dict[str, List[str]] = {}
    func_failures: Dict[str, List[str]] = {}
    for p in files:
        ok, issues = check_header_quality(p)
        if not ok:
            header_failures[str(p.relative_to(ROOT))] = issues
        ext = p.suffix.lstrip('.')
        if ext == 'py':
            probs = check_python_functions(p)
            if probs:
                func_failures[str(p.relative_to(ROOT))] = probs
        if ext in ('ts','tsx','js','jsx'):
            probs = check_js_functions(p)
            if probs:
                func_failures[str(p.relative_to(ROOT))] = probs

    # Report
    print('Header Quality Report')
    print('=====================')
    print(f'Total files scanned: {len(files)}')
    print(f'Files with header issues: {len(header_failures)}')
    print(f'Files with function doc issues: {len(func_failures)}')
    print()
    if header_failures:
        print('Header issues (first 100):')
        for i, (path, issues) in enumerate(header_failures.items()):
            if i >= 100: break
            print(f'  - {path}: {"; ".join(issues)}')
    print()
    if func_failures:
        print('Function doc issues (first 100):')
        for i, (path, issues) in enumerate(func_failures.items()):
            if i >= 100: break
            print(f'  - {path}:')
            for issue in issues[:5]:
                print(f'      * {issue}')

    # Non-zero exit if any failures
    if header_failures or func_failures:
        sys.exit(1)

if __name__ == '__main__':
    main()
