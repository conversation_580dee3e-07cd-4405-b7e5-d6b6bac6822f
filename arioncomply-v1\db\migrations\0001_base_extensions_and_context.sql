-- File: arioncomply-v1/db/migrations/0001_base_extensions_and_context.sql
-- Migration 0001: Base extensions, helpers, and standards
-- Purpose: Establish UUID generation, timestamp triggers, and multi-tenant helpers.
-- Audience: Junior engineers should read top-to-bottom; each helper is used
--           by later migrations to implement RLS and audit patterns.
-- Aligns with:
--   - docs/ArionComplyDesign/DesignGuidanceAndPrinciples/AppDatabase/database_schema_design_principles.md
--   - docs/ArionComplyDesign/application_schema_design.md
--   - docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js

BEGIN;

-- Extensions
-- Extension provides gen_random_uuid() for UUID primary keys.
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Helper: resolve current organization id from session setting or JWT claims
-- app_current_org_id(): resolves the current tenant (organization) id.
-- Priority order:
--   1) session GUC app.current_organization_id (set by backend on behalf of user)
--   2) JWT claim org_id (from Supabase auth)
CREATE OR REPLACE FUNCTION app_current_org_id()
RETURNS uuid
LANGUAGE sql STABLE AS $$
  SELECT COALESCE(
    NULLIF(current_setting('app.current_organization_id', true), '')::uuid,
    NULLIF((current_setting('request.jwt.claims', true)::jsonb ->> 'org_id'), '')::uuid
  );
$$;

-- Helper: check if current JWT includes a specific role
-- app_has_role(role): true if the user's JWT contains the specified role.
-- Used in RLS policies to implement admin bypass and fine-grained access.
CREATE OR REPLACE FUNCTION app_has_role(target_role text)
RETURNS boolean
LANGUAGE plpgsql STABLE AS $$
DECLARE
  roles_json jsonb;
  has_it boolean := false;
BEGIN
  roles_json := (current_setting('request.jwt.claims', true)::jsonb -> 'roles');
  IF roles_json IS NULL THEN
    RETURN false;
  END IF;
  PERFORM 1 FROM jsonb_array_elements_text(roles_json) AS r(val) WHERE r.val = target_role;
  IF FOUND THEN
    has_it := true;
  END IF;
  RETURN has_it;
END;
$$;

-- Audit: generic trigger to maintain updated_at
-- trg_set_updated_at(): standard BEFORE UPDATE trigger to maintain updated_at.
-- Attach this to any table that has an updated_at column.
CREATE OR REPLACE FUNCTION trg_set_updated_at()
RETURNS trigger
LANGUAGE plpgsql AS $$
BEGIN
  NEW.updated_at := NOW();
  RETURN NEW;
END;
$$;

COMMIT;
-- File: arioncomply-v1/db/migrations/0001_base_extensions_and_context.sql
