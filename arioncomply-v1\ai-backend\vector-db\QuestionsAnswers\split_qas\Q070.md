id: Q070
query: >-
  How much time should we budget for dealing with data subject requests?
packs:
  - "GDPR:2016"
  - "ISO27701:2019"
primary_ids:
  - "GDPR:2016/Arts.15-22"
  - "ISO27701:2019/8.3"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Tracker"
  - "Report"
flags: []
sources:
  - title: "GDPR — Data Subject Rights"
    id: "GDPR:2016/Arts.15-22"
    locator: "Articles 15–22"
  - title: "ISO/IEC 27701:2019 — PII Processing"
    id: "ISO27701:2019/8.3"
    locator: "Section 8.3"
ui:
  cards_hint:
    - "DSAR handling workflow"
  actions:
    - type: "start_workflow"
      target: "dsar_process"
      label: "Manage DSARs"
    - type: "open_register"
      target: "request_log"
      label: "View DSAR Log"
output_mode: "both"
graph_required: false
notes: "Budget ~2–4 hours per request depending on complexity"
---
### 70) How much time should we budget for dealing with data subject requests?

**Standard terms**  
- **Data subject rights (GDPR Arts.15–22):** access, rectification, erasure, portability, objection.  
- **PII processing (ISO 27701 Cl. 8.3):** supports DSAR handling.

**Plain-English answer**  
Each DSAR (access, erasure, portability) typically takes **2–4 hours** end-to-end: identification, validation, data retrieval, and response drafting.

**Applies to**  
- **Primary:** GDPR Articles 15–22; ISO/IEC 27701:2019 Section 8.3

**Why it matters**  
Accurate budgeting ensures timely, compliant responses and avoids fines.

**Do next in our platform**  
- Kick off **DSAR Process** workflow.  
- Track incoming requests in the log.

**How our platform will help**  
- **[Workflow]** Automated DSAR handling.  
- **[Tracker]** Request log with SLA timers.  
- **[Report]** DSAR volume and response metrics.

**Likely follow-ups**  
- “Can we extend response time under special circumstances?” (Yes—document justification **[LOCAL LAW CHECK]**)

**Sources**  
- GDPR Articles 15–22; ISO/IEC 27701:2019 Section 8.3
Please review and let me know when you’re ready to proceed with Q071–Q090!









Sources
You said:
proceed with next set


ChatGPT said:
Here are Q071–Q090, fully compliant with the Authoring Guide:

