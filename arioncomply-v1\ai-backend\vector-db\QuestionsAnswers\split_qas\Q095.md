id: Q095
query: >-
  What's the difference between backing up data and making it recoverable?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/A.8.3.3"
overlap_ids: []
capability_tags:
  - "Register"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Annex A.8.3.3 Backup"
    id: "ISO27001:2022/A.8.3.3"
    locator: "Annex A.8.3.3"
ui:
  cards_hint:
    - "Backup vs recovery guide"
  actions:
    - type: "start_workflow"
      target: "backup_recovery_setup"
      label: "Define Backup & Recovery"
output_mode: "both"
graph_required: false
notes: "Recoverability requires regular restore tests, not just data copies"
---
### 95) What's the difference between backing up data and making it recoverable?

**Standard terms**  
- **Backup (A.8.3.3):** process of copying data.  
- **Recovery:** ability to restore and validate data integrity.

**Plain-English answer**  
Backing up is copying files to a safe location. Making recoverable means regularly testing restores, verifying data integrity and ensuring your recovery time objectives (RTO) and recovery point objectives (RPO) are met.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.8.3.3

**Why it matters**  
Unverified backups may fail when you need them most.

**Do next in our platform**  
- Create **Backup & Recovery** workflow.  
- Schedule periodic **restore tests**.

**How our platform will help**  
- **[Workflow]** Restore-test scheduler.  
- **[Report]** Recovery success metrics.

**Likely follow-ups**  
- “How often should we test restores?”  

**Sources**  
- ISO/IEC 27001:2022 Annex A.8.3.3
