id: Q089
query: >-
  How do we measure if our compliance program is actually working?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/9.1"
  - "ISO27001:2022/10.3"
overlap_ids:
  - "ISO27701:2019/9"
capability_tags:
  - "Dashboard"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Monitoring & Evaluation"
    id: "ISO27001:2022/9.1"
    locator: "Clause 9.1"
  - title: "ISO/IEC 27001:2022 — Continual Improvement"
    id: "ISO27001:2022/10.3"
    locator: "Clause 10.3"
  - title: "ISO/IEC 27701:2019 — PIMS Improvement"
    id: "ISO27701:2019/9"
    locator: "Clause 9"
ui:
  cards_hint:
    - "KPI dashboard"
  actions:
    - type: "open_register"
      target: "metrics"
      label: "Define KPIs"
    - type: "start_workflow"
      target: "kpi_setup"
      label: "Set Up Metrics"
output_mode: "both"
graph_required: false
notes: "Use metrics like incident rate, audit findings, control coverage"
---
### 89) How do we measure if our compliance program is actually working?

**Standard terms**  
- **Monitoring & evaluation (Cl. 9.1):** set and review metrics.  
- **Continual improvement (Cl. 10.3):** use data to drive changes.  
- **PIMS improvement (ISO 27701 Cl. 9):** privacy-specific metrics.

**Plain-English answer**  
Track KPIs such as number of incidents detected, time to remediate, audit nonconformities, and percentage of controls implemented. Regular dashboards show trends and highlight areas needing attention.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clauses 9.1; 10.3  
- **Also relevant/Overlaps:** ISO/IEC 27701:2019 Clause 9

**Why it matters**  
Objective metrics validate program effectiveness and justify investment.

**Do next in our platform**  
- Define your **metrics** in the Metrics register.  
- Configure the **KPI Dashboard**.

**How our platform will help**  
- **[Dashboard]** Real-time KPI views.  
- **[Report]** Automated trend analysis.

**Likely follow-ups**  
- “What are industry benchmark values?” (See platform benchmarking data)

**Sources**  
- ISO/IEC 27001:2022 Clause 9.1  
- ISO/IEC 27001:2022 Clause 10.3  
- ISO/IEC 27701:2019 Clause 9