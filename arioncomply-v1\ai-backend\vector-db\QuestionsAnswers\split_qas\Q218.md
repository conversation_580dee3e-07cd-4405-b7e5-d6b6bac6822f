```yaml
id: Q218
question: How do we handle compliance for seasonal businesses or project-based work?
packs: ["ISO27001:2022","GDPR:2016"]
primary_ids: ["ISO27001:2022/Cl.8","ISO27001:2022/Cl.9","GDPR:2016/Art.24"]
overlap_ids: []
capability_tags: ["Planner","Reminder","Workflow","Tracker","Dashboard"]
ui:
  actions:
    - target: "planner"
      action: "open"
      args: { template: "seasonal_controls_calendar" }
    - target: "workflow"
      action: "open"
      args: { key: "restore_test" }
cards_hint:
  - Keep core ISMS continuous.
  - Scale training/access reviews/tests to peak periods.
  - Track coverage and exceptions.
graph_required: false
```

### 218) How do we handle compliance for seasonal businesses or project-based work?

**Standard term(s)**

- **Operational planning (Cl. 8).**

**Plain-English answer**\
Run the core program **year-round**, but scale **training, access reviews, backups/tests** to busy seasons and project timelines.

**Applies to**

- **Primary:** ISO/IEC 27001 **Cl. 8–9**; GDPR **Art. 24**.

**Why it matters**\
Peaks can create **control gaps** without capacity planning.

**Do next in our platform**

- Set **seasonal calendars**; pre-stage tasks; schedule **restore tests** and risk reviews around peaks.

**How our platform will help**

- **[Planner] [Reminder] [Workflow] [Tracker] [Dashboard]** — Season-aware cadences and coverage views.

**Likely follow-ups**

- “Do temps/contractors need full training?” → Role-based minimums (see Q195–197).

**Sources**

- ISO/IEC 27001 **Cl. 8–9**; GDPR **Art. 24**.
