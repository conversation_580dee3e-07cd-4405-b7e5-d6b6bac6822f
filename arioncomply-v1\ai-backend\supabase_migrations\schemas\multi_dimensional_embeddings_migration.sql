-- File: arioncomply-v1/ai-backend/supabase_migrations/schemas/multi_dimensional_embeddings_migration.sql
-- Migration: Multi-dimensional embeddings support for multi-pipeline architecture
-- Purpose: Update vector schema to support multiple embedding dimensions
-- Author: ArionComply - Multi-Pipeline Embedding System
-- Date: 2025-09-13

-- ============================================================================
-- MULTI-DIMENSIONAL EMBEDDINGS MIGRATION
-- Supports: 768-dim (MPNet), 1024-dim (BGE), 1536-dim (OpenAI-small), 3072-dim (OpenAI-large)  
-- ============================================================================

BEGIN;

-- ---------------------------------------------------------------------------
-- 1. Add pipeline metadata columns to vector_chunks
-- ---------------------------------------------------------------------------

-- Add pipeline identification and metadata
ALTER TABLE vector_chunks 
    ADD COLUMN pipeline_name TEXT NOT NULL DEFAULT 'text-embedding-ada-002',
    ADD COLUMN pipeline_model TEXT NOT NULL DEFAULT 'text-embedding-ada-002', 
    ADD COLUMN embedding_dimension INTEGER NOT NULL DEFAULT 1536,
    ADD COLUMN pipeline_version TEXT DEFAULT '1.0.0',
    ADD COLUMN pipeline_provider TEXT DEFAULT 'openai';

-- Add pipeline metadata for complete traceability
ALTER TABLE vector_chunks
    ADD COLUMN pipeline_metadata JSONB DEFAULT '{}'::JSONB;

COMMENT ON COLUMN vector_chunks.pipeline_name IS 'Embedding pipeline identifier (e.g., bge-large-onnx, all-mpnet-base-v2)';
COMMENT ON COLUMN vector_chunks.pipeline_model IS 'Specific model name used for embedding generation';
COMMENT ON COLUMN vector_chunks.embedding_dimension IS 'Actual embedding vector dimension';
COMMENT ON COLUMN vector_chunks.pipeline_metadata IS 'Complete pipeline metadata including quality tier, config, performance metrics';

-- ---------------------------------------------------------------------------
-- 2. Create multi-dimensional embedding tables
-- ---------------------------------------------------------------------------

-- Embeddings table for 768-dimensional vectors (all-mpnet-base-v2, placeholder)
CREATE TABLE vector_chunks_768 (
    chunk_id UUID PRIMARY KEY REFERENCES vector_chunks(id) ON DELETE CASCADE,
    embedding VECTOR(768) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Embeddings table for 1024-dimensional vectors (BGE-Large-EN-v1.5)  
CREATE TABLE vector_chunks_1024 (
    chunk_id UUID PRIMARY KEY REFERENCES vector_chunks(id) ON DELETE CASCADE,
    embedding VECTOR(1024) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Embeddings table for 1536-dimensional vectors (OpenAI text-embedding-3-small, ada-002)
CREATE TABLE vector_chunks_1536 (
    chunk_id UUID PRIMARY KEY REFERENCES vector_chunks(id) ON DELETE CASCADE,
    embedding VECTOR(1536) NOT NULL, 
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Embeddings table for 3072-dimensional vectors (OpenAI text-embedding-3-large)
CREATE TABLE vector_chunks_3072 (
    chunk_id UUID PRIMARY KEY REFERENCES vector_chunks(id) ON DELETE CASCADE,
    embedding VECTOR(3072) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ---------------------------------------------------------------------------
-- 3. Create indexes for optimal vector similarity search performance
-- ---------------------------------------------------------------------------

-- Indexes for 768-dimensional embeddings
CREATE INDEX idx_vector_chunks_768_hnsw ON vector_chunks_768 USING hnsw (embedding vector_cosine_ops) WITH (m = 16, ef_construction = 64);
CREATE INDEX idx_vector_chunks_768_ivfflat ON vector_chunks_768 USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Indexes for 1024-dimensional embeddings (primary BGE pipeline)
CREATE INDEX idx_vector_chunks_1024_hnsw ON vector_chunks_1024 USING hnsw (embedding vector_cosine_ops) WITH (m = 16, ef_construction = 64);
CREATE INDEX idx_vector_chunks_1024_ivfflat ON vector_chunks_1024 USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Indexes for 1536-dimensional embeddings
CREATE INDEX idx_vector_chunks_1536_hnsw ON vector_chunks_1536 USING hnsw (embedding vector_cosine_ops) WITH (m = 16, ef_construction = 64);
CREATE INDEX idx_vector_chunks_1536_ivfflat ON vector_chunks_1536 USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Indexes for 3072-dimensional embeddings
CREATE INDEX idx_vector_chunks_3072_hnsw ON vector_chunks_3072 USING hnsw (embedding vector_cosine_ops) WITH (m = 32, ef_construction = 128);
CREATE INDEX idx_vector_chunks_3072_ivfflat ON vector_chunks_3072 USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Pipeline metadata indexes
CREATE INDEX idx_vector_chunks_pipeline_name ON vector_chunks(pipeline_name);
CREATE INDEX idx_vector_chunks_embedding_dimension ON vector_chunks(embedding_dimension);
CREATE INDEX idx_vector_chunks_pipeline_metadata ON vector_chunks USING GIN (pipeline_metadata);

-- ---------------------------------------------------------------------------
-- 4. Multi-dimensional search functions
-- ---------------------------------------------------------------------------

-- Generic multi-dimensional vector search function
CREATE OR REPLACE FUNCTION match_chunks_multi_dimensional(
    query_embedding_768 VECTOR(768) DEFAULT NULL,
    query_embedding_1024 VECTOR(1024) DEFAULT NULL, 
    query_embedding_1536 VECTOR(1536) DEFAULT NULL,
    query_embedding_3072 VECTOR(3072) DEFAULT NULL,
    preferred_dimension INTEGER DEFAULT NULL,
    org_context JSONB DEFAULT '{}'::JSONB,
    match_threshold FLOAT DEFAULT 0.7,
    match_count INT DEFAULT 10
)
RETURNS TABLE (
    chunk_id UUID,
    document_id UUID,
    content TEXT,
    document_title TEXT,
    similarity FLOAT,
    pipeline_name TEXT,
    pipeline_model TEXT,
    embedding_dimension INTEGER,
    pipeline_metadata JSONB,
    context_relevance_score FLOAT
) 
LANGUAGE plpgsql
AS $$
DECLARE
    user_industry TEXT;
    user_size TEXT;
    user_geography TEXT[];
    user_frameworks TEXT[];
BEGIN
    -- Extract organizational context
    user_industry := org_context->>'industry_secondary';
    user_size := CASE 
        WHEN org_context->'business_context'->>'company_size' IN ('1-10', '11-50') THEN 'small'
        WHEN org_context->'business_context'->>'company_size' IN ('51-200', '201-500') THEN 'medium'
        ELSE 'large'
    END;
    user_geography := ARRAY(SELECT jsonb_array_elements_text(org_context->'business_context'->'geographic_presence'->'countries'));
    user_frameworks := ARRAY(SELECT jsonb_array_elements_text(org_context->'applicable_frameworks'));

    -- Search across all dimensions, with preference for specific dimension if provided
    RETURN QUERY
    WITH multi_dim_results AS (
        -- 768-dimensional results
        SELECT 
            vc.id as chunk_id,
            vc.document_id,
            vc.content,
            d.title as document_title,
            (v768.embedding <=> query_embedding_768) as similarity,
            vc.pipeline_name,
            vc.pipeline_model,
            vc.embedding_dimension,
            vc.pipeline_metadata,
            768 as vector_dimension,
            -- Calculate context relevance score  
            (
                CASE WHEN user_industry = ANY(ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'industry_relevance'))) THEN 0.3 ELSE 0.0 END +
                CASE WHEN user_size = ANY(ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'company_size_applicability'))) THEN 0.3 ELSE 0.0 END +
                CASE WHEN user_geography && ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'geographic_scope')) 
                      OR 'global' = ANY(ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'geographic_scope'))) THEN 0.2 ELSE 0.0 END +
                CASE WHEN user_frameworks && ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'frameworks')) THEN 0.2 ELSE 0.0 END
            ) as context_relevance_score
        FROM vector_chunks vc
        JOIN documents d ON vc.document_id = d.id
        JOIN vector_chunks_768 v768 ON vc.id = v768.chunk_id
        WHERE query_embedding_768 IS NOT NULL
          AND vc.embedding_dimension = 768
          AND (preferred_dimension IS NULL OR preferred_dimension = 768)
          AND (v768.embedding <=> query_embedding_768) < (1 - match_threshold)
        
        UNION ALL
        
        -- 1024-dimensional results  
        SELECT 
            vc.id as chunk_id,
            vc.document_id,
            vc.content,
            d.title as document_title,
            (v1024.embedding <=> query_embedding_1024) as similarity,
            vc.pipeline_name,
            vc.pipeline_model,
            vc.embedding_dimension,
            vc.pipeline_metadata,
            1024 as vector_dimension,
            (
                CASE WHEN user_industry = ANY(ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'industry_relevance'))) THEN 0.3 ELSE 0.0 END +
                CASE WHEN user_size = ANY(ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'company_size_applicability'))) THEN 0.3 ELSE 0.0 END +
                CASE WHEN user_geography && ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'geographic_scope')) 
                      OR 'global' = ANY(ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'geographic_scope'))) THEN 0.2 ELSE 0.0 END +
                CASE WHEN user_frameworks && ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'frameworks')) THEN 0.2 ELSE 0.0 END
            ) as context_relevance_score
        FROM vector_chunks vc
        JOIN documents d ON vc.document_id = d.id
        JOIN vector_chunks_1024 v1024 ON vc.id = v1024.chunk_id
        WHERE query_embedding_1024 IS NOT NULL
          AND vc.embedding_dimension = 1024
          AND (preferred_dimension IS NULL OR preferred_dimension = 1024)
          AND (v1024.embedding <=> query_embedding_1024) < (1 - match_threshold)
        
        UNION ALL
        
        -- 1536-dimensional results
        SELECT 
            vc.id as chunk_id,
            vc.document_id,
            vc.content,
            d.title as document_title,
            (v1536.embedding <=> query_embedding_1536) as similarity,
            vc.pipeline_name,
            vc.pipeline_model,
            vc.embedding_dimension,
            vc.pipeline_metadata,
            1536 as vector_dimension,
            (
                CASE WHEN user_industry = ANY(ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'industry_relevance'))) THEN 0.3 ELSE 0.0 END +
                CASE WHEN user_size = ANY(ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'company_size_applicability'))) THEN 0.3 ELSE 0.0 END +
                CASE WHEN user_geography && ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'geographic_scope')) 
                      OR 'global' = ANY(ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'geographic_scope'))) THEN 0.2 ELSE 0.0 END +
                CASE WHEN user_frameworks && ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'frameworks')) THEN 0.2 ELSE 0.0 END
            ) as context_relevance_score
        FROM vector_chunks vc
        JOIN documents d ON vc.document_id = d.id
        JOIN vector_chunks_1536 v1536 ON vc.id = v1536.chunk_id
        WHERE query_embedding_1536 IS NOT NULL
          AND vc.embedding_dimension = 1536
          AND (preferred_dimension IS NULL OR preferred_dimension = 1536)
          AND (v1536.embedding <=> query_embedding_1536) < (1 - match_threshold)
          
        UNION ALL
        
        -- 3072-dimensional results
        SELECT 
            vc.id as chunk_id,
            vc.document_id,
            vc.content,
            d.title as document_title,
            (v3072.embedding <=> query_embedding_3072) as similarity,
            vc.pipeline_name,
            vc.pipeline_model,
            vc.embedding_dimension,
            vc.pipeline_metadata,
            3072 as vector_dimension,
            (
                CASE WHEN user_industry = ANY(ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'industry_relevance'))) THEN 0.3 ELSE 0.0 END +
                CASE WHEN user_size = ANY(ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'company_size_applicability'))) THEN 0.3 ELSE 0.0 END +
                CASE WHEN user_geography && ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'geographic_scope')) 
                      OR 'global' = ANY(ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'geographic_scope'))) THEN 0.2 ELSE 0.0 END +
                CASE WHEN user_frameworks && ARRAY(SELECT jsonb_array_elements_text(d.content_metadata->'frameworks')) THEN 0.2 ELSE 0.0 END
            ) as context_relevance_score
        FROM vector_chunks vc
        JOIN documents d ON vc.document_id = d.id
        JOIN vector_chunks_3072 v3072 ON vc.id = v3072.chunk_id
        WHERE query_embedding_3072 IS NOT NULL
          AND vc.embedding_dimension = 3072
          AND (preferred_dimension IS NULL OR preferred_dimension = 3072)
          AND (v3072.embedding <=> query_embedding_3072) < (1 - match_threshold)
    )
    SELECT 
        r.chunk_id,
        r.document_id,
        r.content,
        r.document_title,
        r.similarity,
        r.pipeline_name,
        r.pipeline_model,
        r.embedding_dimension,
        r.pipeline_metadata,
        r.context_relevance_score
    FROM multi_dim_results r
    ORDER BY 
        -- Combine similarity and context relevance, with dimension preference bonus
        (r.similarity * 0.7 + (1 - r.context_relevance_score) * 0.3) + 
        (CASE WHEN preferred_dimension IS NOT NULL AND r.vector_dimension = preferred_dimension THEN -0.1 ELSE 0.0 END)
    LIMIT match_count;
END;
$$;

-- Simplified dimension-specific search functions
CREATE OR REPLACE FUNCTION match_chunks_768(
    query_embedding VECTOR(768),
    org_context JSONB DEFAULT '{}'::JSONB,
    match_threshold FLOAT DEFAULT 0.7,
    match_count INT DEFAULT 10
)
RETURNS TABLE (
    chunk_id UUID,
    document_id UUID, 
    content TEXT,
    document_title TEXT,
    similarity FLOAT,
    pipeline_name TEXT,
    pipeline_model TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        vc.id,
        vc.document_id,
        vc.content,
        d.title,
        (v768.embedding <=> query_embedding) as similarity,
        vc.pipeline_name,
        vc.pipeline_model
    FROM vector_chunks vc
    JOIN documents d ON vc.document_id = d.id
    JOIN vector_chunks_768 v768 ON vc.id = v768.chunk_id
    WHERE (v768.embedding <=> query_embedding) < (1 - match_threshold)
    ORDER BY v768.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

CREATE OR REPLACE FUNCTION match_chunks_1024(
    query_embedding VECTOR(1024),
    org_context JSONB DEFAULT '{}'::JSONB,
    match_threshold FLOAT DEFAULT 0.7,
    match_count INT DEFAULT 10
)
RETURNS TABLE (
    chunk_id UUID,
    document_id UUID,
    content TEXT, 
    document_title TEXT,
    similarity FLOAT,
    pipeline_name TEXT,
    pipeline_model TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        vc.id,
        vc.document_id,
        vc.content,
        d.title,
        (v1024.embedding <=> query_embedding) as similarity,
        vc.pipeline_name,
        vc.pipeline_model
    FROM vector_chunks vc
    JOIN documents d ON vc.document_id = d.id
    JOIN vector_chunks_1024 v1024 ON vc.id = v1024.chunk_id
    WHERE (v1024.embedding <=> query_embedding) < (1 - match_threshold)
    ORDER BY v1024.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- ---------------------------------------------------------------------------
-- 5. Data migration helper functions
-- ---------------------------------------------------------------------------

-- Function to migrate existing 1536-dim embeddings from main table
CREATE OR REPLACE FUNCTION migrate_existing_embeddings_1536()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    migrated_count INTEGER := 0;
    chunk_record RECORD;
BEGIN
    -- Migrate existing embeddings to the 1536 table
    FOR chunk_record IN 
        SELECT id, embedding 
        FROM vector_chunks 
        WHERE embedding IS NOT NULL 
        AND embedding_dimension = 1536
        AND id NOT IN (SELECT chunk_id FROM vector_chunks_1536)
    LOOP
        INSERT INTO vector_chunks_1536 (chunk_id, embedding)
        VALUES (chunk_record.id, chunk_record.embedding)
        ON CONFLICT (chunk_id) DO NOTHING;
        
        migrated_count := migrated_count + 1;
    END LOOP;
    
    RETURN migrated_count;
END;
$$;

-- Function to store embedding in appropriate dimension table
CREATE OR REPLACE FUNCTION store_embedding_by_dimension(
    p_chunk_id UUID,
    p_embedding_array FLOAT[],
    p_dimension INTEGER
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
    IF p_dimension = 768 THEN
        INSERT INTO vector_chunks_768 (chunk_id, embedding)
        VALUES (p_chunk_id, p_embedding_array::VECTOR(768))
        ON CONFLICT (chunk_id) DO UPDATE SET
            embedding = EXCLUDED.embedding,
            created_at = NOW();
    ELSIF p_dimension = 1024 THEN
        INSERT INTO vector_chunks_1024 (chunk_id, embedding) 
        VALUES (p_chunk_id, p_embedding_array::VECTOR(1024))
        ON CONFLICT (chunk_id) DO UPDATE SET
            embedding = EXCLUDED.embedding,
            created_at = NOW();
    ELSIF p_dimension = 1536 THEN
        INSERT INTO vector_chunks_1536 (chunk_id, embedding)
        VALUES (p_chunk_id, p_embedding_array::VECTOR(1536))
        ON CONFLICT (chunk_id) DO UPDATE SET
            embedding = EXCLUDED.embedding,
            created_at = NOW();
    ELSIF p_dimension = 3072 THEN
        INSERT INTO vector_chunks_3072 (chunk_id, embedding)
        VALUES (p_chunk_id, p_embedding_array::VECTOR(3072))
        ON CONFLICT (chunk_id) DO UPDATE SET
            embedding = EXCLUDED.embedding,
            created_at = NOW();
    ELSE
        RAISE EXCEPTION 'Unsupported embedding dimension: %. Supported dimensions: 768, 1024, 1536, 3072', p_dimension;
    END IF;
    
    RETURN TRUE;
END;
$$;

-- ---------------------------------------------------------------------------
-- 6. RLS Policies for new tables
-- ---------------------------------------------------------------------------

-- Enable RLS for new embedding tables
ALTER TABLE vector_chunks_768 ENABLE ROW LEVEL SECURITY;
ALTER TABLE vector_chunks_1024 ENABLE ROW LEVEL SECURITY; 
ALTER TABLE vector_chunks_1536 ENABLE ROW LEVEL SECURITY;
ALTER TABLE vector_chunks_3072 ENABLE ROW LEVEL SECURITY;

-- Public read policies (consistent with main vector_chunks table)
CREATE POLICY vector_chunks_768_public_read ON vector_chunks_768 FOR SELECT USING (true);
CREATE POLICY vector_chunks_1024_public_read ON vector_chunks_1024 FOR SELECT USING (true);
CREATE POLICY vector_chunks_1536_public_read ON vector_chunks_1536 FOR SELECT USING (true);
CREATE POLICY vector_chunks_3072_public_read ON vector_chunks_3072 FOR SELECT USING (true);

-- Admin write policies
CREATE POLICY vector_chunks_768_admin_write ON vector_chunks_768
    FOR ALL USING ((current_setting('request.jwt.claims', true)::jsonb ->> 'is_admin')::boolean = true);
CREATE POLICY vector_chunks_1024_admin_write ON vector_chunks_1024
    FOR ALL USING ((current_setting('request.jwt.claims', true)::jsonb ->> 'is_admin')::boolean = true);
CREATE POLICY vector_chunks_1536_admin_write ON vector_chunks_1536
    FOR ALL USING ((current_setting('request.jwt.claims', true)::jsonb ->> 'is_admin')::boolean = true);
CREATE POLICY vector_chunks_3072_admin_write ON vector_chunks_3072
    FOR ALL USING ((current_setting('request.jwt.claims', true)::jsonb ->> 'is_admin')::boolean = true);

-- ---------------------------------------------------------------------------
-- 7. Update existing data with default pipeline information
-- ---------------------------------------------------------------------------

-- Update existing chunks with default pipeline metadata
UPDATE vector_chunks SET
    pipeline_name = 'text-embedding-ada-002',
    pipeline_model = 'text-embedding-ada-002',
    embedding_dimension = 1536,
    pipeline_version = '1.0.0',
    pipeline_provider = 'openai',
    pipeline_metadata = '{
        "quality_tier": "high",
        "is_local": false,
        "context_length": 8192,
        "cost_per_1m_tokens": 0.10,
        "inference_time_ms": 300
    }'::JSONB
WHERE pipeline_name = 'text-embedding-ada-002';  -- Only update if still default

-- Migrate existing embeddings to 1536 table
SELECT migrate_existing_embeddings_1536();

-- ---------------------------------------------------------------------------
-- 8. Comments and documentation
-- ---------------------------------------------------------------------------

COMMENT ON TABLE vector_chunks_768 IS 'Embeddings storage for 768-dimensional vectors (all-mpnet-base-v2, placeholder)';
COMMENT ON TABLE vector_chunks_1024 IS 'Embeddings storage for 1024-dimensional vectors (BGE-Large-EN-v1.5)';
COMMENT ON TABLE vector_chunks_1536 IS 'Embeddings storage for 1536-dimensional vectors (OpenAI text-embedding-3-small, ada-002)';
COMMENT ON TABLE vector_chunks_3072 IS 'Embeddings storage for 3072-dimensional vectors (OpenAI text-embedding-3-large)';

COMMENT ON FUNCTION match_chunks_multi_dimensional IS 'Multi-dimensional vector search supporting all pipeline embedding dimensions';
COMMENT ON FUNCTION store_embedding_by_dimension IS 'Helper function to store embeddings in correct dimension-specific table';

COMMIT;

-- End of Multi-dimensional Embeddings Migration
-- File: arioncomply-v1/ai-backend/supabase_migrations/schemas/multi_dimensional_embeddings_migration.sql
-- File: arioncomply-v1/ai-backend/supabase_migrations/schemas/multi_dimensional_embeddings_migration.sql
-- File Description: Adds multi-dimensional embedding tables and related constraints
-- Purpose: Support different vector dimensions in a normalized fashion
-- Security/RLS: Ensure org_id scoping and policies applied consistently
