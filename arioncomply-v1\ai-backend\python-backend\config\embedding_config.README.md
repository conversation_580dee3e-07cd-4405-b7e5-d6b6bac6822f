File: arioncomply-v1/ai-backend/python-backend/config/embedding_config.README.md
File Description: Companion README for embedding_config.json
Purpose: Document keys and usage for the multi-pipeline embedding configuration

This README documents `embedding_config.json`, which configures the embedding subsystem.
- default_pipeline: initial pipeline name to use
- selection_strategy: one of quality_first | security_first | performance_first | cloud_enabled
- auto_fallback: enable automatic fallback on failures
- health_check_interval_seconds: periodic health check cadence
- pipelines: map of pipeline definitions with class_path, enabled, priority, config, metadata
- deployment_profiles: presets for development/production/security_strict/cloud_enabled

Security notes
- Cloud pipelines (openai/openai-large) are disabled by default and require explicit approval.
- Ensure environment variables and secrets are managed securely when enabling cloud use.
