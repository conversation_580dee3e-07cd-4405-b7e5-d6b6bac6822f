<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentScriptType="application/ecmascript" contentStyleType="text/css" height="1030px" preserveAspectRatio="none" style="width:2008px;height:1030px;" version="1.1" viewBox="0 0 2008 1030" width="2008px" zoomAndPan="magnify"><defs/><g><text fill="#000000" font-family="sans-serif" font-size="18" lengthAdjust="spacingAndGlyphs" textLength="426" x="789.75" y="26.708">RAG Generate Flow with LLM 2.0 Preprocessing</text><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="23" x2="23" y1="117.25" y2="942.9688"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="202" x2="202" y1="117.25" y2="942.9688"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="401.5" x2="401.5" y1="117.25" y2="942.9688"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="510.5" x2="510.5" y1="117.25" y2="942.9688"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="584.5" x2="584.5" y1="117.25" y2="942.9688"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="849" x2="849" y1="117.25" y2="942.9688"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="1082" x2="1082" y1="117.25" y2="942.9688"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="1224" x2="1224" y1="117.25" y2="942.9688"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="1371" x2="1371" y1="117.25" y2="942.9688"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="1511" x2="1511" y1="117.25" y2="942.9688"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="1668.5" x2="1668.5" y1="117.25" y2="942.9688"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="1809.5" x2="1809.5" y1="117.25" y2="942.9688"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="1945.5" x2="1945.5" y1="117.25" y2="942.9688"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="13" x="13.5" y="113.9482">UI</text><ellipse cx="23" cy="47.9531" fill="#F8F8F8" rx="8" ry="8" style="stroke: #383838; stroke-width: 2.0;"/><path d="M23,55.9531 L23,82.9531 M10,63.9531 L36,63.9531 M23,82.9531 L10,97.9531 M23,82.9531 L36,97.9531 " fill="none" style="stroke: #383838; stroke-width: 2.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="13" x="13.5" y="954.9639">UI</text><ellipse cx="23" cy="968.2656" fill="#F8F8F8" rx="8" ry="8" style="stroke: #383838; stroke-width: 2.0;"/><path d="M23,976.2656 L23,1003.2656 M10,984.2656 L36,984.2656 M23,1003.2656 L10,1018.2656 M23,1003.2656 L36,1018.2656 " fill="none" style="stroke: #383838; stroke-width: 2.0;"/><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="50" x="177" y="85.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="36" x="184" y="105.9482">Edge</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="50" x="177" y="941.9688"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="36" x="184" y="961.9639">Edge</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="107" x="348.5" y="85.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="93" x="355.5" y="105.9482">API /generate</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="107" x="348.5" y="941.9688"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="93" x="355.5" y="961.9639">API /generate</text><path d="M486.5,90.9531 L535.5,90.9531 C540.5,90.9531 540.5,104.1016 540.5,104.1016 C540.5,104.1016 540.5,117.25 535.5,117.25 L486.5,117.25 C481.5,117.25 481.5,104.1016 481.5,104.1016 C481.5,104.1016 481.5,90.9531 486.5,90.9531 " fill="#F8F8F8" style="stroke: #383838; stroke-width: 2.0;"/><path d="M535.5,90.9531 C530.5,90.9531 530.5,104.1016 530.5,104.1016 C530.5,117.25 535.5,117.25 535.5,117.25 " fill="none" style="stroke: #383838; stroke-width: 2.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="39" x="486.5" y="108.9482">Redis</text><path d="M486.5,941.9688 L535.5,941.9688 C540.5,941.9688 540.5,955.1172 540.5,955.1172 C540.5,955.1172 540.5,968.2656 535.5,968.2656 L486.5,968.2656 C481.5,968.2656 481.5,955.1172 481.5,955.1172 C481.5,955.1172 481.5,941.9688 486.5,941.9688 " fill="#F8F8F8" style="stroke: #383838; stroke-width: 2.0;"/><path d="M535.5,941.9688 C530.5,941.9688 530.5,955.1172 530.5,955.1172 C530.5,968.2656 535.5,968.2656 535.5,968.2656 " fill="none" style="stroke: #383838; stroke-width: 2.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="39" x="486.5" y="959.9639">Redis</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="63" x="553.5" y="85.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="49" x="560.5" y="105.9482">Worker</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="63" x="553.5" y="941.9688"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="49" x="560.5" y="961.9639">Worker</text><rect fill="#F8F8F8" height="46.5938" style="stroke: #383838; stroke-width: 1.5;" width="314" x="694" y="65.6563"/><rect fill="#F8F8F8" height="46.5938" style="stroke: #383838; stroke-width: 1.5;" width="314" x="690" y="69.6563"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="120" x="787" y="89.6514">Preproc (LLM 2.0)</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="300" x="697" y="105.9482">normalize • intent • entities • rules • redact</text><rect fill="#F8F8F8" height="46.5938" style="stroke: #383838; stroke-width: 1.5;" width="314" x="694" y="941.9688"/><rect fill="#F8F8F8" height="46.5938" style="stroke: #383838; stroke-width: 1.5;" width="314" x="690" y="945.9688"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="120" x="787" y="965.9639">Preproc (LLM 2.0)</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="300" x="697" y="982.2607">normalize • intent • entities • rules • redact</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="123" x="1018" y="113.9482">Chroma (optional)</text><path d="M1064.5,64.9531 C1064.5,54.9531 1082.5,54.9531 1082.5,54.9531 C1082.5,54.9531 1100.5,54.9531 1100.5,64.9531 L1100.5,90.9531 C1100.5,100.9531 1082.5,100.9531 1082.5,100.9531 C1082.5,100.9531 1064.5,100.9531 1064.5,90.9531 L1064.5,64.9531 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M1064.5,64.9531 C1064.5,74.9531 1082.5,74.9531 1082.5,74.9531 C1082.5,74.9531 1100.5,74.9531 1100.5,64.9531 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="123" x="1018" y="954.9639">Chroma (optional)</text><path d="M1064.5,968.2656 C1064.5,958.2656 1082.5,958.2656 1082.5,958.2656 C1082.5,958.2656 1100.5,958.2656 1100.5,968.2656 L1100.5,994.2656 C1100.5,1004.2656 1082.5,1004.2656 1082.5,1004.2656 C1082.5,1004.2656 1064.5,1004.2656 1064.5,994.2656 L1064.5,968.2656 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M1064.5,968.2656 C1064.5,978.2656 1082.5,978.2656 1082.5,978.2656 C1082.5,978.2656 1100.5,978.2656 1100.5,968.2656 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="128" x="1157" y="113.9482">Postgres/pgvector</text><path d="M1206,64.9531 C1206,54.9531 1224,54.9531 1224,54.9531 C1224,54.9531 1242,54.9531 1242,64.9531 L1242,90.9531 C1242,100.9531 1224,100.9531 1224,100.9531 C1224,100.9531 1206,100.9531 1206,90.9531 L1206,64.9531 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M1206,64.9531 C1206,74.9531 1224,74.9531 1224,74.9531 C1224,74.9531 1242,74.9531 1242,64.9531 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="128" x="1157" y="954.9639">Postgres/pgvector</text><path d="M1206,968.2656 C1206,958.2656 1224,958.2656 1224,958.2656 C1224,958.2656 1242,958.2656 1242,968.2656 L1242,994.2656 C1242,1004.2656 1224,1004.2656 1224,1004.2656 C1224,1004.2656 1206,1004.2656 1206,994.2656 L1206,968.2656 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M1206,968.2656 C1206,978.2656 1224,978.2656 1224,978.2656 C1224,978.2656 1242,978.2656 1242,968.2656 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="140" x="1301" y="85.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="126" x="1308" y="105.9482">Prompt Composer</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="140" x="1301" y="941.9688"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="126" x="1308" y="961.9639">Prompt Composer</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="121" x="1451" y="85.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="107" x="1458" y="105.9482">Provider Router</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="121" x="1451" y="941.9688"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="107" x="1458" y="961.9639">Provider Router</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="114" x="1611.5" y="85.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="100" x="1618.5" y="105.9482">Local LLM (/v1)</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="114" x="1611.5" y="941.9688"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="100" x="1618.5" y="961.9639">Local LLM (/v1)</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="149" x="1735.5" y="85.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="135" x="1742.5" y="105.9482">Cloud LLM (fallback)</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="149" x="1735.5" y="941.9688"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="135" x="1742.5" y="961.9639">Cloud LLM (fallback)</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="103" x="1894.5" y="85.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="89" x="1901.5" y="105.9482">Verifier / HITL</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="103" x="1894.5" y="941.9688"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="89" x="1901.5" y="961.9639">Verifier / HITL</text><polygon fill="#383838" points="190,144.3828,200,148.3828,190,152.3828,194,148.3828" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="23" x2="196" y1="148.3828" y2="148.3828"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="155" x="30" y="143.3169">POST conversation.send</text><polygon fill="#383838" points="390,173.5156,400,177.5156,390,181.5156,394,177.5156" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="202" x2="396" y1="177.5156" y2="177.5156"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="176" x="209" y="172.4497">forward (auth, ids, headers)</text><polygon fill="#383838" points="499,202.6484,509,206.6484,499,210.6484,503,206.6484" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="402" x2="505" y1="206.6484" y2="206.6484"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="85" x="409" y="201.5825">enqueue(job)</text><polygon fill="#383838" points="573,231.7813,583,235.7813,573,239.7813,577,235.7813" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="511" x2="579" y1="235.7813" y2="235.7813"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="19" x="518" y="230.7153">job</text><polygon fill="#383838" points="837,276.0469,847,280.0469,837,284.0469,841,280.0469" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="585" x2="843" y1="280.0469" y2="280.0469"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="162" x="592" y="259.8481">normalize, classify intent,</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="240" x="592" y="274.981">extract entities, apply rules, redact PII</text><polygon fill="#383838" points="596,305.1797,586,309.1797,596,313.1797,592,309.1797" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 2.0,2.0;" x1="590" x2="848" y1="309.1797" y2="309.1797"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="226" x="602" y="304.1138">{intent, entities, filters, redactions}</text><line style="stroke: #383838; stroke-width: 1.0;" x1="585" x2="627" y1="338.3125" y2="338.3125"/><line style="stroke: #383838; stroke-width: 1.0;" x1="627" x2="627" y1="338.3125" y2="351.3125"/><line style="stroke: #383838; stroke-width: 1.0;" x1="586" x2="627" y1="351.3125" y2="351.3125"/><polygon fill="#383838" points="596,347.3125,586,351.3125,596,355.3125,592,351.3125" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="200" x="592" y="333.2466">decide path (rules-only vs RAG)</text><polygon fill="#383838" points="1070.5,376.4453,1080.5,380.4453,1070.5,384.4453,1074.5,380.4453" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="585" x2="1076.5" y1="380.4453" y2="380.4453"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="159" x="592" y="375.3794">query (filters) [if present]</text><polygon fill="#383838" points="1212,405.5781,1222,409.5781,1212,413.5781,1216,409.5781" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="585" x2="1218" y1="409.5781" y2="409.5781"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="147" x="592" y="404.5122">query (filters) [fallback]</text><line style="stroke: #383838; stroke-width: 1.0;" x1="585" x2="627" y1="438.7109" y2="438.7109"/><line style="stroke: #383838; stroke-width: 1.0;" x1="627" x2="627" y1="438.7109" y2="451.7109"/><line style="stroke: #383838; stroke-width: 1.0;" x1="586" x2="627" y1="451.7109" y2="451.7109"/><polygon fill="#383838" points="596,447.7109,586,451.7109,596,455.7109,592,451.7109" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="171" x="592" y="433.645">select top-k, build citations</text><polygon fill="#383838" points="1359,491.9766,1369,495.9766,1359,499.9766,1363,495.9766" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="585" x2="1365" y1="495.9766" y2="495.9766"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="101" x="592" y="475.7778">compile prompt</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="221" x="592" y="490.9106">(instructions + context + controls)</text><polygon fill="#383838" points="596,521.1094,586,525.1094,596,529.1094,592,525.1094" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 2.0,2.0;" x1="590" x2="1370" y1="525.1094" y2="525.1094"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="47" x="602" y="520.0435">prompt</text><polygon fill="#383838" points="1499.5,550.2422,1509.5,554.2422,1499.5,558.2422,1503.5,554.2422" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="585" x2="1505.5" y1="554.2422" y2="554.2422"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="240" x="592" y="549.1763">select provider (local first; guard rails)</text><polygon fill="#383838" points="1656.5,579.375,1666.5,583.375,1656.5,587.375,1660.5,583.375" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="1511.5" x2="1662.5" y1="583.375" y2="583.375"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="133" x="1518.5" y="578.3091">/v1/chat/completions</text><polygon fill="#383838" points="1522.5,608.5078,1512.5,612.5078,1522.5,616.5078,1518.5,612.5078" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 2.0,2.0;" x1="1516.5" x2="1667.5" y1="612.5078" y2="612.5078"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="93" x="1528.5" y="607.4419">stream tokens</text><polygon fill="#383838" points="596,637.6406,586,641.6406,596,645.6406,592,641.6406" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 2.0,2.0;" x1="590" x2="1510.5" y1="641.6406" y2="641.6406"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="46" x="602" y="636.5747">stream</text><path d="M1387,654.6406 L1387,679.6406 L1636,679.6406 L1636,664.6406 L1626,654.6406 L1387,654.6406 " fill="#ECECEC" style="stroke: #383838; stroke-width: 1.0;"/><path d="M1626,654.6406 L1626,664.6406 L1636,664.6406 L1626,654.6406 " fill="#ECECEC" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="228" x="1393" y="671.7075">on guard/fail → anonymized fallback</text><polygon fill="#383838" points="1798,701.9063,1808,705.9063,1798,709.9063,1802,705.9063" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="1511.5" x2="1804" y1="705.9063" y2="705.9063"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="120" x="1518.5" y="700.8403">chat (anonymized)</text><polygon fill="#383838" points="1522.5,731.0391,1512.5,735.0391,1522.5,739.0391,1518.5,735.0391" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 2.0,2.0;" x1="1516.5" x2="1809" y1="735.0391" y2="735.0391"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="46" x="1528.5" y="729.9731">stream</text><polygon fill="#383838" points="596,760.1719,586,764.1719,596,768.1719,592,764.1719" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 2.0,2.0;" x1="590" x2="1510.5" y1="764.1719" y2="764.1719"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="46" x="602" y="759.106">stream</text><polygon fill="#383838" points="1934,789.3047,1944,793.3047,1934,797.3047,1938,793.3047" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="585" x2="1940" y1="793.3047" y2="793.3047"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="143" x="592" y="788.2388">optional review/checks</text><polygon fill="#383838" points="596,818.4375,586,822.4375,596,826.4375,592,822.4375" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 2.0,2.0;" x1="590" x2="1945" y1="822.4375" y2="822.4375"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="95" x="602" y="817.3716">approve / edits</text><polygon fill="#383838" points="413,862.7031,403,866.7031,413,870.7031,409,866.7031" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="407" x2="584" y1="866.7031" y2="866.7031"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="141" x="419" y="846.5044">progress/logs + result</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="159" x="419" y="861.6372">(citations, model, usage)</text><polygon fill="#383838" points="213,891.8359,203,895.8359,213,899.8359,209,895.8359" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="207" x2="401" y1="895.8359" y2="895.8359"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="125" x="219" y="890.77">stream/proxy result</text><polygon fill="#383838" points="34,920.9688,24,924.9688,34,928.9688,30,924.9688" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="28" x2="201" y1="924.9688" y2="924.9688"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="59" x="40" y="919.9028">response</text><!--MD5=[935960eb152638aa6718ea610083be25]
@startuml RAG Generate (LLM2.0)
title RAG Generate Flow with LLM 2.0 Preprocessing
skinparam shadowing false
skinparam monochrome true

actor UI
participant Edge
participant "API /generate" as API
queue Redis
participant Worker as W
collections "Preproc (LLM 2.0)\nnormalize • intent • entities • rules • redact" as PRE
database "Chroma (optional)" as Chroma
database "Postgres/pgvector" as PG
participant "Prompt Composer" as PC
participant "Provider Router" as PR
participant "Local LLM (/v1)" as LLM
participant "Cloud LLM (fallback)" as GLLM
participant "Verifier / HITL" as HITL

UI -> Edge : POST conversation.send
Edge -> API : forward (auth, ids, headers)
API -> Redis : enqueue(job)
Redis -> W : job

W -> PRE : normalize, classify intent,\nextract entities, apply rules, redact PII
PRE - -> W : {intent, entities, filters, redactions}

W -> W : decide path (rules-only vs RAG)

W -> Chroma : query (filters) [if present]
W -> PG : query (filters) [fallback]
W -> W : select top-k, build citations

W -> PC : compile prompt\n(instructions + context + controls)
PC - -> W : prompt

W -> PR : select provider (local first; guard rails)
PR -> LLM : /v1/chat/completions
LLM - -> PR : stream tokens
PR - -> W : stream

note over PR
  on guard/fail → anonymized fallback
end note

PR -> GLLM : chat (anonymized)
GLLM - -> PR : stream
PR - -> W : stream

W -> HITL : optional review/checks
HITL - -> W : approve / edits

W -> API : progress/logs + result\n(citations, model, usage)
API -> Edge : stream/proxy result
Edge -> UI : response

@enduml

PlantUML version 1.2020.02(Sun Mar 01 10:22:07 UTC 2020)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Java Version: 17.0.16+8
Operating System: Linux
Default Encoding: UTF-8
Language: en
Country: null
--></g></svg>