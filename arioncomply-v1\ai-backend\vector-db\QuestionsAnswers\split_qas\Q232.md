id: Q232
query: >-
  How do we handle regulatory investigations or inquiries?
packs:
  - "GDPR:2016"
primary_ids:
  - "GDPR:2016/Art.58"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Report"
flags:
  - "LOCAL LAW_CHECK"
sources:
  - title: "GDPR — Powers of Supervisory Authorities"
    id: "GDPR:2016/Art.58"
    locator: "Article 58"
ui:
  cards_hint:
    - "Investigation response plan"
  actions:
    - type: "start_workflow"
      target: "investigation_response"
      label: "Prepare Investigation Response"
    - type: "open_register"
      target: "inquiry_log"
      label: "View Inquiries"
output_mode: "both"
graph_required: false
notes: "Document all communications and maintain an audit trail"
---
### 232) How do we handle regulatory investigations or inquiries?

**Standard terms)**  
- **Supervisory powers (Art.58):** authorities can inspect, request info, and impose measures.

**Plain-English answer**  
Record the inquiry in the **Inquiry Log**, gather evidence via the **Investigation Response Plan**, designate a liaison, and respond within legal timeframes. Keep detailed audit trails of all interactions.

**Applies to**  
- **Primary:** GDPR Article 58

**Why it matters**  
Professional, timely responses reduce risk of enforcement actions.

**Do next in our platform**  
- Launch **Investigation Response** workflow.  
- Tag all documents and communications in the **Inquiry Log**.

**How our platform will help**  
- **[Workflow]** Step-by-step evidence collection and submission templates.  
- **[Report]** Tracks response status and regulator feedback.

**Likely follow-ups**  
- How do we escalate if authority requests exceed scope?

**Sources**  
- GDPR Article 58

**Legal note:** Coordinate with legal counsel on responses.  
