# File: arioncomply-v1/ai-backend/python-backend/services/embedding/embedding_service.py
# File Description: High-level embedding service orchestrator for multi-pipeline architecture
# Purpose: Initialize pipelines, select/fallback, and provide audited embedding operations
"""
Main embedding service orchestrator for multi-pipeline architecture.

Provides high-level interface for embedding operations with automatic
pipeline selection, fallback, health monitoring, and complete audit trail.
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any

from .pipeline_interface import EmbeddingResult
from .pipeline_registry import PipelineRegistry
from .pipelines.placeholder_pipeline import PlaceholderPipeline
from .pipelines.bge_onnx_pipeline import BGEOnnxPipeline
from .pipelines.mpnet_pipeline import MPNetPipeline
from .pipelines.openai_pipeline import OpenAIPipeline

logger = logging.getLogger(__name__)


class EmbeddingService:
    """
    Main embedding service providing high-level embedding operations.
    
    Features:
    - Automatic pipeline selection and fallback
    - Health monitoring and error recovery
    - Complete operation audit trail
    - Multi-dimensional vector support
    - Configuration-driven behavior
    """
    
    def __init__(self, config_path: Optional[Path] = None):
        """Construct an EmbeddingService with optional config path."""
        self.config_path = config_path
        self.registry = PipelineRegistry(config_path)
        self._initialized = False
        self._lock = asyncio.Lock()
        
        # Pipeline class mapping
        self._pipeline_classes = {
            "placeholder": PlaceholderPipeline,
            "bge-large-onnx": BGEOnnxPipeline,
            "all-mpnet-base-v2": MPNetPipeline,
            "openai": OpenAIPipeline
        }
    
    async def initialize(self, config: Optional[Dict] = None) -> None:
        """Initialize the embedding service."""
        if self._initialized:
            return
        
        async with self._lock:
            if self._initialized:  # Double-check
                return
            
            try:
                # Load configuration
                if config is None:
                    config = await self._load_default_config()
                
                # Initialize registry
                await self.registry.initialize(config)
                
                # Initialize pipelines
                await self._initialize_pipelines(config.get("pipelines", {}))
                
                # Select initial pipeline
                initial_pipeline = await self.registry.select_best_pipeline()
                if initial_pipeline:
                    await self.registry.set_current_pipeline(initial_pipeline)
                    logger.info(f"Embedding service initialized with pipeline: {initial_pipeline}")
                else:
                    logger.warning("No healthy pipelines available during initialization")
                
                self._initialized = True
                
            except Exception as e:
                logger.error(f"Failed to initialize embedding service: {e}")
                raise
    
    async def _load_default_config(self) -> Dict:
        """Load default configuration."""
        return {
            "default_pipeline": "bge-large-onnx",
            "selection_strategy": "quality_first",
            "auto_fallback": True,
            "health_check_interval": 300,
            "max_consecutive_failures": 3,
            "fallback_order": ["bge-large-onnx", "all-mpnet-base-v2", "placeholder"],
            "pipelines": {
                "placeholder": {
                    "class_path": "services.embedding.pipelines.placeholder_pipeline.PlaceholderPipeline",
                    "enabled": True,
                    "priority": 300,
                    "config": {}
                },
                "bge-large-onnx": {
                    "class_path": "services.embedding.pipelines.bge_onnx_pipeline.BGEOnnxPipeline",
                    "enabled": True,
                    "priority": 100,
                    "config": {
                        "cache_dir": "~/llms/models/bge",
                        "quantization": "int8",
                        "device": "cpu"
                    }
                },
                "all-mpnet-base-v2": {
                    "class_path": "services.embedding.pipelines.mpnet_pipeline.MPNetPipeline",
                    "enabled": True,
                    "priority": 200,
                    "config": {
                        "device": "cpu",
                        "batch_size": 32
                    }
                },
                "openai": {
                    "class_path": "services.embedding.pipelines.openai_pipeline.OpenAIPipeline",
                    "enabled": False,  # Disabled by default for security
                    "priority": 150,
                    "config": {
                        "model_name": "text-embedding-3-small",
                        "security_approved": False,
                        "explicit_approval_required": True
                    }
                }
            }
        }
    
    async def _initialize_pipelines(self, pipelines_config: Dict) -> None:
        """Initialize pipeline instances."""
        for pipeline_name, pipeline_config in pipelines_config.items():
            if not pipeline_config.get("enabled", True):
                continue
            
            try:
                # Get pipeline class
                pipeline_class = self._pipeline_classes.get(pipeline_name)
                if not pipeline_class:
                    logger.warning(f"Unknown pipeline class for '{pipeline_name}', skipping")
                    continue
                
                # Create pipeline instance
                pipeline_instance = pipeline_class(pipeline_config.get("config", {}))
                
                # Register with registry
                await self.registry.register_pipeline(pipeline_instance)
                
                logger.info(f"Pipeline '{pipeline_name}' initialized successfully")
                
            except Exception as e:
                logger.error(f"Failed to initialize pipeline '{pipeline_name}': {e}")
                # Continue with other pipelines
    
    async def embed_texts(
        self, 
        texts: List[str], 
        pipeline_name: Optional[str] = None,
        trace_id: Optional[str] = None
    ) -> EmbeddingResult:
        """
        Generate embeddings for input texts.
        
        Args:
            texts: List of texts to embed
            pipeline_name: Specific pipeline to use (optional, uses current if None)
            trace_id: Trace ID for operation tracking
            
        Returns:
            EmbeddingResult with complete audit trail
        """
        if not self._initialized:
            await self.initialize()
        
        # Validate inputs
        if not texts:
            raise ValueError("Input texts list cannot be empty")
        
        # Get pipeline
        if pipeline_name:
            pipeline = await self.registry.get_pipeline(pipeline_name)
            if not pipeline:
                raise ValueError(f"Pipeline '{pipeline_name}' not found or not loaded")
        else:
            pipeline = await self.registry.get_pipeline()
            if not pipeline:
                # Attempt to select and load a pipeline
                best_pipeline = await self.registry.select_best_pipeline()
                if best_pipeline and await self.registry.set_current_pipeline(best_pipeline):
                    pipeline = await self.registry.get_pipeline()
                
                if not pipeline:
                    raise RuntimeError("No healthy pipelines available for embedding")
        
        # Generate embeddings with automatic fallback
        try:
            logger.debug(f"Generating embeddings with pipeline '{pipeline.name}' for {len(texts)} texts")
            result = await pipeline.embed_texts(texts, trace_id=trace_id)
            
            if result.success:
                logger.debug(f"Embedding successful: {result.processing_time_ms:.1f}ms")
                return result
            else:
                logger.warning(f"Embedding failed with pipeline '{pipeline.name}': {result.error_message}")
                
                # Attempt fallback if auto_fallback is enabled
                if await self.registry.attempt_fallback():
                    fallback_pipeline = await self.registry.get_pipeline()
                    if fallback_pipeline and fallback_pipeline.name != pipeline.name:
                        logger.info(f"Retrying with fallback pipeline '{fallback_pipeline.name}'")
                        return await fallback_pipeline.embed_texts(texts, trace_id=trace_id)
                
                # Return original failed result if fallback unsuccessful
                return result
                
        except Exception as e:
            logger.error(f"Embedding operation failed: {e}")
            
            # Attempt fallback on exception
            if await self.registry.attempt_fallback():
                fallback_pipeline = await self.registry.get_pipeline()
                if fallback_pipeline and fallback_pipeline.name != pipeline.name:
                    logger.info(f"Retrying with fallback pipeline '{fallback_pipeline.name}' after exception")
                    try:
                        return await fallback_pipeline.embed_texts(texts, trace_id=trace_id)
                    except Exception as fallback_error:
                        logger.error(f"Fallback also failed: {fallback_error}")
            
            raise
    
    async def get_current_pipeline_info(self) -> Optional[Dict]:
        """Get information about the current active pipeline."""
        if not self._initialized:
            await self.initialize()
        
        pipeline = await self.registry.get_pipeline()
        if pipeline:
            return {
                "name": pipeline.name,
                "metadata": pipeline.metadata.__dict__,
                "is_loaded": pipeline.is_loaded
            }
        return None
    
    async def list_available_pipelines(self) -> List[Dict]:
        """List all available pipelines with their status."""
        if not self._initialized:
            await self.initialize()
        
        system_status = await self.registry.get_system_status()
        
        pipelines_info = []
        for name, status in system_status.get("pipelines", {}).items():
            pipeline_info = {
                "name": name,
                "enabled": status.get("enabled", False),
                "status": status.get("status", "unknown"),
                "priority": status.get("priority", 999),
                "consecutive_failures": status.get("consecutive_failures", 0),
                "metadata": status.get("metadata")
            }
            pipelines_info.append(pipeline_info)
        
        # Sort by priority
        pipelines_info.sort(key=lambda x: x["priority"])
        
        return pipelines_info
    
    async def switch_pipeline(self, pipeline_name: str) -> bool:
        """
        Switch to a specific pipeline.
        
        Args:
            pipeline_name: Name of pipeline to switch to
            
        Returns:
            True if switch successful, False otherwise
        """
        if not self._initialized:
            await self.initialize()
        
        return await self.registry.set_current_pipeline(pipeline_name)
    
    async def get_system_status(self) -> Dict:
        """Get comprehensive system status."""
        if not self._initialized:
            return {"status": "not_initialized"}
        
        registry_status = await self.registry.get_system_status()
        
        return {
            "initialized": self._initialized,
            "current_pipeline": registry_status.get("active_pipeline"),
            "total_pipelines": registry_status.get("total_pipelines", 0),
            "healthy_pipelines": registry_status.get("healthy_pipelines", 0),
            "registry": registry_status
        }
    
    async def health_check_all_pipelines(self) -> Dict[str, Dict]:
        """Perform health check on all registered pipelines."""
        if not self._initialized:
            await self.initialize()
        
        results = {}
        
        # Get all pipeline instances
        for name, pipeline in self.registry.pipelines.items():
            try:
                health_result = await pipeline.health_check()
                results[name] = {
                    "status": health_result.status.value,
                    "timestamp": health_result.timestamp.isoformat(),
                    "load_time_ms": health_result.load_time_ms,
                    "test_inference_time_ms": health_result.test_inference_time_ms,
                    "memory_usage_mb": health_result.memory_usage_mb,
                    "error_message": health_result.error_message,
                    "is_model_loaded": health_result.is_model_loaded,
                    "dependencies_available": health_result.dependencies_available
                }
            except Exception as e:
                results[name] = {
                    "status": "failed",
                    "error": str(e)
                }
        
        return results
    
    async def shutdown(self) -> None:
        """Shutdown the embedding service and cleanup resources."""
        logger.info("Shutting down embedding service")
        
        if self.registry:
            await self.registry.shutdown()
        
        self._initialized = False
        logger.info("Embedding service shutdown complete")


# Global service instance
_embedding_service: Optional[EmbeddingService] = None


async def get_embedding_service() -> EmbeddingService:
    """Get the global embedding service instance."""
    global _embedding_service
    
    if _embedding_service is None:
        _embedding_service = EmbeddingService()
        await _embedding_service.initialize()
    
    return _embedding_service


async def embed_texts(texts: List[str], pipeline_name: Optional[str] = None, trace_id: Optional[str] = None) -> EmbeddingResult:
    """
    Convenience function for generating embeddings.
    
    This is the primary interface for embedding operations throughout the system.
    """
    service = await get_embedding_service()
    return await service.embed_texts(texts, pipeline_name=pipeline_name, trace_id=trace_id)
