<!-- File: arioncomply-v1/supabase/functions/README.md -->
# Supabase Edge Functions

This folder is the source of truth for Edge functions. Use the Supabase CLI to serve and deploy.

Functions
- ai-conversation-start: starts a chat session (AI gateway)
- ai-conversation-send: sends a user message and returns assistant reply (AI gateway)
- ai-conversation-stream: SSE token stream (AI gateway)
- compliance-proxy: Unified proxy that accepts simple and rich bodies; can forward to AI backend or call providers directly
- ai-message-feedback: records thumbs feedback on assistant messages (org-scoped)
- ui-suggestions-defaults: returns curated default follow-up suggestions
- ui-shortcuts: returns org-scoped UI slash shortcuts
- app-audit-read: org-scoped audit reader (requests + events) with pagination/filters

Shared Helpers
- _shared/*: jwt parsing, traceparent, logging, envelopes, validation, forwarder config

Run Locally
```bash
supabase functions serve ai-conversation-start ai-conversation-send ai-conversation-stream
```

Deploy
```bash
supabase functions deploy ai-conversation-start ai-conversation-send ai-conversation-stream app-audit-read
```

See also
- `DEPLOY.md` at repo root for environment variables, backend, and migrations

Dev tips
- Logging enforcement: `LOGGING_REQUIRED=false` disables the Edge runtime guard, but the database still requires `org_id` in log rows (NOT NULL). Use `SYSTEM_ORG_ID` if appropriate.
- Terminology: this repo standardizes on `org` for multi-tenancy. Where older docs mention "tenant", read it as "org" — they are equivalent here.
- Internal jobs: set `SYSTEM_ORG_ID` for global/internal events so logs remain valid without a specific customer org. The backend logger and Edge event logger (internal events) will fallback to this value when `org_id` is missing.

## app-audit-read (Audit Logs)

Read org-scoped requests and events with optional filters.

Auth: Bearer anon JWT (must include org_id claim)

GET example
```bash
curl -s \
  -H "Authorization: Bearer $ANON_KEY" \
  "https://$PROJECT_REF.supabase.co/functions/v1/app-audit-read?from=2025-09-12T00:00:00Z&to=2025-09-13T00:00:00Z&route=conversation.send&page=1&pageSize=50"
```

Response (shape)
```json
{
  "items": [
    {
      "request_id": "...",
      "org_id": "...",
      "route": "conversation.send",
      "status": 200,
      "event_type": null,
      "direction": null,
      "trace_id": "...",
      "session_id": "...",
      "ts": "2025-09-12T20:10:52.000Z"
    },
    {
      "request_id": "...",
      "org_id": "...",
      "event_type": "ai_call",
      "direction": "internal",
      "trace_id": "...",
      "session_id": "...",
      "ts": "2025-09-12T20:10:52.120Z"
    }
  ],
  "page": 1,
  "pageSize": 50,
  "hasMore": true
}
```
