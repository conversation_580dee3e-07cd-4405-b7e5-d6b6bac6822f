// File: arioncomply-v1/supabase/functions/app-audit-read/index.ts
// File Description: Org-scoped audit log reader with enhanced filtering and trace support
// Purpose: Provide paginated, filterable access to app_audit_logs_v1 with requestId/sessionId filtering for trace analysis
// Inputs: GET/POST { page?, pageSize?, from?, to?, route?, status?, eventType?, requestId?, sessionId? }
// Outputs: JSON response with { items: AuditLogRecord[], page, pageSize, hasMore } for visualization and debugging
// Dependencies: Deno HTTP server, _shared modules (cors, utils, errors, jwt, supabase), Supabase Admin client
// Security/RLS: JWT-based authentication, org-scoped filtering, admin service role for cross-org audit access
// Notes: Enhanced with requestId/sessionId filtering for interactive trace graph visualization and workflow debugging

// @ts-ignore
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { jsonResponse, readJson } from "../_shared/utils.ts";
import { apiError } from "../_shared/errors.ts";
import { getAuthToken, parseJwtClaims } from "../_shared/jwt.ts";
import { getSupabaseAdmin } from "../_shared/supabase.ts";

// TypeScript interface for audit log query parameters
type Query = {
  page?: number;         // Page number for pagination (1-based)
  pageSize?: number;     // Number of records per page (max 200)
  from?: string;         // Start date filter (ISO 8601)
  to?: string;           // End date filter (ISO 8601)
  route?: string;        // Filter by API route/endpoint
  status?: number;       // Filter by HTTP status code
  eventType?: string;    // Filter by event type (request_start, request_end, etc.)
  requestId?: string;    // Filter by specific request ID for trace debugging
  sessionId?: string;    // Filter by session ID for conversation tracking
};

// Parse URL query parameters into typed Query object
function parseQuery(req: Request): Query {
  const url = new URL(req.url);
  const q = url.searchParams;  // Extract query string parameters
  return {
    // Pagination with sensible defaults and limits
    page: Number(q.get("page") ?? "1") || 1,                        // Default to page 1
    pageSize: Math.min(200, Number(q.get("pageSize") ?? "50") || 50), // Cap at 200 records
    // Date range filters (optional)
    from: q.get("from") || undefined,                               // Start date
    to: q.get("to") || undefined,                                   // End date
    // Content filters (optional)
    route: q.get("route") || undefined,                            // API endpoint filter
    status: q.get("status") ? Number(q.get("status")) : undefined, // HTTP status code
    eventType: q.get("eventType") || undefined,                    // Event type filter
    // Trace/debugging filters (optional) - NEW: enhanced for workflow debugging
    requestId: q.get("requestId") || undefined,                    // Specific request trace
    sessionId: q.get("sessionId") || undefined,                    // Conversation session
  };
}

serve(async (req: Request) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }
  if (req.method !== "GET" && req.method !== "POST") {
    return apiError("method_not_allowed", "Only GET/POST supported", 405);
  }

  // Security: Extract and validate organization ID from JWT token
  // This ensures users can only see audit logs for their own organization
  const token = getAuthToken(req.headers);       // Extract Bearer token from Authorization header
  const claims = parseJwtClaims(token);           // Decode and verify JWT claims
  // Look for org ID in multiple possible claim fields
  const orgId = (claims?.org_id as string | undefined) || (claims?.app_org_id as string | undefined) || null;
  // Reject request if no valid org context - prevents unauthorized access
  if (!orgId) return apiError("forbidden", "Missing org context in JWT claims", 403);

  // Parse query parameters from either URL (GET) or request body (POST)
  let query: Query = {};
  if (req.method === "GET") {
    // GET request: parse filters from URL query string
    query = parseQuery(req);
  } else {
    // POST request: parse filters from JSON body
    try {
      query = await readJson<Query>(req);
    } catch {
      return apiError("invalid_json", "Body must be JSON", 400);
    }
  }

  const page = query.page && query.page > 0 ? query.page : 1;
  const size = query.pageSize && query.pageSize > 0 ? query.pageSize : 50;
  const from = query.from;
  const to = query.to;
  const route = query.route?.trim();
  const status = query.status;
  const eventType = query.eventType?.trim();
  const requestId = query.requestId?.trim();
  const sessionId = query.sessionId?.trim();

  const db = getSupabaseAdmin();
  let sel = db.from("app_audit_logs_v1").select("*", { count: "exact" }).eq("org_id", orgId);
  if (from) sel = sel.gte("ts", from);
  if (to) sel = sel.lte("ts", to);
  if (route) sel = sel.eq("route", route);
  if (typeof status === 'number' && !Number.isNaN(status)) sel = sel.eq("status", status);
  if (eventType) sel = sel.eq("event_type", eventType);
  if (requestId) sel = sel.eq("request_id", requestId);
  if (sessionId) sel = sel.eq("session_id", sessionId);
  sel = sel.order("ts", { ascending: false });

  const fromIdx = (page - 1) * size;
  const toIdx = fromIdx + size - 1;
  const { data, error, count } = await sel.range(fromIdx, toIdx);
  if (error) {
    return apiError("db_error", error.message || "Query failed", 500);
  }
  const hasMore = typeof count === 'number' ? (toIdx + 1) < count : (data?.length === size);
  return jsonResponse({ items: data || [], page, pageSize: size, hasMore }, { headers: corsHeaders });
});
