id: Q015
query: >-
  We’re a small business with 20 employees — do we really need ISO 27001?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/4.1"
overlap_ids:
  - "GDPR:2016/Art.32"
capability_tags:
  - "Register"
  - "Draft Doc"
  - "Approval"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Context of the Organization"
    id: "ISO27001:2022/4.1"
    locator: "Clause 4.1"
ui:
  cards_hint:
    - "Gap analysis for small teams"
    - "Scope vs cert decision"
  actions:
    - type: "start_workflow"
      target: "iso_gap_analysis"
      label: "Run ISO Gap Analysis"
    - type: "open_register"
      target: "risk"
      label: "View Risk Register"
output_mode: "both"
graph_required: false
notes: "Certification optional—focus on business needs"
---
### 15) We’re a small business with 20 employees — do we really need ISO 27001?

**Standard terms)**
- **Certification (ISO/IEC 27001):** voluntary; customers may ask for it.
- **Security of processing (GDPR Art. 32):** applicable if you handle personal data.

**Plain-English answer**
ISO 27001 is not legally mandatory; small firms often adopt it to **win deals** and **structure** security. If customers don’t require certification, you can still run ISMS practices without getting the certificate.

**Applies to**
- **Primary:** ISO/IEC 27001:2022 Clause 4.1
- **Also relevant/Overlaps:** GDPR Article 32

**Why it matters**
Certification can accelerate sales, but building the ISMS itself drives real security benefits.

**Do next in our platform**
- Run a **Gap Analysis**.
- Decide on **certification vs internal** ISMS only.
- Stand up core artifacts (risk, SoA, policies).

**How our platform will help**
- **[Register]** Gap and risk registers.
- **[Draft Doc]** Core policy templates.
- **[Approval]** E-sign scope and SoA.
- **[Workflow]** Guided implementation.

**Likely follow-ups**
- “How long/cost for small teams?” (See Q031–Q040 & Q051–Q060)

**Sources**
- ISO/IEC 27001:2022 Clause 4.1
