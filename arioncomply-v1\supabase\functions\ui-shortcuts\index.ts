// File: arioncomply-v1/supabase/functions/ui-shortcuts/index.ts
// File Description: Return org-scoped UI slash shortcuts
// Purpose: Allow the UI to load command shortcuts without hardcoding
// Input: GET with Authorization bearer; org inferred from JWT/headers
// Output: { shortcuts: [{ shortcut, description, action_type, target, url, enabled }] }
// Security/RLS: org-scoped via RLS; service-role client selects; admin bypass via role

import { serve } from "https://deno.land/std@0.224.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { getSupabaseAdmin } from "../_shared/supabase.ts";
import { getClientMeta, logRequestEnd, logRequestStart } from "../_shared/logger.ts";

serve(async (req) => {
  const requestId = crypto.randomUUID();
  const meta = getClientMeta(req, requestId);
  if (req.method === "OPTIONS") return new Response("ok", { headers: corsHeaders });
  await logRequestStart(meta, req.headers);
  if (req.method !== "GET") {
    const r = new Response(JSON.stringify({ error: "Only GET allowed" }), { status: 405, headers: { ...corsHeaders, "content-type": "application/json" } });
    await logRequestEnd(meta, 405);
    return r;
  }

  const db = getSupabaseAdmin();
  const { data, error } = await db
    .from("ui_shortcuts")
    .select("shortcut,description,action_type,target,url,enabled")
    .eq("enabled", true);

  if (error) {
    const r = new Response(JSON.stringify({ error: "Fetch failed", details: error.message }), { status: 500, headers: { ...corsHeaders, "content-type": "application/json" } });
    await logRequestEnd(meta, 500);
    return r;
  }

  const r = new Response(JSON.stringify({ shortcuts: data || [] }), { status: 200, headers: { ...corsHeaders, "content-type": "application/json" } });
  await logRequestEnd(meta, 200);
  return r;
});

