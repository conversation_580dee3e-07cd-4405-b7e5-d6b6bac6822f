```yaml
id: Q212
question: How do we know if we’re staying compliant or drifting away from it?
packs: ["ISO27001:2022","ISO27701:2019"]
primary_ids: ["ISO27001:2022/Cl.9.1","ISO27001:2022/Cl.9.2","ISO27001:2022/Cl.9.3"]
overlap_ids: ["ISO27701:2019/Cl.9"]
capability_tags: ["Dashboard","Report","Tracker","Workflow","Reminder"]
ui:
  actions:
    - target: "dashboard"
      action: "open"
      args: { key: "compliance_kpis" }
    - target: "workflow"
      action: "open"
      args: { key: "internal_audit" }
    - target: "workflow"
      action: "open"
      args: { key: "management_review" }
cards_hint:
  - Define 5–8 KPIs tied to risks.
  - Schedule internal audits and 9.3 reviews.
  - Turn issues into CAPs with owners/dates.
graph_required: false
```

### 212) How do we know if we’re staying compliant or drifting?

**Standard term(s)**

- **Monitoring & measurement (Cl. 9.1)**; **Internal audit (Cl. 9.2)**; **Management review (Cl. 9.3)**.

**Plain-English answer**\
Use **KPIs**, **internal audits**, and **management reviews**. If indicators trend badly or controls lack evidence, you’re drifting.

**Applies to**

- **Primary:** ISO/IEC 27001:2022 **Cl. 9.1–9.3**.
- **Also relevant/Overlaps:** ISO/IEC 27701 performance evaluation.

**Why it matters**\
Early detection avoids **audit surprises**.

**Do next in our platform**

- Define KPIs (e.g., % controls evidenced, DSR SLA, incident MTTR).
- Schedule internal audits and management reviews.

**How our platform will help**

- **[Dashboard] [Report] [Tracker] [Workflow] [Reminder]** — Live metrics, findings/CAPs, and board-ready 9.3 packs.

**Likely follow-ups**

- “Which KPIs matter most?” → See Q222.

**Sources**

- ISO/IEC 27001:2022 **Cl. 9.1–9.3**.
