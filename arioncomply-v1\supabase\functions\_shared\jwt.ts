// File: arioncomply-v1/supabase/functions/_shared/jwt.ts
// File Description: JWT parsing utilities for Edge
// Purpose: Extract claims (org_id, sub, roles) from Authorization bearer tokens
// Inputs: Request headers (Authorization)
// Outputs: Parsed claims object or null
// Notes: Base64url decode; no signature verification (Supabase Edge already validates JWT upstream for protected routes)

function b64UrlDecode(input: string): string {
  const normalized = input.replace(/-/g, "+").replace(/_/g, "/");
  const pad = normalized.length % 4 === 2 ? "==" : normalized.length % 4 === 3 ? "=" : "";
  const str = atob(normalized + pad);
  try {
    // decode UTF-8
    return decodeURIComponent(
      Array.prototype.map
        .call(str, (c: string) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
        .join("")
    );
  } catch {
    return str;
  }
}

/** Extract Bearer token from Authorization header. */
export function getAuthToken(headers: Headers): string | null {
  const h = headers.get("authorization") || headers.get("Authorization");
  if (!h) return null;
  const m = /^Bearer\s+(.+)$/i.exec(h.trim());
  return m ? m[1] : null;
}

/** Parse minimal claims from a JWT without verifying signature (Edge trust boundary). */
export function parseJwtClaims(token: string | null | undefined): Record<string, any> | null {
  if (!token) return null;
  const parts = token.split(".");
  if (parts.length < 2) return null;
  try {
    const payload = b64UrlDecode(parts[1]);
    return JSON.parse(payload);
  } catch {
    return null;
  }
}

/** Extract orgId, userId, and roles from JWT (or fallback headers). */
export function extractOrgAndUser(headers: Headers): { orgId?: string | null; userId?: string | null; roles?: string[] | null } {
  const token = getAuthToken(headers);
  const claims = parseJwtClaims(token);
  const orgId = (claims?.org_id as string | undefined) || (claims?.app_org_id as string | undefined) || headers.get("x-org-id");
  const userId = (claims?.sub as string | undefined) || headers.get("x-user-id");
  const roles = (claims?.roles as string[] | undefined) || null;
  return { orgId: orgId || null, userId: userId || null, roles };
}
