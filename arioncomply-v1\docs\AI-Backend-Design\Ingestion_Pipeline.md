# Ingestion Pipeline (Placeholder)

Decisions
- Supported inputs: PDF, DOCX, Markdown. Use Locling for extraction instead of OCR.
- Two outputs (forked pipeline) depending on purpose:
  1) Processing-only (assessment/editing/generation pipeline)
  2) RAG indexing (vectorization and metadata graph updates)
- Source of truth: DMS bucket for canonical files; derived artifacts stored alongside with provenance.

Stages
1) Intake: file upload or connector; metadata capture (org, user, source).
2) Extraction: Locling text + structure; detect sections, tables, headings.
3) Normalization: clean, split into chunks (semantic or fixed with overlap); attach metadata.
4) Indexing (RAG): embeddings to vector project (scoped by `org_id`); register doc/chunk records.
5) Processing (Editing/Assessment): rules, checks, and suggested edits for HITL.
6) Versioning: maintain document versions; link generated outputs to source.

Open Questions
- Chunking approach: semantic vs fixed windows; target token/window sizes.
- Update strategy: partial re-index vs full re-index.

Next Steps
- Define doc/chunk schemas and provenance fields.
- Wire Locling extractor and write a chunker with tests.
- Add ingestion events to `api_event_logs` with counts and durations.

