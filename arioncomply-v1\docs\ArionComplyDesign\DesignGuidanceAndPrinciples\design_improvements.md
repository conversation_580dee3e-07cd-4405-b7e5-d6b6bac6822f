# Design Improvements: Resolving System Inconsistencies

This document outlines key inconsistencies identified between the DataDriveUISchemaAndFunctions architecture and the SubscriptionManagement implementation, with proposed resolution strategies for each.

## 1. Schema Naming Conventions

**Issue:** Inconsistent field naming (camelCase vs snake_case) and prefix conventions across systems.

**Resolution Strategy:**
- Standardize on snake_case for all database fields
- Establish consistent prefix rules for related tables
- Document naming patterns in database_schema_design_principles.md

**Rationale:**
- snake_case is selected because it's the PostgreSQL convention and more readable in SQL queries
- Alternative of using camelCase was rejected because it creates friction between SQL and database layers
- Consistent prefixes (e.g., sub_ for subscription) enable clearer understanding of field ownership and reduce naming collisions

## 2. API Endpoint Structure

**Issue:** Mixed resource-based and action-based endpoint paths causing confusion.

**Resolution Strategy:**
- Adopt RESTful resource-based endpoints as standard
- Consolidate paths under consistent resource naming (/subscriptions/...)
- Document API standards in new API_design_principles.md

**Rationale:**
- Resource-based RESTful endpoints provide better alignment with HTTP methods (GET, POST, PUT, DELETE)
- Alternative action-based approach (/subscription/change-plan) was rejected because it creates inconsistent URL patterns and leads to API sprawl
- Standardized path structure makes API autodiscovery and documentation generation more reliable

## 3. Permission Model Integration

**Issue:** Inconsistent permission codes and structure between systems.

**Resolution Strategy:**
- Standardize permission format: `<action>:<resource>`
- Ensure permission_rules JSON structure is consistent
- Update documentation in both database and UI design principles

**Rationale:**
- The `<action>:<resource>` format provides clear scope and intent (e.g., manage:subscriptions)
- Alternative of using free-form permission names was rejected due to difficulty in programmatically understanding permission scope
- This format aligns with OAuth 2.0 and common RBAC implementation patterns, enabling better integration with external systems

## 4. JSON Schema Validation

**Issue:** Varying approaches to JSON validation between systems.

**Resolution Strategy:**
- Implement consistent JSON schema validation across all components
- Replace simple CHECK constraints with proper schema validation
- Document in database_schema_design_principles.md

**Rationale:**
- Robust JSON schema validation prevents data integrity issues and provides better error messages
- Simple CHECK constraints (like jsonb_typeof(plan_configuration) = 'object') provide minimal validation and don't verify structure
- Standardized schema validation enables automatic UI form generation and validation based on the same schema

## 5. Event and Workflow System

**Issue:** Misalignment between general event system and subscription-specific triggers.

**Resolution Strategy:**
- Create unified event_and_workflow_design_principles.md
- Define clear relationship between database triggers and metadata-driven events
- Standardize event naming and payload structure

**Rationale:**
- A unified event system ensures all business logic receives consistent event notifications
- Alternative of separate systems for different modules was rejected due to complexity in event coordination
- Standardized event payloads enable consistent handling across the system, reducing translation layers

## 6. UI Component Interface

**Issue:** UI components don't align with UI configuration schema.

**Resolution Strategy:**
- Create ui_design_principles.md to standardize component interfaces
- Ensure component parameters match database field names
- Define mapping patterns for data transformation where needed

**Rationale:**
- Alignment between component interfaces and database fields reduces translation code
- Alternative of component-specific naming was rejected as it required complex mappings between UI and database
- Mapping patterns enable necessary transformations while keeping the mapping logic explicit and maintainable

## 7. Database Function Integration

**Issue:** Database functions don't leverage metadata-driven architecture.

**Resolution Strategy:**
- Create database_function_integration_gap_analysis.md
- Refactor functions to use metadata registry
- Establish pattern for database function to API handler mapping

**Rationale:**
- Metadata-driven functions are more resilient to schema changes
- Direct SQL queries were rejected as they create tight coupling with schema
- Consistent function-to-handler mapping simplifies API implementation and reduces bugs from misaligned interfaces

## 8. Error Handling Approach

**Issue:** Inconsistent error handling patterns and structures.

**Resolution Strategy:**
- Create error_handling_design_principles.md
- Define standard error codes, messages, and response structures
- Implement consistent error handling across all components

**Rationale:**
- Standardized error handling improves UX by providing consistent error responses
- Ad-hoc error handling was rejected due to inconsistent user experience and difficulty in error tracing
- Defined error codes enable better client-side handling and internationalization of error messages

## 9. Multi-Tenant Isolation

**Issue:** Inconsistent multi-tenant field naming and RLS policies.

**Resolution Strategy:**
- Create tenant_isolation_design_principles.md
- Standardize on organization_id as the tenant identifier
- Define configurable approach for non-tenant environments
- Unify RLS policy implementation

**Rationale:**
- Consistent tenant identifier simplifies RLS policies and improves security
- Using multiple tenant identifiers (organization_id and sub_org_id) was rejected due to complexity and risk of policy gaps
- Configurable approach enables single-tenant deployments without code changes

## 10. Field Type Definitions

**Issue:** Inconsistent type systems and validation approaches.

**Resolution Strategy:**
- Create db_design_type_system_and_validators.md
- Define clear mapping between SQL types, API types, and UI types
- Standardize validation patterns across the system

**Rationale:**
- Unified type system ensures data consistency across all layers
- Simple SQL types with CHECK constraints were rejected due to limited validation capabilities
- Consistent mapping between layers reduces translation errors and simplifies debugging

## Implementation Roadmap

1. Review and approve this high-level strategy
2. Create individual design principle documents
3. Perform gap analysis on database function integration
4. Develop migration plan for existing components
5. Implement changes in priority order:
   - Naming conventions
   - API structure
   - Error handling
   - Permission model
   - Remaining items

**Prioritization Rationale:**
- Naming conventions are prioritized as they impact all other changes and are relatively simple to document
- API structure changes are next as they affect client integration and have high visibility
- Error handling improvements provide immediate user experience benefits
- Permission model updates improve security without requiring extensive schema changes
- Remaining items are sequenced based on dependencies and complexity

## Next Steps

- Finalize and approve this document
- Create database_schema_design_principles.md to address points 1, 3, 4
- Create API_design_principles.md to address point 2
- Proceed with remaining documents based on priority
