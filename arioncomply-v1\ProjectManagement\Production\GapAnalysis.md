<!-- File: arioncomply-v1/ProjectManagement/Production/GapAnalysis.md -->
# ArionComply Phase 1–2 Gap Analysis (Repo + E2E)

Version: 2025-09-12

Scope
- Verify file headers include correct repo-relative path and match actual location.
- Assess end-to-end readiness for Phase 1 (MVP-Assessment-App) and Phase 2 (MVP-Demo-Light-App).
- Focus on DB migrations, Edge functions, AI backend, and testing harness (workflow-gui as interim UI).

Summary
- Header compliance: Failing. Many files lack the required `File: arioncomply-v1/...` header or have incorrect paths. A strict validator was added: `tools/checks/check-header-paths.py`. Latest report: `tools/checks/latest-header-report.txt`.
- DB foundation (0001–0011): Strong. Migrations exist and align with RLS/tenant patterns; minor follow-ups for logging RLS tightening are tracked.
- Edge/API: `compliance-proxy` exists and normalizes responses; requires `AI_BACKEND_URL` and CORS is configured. Missing: Audit read endpoint for logs.
- AI backend: Functional skeleton (FastAPI + router + logging + retrieval stub). Missing: deployment, auth claims enforcement, provider integration, and vector DB RPC connectivity configuration.
- Testing UIs: `workflow-gui` and `llm-comparison` available for local testing. Production UI is intentionally deferred; use workflow-gui for Phase 1/2 validation.

Header Compliance Audit
- Tooling:
  - Strict validator: `tools/checks/check-header-paths.py` (compares declared header path vs actual file path).
  - Latest output saved: `tools/checks/latest-header-report.txt`.
- Findings (high level):
  - Missing header: hundreds of files across `.github/workflows`, `db/migrations`, `testing/`, `ai-backend/python-backend/services/**`, and many docs lack the `File: arioncomply-v1/...` line in the first 10 lines.
  - Path mismatches: a few files declare paths that don’t match location, e.g. `tools/checks/check-headers.sh` and SQLs under `ai-backend/supabase_migrations/schemas/` with `docs/...` prefixes.
  - Notable incorrect header: `arioncomply-v1/testing/llm-comparison/index.html` starts with `<!-- File path: index.html (in project root) -->` but should be `<!-- File: arioncomply-v1/testing/llm-comparison/index.html -->`.
- Recommended remediation plan:
  - Phase A (CI-safety): Add headers to migration SQLs and active code paths first: `db/migrations/**`, `supabase/functions/**`, `ai-backend/python-backend/**`, `testing/**`. Defer long-tail docs.
  - Phase B: Fix path mismatches flagged in report (5 files at time of scan).
  - Phase C: Sweep remaining docs and scripts. Enforce in CI via `tools/checks/check-header-paths.py`.

Phase 1 Readiness (MVP-Assessment-App)
- Expected scope (from LastContext and trackers):
  - Apply `0001–0011` migrations; seed demo org/user; verify RLS; logging tables available with org scoping.
  - Edge functions: Deploy `compliance-proxy` with proper CORS and env; implement/read audit logs (org-scoped, paginated).
  - AI backend: Provide minimal chat capability: accept messages, emit events (`deterministic_preprocessing`, `retrieval_run`, `ai_call`), return normalized envelope.
  - Testing: Use `workflow-gui` to call the deployed `compliance-proxy` and exercise flows; show `requestId`, basic results, and suggestions.
- Current status:
  - DB: Migrations present; follow-up “tighten log RLS” migration is tracked but not implemented; multiple migration files lack headers.
  - Edge: `supabase/functions/compliance-proxy/index.ts` present; requires `AI_BACKEND_URL`. CORS shared module present.
  - AI backend: FastAPI skeleton (`python-backend/app/main.py`), router (`services/router.py`), logging client (`services/logging/events.py`). Retrieval client targets `match_chunks` RPC in separate Supabase project via service key.
  - Testing: `workflow-gui` JS present; header missing; connects via anon key to functions.
- Gaps to close for Phase 1:
  - DB
    - Add “tighten log visibility” migration (remove `org_id IS NULL` reads) and ensure `org_id` NOT NULL or CHECK for logs.
    - Seed script for demo tenant + user aligned with 0008.
  - Edge/API
    - Implement audit read endpoint (org-scoped, filters by `route/status/event_type/time`, paginated). Could be an Edge function or direct PostgREST view.
    - Configure and deploy `compliance-proxy` with `AI_BACKEND_URL` to the FastAPI backend; verify envelopes and CORS.
  - AI backend
    - Add containerization (Dockerfile) and a Procfile or deployment instructions; define envs: `SUPABASE_URL`, `SUPABASE_SERVICE_ROLE_KEY`, `VECTOR_SUPABASE_URL`, `VECTOR_SUPABASE_SERVICE_KEY`, `SYSTEM_ORG_ID`.
    - Enforce org/user claims: derive from JWT or forwarded headers; reject missing org_id for public events.
    - Implement provider integration for at least one model path (or retain stub but ensure consistent envelopes and logging).
    - Validate vector RPC contract (`match_chunks`) and connectivity.
  - Testing harness
    - Wire `workflow-gui` to display `requestId` and `traceparent` (partially present), and add a simple tab to query audit logs via new endpoint.
    - Fix headers and paths in `testing/**` files.

Phase 2 Readiness (MVP-Demo-Light-App)
- Expected scope:
  - Minimal metadata registry: `metadata_entities`, `metadata_fields`, `metadata_views` seeded for demo ListView/Form.
  - ListView Data API and Form submission with runtime validation (Zod/JSON Schema) via Edge/API.
  - Assistant router MVP + casing mapper for snake_case DB ↔ camelCase API; standardized envelopes.
  - Document generation hooks stubs are acceptable; ensure events logged with correlation IDs.
- Current status:
  - Metadata registry: Not implemented in migrations; exists in design docs only.
  - Data API: No explicit Edge function for ListView/Form; casing/conversion helpers referenced in docs, not implemented.
  - Assistant router: Python router skeleton exists; Edge router (JS/TS) not present.
  - Events: Core logging tables exist; additional `ai_call` and DB read/write event coverage recommended.
- Gaps to close for Phase 2:
  - DB
    - Add migrations for `metadata_entities`, `metadata_fields`, `metadata_views` and seed minimal demo data.
  - Edge/API
    - Implement ListView query endpoint and Form submit endpoint; apply validation (Zod/JSON Schema) and casing mapper.
    - Introduce `assistant_router` endpoint or forward `compliance-proxy` to the backend with enriched envelopes.
  - AI backend
    - Expand router to support output modes (cards/prose/both) already scaffolded; persist minimal explainability fields as part of response.
  - Testing harness
    - Add non-destructive panels in `workflow-gui` for the new endpoints and show mapped fields and validation errors.

Action Plan (Prioritized)
1) Headers and Path Accuracy (blocking for consistency/CI)
   - Fix path mismatches: `tools/checks/check-*.sh/py` and AI backend schema SQLs with incorrect declared paths.
   - Add missing headers to: `db/migrations/**`, `ai-backend/python-backend/**`, `testing/**`, `supabase/functions/**`.
   - Enforce via CI using `tools/checks/check-header-paths.py`.
2) Phase 1 Core
   - Apply `0001–0011` to target Supabase; seed demo org/user.
   - Add migration to tighten log RLS and ensure `org_id` is set on all log rows.
   - Deploy `compliance-proxy` with `AI_BACKEND_URL` to FastAPI service; verify happy path from `workflow-gui`.
   - Implement audit read endpoint (view + Edge handler) and surface in `workflow-gui`.
3) AI Backend Minimalization
   - Containerize FastAPI app; provide deploy guide; configure envs for Supabase and vector project.
   - Ensure event logging writes to `api_event_logs` with org scoping; add `ai_call`, `retrieval_run` events already scaffolded.
4) Phase 2 Enablers
   - Implement and seed minimal metadata registry.
   - Add ListView/Form endpoints with validation and casing mapper.
   - Expand router outputs to include cards/evidence consistently.

Artifacts
- Strict header report: `tools/checks/latest-header-report.txt` (updated by running the validator).
- Validator: `tools/checks/check-header-paths.py`.

Notes
- The production UI is intentionally deferred; `testing/workflow-gui` serves for Phase 1/2 validation.
- The vector DB is a separate Supabase project; retrieval client expects RPC `match_chunks` with org scoping.
- Secrets: `frontend-config.js` includes an anon key for testing only; rotate/change for production.

