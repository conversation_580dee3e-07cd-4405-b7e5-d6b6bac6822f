#!/usr/bin/env bash
# File: tools/generic-checks/run-all.sh
# File Description: Convenience runner for generic checks
# Purpose: Execute generic path and quality checkers and write reports

set -euo pipefail

root_dir="$(git rev-parse --show-toplevel 2>/dev/null || pwd)"
cd "$root_dir"

out_dir="tools/generic-checks/reports"
mkdir -p "$out_dir"

python3 tools/generic-checks/check-header-paths.py \
  --write-report "$out_dir/latest-header-report.txt" || true

python3 tools/generic-checks/check-header-quality.py \
  --policy tools/generic-checks/policy.example.yml \
  --write-report "$out_dir/header-quality-report.txt" || true

echo "Wrote: $out_dir/latest-header-report.txt"
echo "Wrote: $out_dir/header-quality-report.txt"

