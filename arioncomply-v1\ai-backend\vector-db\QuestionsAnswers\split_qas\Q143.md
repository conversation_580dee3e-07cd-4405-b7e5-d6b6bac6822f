id: Q143
query: >-
  What if something goes wrong (like a security incident) right before or during an audit?
packs:
  - "ISO27001:2022"
  - "ISO17021-1:2015"
primary_ids:
  - "ISO27001:2022/A.5.24"
  - "ISO17021-1:2015/7.3"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Incident Management"
    id: "ISO27001:2022/A.5.24"
    locator: "Annex A.5.24"
  - title: "ISO/IEC 17021-1:2015 — Surveillance Requirements"
    id: "ISO17021-1:2015/7.3"
    locator: "Clause 7.3"
ui:
  cards_hint:
    - "Incident during audit plan"
  actions:
    - type: "start_workflow"
      target: "audit_incident_response"
      label: "Respond During Audit"
output_mode: "both"
graph_required: false
notes: "Inform auditor immediately and follow incident process in parallel"
---
### 143) What if something goes wrong (like a security incident) right before or during an audit?

**Standard terms**  
- **Incident management (ISO 27001 A.5.24):** handle incidents systematically.  
- **Surveillance (ISO 17021-1 Cl.7.3):** auditors note significant events.

**Plain-English answer**  
Notify your auditor as soon as possible. Continue incident response per your runbook while cooperating with the auditor. Document both processes—this shows resilience and transparency.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.5.24; ISO/IEC 17021-1:2015 Clause 7.3

**Why it matters**  
Maintaining transparency preserves trust and demonstrates control.

**Do next in our platform**  
- Trigger **Audit Incident Response** workflow.  
- Update **Incident Log** and notify auditor.

**How our platform will help**  
- **[Workflow]** Dual-track incident and audit tasks.  
- **[Report]** Real-time incident metrics for auditor.

**Likely follow-ups**  
- “Can we pause the audit?” (Discuss with CB—minor incidents typically don’t pause)

**Sources**  
- ISO/IEC 27001:2022 Annex A.5.24; ISO/IEC 17021-1:2015 Clause 7.3  
