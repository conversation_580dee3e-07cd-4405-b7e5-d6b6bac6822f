# AI Backend Deployment

This document describes how to deploy and operate the AI Backend locally and in hosted environments. It focuses on practical integration across local LLM servers, Supabase (Edge Functions, Postgres/pgvector, Storage), and an optional local ChromaDB store, while keeping the repository free of runtime artifacts.

## Topology Overview

- API: FastAPI app (internal) exposing endpoints for ingest, generate (RAG), health, and job submission.
- Worker: Background execution via a queue (RQ+Redis or Celery+Redis). API enqueues tasks, workers process pipelines.
- Supabase: Edge Functions trigger backend actions; Postgres (pgvector) stores embeddings; Storage hosts raw artifacts.
- Local LLMs: OpenAI-compatible servers discovered from a JSON manifest; requests use the OpenAI API schema.
- Optional Vector Store: ChromaDB for fast offline/local experimentation; pgvector remains the shared source of truth.

- Mermaid (high-level)

```mermaid
flowchart TD
  subgraph SUPA[Supabase]
    Edge[Edge Functions (Deno)]
    PG[(Postgres / pgvector)]
    Storage[Storage]
  end

  subgraph BE[AI Backend]
    API[FastAPI]
    Worker[Worker]
    Redis[(Redis)]
  end

  UI[Web App] --> Edge
  Edge --> API
  API --> Redis
  Redis --> Worker
  Worker --> PG
  Worker --> Storage
  API --> LLMs[Local LLMs (OpenAI /v1)]
```

- PlantUML (SVG rendered via GitHub Action)

![AI Backend Topology](AI-Backend-Design/diagrams/AI_Backend_Topology.svg)

```plantuml
!include diagrams/AI_Backend_Topology.puml
```

## Environments

- Local Dev: Runs FastAPI+worker+Redis locally. Uses local LLMs (preferred) and can enable ChromaDB. Writes outputs to `$HOME/llms/...` and never into the repo.
- Staging/Prod: Same code paths, but LLM provider fallback (cloud) and pgvector are primary; Chroma is optional/off.

## Local LLM Discovery and Policy

- Discovery file: `$HOME/llms/config/local_sllm_endpoints.json` (generated by `LLMs/collectSLLMinfo.sh`).
- Selection policy: pick first model with `health == "ok"` and `openai_compatible == true`, honoring an optional `PREFERRED_LOCAL_MODEL`.
- OpenAI-compatible calls: `http://127.0.0.1:<port>/v1/...` for chat/completions/embeddings.
- Fallback: if no healthy local model, use provider API keys (e.g., `OPENAI_API_KEY`).
- Safety: Do not write logs or JSON inside the repo. Runtime files go under `$HOME/llms/{logs,config}`.

## Vector Store Strategy

- Primary: Supabase Postgres + pgvector for embeddings and retrieval in shared environments.
- Optional Local: ChromaDB persistent store for offline/iterative development.
- Dual‑write (optional): In ingest pipelines, write to both pgvector and Chroma; retrieval can prefer Chroma locally and fall back to pgvector.
- Chroma location: `CHROMA_DIR` (default `~/llms/chroma`). Reject initializing if path resolves inside the repo.

## Pipelines

### Ingest/Index

1. Fetch artifact (Supabase Storage signed URL or upload) → store temporarily under `/tmp`.
2. Convert to text/markdown:
   - PDFs: `pymupdf` (primary), `pdfplumber` fallback; OCR via `pytesseract` for image PDFs.
   - DOCX/ODT: `mammoth`/`docx2txt`/`odfpy`.
   - HTML: `readability-lxml` + `beautifulsoup4`.
3. Normalize text (clean headings/tables), chunk with size/overlap, attach metadata (doc_id, page/section/mime, tags).
4. Embeddings: local embedding endpoint (OpenAI-compatible) or `sentence-transformers` locally.
5. Upsert: pgvector (primary). Optionally also upsert to Chroma.
6. Update job status row in Supabase (progress/errors, counts) for observability.

- Mermaid (sequence)

```mermaid
sequenceDiagram
  participant Edge as Edge Function
  participant API as API (FastAPI)
  participant R as Redis
  participant W as Worker
  participant ST as Supabase Storage
  participant PG as Postgres/pgvector
  Edge->>API: POST /ingest
  API->>R: enqueue(job)
  R-->>W: job
  W->>ST: download object
  W->>W: convert → normalize → chunk
  W->>PG: upsert embeddings + metadata
  W-->>API: status update
  API-->>Edge: 202 Accepted
```

- PlantUML (SVG rendered via GitHub Action)

![Ingest Pipeline](AI-Backend-Design/diagrams/AI_Ingest_Pipeline.svg)

```plantuml
!include diagrams/AI_Ingest_Pipeline.puml
```

### Generate (RAG)

1. Retrieve top‑k from vector store (filters by tenant/project/doc). Prefer Chroma in local dev if present; otherwise pgvector.
2. Compose prompt (system + task + citations block with chunk metadata).
3. Call LLM (local preferred; cloud fallback).
4. Stream tokens to the client; persist run metadata (retrieved chunk IDs, scores, provider used) for audit.

- Mermaid (sequence)

```mermaid
sequenceDiagram
  participant UI
  participant Edge
  participant API as API /generate
  participant R as Redis
  participant W as Worker
  participant Chroma as Chroma (optional)
  participant PG as Postgres/pgvector
  participant LLM as Local LLM (/v1)
  UI->>Edge: POST conversation.send
  Edge->>API: forward (auth, ids)
  API->>R: enqueue(job)
  R-->>W: job
  W->>Chroma: query (if present)
  W->>PG: query (fallback/filters)
  W->>W: prompt compose
  W->>LLM: chat/completions
  LLM-->>W: stream tokens
  W-->>API: progress/logs
  API-->>Edge: stream/proxy result
  Edge-->>UI: response
```

- PlantUML (SVG rendered via GitHub Action)

![RAG Flow](AI-Backend-Design/diagrams/AI_RAG_Sequence.svg)

```plantuml
!include diagrams/AI_RAG_Sequence.puml
```

## Queue and Triggers

- Edge Function (Supabase) → HTTP POST to backend `/ingest` with payload: bucket/path, doc_id, tenant/project.
- API enqueues a job; worker processes and posts status updates back to Supabase.

## Configuration

Environment variables (suggested):

- Supabase: `SUPABASE_URL`, `SUPABASE_SERVICE_ROLE_KEY`.
- LLMs: `LOCAL_SLLM_JSON=$HOME/llms/config/local_sllm_endpoints.json`, `PREFERRED_LOCAL_MODEL`, `OPENAI_API_KEY` (fallback).
- Queue: `REDIS_URL=redis://localhost:6379/0`.
- Chroma: `CHROMA_DIR=~/llms/chroma`, `CHROMA_COLLECTION=arioncomply`.
- General: `AI_BACKEND_ENV=local|staging|prod`.

Path guards:

- The backend and helper scripts must never write into the repo. All default outputs resolve to `$HOME/llms/...` and explicit in‑repo paths are rejected.

## Observability

- Structured logs with request/job IDs.
- Health endpoints: `/health` (API) and per‑service probes (Redis, database, LLM reachability).
- Persist minimal execution traces for RAG (retrieved chunks, scores, prompt template version).

## Security and Access

- Secrets via environment (never committed): Supabase service role, provider keys.
- Per‑tenant isolation: include `tenant_id` in metadata and vector filters.
- No external egress by default for local runs unless explicitly configured.

## Deployment Outline

Local:

1. `python -m venv .venv && source .venv/bin/activate`
2. `pip install -r requirements.txt` (or `uv`/`poetry`)
3. Start Redis (`brew services start redis` or docker)
4. Run API: `uvicorn ai_backend.app:app --reload`
5. Run worker: `rq worker` (or `celery -A ai_backend.workers worker -l info`)

Hosted (illustrative):

1. Provision Supabase (Postgres/pgvector, Storage, Edge Functions).
2. Deploy API + workers (e.g., Fly.io, Render, Heroku, k8s) with Redis/worker dynos.
3. Configure environment variables and secrets; disable Chroma unless needed.
4. Wire Edge Functions to call backend endpoints; lock down with service keys.

## Open Questions / Next Steps

- Settle on queue framework (RQ vs Celery) and bake minimal wrappers.
- Scaffold `llm_clients` (local discovery + OpenAI-compatible client) and `vector` (pgvector + chroma) modules.
- Provide CLI utilities for ingest, reindex, and query (RAG dry runs).
- Add `.env.example` and Makefile targets for common flows.

## Source of Truth

This document is the source of truth for AI backend deployment. Other design documents should reference this file and its diagrams for up‑to‑date deployment topology and flows.
