id: Q106
query: >-
  How do we handle customer data deletion requests without breaking other systems?
packs:
  - "GDPR:2016"
  - "ISO27001:2022"
primary_ids:
  - "GDPR:2016/Art.17"
  - "ISO27001:2022/8.5"
overlap_ids:
  - "ISO27701:2019/8.3"
capability_tags:
  - "Workflow"
  - "Tracker"
flags: []
sources:
  - title: "GDPR — Right to Erasure"
    id: "GDPR:2016/Art.17"
    locator: "Article 17"
  - title: "ISO/IEC 27001:2022 — Release of Information"
    id: "ISO27001:2022/8.5"
    locator: "Clause 8.5"
  - title: "ISO/IEC 27701:2019 — PII Processing"
    id: "ISO27701:2019/8.3"
    locator: "Section 8.3"
ui:
  cards_hint:
    - "DSAR orchestration"
  actions:
    - type: "start_workflow"
      target: "deletion_request"
      label: "Manage Deletion"
    - type: "open_register"
      target: "dsar_log"
      label: "View DSAR Log"
output_mode: "both"
graph_required: false
notes: "Automate flags in all systems and maintain audit trail"
---
### 106) How do we handle customer data deletion requests without breaking other systems?

**Standard terms**  
- **Erasure (GDPR Art. 17):** right to have data erased.  
- **Release of information (ISO 27001 Cl. 8.5):** controls on data dissemination.  
- **PII processing (ISO 27701 Cl. 8.3):** supports DSAR handling.

**Plain-English answer**  
Use a **central orchestration**: flag data for deletion, execute deletions across systems via APIs or workflows, maintain an audit trail. Soft-delete or anonymize where deletion breaks dependencies, and track exceptions.

**Applies to**  
- **Primary:** GDPR Article 17; ISO/IEC 27001:2022 Clause 8.5  
- **Also relevant/Overlaps:** ISO/IEC 27701:2019 Section 8.3

**Why it matters**  
Ensures full compliance without data integrity issues.

**Do next in our platform**  
- Trigger **Deletion Request** workflow.  
- Monitor progress in DSAR Log.

**How our platform will help**  
- **[Workflow]** Automated DSAR orchestration.  
- **[Tracker]** DSAR log with SLA timers.  
- **[Report]** Anonymization audit trail.

**Likely follow-ups**  
- “How do we handle shared data sets?” (Coordinate with data owners)

**Sources**  
- GDPR Article 17; ISO/IEC 27001:2022 Clause 8.5; ISO/IEC 27701:2019 Section 8.3
