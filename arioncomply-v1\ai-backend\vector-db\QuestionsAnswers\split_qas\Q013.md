id: Q013
query: >-
  How do I know if my company needs to comply with GDPR?
packs:
  - "GDPR:2016"
primary_ids:
  - "GDPR:2016/Art.3"
overlap_ids:
  - "ISO27001:2022/A.18"
capability_tags:
  - "Register"
  - "Workflow"
  - "Report"
flags: []
sources:
  - title: "GDPR (Regulation 2016/679) — Territorial Scope"
    id: "GDPR:2016/Art.3"
    locator: "Article 3"
ui:
  cards_hint:
    - "GDPR scope assessment"
    - "Processing map"
  actions:
    - type: "start_workflow"
      target: "gdpr_scope_check"
      label: "Run GDPR Scope Check"
    - type: "open_register"
      target: "ropa"
      label: "Map Processing Activities"
output_mode: "both"
graph_required: false
notes: "Highlight that scope is based on activity, not headquarters location"
---
### 13) How do I know if my company needs to comply with GDPR?

**Standard terms)**
- **Territorial scope (GDPR Article 3):** applies if you’re offering goods/services to, or monitoring, EU individuals or have an EU establishment.
- **Security of processing (ISO/IEC 27001 Annex A.18):** baseline controls once in-scope.

**Plain-English answer**
You’re in-scope if you **target** EU/EEA individuals (e.g., local marketing, pricing, shipping) or **monitor** them (analytics, profiling). If all processing happens entirely outside the EU and you don’t target EU individuals, GDPR likely does not apply.

**Applies to**
- **Primary:** GDPR Article 3
- **Also relevant/Overlaps:** ISO/IEC 27001 Annex A.18

**Why it matters**
Scope determines whether you must do notices, rights handling, breach reporting, etc.

**Do next in our platform**
- Run the **GDPR Scope Check** wizard.
- If in-scope, create your **RoPA** and tag systems.
- Start required workstreams (notices, DSARs, security).

**How our platform will help**
- **[Workflow]** Automated scope assessment.
- **[Register]** Processing inventory.
- **[Report]** Scope vs non-scope summary.

**Likely follow-ups**
- “Do we need an EU representative?” (Art. 27—if no EU HQ)
- “Do we need a DPO?” (See Q010)

**Sources**
- GDPR Article 3
