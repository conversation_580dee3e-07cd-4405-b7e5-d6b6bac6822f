"""
File: arioncomply-v1/ai-backend/python-backend/services/ingestion/ingest_pipeline.py
File Description: Ingestion pipeline orchestrator (skeleton)
Purpose: Extract → chunk → embed → insert into vector project with provenance and metadata
Inputs: org_id, file_path, source, version, hash (optional)
Outputs: dict { doc_id, chunks: n, embeddings: n }
Security: Requires VECTOR_SUPABASE_URL / VECTOR_SUPABASE_SERVICE_KEY
Notes: Replace extract/embed with real providers; add error handling and logging
"""

import os
import json
from typing import Dict, Any
import requests
from .locling_extractor import extract_blocks
from .chunker import chunk_blocks
from .embedder import embed_texts, DIM
from .chromadb_client import chroma_client, CHROMA_ENABLED

VECTOR_SUPABASE_URL = os.getenv("VECTOR_SUPABASE_URL")
VECTOR_SUPABASE_SERVICE_KEY = os.getenv("VECTOR_SUPABASE_SERVICE_KEY")


def _post(url: str, row: Dict[str, Any]) -> str:
    """POST a JSON row to Supabase REST and return inserted id.

    Args:
        url: REST endpoint URL.
        row: Row payload to insert.
    """
    headers = {
        "apikey": VECTOR_SUPABASE_SERVICE_KEY or "",
        "Authorization": f"Bearer {VECTOR_SUPABASE_SERVICE_KEY or ''}",
        "Content-Type": "application/json",
        "Prefer": "return=representation",
    }
    r = requests.post(url, headers=headers, data=json.dumps(row), timeout=5)
    r.raise_for_status()
    data = r.json()
    return (data[0]["id"] if isinstance(data, list) and data else data.get("id")) or ""


def ingest_one(org_id: str, file_path: str, *, title: str = None, source: str = None, version: str = None, hash_: str = None) -> Dict[str, Any]:
    """Ingest a single file: extract, chunk, embed, and insert rows.

    Returns a dict with new document id and counts.
    """
    assert VECTOR_SUPABASE_URL and VECTOR_SUPABASE_SERVICE_KEY, "Vector Supabase env missing"
    # 1) Extract
    blocks = extract_blocks(file_path)
    # 2) Chunk
    chunks = chunk_blocks(blocks)
    # 3) Embed
    embeddings = embed_texts([c["text"] for c in chunks])
    # 4) Insert document
    doc_row = {
        "org_id": org_id,
        "title": title or os.path.basename(file_path),
        "source": source or "upload",
        "version": version or "v1",
        "hash": hash_ or None,
        "metadata": None,
    }
    doc_id = _post(f"{VECTOR_SUPABASE_URL}/rest/v1/documents", doc_row)
    # 5) Insert chunks
    for c in chunks:
        c_row = {
            "org_id": org_id,
            "doc_id": doc_id,
            "seq": c["seq"],
            "text": c["text"],
            "tokens": None,
            "metadata": {
                "section": c.get("section"),
                "heading": c.get("heading"),
                "page": c.get("page"),
                # framework_ids/control_ids/actions can be added by a classifier
            },
        }
        chunk_id = _post(f"{VECTOR_SUPABASE_URL}/rest/v1/chunks", c_row)
        # 6) Insert embedding
        idx = c["seq"] - 1
        e_row = {
            "org_id": org_id,
            "chunk_id": chunk_id,
            "model": "stub-emb-768",
            "dim": DIM,
            "embedding": embeddings[idx],
        }
        _post(f"{VECTOR_SUPABASE_URL}/rest/v1/embeddings", e_row)
    
    # Optional: Also write to ChromaDB for local development
    if CHROMA_ENABLED:
        try:
            _write_to_chromadb(org_id, doc_id, chunks, embeddings)
        except Exception as e:
            print(f"[Ingest] ChromaDB write failed (non-blocking): {e}")
    
    return {"doc_id": doc_id, "chunks": len(chunks), "embeddings": len(chunks)}


def _write_to_chromadb(org_id: str, doc_id: str, chunks: list, embeddings: list):
    """Optional dual-write to ChromaDB for local development."""
    if not CHROMA_ENABLED:
        return
    
    # Determine collection from first chunk's metadata or use default
    collection_name = "ariocomply-qa-dev"  # Default collection
    
    # TODO: Extract collection name from content metadata if available
    # This could come from vector_db_metadata.chromadb_collection in the future
    
    chunk_ids = [f"{doc_id}_chunk_{c['seq']}" for c in chunks]
    texts = [c["text"] for c in chunks]
    metadatas = []
    
    for c in chunks:
        metadata = {
            "doc_id": doc_id,
            "org_id": org_id,
            "seq": c["seq"],
            "section": c.get("section"),
            "heading": c.get("heading"),
            "page": c.get("page"),
        }
        metadatas.append(metadata)
    
    success = chroma_client.upsert_chunks(
        org_id=org_id,
        collection_name=collection_name,
        chunk_ids=chunk_ids,
        embeddings=embeddings,
        texts=texts,
        metadatas=metadatas
    )
    
    if success:
        print(f"[Ingest] ChromaDB dual-write successful: {len(chunks)} chunks")
    else:
        print(f"[Ingest] ChromaDB dual-write skipped or failed")
"""
File: arioncomply-v1/ai-backend/python-backend/services/ingestion/ingest_pipeline.py
File Description: Orchestrates chunking, embedding, and metadata validation for ingestion
"""
