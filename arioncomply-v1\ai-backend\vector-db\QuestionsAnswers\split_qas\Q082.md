id: Q082
query: >-
  What's the difference between policies, procedures, and work instructions?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/7.5"
overlap_ids: []
capability_tags:
  - "Draft Doc"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Documented Information"
    id: "ISO27001:2022/7.5"
    locator: "Clause 7.5"
ui:
  cards_hint:
    - "Document hierarchy guide"
  actions:
    - type: "start_workflow"
      target: "doc_hierarchy_setup"
      label: "Define Document Structure"
output_mode: "both"
graph_required: false
notes: "Clarifying levels ensures clarity and compliance"
---
### 82) What's the difference between policies, procedures, and work instructions?

**Standard terms**  
- **Documented information (ISO 27001 Cl. 7.5):** covers all three levels.

**Plain-English answer**  
- **Policies** define **what** and **why** (high-level objectives and principles).  
- **Procedures** outline **who** does **what** and **when** (process flows).  
- **Work instructions** specify **step-by-step** tasks (detailed operator guidance).

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 7.5

**Why it matters**  
Correct classification prevents audit findings and ensures users know where to look.

**Do next in our platform**  
- Use **Document Hierarchy Guide** to map your docs.  
- Assign each template to the correct level.

**How our platform will help**  
- **[Draft Doc]** Templates categorized by policy/procedure/instruction.  
- **[Workflow]** Review and approve each document.

**Likely follow-ups**  
- “Can a single document serve multiple levels?” (Only in simple cases—best practice is separation)

**Sources**  
- ISO/IEC 27001:2022 Clause 7.5
