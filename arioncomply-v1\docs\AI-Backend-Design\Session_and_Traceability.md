# Session and Traceability (Placeholder)

Decisions
- End-to-end traceability via IDs passed through all layers and logs.
- Maintain: `sessionId` (conversation), `requestId` (per HTTP request), `correlationId` (client-provided, optional), and `processId` (long-running workflows).

ID Strategy
- `requestId`: UUIDv4 (already used in Edge). Unique per request.
- `sessionId`: UUID/ULID per conversation. Returned by `conversation.start`.
- `processId`: ULID for sortability across multi-step processes.
- Propagate `orgId`/`userId` from JWT into `RequestMeta`.

Logging Links
- `api_request_logs.request_id` is the spine key; `api_event_logs.request_id` ties granular events.
- Store `session_id`, `process_id`, and `correlation_id` in `details` where useful.

Open Questions
- Finalize ULID vs UUID for session/process; sort order needs.
- Client responsibilities for `correlationId` propagation.

Next Steps
- Add JWT parsing middleware to set `orgId`/`userId` consistently.
- Include `response_sent` and `stream_finished` with durations.
- Document log query recipes for end-to-end tracing.

