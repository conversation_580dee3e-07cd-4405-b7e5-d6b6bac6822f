#!/usr/bin/env python3
# File: tools/checks/check-header-paths.py
# File Description: Strictly validates header 'File: arioncomply-v1/...' against real path
# Purpose: Ensure every file's header path matches its repository-relative path exactly
# Notes: Mirrors include/exclude rules from check-headers.sh; prints mismatches and missing headers

import os
import re
import subprocess
from pathlib import Path

INCLUDE_EXTS = {
    'ts', 'tsx', 'js', 'jsx', 'py', 'sh', 'sql', 'md', 'html', 'css', 'toml', 'yaml', 'yml', 'mdx', 'dart'
}
INCLUDE_BASENAMES = set()  # limit to scoped dirs only
SKIP_EXTS = {
    'json', 'png', 'jpg', 'jpeg', 'gif', 'pdf', 'svg', 'ico', 'zip', 'gz', 'bz2', 'xz', '7z', 'rar',
    'bmp', 'tiff', 'webp', 'mp3', 'mp4', 'mov', 'avi', 'woff', 'woff2', 'eot', 'ttf', 'otf', 'wasm',
    'class', 'jar', 'war', 'iso', 'bin', 'dylib', 'so', 'dll', 'parquet', 'csv', 'tsv', 'doc', 'docx',
    'xlsx'
}

# Scope prefixes: only check files under these repo-relative directories
SCOPE_PREFIXES = [
    'arioncomply-v1/supabase/functions/',
    'arioncomply-v1/ai-backend/python-backend/',
    'arioncomply-v1/db/migrations/',
    'arioncomply-v1/ai-backend/supabase_migrations/',
    'arioncomply-v1/config/',
    'arioncomply-v1/schemas/',
    'arioncomply-v1/testing/workflow-gui/',
    'arioncomply-v1/frontend-flutter/',
    'arioncomply-v1/.kiro/specs/',
    'arioncomply-v1/ai-backend/vector-db/',
]

# Vector DB content-like extensions to exclude even within scope
VECTOR_CONTENT_EXTS = {'pdf', 'doc', 'docx', 'csv', 'tsv', 'parquet', 'txt'}

# Vector DB subpaths to exclude (content corpora)
VECTOR_EXCLUDE_DIRS = [
    'arioncomply-v1/ai-backend/vector-db/QuestionsAnswers/',
    'arioncomply-v1/ai-backend/vector-db/inputs/',
]

HEADER_RE = re.compile(r"File:\s*([^\s>]+)")

def repo_root() -> Path:
    """Return the repository root directory (via git) or current directory."""
    try:
        root = subprocess.check_output(["git", "rev-parse", "--show-toplevel"], text=True).strip()
        return Path(root)
    except Exception:
        return Path.cwd()

def iter_files(root: Path):
    """Yield file paths under root honoring basic ignore rules."""
    # Use ripgrep to mirror .gitignore behavior and exclusions
    try:
        cmd = [
            'rg', '--files', '--hidden', '--no-ignore-vcs',
            '-g', '!**/node_modules/**',
            '-g', '!**/.git/**',
            '-g', '!**/dist/**',
            '-g', '!**/build/**'
        ]
        out = subprocess.check_output(cmd, cwd=root, text=True)
        for line in out.splitlines():
            yield root / line
    except subprocess.CalledProcessError:
        # Fallback to walking the tree
        for p in root.rglob('*'):
            if p.is_file():
                yield p

def _in_scope(rel: str) -> bool:
    return any(rel.startswith(prefix) for prefix in SCOPE_PREFIXES)

def should_check(p: Path, repo: Path) -> bool:
    """Whether the file extension/basename should be header-checked."""
    rel = str(p.relative_to(repo)).replace('\\', '/')
    if not _in_scope(rel):
        return False
    ext = p.suffix.lstrip('.')
    base = p.name
    if ext in SKIP_EXTS:
        return False
    # Exclude vector-db content directories entirely, but still allow utility scripts under them
    if any(rel.startswith(d) for d in VECTOR_EXCLUDE_DIRS):
        # allow python or shell utilities under excluded dirs
        if ext not in {'py', 'sh'}:
            return False
    # Exclude vector-db content file extensions
    if rel.startswith('arioncomply-v1/ai-backend/vector-db/') and ext in VECTOR_CONTENT_EXTS:
        return False
    return (ext in INCLUDE_EXTS) or (base in INCLUDE_BASENAMES)

def find_header_path(p: Path) -> str | None:
    """Search the first ~10 lines for a 'File: ...' header and return its value."""
    try:
        with p.open('r', encoding='utf-8', errors='ignore') as f:
            for i in range(10):
                line = f.readline()
                if not line:
                    break
                m = HEADER_RE.search(line)
                if m:
                    return m.group(1)
        return None
    except Exception:
        return None

def main():
    """Scan files and print missing/mismatched header paths; exit non-zero on failures."""
    root = repo_root()
    mismatches = []
    missing = []
    ok = 0
    for p in iter_files(root):
        if not should_check(p, root):
            continue
        header_path = find_header_path(p)
        if not header_path:
            missing.append(str(p.relative_to(root)))
            continue
        # Normalize paths
        declared = header_path.strip()
        # Accept optional surrounding quotes or trailing ' -->'
        declared = declared.strip('"\' )')
        # Compute expected path (repo relative)
        rel = str(p.relative_to(root)).replace('\\', '/')
        expected = rel
        # Some headers include the repo folder prefix (arioncomply-v1/...). Enforce that.
        # If declared does not start with that prefix, treat it as mismatch.
        if not declared.startswith('arioncomply-v1/') and rel.startswith('arioncomply-v1/'):
            mismatches.append((declared, rel))
            continue
        # Compare exact match
        if declared != rel:
            mismatches.append((declared, rel))
        else:
            ok += 1

    if missing:
        print('Files missing header (first 10 lines must contain "File: arioncomply-v1/..." ):\n')
        for f in missing:
            print(f'  {f}')
        print()
    if mismatches:
        print('Files with header path mismatch (declared vs actual):\n')
        for declared, rel in mismatches:
            print(f'  declared: {declared}\n  actual:   {rel}\n')
    if not missing and not mismatches:
        print(f'All checked files have correct header paths. OK={ok}')
    else:
        # Non-zero exit for CI-style visibility
        exit(1)

if __name__ == '__main__':
    main()
