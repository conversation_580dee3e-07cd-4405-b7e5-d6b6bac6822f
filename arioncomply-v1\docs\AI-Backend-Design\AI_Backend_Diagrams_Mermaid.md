# AI Backend Diagrams (Mermaid)

This page mirrors the PlantUML diagrams using Mermaid so you can compare rendering and content side‑by‑side on GitHub.

## Topology

```mermaid
flowchart TD
  subgraph SUPA[Supabase]
    Edge[Edge Functions (Deno)]
    APPDB[(Postgres / App DB)]
    PG[(Postgres / pgvector)]
    Storage[Storage]
  end

  subgraph BE[AI Backend]
    API[API (FastAPI)]
    Worker[Worker (RQ/Celery)]
    Redis[(Redis)]
  end

  subgraph LLMs[Local LLMs]
    LLM[OpenAI‑compatible endpoints]
  end

  Chroma[(Chroma - optional)]
  UI[Web App]

  UI -->|HTTPS (JWT)| Edge
  Edge -->|forward requests| API

  API -->|enqueue jobs| Redis
  Redis -->|job| Worker

  Worker -->|embeddings upsert/search| PG
  Worker -->|download/upload artifacts (signed URLs)| Storage

  API -->|/v1 chat| LLM
  Worker -->|embeddings (optional)| LLM
  Worker -->|local vector (optional)| Chroma
  Edge -->|minimal logs (optional)| APPDB
  API -->|configs/status/logs| APPDB
```

## Ingest / Index (LLM 2.0)

```mermaid
sequenceDiagram
  autonumber
  participant Edge as Edge Function
  participant API as API (FastAPI) /ingest
  participant R as Redis
  participant W as Worker
  participant APPDB as App DB
  participant ST as Supabase Storage
  participant ETL as ETL: detect • convert • OCR • normalize • chunk
  participant EMB as Embedder (local first)
  participant PG as Postgres/pgvector
  participant Chroma as Chroma (optional)

  Edge->>API: POST /ingest (tenant, doc_id, path, meta)
  API->>APPDB: create job(status=queued)
  API->>R: enqueue(job_id)
  API-->>Edge: 202 Accepted

  R-->>W: job(job_id)
  W->>ST: signed URL (download)
  W->>ST: download to /tmp
  W->>W: checksum • MIME detect • size
  W->>APPDB: lookup (doc_id, checksum)
  APPDB-->>W: exists? (duplicate)

  alt duplicate content
    W->>APPDB: update job(status=skipped) (reason=duplicate)
  else new or changed
    W->>ETL: convert / OCR / normalize / chunk
    ETL-->>W: chunks[] + meta (pages, sections, tags)
    W->>EMB: embed(chunks) (local /v1; fallback provider)
    EMB-->>W: vectors
    W->>PG: upsert vectors + metadata
    W->>Chroma: upsert (optional)
    W->>APPDB: update doc + job (status=completed, counts, timings)
  end

  W->>ST: cleanup temp (optional)
  W-->>API: progress/logs (optional)
```

## RAG Generate (LLM 2.0)

```mermaid
sequenceDiagram
  autonumber
  participant UI
  participant Edge
  participant API as API /generate
  participant R as Redis
  participant W as Worker
  participant PRE as Preproc (LLM 2.0)
  participant Chroma as Chroma (optional)
  participant PG as Postgres/pgvector
  participant PC as Prompt Composer
  participant PR as Provider Router
  participant LLM as Local LLM (/v1)
  participant GLLM as Cloud LLM (fallback)
  participant HITL as Verifier / HITL

  UI->>Edge: POST conversation.send
  Edge->>API: forward (auth, ids, headers)
  API->>R: enqueue(job)
  R-->>W: job

  W->>PRE: normalize, classify intent, extract entities,\napply rules, redact PII
  PRE-->>W: {intent, entities, filters, redactions}
  W->>W: decide path (rules‑only vs RAG)

  W->>Chroma: query (filters) [if present]
  W->>PG: query (filters) [fallback]
  W->>W: select top‑k, build citations

  W->>PC: compile prompt (instructions + context + controls)
  PC-->>W: prompt

  W->>PR: select provider (local first; guard rails)
  PR->>LLM: /v1/chat/completions
  LLM-->>PR: stream tokens
  PR-->>W: stream
  Note over PR: on guard/fail → anonymized fallback
  PR->>GLLM: chat (anonymized)
  GLLM-->>PR: stream
  PR-->>W: stream

  W->>HITL: optional review/checks
  HITL-->>W: approve / edits

  W-->>API: progress/logs + result (citations, model, usage)
  API-->>Edge: stream/proxy result
  Edge-->>UI: response
```

