#!/usr/bin/env python3
# File: tools/generic-checks/check-header-quality.py
# File Description: Generic header quality and function docs checker
# Purpose: Validate File/Description/Purpose headers and basic function docs with a configurable policy

import argparse
import re
import sys
from pathlib import Path
from typing import List, Tuple, Dict, Any

DEFAULT_POLICY = {
    "header": {"required_keys": ["File Description", "Purpose"], "search_lines": 30, "prefix": "auto"},
    "json": {"policy": "companion_readme"},
    "yaml": {"policy": "header_or_readme"},
    "markdown": {"require_path_header": False},
    "languages": {"python": {"check_docstrings": True}, "js": {"check_jsdoc": True, "jsdoc_lookback": 600}},
    "include": {"exts": ["ts","tsx","js","jsx","py","sh","sql","md","html","css","yaml","yml","toml"]},
    "skip": {"exts": ["png","jpg","jpeg","gif","svg","ico","zip","gz","bz2","xz","rar","7z","pdf","woff","woff2","eot","ttf","otf","mp3","mp4","mov","avi"],
              "dirs": [".git","node_modules","dist","build",".venv",".mypy_cache",".pytest_cache"]},
    "scope": {"include_globs": [], "exclude_globs": []},
}

def load_policy(path: str | None) -> Dict[str, Any]:
    if not path:
        return DEFAULT_POLICY
    p = Path(path)
    if not p.exists():
        return DEFAULT_POLICY
    try:
        import yaml  # type: ignore
        data = yaml.safe_load(p.read_text())
        # Shallow-merge with defaults
        policy = DEFAULT_POLICY.copy()
        for k, v in (data or {}).items():
            if isinstance(v, dict) and isinstance(policy.get(k), dict):
                merged = policy[k].copy()
                merged.update(v)
                policy[k] = merged
            else:
                policy[k] = v
        return policy
    except Exception:
        return DEFAULT_POLICY

def repo_root() -> Path:
    try:
        import subprocess
        out = subprocess.check_output(["git","rev-parse","--show-toplevel"], text=True).strip()
        return Path(out)
    except Exception:
        return Path.cwd()

def list_files(root: Path) -> List[Path]:
    try:
        import subprocess
        out = subprocess.check_output(["rg","--files","--hidden","--no-ignore-vcs","-g","!**/.git/**","-g","!**/node_modules/**"], cwd=root, text=True)
        return [root / p for p in out.splitlines()]
    except Exception:
        return [p for p in root.rglob("*") if p.is_file()]

def within_scope(rel: str, includes: List[str], excludes: List[str]) -> bool:
    from fnmatch import fnmatch
    if includes:
        if not any(fnmatch(rel, pat) for pat in includes):
            return False
    if excludes:
        if any(fnmatch(rel, pat) for pat in excludes):
            return False
    return True

def read_head(p: Path, lines: int) -> str:
    try:
        with p.open("r", encoding="utf-8", errors="ignore") as f:
            return "".join(f.readline() for _ in range(lines))
    except Exception:
        return ""

def _has_companion_readme(p: Path) -> bool:
    d = p.parent
    base = p.stem
    for cand in (f"{base}.README.md", f"README.{base}.md", f"{base}-README.md", f"{base}_README.md", "README.md"):
        if (d / cand).exists():
            return True
    return False

def check_header_quality(p: Path, policy: Dict[str, Any], repo: Path) -> Tuple[bool, List[str]]:
    head = read_head(p, policy["header"]["search_lines"]) 
    problems: List[str] = []
    ext = p.suffix.lstrip('.')

    if ext == 'md':
        if policy.get("markdown",{}).get("require_path_header", False):
            if 'File:' not in head:
                problems.append('markdown missing File: path header')
        return (len(problems) == 0, problems)

    if ext == 'json':
        pol = policy.get('json',{}).get('policy','companion_readme')
        if pol == 'companion_readme' and not _has_companion_readme(p):
            problems.append('json missing companion README')
        elif pol == 'inline_header' and 'File:' not in head:
            problems.append('json missing inline File: header')
        return (len(problems) == 0, problems)

    if ext in ('yaml','yml'):
        pol = policy.get('yaml',{}).get('policy','header_or_readme')
        has_header = ('File:' in head) or head.strip().startswith('#')
        if pol == 'companion_readme' and not _has_companion_readme(p):
            problems.append('yaml missing companion README')
        elif pol == 'header_or_readme' and not has_header and not _has_companion_readme(p):
            problems.append('yaml missing header or companion README')
        return (len(problems) == 0, problems)

    # Code files require File header and required keys
    required = policy['header'].get('required_keys', [])
    if 'File:' not in head:
        problems.append('missing File: header')
    for k in required:
        if k not in head:
            problems.append(f'missing {k}')

    if ext == 'sql':
        if not any(k in head for k in ('Migration','Purpose','Security','RLS','Policies')):
            problems.append('sql header lacks Migration/Purpose/Security/RLS notes')
    if ext == 'sh':
        if not head.startswith('#!'):
            problems.append('missing shebang')
        if not any(k in head for k in ('Usage','Env','Purpose')):
            problems.append('shell header lacks Usage/Env/Purpose')
    if ext == 'html':
        if '<!--' not in head:
            problems.append('missing HTML header comment block')
    return (len(problems) == 0, problems)

def check_python_functions(p: Path, policy: Dict[str, Any]) -> List[str]:
    if not policy.get('languages',{}).get('python',{}).get('check_docstrings', True):
        return []
    try:
        import ast
        src = p.read_text(encoding='utf-8', errors='ignore')
        tree = ast.parse(src)
    except Exception:
        return ['python parse error']
    problems: List[str] = []
    for node in ast.walk(tree):
        if isinstance(node, ast.FunctionDef):
            doc = ast.get_docstring(node, clean=False)
            if not doc:
                problems.append(f'missing docstring: def {node.name}()')
    return problems

JS_FUNC_RE = re.compile(r'export\s+(async\s+)?function\s+(\w+)\s*\(', re.MULTILINE)
JS_CONST_FUNC_RE = re.compile(r'export\s+const\s+(\w+)\s*=\s*(async\s*)?\([^)]*\)\s*=>', re.MULTILINE)

def check_js_functions(p: Path, policy: Dict[str, Any]) -> List[str]:
    if not policy.get('languages',{}).get('js',{}).get('check_jsdoc', True):
        return []
    src = p.read_text(encoding='utf-8', errors='ignore')
    lookback = int(policy.get('languages',{}).get('js',{}).get('jsdoc_lookback', 600))
    problems: List[str] = []
    for m in JS_FUNC_RE.finditer(src):
        name = m.group(2)
        start = m.start()
        prev = src[max(0, start-lookback):start]
        if '/**' not in prev:
            problems.append(f'missing JSDoc: function {name}()')
    for m in JS_CONST_FUNC_RE.finditer(src):
        name = m.group(1)
        start = m.start()
        prev = src[max(0, start-lookback):start]
        if '/**' not in prev:
            problems.append(f'missing JSDoc: const {name} = () =>')
    return problems

def main() -> int:
    parser = argparse.ArgumentParser(description='Header quality and function docs checker')
    parser.add_argument('--policy', default=None, help='Path to YAML policy file')
    parser.add_argument('--include', action='append', default=[])
    parser.add_argument('--exclude', action='append', default=[])
    parser.add_argument('--ext', action='append', default=[])
    parser.add_argument('--skip-ext', action='append', default=[])
    parser.add_argument('--write-report', default=None)
    args = parser.parse_args()

    policy = load_policy(args.policy)
    root = repo_root()
    include_exts = set(policy['include'].get('exts', [])) | set(args.ext or [])
    skip_exts = set(policy['skip'].get('exts', [])) | set(args.skip_ext or [])
    skip_dirs = set(policy['skip'].get('dirs', []))
    scope_includes = policy.get('scope',{}).get('include_globs', []) + (args.include or [])
    scope_excludes = policy.get('scope',{}).get('exclude_globs', []) + (args.exclude or [])

    files = []
    for p in list_files(root):
        try:
            rel = str(p.relative_to(root)).replace('\\','/')
        except Exception:
            continue
        if any(seg in skip_dirs for seg in Path(rel).parts):
            continue
        ext = p.suffix.lstrip('.')
        if ext in skip_exts:
            continue
        if ext not in include_exts and p.name not in ('Makefile',):
            continue
        if not within_scope(rel, scope_includes, scope_excludes):
            continue
        files.append(p)

    header_failures: Dict[str, List[str]] = {}
    func_failures: Dict[str, List[str]] = {}
    for p in files:
        ok, issues = check_header_quality(p, policy, root)
        if not ok:
            header_failures[str(p.relative_to(root))] = issues
        ext = p.suffix.lstrip('.')
        if ext == 'py':
            probs = check_python_functions(p, policy)
            if probs:
                func_failures[str(p.relative_to(root))] = probs
        if ext in ('ts','tsx','js','jsx'):
            probs = check_js_functions(p, policy)
            if probs:
                func_failures[str(p.relative_to(root))] = probs

    out_lines: List[str] = []
    out_lines.append('Header Quality Report')
    out_lines.append('=====================')
    out_lines.append(f'Total files scanned: {len(files)}')
    out_lines.append(f'Files with header issues: {len(header_failures)}')
    out_lines.append(f'Files with function doc issues: {len(func_failures)}')
    out_lines.append('')
    if header_failures:
        out_lines.append('Header issues (first 100):')
        for i,(path,issues) in enumerate(header_failures.items()):
            if i>=100: break
            out_lines.append(f'  - {path}: {"; ".join(issues)}')
        out_lines.append('')
    if func_failures:
        out_lines.append('Function doc issues (first 100):')
        for i,(path,issues) in enumerate(func_failures.items()):
            if i>=100: break
            out_lines.append(f'  - {path}:')
            for issue in issues[:5]:
                out_lines.append(f'      * {issue}')

    text = "\n".join(out_lines)
    if args.write_report:
        Path(args.write_report).parent.mkdir(parents=True, exist_ok=True)
        Path(args.write_report).write_text(text)
    else:
        print(text)

    return 0 if (not header_failures and not func_failures) else 1

if __name__ == '__main__':
    sys.exit(main())

