header:
  required_keys: ["File Description", "Purpose"]
  search_lines: 30
  prefix: auto   # auto | none | custom-string

json:
  policy: companion_readme   # companion_readme | inline_header | skip

yaml:
  policy: header_or_readme   # header_or_readme | companion_readme | skip

markdown:
  require_path_header: false

languages:
  python:
    check_docstrings: true
  js:
    check_jsdoc: true
    jsdoc_lookback: 600

include:
  exts: ["ts","tsx","js","jsx","py","sh","sql","md","html","css","yaml","yml","toml"]
skip:
  exts: ["png","jpg","jpeg","gif","svg","ico","zip","gz","bz2","xz","rar","7z","pdf","woff","woff2","eot","ttf","otf","mp3","mp4","mov","avi"]
  dirs: [".git","node_modules","dist","build",".venv",".mypy_cache",".pytest_cache"]

scope:
  include_globs: []   # e.g., ["src/**","docs/**"]
  exclude_globs: []   # e.g., ["**/vendor/**"]

