<!-- File: arioncomply-v1/testing/llm-comparison/web-interface/index.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Compliance Assistant: <PERSON> vs GPT</title>
  <link rel="stylesheet" href="style.css">
  <script src="frontend-config.js"></script>
  <style>
    body { font-family: sans-serif; margin: 1em; line-height: 1.5; }
    
    /* Resizable text areas with improved styling */
    textarea { 
      width: 100%; 
      font-size: 1em; 
      margin-top: 1em; 
      font-family: inherit;
      line-height: 1.4;
      padding: 0.75em;
      border: 2px solid #ddd;
      border-radius: 4px;
      resize: both; /* Allow both horizontal and vertical resizing */
      min-height: 100px;
      box-sizing: border-box;
      transition: border-color 0.2s ease;
    }
    
    textarea:focus {
      border-color: #4CAF50;
      outline: none;
      box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
    }
    
    textarea:hover {
      border-color: #aaa;
    }
    
    /* Add a subtle resize indicator */
    textarea::after {
      content: "↘";
      position: absolute;
      bottom: 2px;
      right: 2px;
      color: #ccc;
      pointer-events: none;
    }
    
    /* System prompt specific styling */
    #systemPrompt {
      min-height: 120px;
      background-color: #f9f9f9;
    }
    
    /* User input specific styling */
    #userInput {
      min-height: 80px;
      font-size: 1.1em;
    }
    
    /* Results section */
    #results { 
      display: flex; 
      gap: 1em; 
      margin-top: 1em; 
    }
    
    .panel { 
      width: 50%; 
      background: #f8f8f8; 
      padding: 1em; 
      border: 1px solid #ccc; 
      border-radius: 6px;
      min-height: 200px;
    }
    
    /* Response output areas - resizable and with proper text wrapping */
    .response-output {
      width: 100%;
      min-height: 150px;
      max-height: 600px;
      padding: 0.75em;
      border: 2px solid #e0e0e0;
      border-radius: 4px;
      background: white;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-size: 0.95em;
      line-height: 1.5;
      white-space: pre-wrap; /* Preserve line breaks but allow wrapping */
      word-wrap: break-word; /* Break long words if necessary */
      overflow-y: auto;
      resize: both; /* Make resizable */
      box-sizing: border-box;
      margin-top: 0.5em;
      transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }
    
    .response-output:hover {
      border-color: #bbb;
    }
    
    .response-output:focus {
      outline: none;
      border-color: #4CAF50;
      box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
    }
    
    .response-output.loading {
      background-color: #f0f8ff;
      border-color: #87CEEB;
      font-style: italic;
      color: #666;
    }
    
    .response-output.error {
      background-color: #fff5f5;
      border-color: #ff6b6b;
      color: #d32f2f;
    }
    
    .response-output.success {
      background-color: #f8fff8;
      border-color: #4CAF50;
    }
    
    /* Config section */
    #config input { 
      margin-right: 1em; 
      padding: 0.5em;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    
    /* Button styling */
    button {
      padding: 0.6em 1.2em;
      margin: 0.25em;
      border: none;
      border-radius: 4px;
      background-color: #4CAF50;
      color: white;
      cursor: pointer;
      font-size: 0.95em;
      transition: background-color 0.2s;
    }
    
    button:hover {
      background-color: #45a049;
    }
    
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    
    /* Save form styling */
    #saveTestForm {
      background: #f5f5f5;
      border: 2px solid #ddd;
      border-radius: 8px;
    }
    
    #saveTestForm label {
      display: block;
      margin-bottom: 0.5em;
      font-weight: 500;
    }
    
    #saveTestForm input, #saveTestForm select, #saveTestForm textarea {
      width: 100%;
      margin-top: 0.25em;
      margin-bottom: 0.5em;
      padding: 0.5em;
      border: 2px solid #ddd;
      border-radius: 4px;
      font-family: inherit;
    }
    
    #saveTestForm textarea {
      resize: both;
      min-height: 80px;
      line-height: 1.4;
    }
    
    #saveTestForm input:focus, #saveTestForm select:focus, #saveTestForm textarea:focus {
      border-color: #4CAF50;
      outline: none;
      box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
    }
    
    /* Responsive design for smaller screens */
    @media (max-width: 768px) {
      #results {
        flex-direction: column;
      }
      
      .panel {
        width: 100%;
      }
      
      /* Adjust textarea sizes for mobile */
      textarea {
        min-height: 80px;
      }
      
      .response-output {
        min-height: 120px;
      }
    }
    
    /* Saved tests response areas */
    .saved-test-response {
      background: #f8f8f8; 
      padding: 0.5em; 
      border-radius: 3px; 
      max-height: 150px; 
      overflow-y: auto;
      white-space: pre-wrap;
      word-wrap: break-word;
      font-family: inherit;
      line-height: 1.4;
      border: 1px solid #e0e0e0;
      resize: vertical;
      min-height: 60px;
    }
    
    .saved-test-error {
      background: #ffe6e6; 
      padding: 0.5em; 
      border-radius: 3px; 
      color: #d32f2f;
      white-space: pre-wrap;
      word-wrap: break-word;
      border: 1px solid #ffcdd2;
    }
    /* Parameter testing additions - minimal styling */
.parameter-section {
  background: #f8f9fa;
  padding: 1em;
  margin: 1em 0;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.parameter-controls {
  display: none;
  margin-top: 1em;
}

.parameter-controls.expanded {
  display: block;
}

.parameter-row {
  display: flex;
  gap: 1em;
  margin: 0.5em 0;
  align-items: center;
}

.parameter-input {
  width: 80px;
  padding: 0.3em;
  border: 1px solid #ccc;
  border-radius: 3px;
}

.toggle-btn {
  background: #007acc;
  color: white;
  border: none;
  padding: 0.5em 1em;
  border-radius: 3px;
  cursor: pointer;
}

.toggle-btn:hover {
  background: #005a99;
}
  </style>
</head>
<body>
  <h1>Compliance Assistant Tester (Claude vs GPT)</h1>

  <div id="config">
    <label>Supabase URL: <input type="text" id="supabaseUrl" placeholder="https://your-project.supabase.co" /></label>
    <button onclick="saveConfig()">Save Config</button>
  </div>

  <textarea id="systemPrompt" rows="6" placeholder="Define the assistant's role and boundaries here...">
You are an ISO 27001 and GDPR compliance assistant.
- Provide clear, concise guidance based on ISO/IEC 27001:2022 and GDPR.
- Ask clarifying questions before answering.
- Use bullet points, section references, and avoid speculation.
Restrictions:
- Do NOT provide legal advice.
- Do NOT guess or hallucinate.
- Do NOT answer non-compliance questions.
  </textarea>

  <textarea id="userInput" placeholder="Ask your compliance question..."></textarea>
  <div style="display: flex; gap: 1em; margin-top: 0.5em;">
    <button onclick="submitPrompt('both')">Submit to Both</button>
    <button onclick="submitPrompt('claude')">Claude Only</button>
    <button onclick="submitPrompt('openai')">OpenAI Only</button>
    <button onclick="saveCurrentTest()" id="saveTestBtn" disabled>Save Test Results</button>
    <button onclick="loadSavedTests()">View Saved Tests</button>
  </div>

  <!-- Parameter Testing Section - ADD AFTER systemPrompt textarea -->
<div class="parameter-section">
  <button class="toggle-btn" onclick="toggleParameters()">
    🔧 Show Parameter Controls
  </button>
  
  <div id="parameter-controls" class="parameter-controls">
    <div class="parameter-row">
      <label>OpenAI Temperature:</label>
      <input type="number" id="openai-temp" class="parameter-input" min="0" max="1" step="0.1" value="0.7">
      
      <label>Claude Temperature:</label>
      <input type="number" id="claude-temp" class="parameter-input" min="0" max="1" step="0.1" value="0.7">
    </div>
    
    <div class="parameter-row">
      <label>Max Tokens:</label>
      <input type="number" id="max-tokens" class="parameter-input" min="100" max="2000" value="1024">
      
      <button onclick="resetParams()">Reset</button>
      <button onclick="showCurrentParams()">Show Current</button>
    </div>
  </div>
</div>

  <!-- Save Test Form (hidden by default) -->
  <div id="saveTestForm" style="display: none; background: #f0f0f0; padding: 1em; margin: 1em 0; border-radius: 5px;">
    <h3>Save Test Results</h3>
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1em;">
      <div>
        <label>Test Name: <input type="text" id="testName" placeholder="e.g., ISO 27001 Risk Assessment" /></label>
        <label>Category: 
          <select id="testCategory">
            <option value="general">General</option>
            <option value="iso27001">ISO 27001</option>
            <option value="gdpr">GDPR</option>
            <option value="risk-assessment">Risk Assessment</option>
            <option value="documentation">Documentation</option>
            <option value="compliance">Compliance</option>
          </select>
        </label>
      </div>
      <div>
        <label>Rating (1-5): 
          <select id="testRating">
            <option value="">No rating</option>
            <option value="5">5 - Excellent</option>
            <option value="4">4 - Good</option>
            <option value="3">3 - Average</option>
            <option value="2">2 - Poor</option>
            <option value="1">1 - Very Poor</option>
          </select>
        </label>
        <label>Preferred Response: 
          <select id="preferredResponse">
            <option value="">No preference</option>
            <option value="claude">Claude</option>
            <option value="openai">OpenAI</option>
            <option value="both">Both equally good</option>
            <option value="neither">Neither satisfactory</option>
          </select>
        </label>
      </div>
    </div>
    <label>Notes: <textarea id="testNotes" rows="3" placeholder="Additional observations, feedback, or analysis..."></textarea></label>
    <div style="margin-top: 1em;">
      <button onclick="confirmSaveTest()">Save to Database</button>
      <button onclick="cancelSaveTest()">Cancel</button>
    </div>
  </div>

  <div id="results">
    <div class="panel">
      <h2>Claude Response</h2>
      <div id="claudeOutput" class="response-output" tabindex="0">...</div>
    </div>
    <div class="panel">
      <h2>OpenAI GPT Response</h2>
      <div id="gptOutput" class="response-output" tabindex="0">...</div>
    </div>
  </div>

  <div style="text-align: center; margin-top: 1em; color: #666; font-size: 0.9em;">
    💡 Tip: All text areas are resizable - drag the corner to make them larger
  </div>

  <!-- Saved Tests Section -->
  <div id="savedTestsSection" style="display: none; margin-top: 2em;">
    <h2>Saved Compliance Tests</h2>
    <div style="margin-bottom: 1em;">
      <label>Filter by category: 
        <select id="categoryFilter" onchange="loadSavedTests()">
          <option value="">All categories</option>
          <option value="general">General</option>
          <option value="iso27001">ISO 27001</option>
          <option value="gdpr">GDPR</option>
          <option value="risk-assessment">Risk Assessment</option>
          <option value="documentation">Documentation</option>
          <option value="compliance">Compliance</option>
        </select>
      </label>
      <button onclick="closeSavedTests()" style="float: right;">Close</button>
    </div>
    <div id="savedTestsList" style="max-height: 400px; overflow-y: auto;">
      Loading...
    </div>
  </div>

  <script>
    let supabaseUrl = localStorage.getItem('supabaseUrl') || '';
    let messageHistory = [];
    let currentTestResults = null; // Store current test results for saving

    // Load saved config on page load
    window.onload = function() {
      // Try to load from frontend-config.js first
      if (typeof window.COMPLIANCE_CONFIG !== 'undefined') {
        supabaseUrl = window.COMPLIANCE_CONFIG.supabaseUrl;
        document.getElementById('supabaseUrl').value = supabaseUrl;
        localStorage.setItem('supabaseUrl', supabaseUrl);
        console.log('✅ Auto-loaded configuration from deployment');
      } else if (supabaseUrl) {
        document.getElementById('supabaseUrl').value = supabaseUrl;
      }
    }

    function saveConfig() {
      supabaseUrl = document.getElementById('supabaseUrl').value;
      localStorage.setItem('supabaseUrl', supabaseUrl);
      alert('Configuration saved!');
    }

    async function submitPrompt(mode = 'both') {
        const testParams = getTestParams();
        console.log('Running test with parameters:', testParams);
        
      const userInput = document.getElementById('userInput').value.trim();
      const systemPrompt = document.getElementById('systemPrompt').value.trim();

      if (!userInput) {
        alert('Please enter a question');
        return;
      }

      if (!supabaseUrl) {
        alert('Please configure your Supabase URL first');
        return;
      }

      // Add user message to history
      messageHistory.push({ role: "user", content: userInput });

      // Reset outputs based on mode and set loading state
      const claudeOutput = document.getElementById('claudeOutput');
      const gptOutput = document.getElementById('gptOutput');
      
      if (mode === 'both' || mode === 'claude') {
        claudeOutput.textContent = "Loading...";
        claudeOutput.className = "response-output loading";
      } else {
        claudeOutput.textContent = "Not requested";
        claudeOutput.className = "response-output";
      }
      
      if (mode === 'both' || mode === 'openai') {
        gptOutput.textContent = "Loading...";
        gptOutput.className = "response-output loading";
      } else {
        gptOutput.textContent = "Not requested";
        gptOutput.className = "response-output";
      }

      document.getElementById('saveTestBtn').disabled = true;

      // Store test data for potential saving
      currentTestResults = {
        userQuestion: userInput,
        systemPrompt: systemPrompt,
        claudeResponse: null,
        openaiResponse: null,
        claudeResponseTime: null,
        openaiResponseTime: null,
        testMode: mode
      };

      // Call APIs based on mode
      const promises = [];
      
      if (mode === 'both' || mode === 'claude') {
        promises.push(callAPI('claude', [...messageHistory], systemPrompt));
      }
      
      if (mode === 'both' || mode === 'openai') {
        promises.push(callAPI('openai', [...messageHistory], systemPrompt));
      }

      // Wait for all requested APIs to complete
      if (promises.length > 0) {
        await Promise.allSettled(promises);
      }

      // Enable save button if we have any responses
      if (currentTestResults.claudeResponse || currentTestResults.openaiResponse) {
        document.getElementById('saveTestBtn').disabled = false;
      }

      // Clear input
      document.getElementById('userInput').value = '';
    }

    async function callAPI(provider, messages, systemPrompt) {
      const startTime = Date.now();
      const TIMEOUT_MS = 30000; // 30 second timeout
      
      try {
        // Create abort controller for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_MS);
        
        const response = await fetch(`${supabaseUrl}/functions/v1/compliance-proxy`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${window.COMPLIANCE_CONFIG?.anonKey || ''}`,
          },
          body: JSON.stringify({
            provider: provider,
            messages: messages,
            systemPrompt: systemPrompt
          }),
          signal: controller.signal
        });

        // Clear timeout if request completes
        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        
        if (data.error) {
          throw new Error(data.error);
        }

        const reply = data.content || "No response";
        const responseTime = Date.now() - startTime;
        
        // Update the appropriate output panel with success
        if (provider === 'claude') {
          const claudeOutput = document.getElementById('claudeOutput');
          claudeOutput.textContent = reply;
          claudeOutput.className = "response-output success";
          currentTestResults.claudeResponse = reply;
          currentTestResults.claudeResponseTime = responseTime;
        } else if (provider === 'openai') {
          const gptOutput = document.getElementById('gptOutput');
          gptOutput.textContent = reply;
          gptOutput.className = "response-output success";
          currentTestResults.openaiResponse = reply;
          currentTestResults.openaiResponseTime = responseTime;
        }

        // Add assistant response to history (only once, not for both providers)
        if (provider === 'claude') {
          messageHistory.push({ role: "assistant", content: reply });
        }

        return { provider, content: reply, responseTime, status: 'success' };

      } catch (error) {
        const responseTime = Date.now() - startTime;
        console.error(`${provider} error:`, error);
        
        // Determine error type and message
        let errorMessage;
        let errorType;
        
        if (error.name === 'AbortError') {
          errorType = 'timeout';
          errorMessage = `⏰ Timeout after ${TIMEOUT_MS/1000}s`;
        } else if (error.message.includes('HTTP 401')) {
          errorType = 'auth';
          errorMessage = `🔐 Authentication failed`;
        } else if (error.message.includes('HTTP 429')) {
          errorType = 'rate_limit';
          errorMessage = `🚦 Rate limit exceeded`;
        } else if (error.message.includes('HTTP 503') || error.message.includes('HTTP 502')) {
          errorType = 'service_unavailable';
          errorMessage = `🚫 Service unavailable`;
        } else if (!navigator.onLine) {
          errorType = 'network';
          errorMessage = `📡 No internet connection`;
        } else {
          errorType = 'unknown';
          errorMessage = `❌ Error: ${error.message}`;
        }
        
        // Update UI with error state
        const fullErrorMessage = `${errorMessage}\n(${responseTime}ms before failure)`;
        
        if (provider === 'claude') {
          const claudeOutput = document.getElementById('claudeOutput');
          claudeOutput.textContent = fullErrorMessage;
          claudeOutput.className = "response-output error";
          // Record error details for potential saving
          currentTestResults.claudeResponse = null;
          currentTestResults.claudeError = errorMessage;
          currentTestResults.claudeErrorType = errorType;
          currentTestResults.claudeResponseTime = responseTime;
        } else if (provider === 'openai') {
          const gptOutput = document.getElementById('gptOutput');
          gptOutput.textContent = fullErrorMessage;
          gptOutput.className = "response-output error";
          // Record error details for potential saving
          currentTestResults.openaiResponse = null;
          currentTestResults.openaiError = errorMessage;
          currentTestResults.openaiErrorType = errorType;
          currentTestResults.openaiResponseTime = responseTime;
        }
        
        return { provider, error: errorMessage, errorType, responseTime, status: 'error' };
      }
    }

    function saveCurrentTest() {
      if (!currentTestResults || (!currentTestResults.claudeResponse && !currentTestResults.openaiResponse)) {
        alert('No test results to save');
        return;
      }
      
      // Update preferred response options based on what was tested
      const preferredSelect = document.getElementById('preferredResponse');
      const hasClaudeResponse = currentTestResults.claudeResponse && currentTestResults.claudeResponse.trim() !== '';
      const hasOpenAIResponse = currentTestResults.openaiResponse && currentTestResults.openaiResponse.trim() !== '';
      
      // Clear existing options
      preferredSelect.innerHTML = '<option value="">No preference</option>';
      
      // Add options based on available responses
      if (hasClaudeResponse && hasOpenAIResponse) {
        preferredSelect.innerHTML += `
          <option value="claude">Claude</option>
          <option value="openai">OpenAI</option>
          <option value="both">Both equally good</option>
          <option value="neither">Neither satisfactory</option>
        `;
      } else if (hasClaudeResponse) {
        preferredSelect.innerHTML += `
          <option value="claude">Claude (only tested)</option>
          <option value="neither">Not satisfactory</option>
        `;
      } else if (hasOpenAIResponse) {
        preferredSelect.innerHTML += `
          <option value="openai">OpenAI (only tested)</option>
          <option value="neither">Not satisfactory</option>
        `;
      }
      
      document.getElementById('saveTestForm').style.display = 'block';
      document.getElementById('testName').focus();
    }

    function cancelSaveTest() {
      document.getElementById('saveTestForm').style.display = 'none';
      clearSaveForm();
    }

    async function confirmSaveTest() {
      const testName = document.getElementById('testName').value.trim();
      const category = document.getElementById('testCategory').value;
      const rating = document.getElementById('testRating').value ? parseInt(document.getElementById('testRating').value) : null;
      const preferredResponse = document.getElementById('preferredResponse').value || null;
      const notes = document.getElementById('testNotes').value.trim();

      if (!testName) {
        alert('Please enter a test name');
        return;
      }

      try {
        const response = await fetch(`${supabaseUrl}/functions/v1/compliance-proxy/save-test`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${window.COMPLIANCE_CONFIG?.anonKey || ''}`,
          },
          body: JSON.stringify({
            ...currentTestResults,
            testName,
            category,
            rating,
            preferredResponse,
            notes
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        
        if (result.error) {
          throw new Error(result.error);
        }

        alert('Test saved successfully!');
        document.getElementById('saveTestForm').style.display = 'none';
        clearSaveForm();

      } catch (error) {
        console.error('Error saving test:', error);
        alert(`Failed to save test: ${error.message}`);
      }
    }

    function clearSaveForm() {
      document.getElementById('testName').value = '';
      document.getElementById('testCategory').value = 'general';
      document.getElementById('testRating').value = '';
      document.getElementById('preferredResponse').value = '';
      document.getElementById('testNotes').value = '';
    }

    // Load saved tests functionality
    async function loadSavedTests() {
      const category = document.getElementById('categoryFilter')?.value || '';
      
      try {
        let url = `${supabaseUrl}/functions/v1/compliance-proxy/get-tests?limit=50`;
        if (category) {
          url += `&category=${category}`;
        }

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${window.COMPLIANCE_CONFIG?.anonKey || ''}`,
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        if (data.error) {
          throw new Error(data.error);
        }

        displaySavedTests(data.tests);
        document.getElementById('savedTestsSection').style.display = 'block';

      } catch (error) {
        console.error('Error loading tests:', error);
        alert(`Failed to load tests: ${error.message}`);
      }
    }

    function displaySavedTests(tests) {
      const container = document.getElementById('savedTestsList');
      
      if (!tests || tests.length === 0) {
        container.innerHTML = '<p>No saved tests found.</p>';
        return;
      }
// Parameter control functions
      function toggleParameters() {
  const controls = document.getElementById('parameter-controls');
  const button = document.querySelector('.toggle-btn');
  
  if (controls.classList.contains('expanded')) {
    controls.classList.remove('expanded');
    button.textContent = '🔧 Show Parameter Controls';
  } else {
    controls.classList.add('expanded');
    button.textContent = '🔧 Hide Parameter Controls';
  }
}

function resetParams() {
  document.getElementById('openai-temp').value = '0.7';
  document.getElementById('claude-temp').value = '0.7';
  document.getElementById('max-tokens').value = '1024';
  console.log('Parameters reset to defaults');
}

function showCurrentParams() {
  const params = {
    openai_temp: document.getElementById('openai-temp').value,
    claude_temp: document.getElementById('claude-temp').value,
    max_tokens: document.getElementById('max-tokens').value
  };
  
  alert(`Current Parameters:\nOpenAI Temp: ${params.openai_temp}\nClaude Temp: ${params.claude_temp}\nMax Tokens: ${params.max_tokens}`);
  console.log('Current parameters:', params);
}

function getCurrentTestParameters() {
  return {
    openai_temperature: parseFloat(document.getElementById('openai-temp').value),
    claude_temperature: parseFloat(document.getElementById('claude-temp').value),
    max_tokens: parseInt(document.getElementById('max-tokens').value)
  };
}

      const html = tests.map(test => {
        const hasClaudeResponse = test.claude_response && test.claude_response.trim() !== '';
        const hasOpenAIResponse = test.openai_response && test.openai_response.trim() !== '';
        const hasClaudeError = test.claude_error && test.claude_error.trim() !== '';
        const hasOpenAIError = test.openai_error && test.openai_error.trim() !== '';
        
        return `
        <div style="border: 1px solid #ddd; padding: 1em; margin-bottom: 1em; border-radius: 5px;">
          <h4>${test.test_name || 'Untitled Test'} 
            <span style="font-size: 0.8em; color: #666;">(${test.category || 'general'})</span>
            ${test.rating ? `<span style="color: #f39c12;">★${test.rating}</span>` : ''}
            <span style="font-size: 0.8em; color: #999;">
              [${hasClaudeResponse ? 'Claude ✅' : hasClaudeError ? 'Claude ❌' : ''}${hasClaudeResponse && hasOpenAIResponse ? ' + ' : hasClaudeError && hasOpenAIError ? ' + ' : ''}${hasOpenAIResponse ? 'OpenAI ✅' : hasOpenAIError ? 'OpenAI ❌' : ''}]
            </span>
          </h4>
          <p><strong>Question:</strong> ${test.user_question}</p>
          <div style="display: grid; grid-template-columns: ${hasClaudeResponse && hasOpenAIResponse ? '1fr 1fr' : '1fr'}; gap: 1em; margin: 1em 0;">
            ${hasClaudeResponse ? `
              <div>
                <strong>Claude Response:</strong>
                <div class="saved-test-response">
                  ${test.claude_response.substring(0, 300)}${test.claude_response.length > 300 ? '...' : ''}
                </div>
                ${test.claude_response_time_ms ? `<small>Response time: ${test.claude_response_time_ms}ms</small>` : ''}
              </div>
            ` : hasClaudeError ? `
              <div>
                <strong>Claude Error:</strong>
                <div class="saved-test-error">
                  ${test.claude_error}
                </div>
                <small>Error type: ${test.claude_error_type || 'unknown'}</small>
                ${test.claude_response_time_ms ? `<small> • Time before failure: ${test.claude_response_time_ms}ms</small>` : ''}
              </div>
            ` : `
              <div style="color: #999; font-style: italic;">
                <strong>Claude Response:</strong> Not tested
              </div>
            `}
            
            <!-- OpenAI Response/Error -->
            ${hasOpenAIResponse ? `
              <div>
                <strong>OpenAI Response:</strong>
                <div class="saved-test-response">
                  ${test.openai_response.substring(0, 300)}${test.openai_response.length > 300 ? '...' : ''}
                </div>
                ${test.openai_response_time_ms ? `<small>Response time: ${test.openai_response_time_ms}ms</small>` : ''}
              </div>
            ` : hasOpenAIError ? `
              <div>
                <strong>OpenAI Error:</strong>
                <div class="saved-test-error">
                  ${test.openai_error}
                </div>
                <small>Error type: ${test.openai_error_type || 'unknown'}</small>
                ${test.openai_response_time_ms ? `<small> • Time before failure: ${test.openai_response_time_ms}ms</small>` : ''}
              </div>
            ` : `
              <div style="color: #999; font-style: italic;">
                <strong>OpenAI Response:</strong> Not tested
              </div>
            `}
          </div>
          ${test.preferred_response ? `<p><strong>Preferred:</strong> ${test.preferred_response}</p>` : ''}
          ${test.notes ? `<p><strong>Notes:</strong> ${test.notes}</p>` : ''}
          <small style="color: #666;">
            Saved: ${new Date(test.created_at).toLocaleString()}
            ${(!hasClaudeResponse && !hasOpenAIResponse) ? ' • Failed test' : 
              (!hasClaudeResponse || !hasOpenAIResponse) ? ' • Partial result' : ''}
          </small>
        </div>
      `}).join('');

      container.innerHTML = html;
    }

    function closeSavedTests() {
      document.getElementById('savedTestsSection').style.display = 'none';
    }

    // Allow Enter key to submit
    document.addEventListener('DOMContentLoaded', function() {
      document.getElementById('userInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          submitPrompt();
        }
      });
    });
  </script>
</body>
</html>
<!-- File: arioncomply-v1/testing/llm-comparison/web-interface/index.html -->
