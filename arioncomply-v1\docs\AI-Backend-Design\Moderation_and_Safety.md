# Moderation and Safety (Placeholder)

Decisions
- Pre-call moderation for user messages; post-call safety checks for outputs.
- Stricter anonymization/redaction before any GLLM call.

Design Points
- Lightweight policies: toxicity, PII leakage, data classification tags.
- Escalation paths: block, warn-and-continue, or HITL required.
- Provider-specific safety settings vs custom policies.

Open Questions
- Taxonomy and thresholds; jurisdictional differences.
- Logging fields for safety outcomes without leaking content.

Next Steps
- Implement moderation adapter and policy config.
- Unit tests with sample edge cases; add `moderation_result` to `api_event_logs`.

