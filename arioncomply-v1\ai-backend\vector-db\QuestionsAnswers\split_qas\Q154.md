id: Q154
query: >-
  What's this about data not being allowed to leave certain countries?
packs:
  - "GDPR:2016"
  - "NIS2:2023"
primary_ids:
  - "GDPR:2016/Arts.44-50"
  - "NIS2:2023/Art.5"
overlap_ids: []
capability_tags:
  - "Register"
  - "Workflow"
flags:
  - "LOCAL LAW_CHECK"
sources:
  - title: "GDPR — International Transfers"
    id: "GDPR:2016/Arts.44-50"
    locator: "Articles 44–50"
  - title: "NIS2 — Territorial Scope"
    id: "NIS2:2023/Art.5"
    locator: "Article 5"
ui:
  cards_hint:
    - "Data residency register"
  actions:
    - type: "open_register"
      target: "residency_register"
      label: "View Residency Rules"
    - type: "start_workflow"
      target: "residency_compliance"
      label: "Ensure Residency"
output_mode: "both"
graph_required: false
notes: "Some countries prohibit transfers without local storage or permit regimes"
---
### 154) What's this about data not being allowed to leave certain countries?

**Standard terms**  
- **International transfers (GDPR Arts.44–50):** SCCs, adequacy, binding rules.  
- **Territorial scope (NIS2 Art.5):** national implementations may restrict transfers.

**Plain-English answer**  
Countries like Russia, China, and some APAC require data to stay in-country or under approved schemes. Record each in the **Data Residency Register** and apply local controls or keep servers onsite **[LOCAL LAW_CHECK]**.

**Applies to**  
- **Primary:** GDPR Articles 44–50; NIS2 Directive Article 5

**Why it matters**  
Violating residency rules can lead to data seizure and fines.

**Do next in our platform**  
- Populate **Residency Register**.  
- Launch **Residency Compliance** workflow.

**How our platform will help**  
- **[Register]** Lists country-specific residency requirements.  
- **[Workflow]** Guides local storage and encryption tasks.

**Likely follow-ups**  
- “How do we set up local cloud zones?” (Use regional cloud services)

**Sources**  
- GDPR Articles 44–50; NIS2 Directive Article 5

**Legal note:** Always verify local legislation with counsel.  
