#!/usr/bin/env python3
# File: tools/validation/check-markdown-links.py
# File Description: Validate Markdown links and local file references
# Purpose: Catch broken local links in Markdown (.md/.mdx) without network access

import re
import sys
import subprocess
from pathlib import Path
from typing import Dict, List, Tuple, Iterable

ROOT = Path(__file__).resolve().parents[2]

MD_LINK_RE = re.compile(r"\[([^\]]+)\]\(([^)]+)\)")
MD_IMAGE_RE = re.compile(r"!\[[^\]]*\]\(([^)]+)\)")
REF_DEF_RE = re.compile(r"^\s*\[([^\]]+)\]:\s*(\S+)\s*$")
REF_USE_RE = re.compile(r"\[([^\]]+)\]\[([^\]]+)\]")

SKIP_SCHEMES = ("http://", "https://", "mailto:", "tel:")


def list_md_files(args: List[str]) -> List[Path]:
    if args:
        return [ROOT / a for a in args if a.endswith((".md", ".mdx")) and (ROOT / a).exists()]
    try:
        out = subprocess.check_output([
            "rg", "--files", "--hidden", "--no-ignore-vcs",
            "-g", "!**/.git/**", "-g", "!**/node_modules/**",
            "-g", "**/*.md", "-g", "**/*.mdx",
        ], cwd=ROOT, text=True)
        return [ROOT / p for p in out.splitlines()]
    except Exception:
        return [p for p in ROOT.rglob("*.md")] + [p for p in ROOT.rglob("*.mdx")]


def normalize_target(target: str) -> Tuple[str, str]:
    """Strip anchors and query; return (path, anchor)."""
    anchor = ""
    if "#" in target:
        path, frag = target.split("#", 1)
        anchor = frag
    else:
        path = target
    # Strip query if present
    if "?" in path:
        path = path.split("?", 1)[0]
    return path, anchor


def check_file_exists(base: Path, href: str) -> bool:
    # absolute-like reference: treat leading '/' as repo root
    path_str, _ = normalize_target(href)
    if not path_str or path_str.startswith(SKIP_SCHEMES) or path_str.startswith("#"):
        return True
    # ignore pure anchors
    if path_str == "#":
        return True
    # data URIs or fragments
    if ":" in path_str and not path_str.startswith("./") and not path_str.startswith("../") and not path_str.startswith("/"):
        # likely a protocol; skip
        return True
    if path_str.startswith("/"):
        target = ROOT / path_str.lstrip("/")
    else:
        target = (base.parent / path_str).resolve()
    return target.exists()


def parse_reference_defs(lines: List[str]) -> Dict[str, str]:
    refs: Dict[str, str] = {}
    for ln in lines:
        m = REF_DEF_RE.match(ln)
        if m:
            refs[m.group(1)] = m.group(2)
    return refs


def check_markdown_file(p: Path) -> List[str]:
    problems: List[str] = []
    try:
        lines = p.read_text(encoding="utf-8", errors="ignore").splitlines()
    except Exception as e:
        problems.append(f"{p}: unable to read file: {e}")
        return problems

    ref_defs = parse_reference_defs(lines)

    for i, line in enumerate(lines, start=1):
        # Inline links
        for m in MD_LINK_RE.finditer(line):
            href = m.group(2).strip()
            if any(href.startswith(s) for s in SKIP_SCHEMES) or href.startswith("#"):
                continue
            if not check_file_exists(p, href):
                problems.append(f"{p}:{i}: broken link -> {href}")
        # Images
        for m in MD_IMAGE_RE.finditer(line):
            src = m.group(1).strip()
            if any(src.startswith(s) for s in SKIP_SCHEMES) or src.startswith("#"):
                continue
            if not check_file_exists(p, src):
                problems.append(f"{p}:{i}: missing image -> {src}")
        # Reference-style uses [text][ref]
        for m in REF_USE_RE.finditer(line):
            ref = m.group(2).strip()
            target = ref_defs.get(ref)
            if not target:
                problems.append(f"{p}:{i}: undefined reference [{ref}]")
                continue
            if any(target.startswith(s) for s in SKIP_SCHEMES) or target.startswith("#"):
                continue
            if not check_file_exists(p, target):
                problems.append(f"{p}:{i}: broken reference [{ref}] -> {target}")

    return problems


def main(argv: List[str]) -> int:
    files = list_md_files(argv)
    all_problems: List[str] = []
    for f in files:
        all_problems.extend(check_markdown_file(f))
    if all_problems:
        print("Markdown link check failures:")
        for p in all_problems:
            print("  ", p)
        print(f"Total issues: {len(all_problems)} across {len(files)} files")
        return 1
    print(f"Markdown links OK (checked {len(files)} files)")
    return 0


if __name__ == "__main__":
    sys.exit(main(sys.argv[1:]))

