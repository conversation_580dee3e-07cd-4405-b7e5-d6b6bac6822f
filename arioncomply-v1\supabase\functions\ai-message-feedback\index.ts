// File: arioncomply-v1/supabase/functions/ai-message-feedback/index.ts
// File Description: Accept thumbs feedback (up/down) + optional comment for a message
// Purpose: Insert feedback rows scoped by org/user/session/message into conversation_message_feedback
// Input: POST JSON { sessionId, messageId, rating:'up'|'down', comment?, reasonCode? }
// Output: { ok: true } + traceparent; logs event 'message_feedback'
// Dependencies: _shared/supabase (service-role client), _shared/logger (request/event logs), _shared/cors
// Security/RLS: Org-scoped RLS; requires org_id claim in JWT or header extraction

import { serve } from "https://deno.land/std@0.224.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { getSupabaseAdmin } from "../_shared/supabase.ts";
import { getClientMeta, logEvent, logRequestEnd, logRequestStart } from "../_shared/logger.ts";

type Body = {
  sessionId: string;
  messageId: string;
  rating: "up" | "down";
  comment?: string;
  reasonCode?: string;
};

serve(async (req) => {
  const requestId = crypto.randomUUID();
  const meta = getClientMeta(req, requestId);

  if (req.method === "OPTIONS") return new Response("ok", { headers: corsHeaders });
  await logRequestStart(meta, req.headers);
  if (req.method !== "POST") {
    const r = new Response(JSON.stringify({ error: "Method not allowed" }), { status: 405, headers: { ...corsHeaders, "content-type": "application/json" } });
    await logRequestEnd(meta, 405);
    return r;
  }

  let body: Body;
  try {
    body = await req.json() as Body;
  } catch {
    const r = new Response(JSON.stringify({ error: "Invalid JSON body" }), { status: 400, headers: { ...corsHeaders, "content-type": "application/json" } });
    await logRequestEnd(meta, 400);
    return r;
  }

  if (!body.sessionId || !body.messageId || !body.rating || !["up", "down"].includes(body.rating)) {
    const r = new Response(JSON.stringify({ error: "sessionId, messageId, and rating ('up'|'down') are required" }), { status: 400, headers: { ...corsHeaders, "content-type": "application/json" } });
    await logRequestEnd(meta, 400);
    return r;
  }

  const db = getSupabaseAdmin();
  const row = {
    org_id: meta.orgId,
    user_id: meta.userId ?? null,
    session_id: body.sessionId,
    message_id: body.messageId,
    rating: body.rating,
    reason_code: body.reasonCode ?? null,
    comment: body.comment ?? null,
  } as const;
  const { error } = await db.from("conversation_message_feedback").insert(row);
  if (error) {
    const r = new Response(JSON.stringify({ error: "Insert failed", details: error.message }), { status: 500, headers: { ...corsHeaders, "content-type": "application/json" } });
    await logRequestEnd(meta, 500);
    return r;
  }

  await logEvent(meta, { eventType: "message_feedback", direction: "inbound", status: "ok", details: { session_id: body.sessionId, message_id: body.messageId, rating: body.rating } });
  const res = new Response(JSON.stringify({ ok: true }), { headers: { ...corsHeaders, "content-type": "application/json", "traceparent": meta.traceHeader ?? "" } });
  await logRequestEnd(meta, 200);
  return res;
});

