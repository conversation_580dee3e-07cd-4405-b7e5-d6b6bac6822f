id: Q177
query: >-
  What’s algorithmic bias and are we responsible for testing our AI for it?
packs:
  - "EUAI:2024"
primary_ids:
  - "EUAI:2024/Art.10"
overlap_ids: []
capability_tags:
  - "Report"
  - "Workflow"
flags: []
sources:
  - title: "EU AI Act — Bias & Discrimination"
    id: "EUAI:2024/Art.10"
    locator: "Article 10"
ui:
  cards_hint:
    - "Bias testing toolkit"
  actions:
    - type: "start_workflow"
      target: "bias_testing"
      label: "Test for Bias"
output_mode: "both"
graph_required: false
notes: "Obligatory for high-risk AI; test, document, mitigate bias"
---
### 177) What’s algorithmic bias and are we responsible for testing our AI for it?

**Standard term(s)**  
- **Algorithmic bias (EU AI Act Art. 10):** systematic and unfair errors in AI outputs that disadvantage certain groups, often arising from biased training data, flawed model design, or improper deployment.

**Plain-English answer**  
Algorithmic bias occurs when an AI system’s results consistently favor or disadvantage certain individuals or groups in a way that is not justified by the intended purpose. Under the EU AI Act, providers and deployers of **high-risk AI systems** must assess, test for, and mitigate bias before placing the system on the market or putting it into service. This includes validating training datasets, testing outputs across different demographic groups, and documenting the process.

**Applies to**  
- **Primary:** EU AI Act Article 10

**Why it matters**  
Bias can lead to legal violations, reputational harm, and loss of trust, especially in regulated sectors like recruitment, lending, and law enforcement.

**Do next in our platform**  
- Launch the **Bias Testing** workflow to define testing criteria, run validation, and document results.  
- Record mitigation measures and retesting schedules in your compliance documentation.

**How our platform will help**  
- **[Workflow]** Guided process for bias detection, testing, and mitigation.  
- **[Report]** Output bias assessment reports suitable for auditors or regulators.

**Likely follow-ups**  
- “Do we need to retest periodically?” (Yes — especially when data, models, or contexts change)

**Sources**  
- EU AI Act Article 10
