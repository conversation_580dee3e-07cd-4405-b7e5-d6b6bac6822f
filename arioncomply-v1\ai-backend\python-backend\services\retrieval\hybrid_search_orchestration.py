"""
File: arioncomply-v1/ai-backend/python-backend/services/retrieval/hybrid_search_orchestration.py
File Description: Hybrid search orchestration for dual-vector architecture with progressive tier escalation
Purpose: Orchestrate parallel queries across ChromaDB (public) and Supabase Vector (private) with intelligent fusion
Inputs: SearchParameters (org_id, query_text, query_embedding, limit), intent classification from EdgeFunction
Outputs: HybridSearchResult with fused results, confidence scores, tier routing decisions, and database persistence
Dependencies: supabase_vector, chromadb_client, embedding service, asyncio, requests (for Supabase REST API)
Security/RLS: Enforces org-scoped data separation, audit trail persistence, secure API calls to Supabase
Notes: Core orchestration engine for multi-tier retrieval with progressive escalation (Tier 0-3) and confidence tracking
"""

import os
import logging
import asyncio
from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass
from enum import Enum
import json

from .supabase_vector import search as supabase_vector_search
from ..ingestion.chromadb_client import chroma_client, CHROMA_ENABLED
from ..embedding import embed_texts as async_embed_texts

logger = logging.getLogger(__name__)


class SearchTier(str, Enum):
    """Search tiers for progressive escalation.

    These tiers represent increasing levels of search complexity and cost:
    - Tier 0: Fast deterministic lookup based on exact matches or patterns
    - Tier 1: Semantic search across both global (public) and org (private) vector stores
    - Tier 2: Cloud LLM-powered search for complex queries requiring reasoning
    - Tier 3: Request user clarification when query intent is unclear
    """
    TIER_0_DETERMINISTIC = "tier_0_deterministic"  # Simple pattern matching, fastest response
    TIER_1_DUAL_VECTOR = "tier_1_dual_vector"      # Semantic search across ChromaDB + Supabase
    TIER_2_CLOUD_LLM = "tier_2_cloud_llm"          # LLM-powered search for complex reasoning
    TIER_3_CLARIFY = "tier_3_clarify"              # Ask user to clarify ambiguous queries


class IntentCategory(str, Enum):
    """Intent categories for routing decisions.

    These categories help determine which vector stores to prioritize:
    - Policy/Security inquiries: Favor org-specific (private) knowledge
    - Standards/Compliance inquiries: Favor global (public) regulatory knowledge
    - Mixed inquiries: Balance between both stores with weighted fusion
    """
    POLICY_INQUIRY = "policy_inquiry"                      # Org policies, procedures (org-heavy)
    ASSESSMENT_INQUIRY = "assessment_inquiry"              # Assessment frameworks, results (mixed)
    COMPLIANCE_INQUIRY = "compliance_inquiry"              # Regulatory compliance (global-heavy)
    SECURITY_INQUIRY = "security_inquiry"                  # Security controls, practices (org-heavy)
    STANDARDS_INQUIRY = "standards_inquiry"                # ISO, NIST standards (global-heavy)
    IMPLEMENTATION_GUIDANCE = "implementation_guidance"    # How-to guidance (mixed)
    GENERAL_INQUIRY = "general_inquiry"                    # Balanced search across both stores


class VectorStore(str, Enum):
    """Vector store types for dual-vector architecture.

    - CHROMADB: Local development, global public standards (ISO, GDPR, NIST)
    - SUPABASE_VECTOR: Production, org-specific private documents (policies, assessments)
    - HYBRID: Search both stores in parallel and intelligently fuse results
    """
    CHROMADB = "chromadb"              # Local ChromaDB for public standards/regulations
    SUPABASE_VECTOR = "supabase_vector" # Supabase pgvector for org-specific private docs
    HYBRID = "hybrid"                   # Search both stores and merge results


@dataclass
class IntentClassification:
    """Intent classification result from Edge Function.

    This data structure carries the intent classification decision from the Edge Function
    to help the orchestrator decide which vector stores to query and how to weight results.
    """
    category: IntentCategory                              # Primary intent category (policy, standards, etc.)
    subcategory: Optional[str]                           # More specific sub-category if available
    confidence_score: float                              # How confident the classifier is (0.0-1.0)
    requires_public_knowledge: bool                      # Should we query global/public knowledge?
    requires_private_knowledge: bool                     # Should we query org-specific knowledge?
    suggested_vector_stores: List[VectorStore]           # Which stores the classifier recommends
    classification_metadata: Dict[str, Any]              # Additional context from classification


@dataclass
class SearchResult:
    """Individual search result from a vector store.

    Represents a single chunk of text retrieved from either ChromaDB or Supabase Vector.
    Each result includes similarity score, source tracking, and metadata for citation.
    """
    chunk_id: str                    # Unique identifier for this text chunk
    doc_id: str                      # ID of the parent document this chunk came from
    score: float                     # Similarity score (higher = more relevant)
    text: str                        # The actual text content of this chunk
    metadata: Dict[str, Any]         # Document metadata (title, source, date, etc.)
    source_store: VectorStore        # Which vector store this result came from
    pipeline_used: str               # Which search pipeline was used to find this
    embedding_dimension: int         # Dimensionality of the embedding vector used


@dataclass
class HybridSearchResult:
    """Hybrid search result with fusion and confidence scoring.

    The final result from the orchestrator after querying multiple vector stores,
    fusing results, and applying confidence-based tier escalation logic.
    """
    query_hash: str                           # Hash of the original query for deduplication
    intent_classification: IntentClassification # Original intent classification from Edge
    chunks: List[SearchResult]                # Fused and ranked search results
    confidence_score: float                   # Overall confidence in the search results
    tier_used: SearchTier                     # Which search tier was ultimately used
    search_breakdown: Dict[str, Any]          # How many results came from each store
    fusion_metadata: Dict[str, Any]           # Details about how results were combined
    escalation_reason: Optional[str]          # Why we escalated to higher tier (if any)
    total_latency_ms: float                   # Total time taken for the search
    audit_trail: Dict[str, Any]               # Tracking info for debugging and monitoring


@dataclass
class SearchParameters:
    """Search parameters for hybrid orchestration.

    Contains all the information needed to perform a hybrid search across
    multiple vector stores with proper org scoping and result limits.
    """
    org_id: str                                    # Organization ID for scoping private docs
    query_text: str                                # The user's search query in natural language
    query_embedding: Optional[List[float]] = None  # Pre-computed embedding (optional optimization)
    pipeline_name: Optional[str] = None            # Specific search pipeline to use (optional)
    limit: int = 8                                 # Maximum number of results to return
    min_score: Optional[float] = None       # Minimum similarity score threshold
    force_tier: Optional[SearchTier] = None # Force a specific tier (for testing)
    include_audit_trail: bool = True        # Whether to include detailed audit information


class HybridSearchOrchestrator:
    """Orchestrates hybrid search across dual vector stores with progressive escalation.

    This is the main orchestration engine that:
    1. Decides which vector stores to query based on intent classification
    2. Runs searches in parallel across multiple stores
    3. Fuses and ranks results intelligently
    4. Escalates to higher tiers if confidence is low
    5. Tracks performance metrics and audit trails
    """

    def __init__(self):
        """Initialize the orchestrator with vector store connections and thresholds.

        Sets up connections to both ChromaDB (local/global) and Supabase Vector (org-specific)
        and defines confidence thresholds for progressive tier escalation.
        """
        # Check which vector stores are available for this deployment
        self.chromadb_enabled = CHROMA_ENABLED  # Local ChromaDB for global standards
        self.supabase_enabled = bool(os.getenv("VECTOR_SUPABASE_URL"))  # Supabase for org docs

        # Confidence thresholds determine when to escalate to higher (more expensive) tiers
        # Higher threshold = more conservative, escalates to expensive tiers sooner
        self.confidence_thresholds = {
            "tier_0_deterministic": 0.95,  # Pattern match must be very confident
            "tier_1_dual_vector": 0.75,   # Vector search must be reasonably confident
            "tier_2_cloud_llm": 0.60,     # LLM search for lower confidence queries
            "tier_3_clarify": 0.0         # Always ask for clarification as last resort
        }
        
        # Vector store routing weights - determine how to balance results from each store
        # Based on intent: standards/compliance favor global knowledge, policy/security favor org knowledge
        self.routing_weights = {
            # Standards/regulatory questions: favor public knowledge (ISO, NIST, GDPR)
            IntentCategory.STANDARDS_INQUIRY: {"chromadb": 0.8, "supabase_vector": 0.2},
            # Policy questions: favor org-specific policies and procedures
            IntentCategory.POLICY_INQUIRY: {"chromadb": 0.3, "supabase_vector": 0.7},
            # Assessment questions: mixed - frameworks are global, results are org-specific
            IntentCategory.ASSESSMENT_INQUIRY: {"chromadb": 0.6, "supabase_vector": 0.4},
            # Security questions: favor org-specific security controls and practices
            IntentCategory.SECURITY_INQUIRY: {"chromadb": 0.4, "supabase_vector": 0.6},
            # Compliance questions: favor global regulatory knowledge
            IntentCategory.COMPLIANCE_INQUIRY: {"chromadb": 0.7, "supabase_vector": 0.3},
            # Implementation guidance: balanced mix of standards and org practices
            IntentCategory.IMPLEMENTATION_GUIDANCE: {"chromadb": 0.6, "supabase_vector": 0.4},
            # General questions: balanced search across both knowledge bases
            IntentCategory.GENERAL_INQUIRY: {"chromadb": 0.5, "supabase_vector": 0.5}
        }
        
        # Warn if no vector stores are available - this would break search functionality
        if not (self.chromadb_enabled or self.supabase_enabled):
            logger.warning("Neither ChromaDB nor Supabase Vector is available - search will fail")
    
    async def hybrid_search(self, params: SearchParameters) -> HybridSearchResult:
        """
        Execute hybrid search with progressive tier escalation.

        This is the main entry point for search requests. It:
        1. Classifies the query intent (if not provided)
        2. Starts with fast/cheap search tiers (deterministic, vector)
        3. Escalates to expensive tiers (LLM, clarification) if confidence is low
        4. Tracks performance and audit information throughout

        Args:
            params: Search parameters including query, org_id, and options

        Returns:
            HybridSearchResult with fused results and confidence scoring
        """
        # Track total time taken for performance monitoring
        start_time = asyncio.get_event_loop().time()
        # Generate consistent hash of query for deduplication and caching
        query_hash = self._generate_query_hash(params.query_text)

        # Build audit trail for debugging and monitoring
        audit_trail = {
            "query_hash": query_hash,          # For tracking duplicate queries
            "org_id": params.org_id,           # For org-scoped analysis
            "start_time": self._get_timestamp(),  # When search started
            "steps": [],
            "search_decisions": []
        }
        
        try:
            # Step 1: Intent Classification
            intent_classification = await self._classify_intent(params.query_text, params.org_id)
            audit_trail["steps"].append("intent_classified")
            audit_trail["intent_classification"] = intent_classification.__dict__
            
            # Step 2: Generate embeddings if not provided
            if not params.query_embedding:
                embedding_result = await async_embed_texts([params.query_text], pipeline_name=params.pipeline_name)
                params.query_embedding = embedding_result.embeddings[0]
                pipeline_used = embedding_result.pipeline_name
                audit_trail["steps"].append(f"embeddings_generated_with_{pipeline_used}")
            else:
                pipeline_used = params.pipeline_name or "unknown"
            
            # Step 3: Check for forced tier or deterministic match
            if params.force_tier == SearchTier.TIER_0_DETERMINISTIC:
                result = await self._tier_0_deterministic_search(params, intent_classification, audit_trail)
            else:
                # Step 4: Execute dual vector search (Tier 1)
                result = await self._tier_1_dual_vector_search(
                    params, intent_classification, pipeline_used, audit_trail
                )
                
                # Step 5: Evaluate confidence and escalate if needed
                if result.confidence_score < self.confidence_thresholds["tier_1_dual_vector"]:
                    if result.confidence_score >= self.confidence_thresholds["tier_2_cloud_llm"]:
                        result = await self._tier_2_cloud_llm_search(result, params, audit_trail)
                    else:
                        result = await self._tier_3_clarifying_questions(result, params, audit_trail)
            
            # Step 6: Finalize result
            end_time = asyncio.get_event_loop().time()
            result.total_latency_ms = (end_time - start_time) * 1000
            
            audit_trail["end_time"] = self._get_timestamp()
            audit_trail["success"] = True
            audit_trail["final_confidence"] = result.confidence_score
            audit_trail["tier_used"] = result.tier_used.value
            
            if params.include_audit_trail:
                result.audit_trail = audit_trail
            
            # Step 7: Record search confidence for future optimization
            await self._record_search_confidence(params.org_id, result)
            
            logger.info(f"Hybrid search completed: tier={result.tier_used.value}, confidence={result.confidence_score:.3f}, chunks={len(result.chunks)}")
            return result
            
        except Exception as e:
            error_msg = f"Hybrid search failed: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            audit_trail["end_time"] = self._get_timestamp()
            audit_trail["success"] = False
            audit_trail["error"] = error_msg
            
            # Return error result
            return HybridSearchResult(
                query_hash=query_hash,
                intent_classification=IntentClassification(
                    category=IntentCategory.GENERAL_INQUIRY,
                    subcategory=None,
                    confidence_score=0.0,
                    requires_public_knowledge=False,
                    requires_private_knowledge=False,
                    suggested_vector_stores=[],
                    classification_metadata={}
                ),
                chunks=[],
                confidence_score=0.0,
                tier_used=SearchTier.TIER_3_CLARIFY,
                search_breakdown={},
                fusion_metadata={},
                escalation_reason="search_error",
                total_latency_ms=0.0,
                audit_trail=audit_trail if params.include_audit_trail else {}
            )
    
    async def _classify_intent(self, query_text: str, org_id: str) -> IntentClassification:
        """Classify query intent for routing decisions.

        This is a simple heuristic classifier that looks for keywords to determine
        the user's intent. In production, this would be replaced with a trained ML model
        or the classification would come from the Edge Function.

        Args:
            query_text: The user's natural language query
            org_id: Organization ID for context

        Returns:
            IntentClassification with category, confidence, and routing suggestions
        """
        # Convert to lowercase for case-insensitive keyword matching
        # TODO: Replace with proper ML model or use classification from Edge Function
        query_lower = query_text.lower()
        
        # Keyword-based intent classification - match query words to intent categories
        # Policy/procedure questions usually need org-specific knowledge
        if any(keyword in query_lower for keyword in ["policy", "procedure", "process"]):
            category = IntentCategory.POLICY_INQUIRY
        # Assessment questions need both frameworks (global) and results (org-specific)
        elif any(keyword in query_lower for keyword in ["assessment", "audit", "evaluation", "review"]):
            category = IntentCategory.ASSESSMENT_INQUIRY
        # Compliance questions usually need regulatory/legal knowledge (global)
        elif any(keyword in query_lower for keyword in ["compliance", "regulation", "requirement", "mandatory"]):
            category = IntentCategory.COMPLIANCE_INQUIRY
        # Security questions often need org-specific controls and practices
        elif any(keyword in query_lower for keyword in ["incident", "security", "breach", "vulnerability"]):
            category = IntentCategory.SECURITY_INQUIRY
        # Standards questions need public regulatory knowledge (ISO, NIST, etc.)
        elif any(keyword in query_lower for keyword in ["standard", "iso", "gdpr", "ccpa", "sox", "nist", "pci"]):
            category = IntentCategory.STANDARDS_INQUIRY
        # Implementation questions need both standards (global) and practices (org)
        elif any(keyword in query_lower for keyword in ["how to", "implement", "setup", "configure"]):
            category = IntentCategory.IMPLEMENTATION_GUIDANCE
        # Fallback for unclear queries - search both knowledge bases equally
        else:
            category = IntentCategory.GENERAL_INQUIRY
        
        # Determine subcategory
        subcategory = None
        if any(keyword in query_lower for keyword in ["company", "our", "internal", "organization"]):
            subcategory = "company_specific"
        elif any(keyword in query_lower for keyword in ["what is", "define", "definition", "meaning"]):
            subcategory = "definition_inquiry"
        elif any(keyword in query_lower for keyword in ["how to", "implement", "steps", "guide"]):
            subcategory = "implementation_guidance"
        
        # Determine knowledge requirements
        requires_public = any(keyword in query_lower for keyword in [
            "standard", "regulation", "law", "gdpr", "iso", "nist", "pci", "compliance", "requirement"
        ])
        
        requires_private = any(keyword in query_lower for keyword in [
            "company", "our", "internal", "policy", "procedure", "organization", "assessment result"
        ])
        
        # Suggest vector stores based on intent
        suggested_stores = []
        if requires_public:
            suggested_stores.append(VectorStore.CHROMADB)
        if requires_private:
            suggested_stores.append(VectorStore.SUPABASE_VECTOR)
        if not suggested_stores:
            suggested_stores = [VectorStore.CHROMADB, VectorStore.SUPABASE_VECTOR]  # Both
        
        # Calculate confidence (placeholder - would use ML model)
        confidence = 0.8 if len(suggested_stores) == 1 else 0.6
        
        return IntentClassification(
            category=category,
            subcategory=subcategory,
            confidence_score=confidence,
            requires_public_knowledge=requires_public,
            requires_private_knowledge=requires_private,
            suggested_vector_stores=suggested_stores,
            classification_metadata={
                "query_length": len(query_text),
                "keyword_matches": self._extract_keywords(query_lower),
                "classifier_version": "heuristic_v1"
            }
        )
    
    async def _tier_0_deterministic_search(
        self, 
        params: SearchParameters, 
        intent_classification: IntentClassification,
        audit_trail: Dict[str, Any]
    ) -> HybridSearchResult:
        """Tier 0: Deterministic/direct response (high confidence matches)."""
        
        # This would contain deterministic responses for common queries
        # For now, return empty result to trigger vector search
        audit_trail["steps"].append("tier_0_no_deterministic_match")
        
        return await self._tier_1_dual_vector_search(params, intent_classification, "deterministic", audit_trail)
    
    async def _tier_1_dual_vector_search(
        self, 
        params: SearchParameters, 
        intent_classification: IntentClassification,
        pipeline_used: str,
        audit_trail: Dict[str, Any]
    ) -> HybridSearchResult:
        """Tier 1: Dual vector search with intelligent fusion."""
        
        audit_trail["steps"].append("tier_1_dual_vector_search_started")
        
        # Determine which vector stores to query based on intent
        query_chromadb = VectorStore.CHROMADB in intent_classification.suggested_vector_stores
        query_supabase = VectorStore.SUPABASE_VECTOR in intent_classification.suggested_vector_stores
        
        # Execute parallel searches
        search_tasks = []
        
        if query_chromadb and self.chromadb_enabled:
            search_tasks.append(self._search_chromadb(params, intent_classification))
        
        if query_supabase and self.supabase_enabled:
            search_tasks.append(self._search_supabase_vector(params, intent_classification))
        
        if not search_tasks:
            # Fallback: try both if available
            if self.chromadb_enabled:
                search_tasks.append(self._search_chromadb(params, intent_classification))
            if self.supabase_enabled:
                search_tasks.append(self._search_supabase_vector(params, intent_classification))
        
        # Await all searches
        search_results = await asyncio.gather(*search_tasks, return_exceptions=True)
        
        # Process results and handle exceptions
        chromadb_results = []
        supabase_results = []
        
        for i, result in enumerate(search_results):
            if isinstance(result, Exception):
                logger.warning(f"Search task {i} failed: {result}")
                continue
                
            store_type, chunks = result
            if store_type == VectorStore.CHROMADB:
                chromadb_results = chunks
            elif store_type == VectorStore.SUPABASE_VECTOR:
                supabase_results = chunks
        
        audit_trail["search_results"] = {
            "chromadb_chunks": len(chromadb_results),
            "supabase_chunks": len(supabase_results)
        }
        
        # Fuse results intelligently
        fused_results, fusion_metadata = self._fuse_search_results(
            chromadb_results, supabase_results, intent_classification, params.limit
        )
        
        # Calculate hybrid confidence score
        confidence_score = self._calculate_hybrid_confidence(
            fused_results, intent_classification, fusion_metadata
        )
        
        audit_trail["steps"].append("tier_1_results_fused")
        audit_trail["fusion_metadata"] = fusion_metadata
        
        return HybridSearchResult(
            query_hash=self._generate_query_hash(params.query_text),
            intent_classification=intent_classification,
            chunks=fused_results,
            confidence_score=confidence_score,
            tier_used=SearchTier.TIER_1_DUAL_VECTOR,
            search_breakdown={
                "chromadb_results": len(chromadb_results),
                "supabase_results": len(supabase_results),
                "fused_results": len(fused_results)
            },
            fusion_metadata=fusion_metadata,
            escalation_reason=None,
            total_latency_ms=0.0,  # Will be set by caller
            audit_trail={}  # Will be set by caller
        )
    
    async def _search_chromadb(
        self, 
        params: SearchParameters, 
        intent_classification: IntentClassification
    ) -> Tuple[VectorStore, List[SearchResult]]:
        """Search ChromaDB for public knowledge."""
        
        if not self.chromadb_enabled:
            return VectorStore.CHROMADB, []
        
        try:
            # Determine collection based on intent
            collection_name = self._get_chromadb_collection(intent_classification.category)
            
            # Execute search
            raw_results = chroma_client.search_chunks(
                org_id="shared",  # Public shared data
                collection_name=collection_name,
                query_embedding=params.query_embedding,
                limit=params.limit,
                where=None
            )
            
            # Convert to SearchResult format
            results = []
            for result in raw_results:
                results.append(SearchResult(
                    chunk_id=result.get("chunk_id", ""),
                    doc_id=result.get("doc_id", ""),
                    score=result.get("score", 1.0),
                    text=result.get("text", ""),
                    metadata=result.get("metadata", {}),
                    source_store=VectorStore.CHROMADB,
                    pipeline_used=result.get("metadata", {}).get("pipeline_used", "unknown"),
                    embedding_dimension=result.get("metadata", {}).get("embedding_dim", 768)
                ))
            
            return VectorStore.CHROMADB, results
            
        except Exception as e:
            logger.error(f"ChromaDB search failed: {e}")
            return VectorStore.CHROMADB, []
    
    async def _search_supabase_vector(
        self, 
        params: SearchParameters, 
        intent_classification: IntentClassification
    ) -> Tuple[VectorStore, List[SearchResult]]:
        """Search Supabase Vector for private organizational data."""
        
        if not self.supabase_enabled:
            return VectorStore.SUPABASE_VECTOR, []
        
        try:
            # Execute search with org-scoped isolation
            raw_results = supabase_vector_search(
                org_id=params.org_id,
                query_embedding=params.query_embedding,
                limit=params.limit,
                min_score=params.min_score
            )
            
            # Convert to SearchResult format
            results = []
            for result in raw_results:
                results.append(SearchResult(
                    chunk_id=result.get("chunk_id", ""),
                    doc_id=result.get("doc_id", ""),
                    score=result.get("score", 1.0),
                    text=result.get("text", ""),
                    metadata=result.get("metadata", {}),
                    source_store=VectorStore.SUPABASE_VECTOR,
                    pipeline_used=result.get("metadata", {}).get("pipeline_used", "unknown"),
                    embedding_dimension=result.get("metadata", {}).get("embedding_dim", 768)
                ))
            
            return VectorStore.SUPABASE_VECTOR, results
            
        except Exception as e:
            logger.error(f"Supabase Vector search failed: {e}")
            return VectorStore.SUPABASE_VECTOR, []
    
    def _fuse_search_results(
        self, 
        chromadb_results: List[SearchResult], 
        supabase_results: List[SearchResult],
        intent_classification: IntentClassification,
        limit: int
    ) -> Tuple[List[SearchResult], Dict[str, Any]]:
        """Intelligently fuse results from both vector stores."""
        
        # Get routing weights for this intent category
        weights = self.routing_weights.get(intent_classification.category, {"chromadb": 0.5, "supabase_vector": 0.5})
        
        # Apply authority boosts
        for result in chromadb_results:
            # Boost public standards/regulations
            if any(keyword in result.metadata.get("content_type", "").lower() 
                   for keyword in ["standard", "regulation", "assessment", "framework"]):
                result.score *= 0.9  # Lower is better for similarity scores
                result.metadata["authority_boost"] = 0.1
        
        for result in supabase_results:
            # Boost company-specific content for policy queries
            if (intent_classification.category == IntentCategory.POLICY_INQUIRY and
                any(keyword in result.metadata.get("document_type", "").lower() 
                    for keyword in ["policy", "procedure", "company"])):
                result.score *= 0.95  # Slight boost
                result.metadata["relevance_boost"] = 0.05
        
        # Combine and sort by adjusted scores
        all_results = chromadb_results + supabase_results
        all_results.sort(key=lambda x: x.score)  # Lower scores are better
        
        # Apply diversity filtering to ensure representation from both stores
        final_results = self._apply_diversity_filtering(all_results, weights, limit)
        
        fusion_metadata = {
            "chromadb_original_count": len(chromadb_results),
            "supabase_original_count": len(supabase_results),
            "fusion_weights": weights,
            "final_count": len(final_results),
            "diversity_applied": len(final_results) != len(all_results[:limit]),
            "source_distribution": {
                "chromadb": len([r for r in final_results if r.source_store == VectorStore.CHROMADB]),
                "supabase_vector": len([r for r in final_results if r.source_store == VectorStore.SUPABASE_VECTOR])
            }
        }
        
        return final_results, fusion_metadata
    
    def _apply_diversity_filtering(
        self, 
        all_results: List[SearchResult], 
        weights: Dict[str, float], 
        limit: int
    ) -> List[SearchResult]:
        """Apply diversity filtering to ensure balanced representation."""
        
        if not all_results or limit <= 0:
            return []
        
        # Calculate target counts based on weights
        chromadb_target = max(1, int(limit * weights["chromadb"]))
        supabase_target = max(1, int(limit * weights["supabase_vector"]))
        
        # Separate by source
        chromadb_results = [r for r in all_results if r.source_store == VectorStore.CHROMADB]
        supabase_results = [r for r in all_results if r.source_store == VectorStore.SUPABASE_VECTOR]
        
        # Take top results from each source
        selected_chromadb = chromadb_results[:chromadb_target]
        selected_supabase = supabase_results[:supabase_target]
        
        # Combine and fill remaining slots with best overall results
        selected = selected_chromadb + selected_supabase
        remaining_slots = limit - len(selected)
        
        if remaining_slots > 0:
            used_chunk_ids = {r.chunk_id for r in selected}
            remaining_results = [r for r in all_results if r.chunk_id not in used_chunk_ids]
            selected.extend(remaining_results[:remaining_slots])
        
        # Final sort by score
        selected.sort(key=lambda x: x.score)
        return selected[:limit]
    
    def _calculate_hybrid_confidence(
        self, 
        fused_results: List[SearchResult], 
        intent_classification: IntentClassification,
        fusion_metadata: Dict[str, Any]
    ) -> float:
        """Calculate confidence score for hybrid search results."""
        
        if not fused_results:
            return 0.0
        
        # Base confidence from average similarity scores
        avg_score = sum(r.score for r in fused_results) / len(fused_results)
        base_confidence = max(0.0, 1.0 - avg_score)  # Convert similarity score to confidence
        
        # Intent classification confidence boost
        intent_boost = intent_classification.confidence_score * 0.1
        
        # Source diversity boost
        has_both_sources = (fusion_metadata["source_distribution"]["chromadb"] > 0 and
                           fusion_metadata["source_distribution"]["supabase_vector"] > 0)
        diversity_boost = 0.15 if has_both_sources else 0.0
        
        # Authority boost for public standards
        authority_boost = sum(
            0.05 for r in fused_results 
            if r.metadata.get("authority_boost", 0) > 0
        ) / len(fused_results)
        
        # Results count confidence
        count_confidence = min(1.0, len(fused_results) / 5.0) * 0.1  # Max boost for 5+ results
        
        final_confidence = min(1.0, 
            base_confidence + intent_boost + diversity_boost + authority_boost + count_confidence
        )
        
        return final_confidence
    
    async def _tier_2_cloud_llm_search(
        self, 
        tier1_result: HybridSearchResult, 
        params: SearchParameters,
        audit_trail: Dict[str, Any]
    ) -> HybridSearchResult:
        """Tier 2: Cloud LLM enhanced reasoning (placeholder)."""
        
        audit_trail["steps"].append("tier_2_cloud_llm_escalation")
        
        # This would integrate with cloud LLM for enhanced reasoning
        # For now, return tier 1 result with escalation metadata
        
        tier1_result.tier_used = SearchTier.TIER_2_CLOUD_LLM
        tier1_result.escalation_reason = "low_confidence_tier1"
        tier1_result.confidence_score = max(0.65, tier1_result.confidence_score)  # Boost after LLM
        
        return tier1_result
    
    async def _tier_3_clarifying_questions(
        self, 
        tier2_result: HybridSearchResult, 
        params: SearchParameters,
        audit_trail: Dict[str, Any]
    ) -> HybridSearchResult:
        """Tier 3: Generate clarifying questions."""
        
        audit_trail["steps"].append("tier_3_clarifying_questions")
        
        # Generate clarifying questions based on intent and available results
        clarifying_questions = self._generate_clarifying_questions(
            tier2_result.intent_classification, tier2_result.chunks
        )
        
        # Add clarifying questions to metadata
        tier2_result.fusion_metadata["clarifying_questions"] = clarifying_questions
        tier2_result.tier_used = SearchTier.TIER_3_CLARIFY
        tier2_result.escalation_reason = "insufficient_confidence_all_tiers"
        
        return tier2_result
    
    def _generate_clarifying_questions(
        self, 
        intent_classification: IntentClassification, 
        existing_chunks: List[SearchResult]
    ) -> List[str]:
        """Generate clarifying questions based on intent and context."""
        
        questions = []
        category = intent_classification.category
        
        if category == IntentCategory.POLICY_INQUIRY:
            questions.extend([
                "Which specific policy area are you asking about (e.g., security, HR, finance)?",
                "Are you looking for company-specific policies or industry best practices?",
                "Do you need implementation guidance or just policy content?"
            ])
        elif category == IntentCategory.COMPLIANCE_INQUIRY:
            questions.extend([
                "Which compliance framework are you asking about (ISO 27001, GDPR, SOX, etc.)?",
                "Are you looking for requirements or implementation guidance?",
                "Is this for a specific business unit or company-wide?"
            ])
        elif category == IntentCategory.ASSESSMENT_INQUIRY:
            questions.extend([
                "Are you looking for assessment questions or results?",
                "Which compliance domain should the assessment cover?",
                "Do you need a full assessment or specific control areas?"
            ])
        else:
            questions.extend([
                "Could you provide more specific details about what you're looking for?",
                "Are you asking about company-specific information or general guidance?",
                "Which compliance area or business function does this relate to?"
            ])
        
        # Context-specific questions based on existing results
        if existing_chunks:
            source_types = {r.source_store for r in existing_chunks}
            if VectorStore.CHROMADB in source_types and VectorStore.SUPABASE_VECTOR not in source_types:
                questions.append("Would you like to see how this applies to your specific organization?")
            elif VectorStore.SUPABASE_VECTOR in source_types and VectorStore.CHROMADB not in source_types:
                questions.append("Would you like to see the broader regulatory context or best practices?")
        
        return questions[:3]  # Return top 3 questions
    
    async def _record_search_confidence(self, org_id: str, result: HybridSearchResult):
        """Record search confidence for system optimization."""

        try:
            # Prepare data for the search_confidence_scores table
            # Determine primary vector store used
            source_distribution = result.fusion_metadata.get("source_distribution", {})
            chromadb_count = source_distribution.get("chromadb", 0)
            supabase_count = source_distribution.get("supabase_vector", 0)

            if chromadb_count > 0 and supabase_count > 0:
                vector_store = "hybrid"
            elif chromadb_count > 0:
                vector_store = "chromadb"
            elif supabase_count > 0:
                vector_store = "supabase_vector"
            else:
                vector_store = "unknown"

            # Extract pipeline information
            pipeline_used = "unknown"
            if result.chunks:
                pipeline_used = result.chunks[0].pipeline_used or "unknown"

            # Prepare search results metadata (without embedding vectors for storage efficiency)
            search_results_metadata = {
                "total_chunks": len(result.chunks),
                "intent_category": result.intent_classification.category.value,
                "intent_confidence": result.intent_classification.confidence_score,
                "requires_public": result.intent_classification.requires_public_knowledge,
                "requires_private": result.intent_classification.requires_private_knowledge,
                "source_distribution": source_distribution,
                "top_scores": [chunk.score for chunk in result.chunks[:3]],  # Top 3 scores for analysis
                "fusion_metadata": {
                    k: v for k, v in result.fusion_metadata.items()
                    if k not in ["source_distribution"]  # Already stored separately
                }
            }

            # Prepare search metadata
            search_metadata = {
                "total_latency_ms": result.total_latency_ms,
                "escalation_reason": result.escalation_reason,
                "search_breakdown": result.search_breakdown,
                "audit_trail_steps": result.audit_trail.get("steps", []) if result.audit_trail else []
            }

            # Call Supabase to insert the confidence record
            await self._insert_confidence_record(
                org_id=org_id,
                query_hash=result.query_hash,
                vector_store=vector_store,
                pipeline_used=pipeline_used,
                search_results=search_results_metadata,
                confidence_score=result.confidence_score,
                tier_used=result.tier_used.value,
                escalation_reason=result.escalation_reason,
                search_metadata=search_metadata
            )

            logger.debug(f"Recorded search confidence: org={org_id}, tier={result.tier_used.value}, confidence={result.confidence_score:.3f}")

        except Exception as e:
            logger.warning(f"Failed to record search confidence: {e}")

    async def _insert_confidence_record(
        self,
        org_id: str,
        query_hash: str,
        vector_store: str,
        pipeline_used: str,
        search_results: dict,
        confidence_score: float,
        tier_used: str,
        escalation_reason: Optional[str],
        search_metadata: dict
    ):
        """Insert search confidence record into Supabase database."""
        import requests
        import json

        # Get Supabase configuration from environment
        supabase_url = os.getenv("VECTOR_SUPABASE_URL")
        service_key = os.getenv("VECTOR_SUPABASE_SERVICE_KEY")

        if not supabase_url or not service_key:
            logger.warning("Supabase configuration missing, skipping confidence record insertion")
            return

        # Prepare the payload for the search_confidence_scores table
        payload = {
            "org_id": org_id,
            "query_hash": query_hash,
            "vector_store": vector_store,
            "pipeline_used": pipeline_used,
            "search_results": search_results,
            "confidence_score": confidence_score,
            "tier_used": tier_used,
            "escalation_reason": escalation_reason,
            "response_quality_score": None,  # Would be populated by user feedback
            "user_satisfaction_score": None,  # Would be populated by user feedback
            "search_metadata": search_metadata
        }

        # Insert into search_confidence_scores table
        url = f"{supabase_url}/rest/v1/search_confidence_scores"
        headers = {
            "apikey": service_key,
            "Authorization": f"Bearer {service_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }

        try:
            response = requests.post(
                url,
                headers=headers,
                data=json.dumps(payload),
                timeout=5
            )

            if response.status_code not in [200, 201]:
                logger.warning(f"Failed to insert confidence record: HTTP {response.status_code}, {response.text}")
            else:
                logger.debug("Successfully inserted search confidence record")

        except requests.RequestException as e:
            logger.warning(f"Request failed when inserting confidence record: {e}")

    def _get_chromadb_collection(self, intent_category: IntentCategory) -> str:
        """Get ChromaDB collection name based on intent category."""
        
        collection_mapping = {
            IntentCategory.STANDARDS_INQUIRY: "standards_knowledge",
            IntentCategory.ASSESSMENT_INQUIRY: "assessment_frameworks",
            IntentCategory.COMPLIANCE_INQUIRY: "regulatory_knowledge",
            IntentCategory.IMPLEMENTATION_GUIDANCE: "implementation_guidance",
            IntentCategory.POLICY_INQUIRY: "standards_knowledge",  # Policy templates
            IntentCategory.SECURITY_INQUIRY: "standards_knowledge",
            IntentCategory.GENERAL_INQUIRY: "standards_knowledge"
        }
        
        return collection_mapping.get(intent_category, "standards_knowledge")
    
    def _generate_query_hash(self, query_text: str) -> str:
        """Generate hash for query text."""
        import hashlib
        return hashlib.sha256(query_text.encode('utf-8')).hexdigest()[:16]
    
    def _extract_keywords(self, query_lower: str) -> List[str]:
        """Extract relevant keywords from query."""
        keywords = []
        
        # Compliance frameworks
        frameworks = ["iso", "gdpr", "ccpa", "sox", "nist", "pci", "hipaa", "soc2"]
        keywords.extend([fw for fw in frameworks if fw in query_lower])
        
        # Key concepts
        concepts = ["policy", "procedure", "assessment", "audit", "compliance", "security", "incident"]
        keywords.extend([concept for concept in concepts if concept in query_lower])
        
        return keywords
    
    def _get_timestamp(self) -> str:
        """Get ISO timestamp."""
        from datetime import datetime
        return datetime.utcnow().isoformat() + "Z"


# Convenience functions
async def search_hybrid(
    org_id: str, 
    query_text: str, 
    limit: int = 8,
    pipeline_name: Optional[str] = None
) -> HybridSearchResult:
    """Convenience function for hybrid search."""
    orchestrator = HybridSearchOrchestrator()
    params = SearchParameters(
        org_id=org_id,
        query_text=query_text,
        limit=limit,
        pipeline_name=pipeline_name
    )
    return await orchestrator.hybrid_search(params)


async def search_with_intent(
    org_id: str,
    query_text: str,
    intent_category: IntentCategory,
    limit: int = 8
) -> HybridSearchResult:
    """Search with explicit intent classification."""
    orchestrator = HybridSearchOrchestrator()
    
    # Create explicit intent classification
    intent = IntentClassification(
        category=intent_category,
        subcategory=None,
        confidence_score=1.0,  # High confidence since it's explicit
        requires_public_knowledge=intent_category in [IntentCategory.STANDARDS_INQUIRY, IntentCategory.COMPLIANCE_INQUIRY],
        requires_private_knowledge=intent_category in [IntentCategory.POLICY_INQUIRY, IntentCategory.SECURITY_INQUIRY],
        suggested_vector_stores=[VectorStore.CHROMADB, VectorStore.SUPABASE_VECTOR],
        classification_metadata={"explicit_intent": True}
    )
    
    # Override intent classification in orchestrator temporarily
    original_classify = orchestrator._classify_intent
    async def mock_classify(query_text: str, org_id: str) -> IntentClassification:
        return intent
    
    orchestrator._classify_intent = mock_classify
    
    params = SearchParameters(
        org_id=org_id,
        query_text=query_text,
        limit=limit
    )
    
    result = await orchestrator.hybrid_search(params)
    
    # Restore original method
    orchestrator._classify_intent = original_classify
    
    return result
