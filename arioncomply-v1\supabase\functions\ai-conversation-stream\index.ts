// File: arioncomply-v1/supabase/functions/ai-conversation-stream/index.ts
// File Description: conversation.stream endpoint (SSE)
// Purpose: Stream assistant tokens for a prompt (stub).
// Input: POST JSON { sessionId, message } (camelCase accepted)
// Output: SSE events: start, token*, end; 200 Response
// Notes: Logs request start; emits stream events; complete production will stream model output.
// conversation.stream - stream assistant tokens via SSE

import { serve } from "https://deno.land/std@0.224.0/http/server.ts";
import { readJson, convertKeys } from "../_shared/utils.ts";
import { apiError } from "../_shared/errors.ts";
import { getClientMeta, logEvent, logRequestEnd, logRequestStart } from "../_shared/logger.ts";

type StreamBody = {
  session_id: string;
  message: string;
};

function sse(meta: ReturnType<typeof getClientMeta>, headers: HeadersInit = {}) {
  const h = new Headers({
    "content-type": "text/event-stream",
    "cache-control": "no-cache",
    "connection": "keep-alive",
    ...(headers || {}),
    ...(meta.traceHeader ? { "traceparent": meta.traceHeader } : {}),
  });
  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder();
      const send = (event: string, data: string) => {
        controller.enqueue(encoder.encode(`event: ${event}\n`));
        controller.enqueue(encoder.encode(`data: ${data}\n\n`));
      };

      // Simulated token stream
      send("start", JSON.stringify({ id: crypto.randomUUID() }));
      const parts = ["Hello", ", ", "this ", "is ", "a ", "stream." ];
      let i = 0;
      const interval = setInterval(() => {
        if (i < parts.length) {
          send("token", JSON.stringify({ text: parts[i++] }));
        } else {
          clearInterval(interval);
          send("end", JSON.stringify({ done: true }));
          // Log stream_finished before closing for traceability
          // Fire-and-forget; errors are handled inside logger
          logEvent(meta, { eventType: "stream_finished", direction: "outbound", status: "ok" }).finally(() => {
            controller.close();
          });
        }
      }, 120);
    },
  });
  return new Response(stream, { headers: h });
}

serve(async (req) => {
  const requestId = crypto.randomUUID();
  const meta = getClientMeta(req, requestId);
  await logRequestStart(meta, req.headers);
  if (req.method !== "POST") {
    const res = apiError("method_not_allowed", "Method not allowed", 405, undefined, requestId);
    await logRequestEnd(meta, 405);
    return res;
  }

  let body: StreamBody;
  try {
    const raw = await readJson<unknown>(req);
    body = convertKeys<StreamBody>(raw, "toSnake");
    if (!body.session_id || !body.message?.trim()) {
      const res = apiError("validation_error", "session_id and message are required", 400, undefined, requestId);
      await logRequestEnd(meta, 400);
      return res;
    }
    const metaWithSession = { ...meta, sessionId: body.session_id } as typeof meta & { sessionId?: string | null };
    await logEvent(metaWithSession, { eventType: "stream_request_received", direction: "inbound", status: "ok", details: { session_id: body.session_id } });
  } catch (_err) {
    const res = apiError("invalid_json", "Invalid JSON body", 400, undefined, requestId);
    await logRequestEnd(meta, 400);
    return res;
  }

  // For SSE, we can’t attach the body post-send; log start and then end after close isn’t trivial.
  // We still record a request_end with 200 status here.
  const metaWithSession = { ...meta, sessionId: body.session_id } as typeof meta & { sessionId?: string | null };
  await logEvent(metaWithSession, { eventType: "stream_started", direction: "internal", status: "ok", details: { session_id: body.session_id } });
  await logRequestEnd(metaWithSession, 200);
  return sse(metaWithSession);
});
// File: arioncomply-v1/supabase/functions/ai-conversation-stream/index.ts
