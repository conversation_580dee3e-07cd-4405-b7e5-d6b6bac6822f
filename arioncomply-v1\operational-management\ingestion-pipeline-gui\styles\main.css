/* ArionComply Ingestion Pipeline Management GUI - Main Styles */

/* CSS Variables for consistent theming */
:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-card: #ffffff;
    
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    
    --border-color: #e5e7eb;
    --border-radius: 6px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    --font-sm: 0.875rem;
    --font-base: 1rem;
    --font-lg: 1.125rem;
    --font-xl: 1.25rem;
    --font-2xl: 1.5rem;
    
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

/* Dark mode variables */
[data-theme="dark"] {
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --bg-card: #1f2937;
    
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-light: #9ca3af;
    
    --border-color: #374151;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.5;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
    max-width: 1400px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo-section i {
    font-size: var(--font-2xl);
    color: var(--primary-color);
}

.logo-section h1 {
    font-size: var(--font-xl);
    font-weight: 600;
    color: var(--text-primary);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

/* System Status */
.system-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--secondary-color);
    transition: background-color 0.2s ease;
}

.status-indicator.healthy {
    background: var(--success-color);
}

.status-indicator.warning {
    background: var(--warning-color);
}

.status-indicator.error {
    background: var(--error-color);
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    overflow-x: auto;
}

.nav-tab {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: none;
    border: none;
    font-size: var(--font-sm);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
    white-space: nowrap;
}

.nav-tab:hover {
    color: var(--text-primary);
    background: var(--bg-tertiary);
}

.nav-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: var(--bg-tertiary);
}

/* Main Content */
.main-content {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    padding: var(--spacing-xl);
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Sections */
.section {
    margin-bottom: var(--spacing-2xl);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.section-header h2 {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: var(--font-xl);
    font-weight: 600;
    color: var(--text-primary);
}

.section-actions {
    display: flex;
    gap: var(--spacing-md);
}

/* Cards */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-lg);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-tertiary);
}

.card-header h3 {
    font-size: var(--font-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.card-content {
    padding: var(--spacing-lg);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.btn-outline {
    background: transparent;
    border-color: var(--border-color);
}

.btn-success {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.btn-warning {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.btn-error {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: calc(var(--border-radius) / 2);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.healthy {
    background: rgb(16 185 129 / 0.1);
    color: var(--success-color);
}

.status-badge.warning {
    background: rgb(245 158 11 / 0.1);
    color: var(--warning-color);
}

.status-badge.error {
    background: rgb(239 68 68 / 0.1);
    color: var(--error-color);
}

.status-badge.info {
    background: rgb(59 130 246 / 0.1);
    color: var(--info-color);
}

/* Pipeline Management Styles */
.current-pipeline-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.pipeline-main-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pipeline-name {
    font-size: var(--font-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.pipeline-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.detail-label {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.detail-value {
    font-size: var(--font-base);
    color: var(--text-primary);
    font-weight: 600;
}

.pipeline-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

/* Pipelines Grid */
.pipelines-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.pipeline-card {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    background: var(--bg-tertiary);
    transition: all 0.2s ease;
}

.pipeline-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.pipeline-card.active {
    border-color: var(--primary-color);
    background: rgb(37 99 235 / 0.05);
}

.pipeline-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.pipeline-card-title {
    font-size: var(--font-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.pipeline-card-meta {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-sm);
}

.meta-item {
    display: flex;
    justify-content: space-between;
}

.meta-label {
    color: var(--text-secondary);
}

.meta-value {
    color: var(--text-primary);
    font-weight: 500;
}

.pipeline-card-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.metric-card {
    padding: var(--spacing-lg);
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.metric-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.metric-icon {
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    background: rgb(37 99 235 / 0.1);
    color: var(--primary-color);
}

.metric-title {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.metric-value {
    font-size: var(--font-2xl);
    font-weight: 700;
    color: var(--text-primary);
}

.metric-unit {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    margin-left: var(--spacing-xs);
}

/* Form Styles */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-base);
    transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-overlay.active {
    display: flex;
}

.modal {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    max-width: 90vw;
    max-height: 90vh;
    width: 600px;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-lg);
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-lg);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
}

.modal-content {
    padding: var(--spacing-lg);
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 2000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius);
    color: white;
    font-size: var(--font-sm);
    font-weight: 500;
    box-shadow: var(--shadow-md);
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast.success {
    background: var(--success-color);
}

.toast.error {
    background: var(--error-color);
}

.toast.warning {
    background: var(--warning-color);
}

.toast.info {
    background: var(--info-color);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        padding: var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .main-content {
        padding: var(--spacing-md);
    }
    
    .pipelines-grid {
        grid-template-columns: 1fr;
    }
    
    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .pipeline-main-info {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
    
    .modal {
        width: 95vw;
        margin: var(--spacing-md);
    }
}