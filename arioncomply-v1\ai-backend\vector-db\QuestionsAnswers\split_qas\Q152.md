id: Q152
query: >-
  What if we have employees in multiple countries — whose rules do we follow?
packs:
  - "GDPR:2016"
  - "ISO27001:2022"
primary_ids:
  - "GDPR:2016/Art.3"
  - "ISO27001:2022/4.3"
overlap_ids: []
capability_tags:
  - "Register"
  - "Workflow"
flags:
  - "LOCAL LAW CHECK"
sources:
  - title: "GDPR — Territorial Scope"
    id: "GDPR:2016/Art.3"
    locator: "Article 3"
  - title: "ISO/IEC 27001:2022 — Scope"
    id: "ISO27001:2022/4.3"
    locator: "Clause 4.3"
ui:
  cards_hint:
    - "Jurisdiction matrix"
  actions:
    - type: "open_register"
      target: "jurisdiction_register"
      label: "View Jurisdictions"
    - type: "start_workflow"
      target: "multi_jurisdiction_setup"
      label: "Configure Multi-Jurisdiction"
output_mode: "both"
graph_required: false
notes: "Follow local privacy laws for employee data; maintain global baseline via ISMS"
---
### 152) What if we have employees in multiple countries — whose rules do we follow?

**Standard terms**  
- **Territorial scope (GDPR Art.3):** applies to processing of EU residents’ data.  
- **<PERSON>ope (ISO 27001 Cl.4.3):** define ISMS boundaries covering all operations.

**Plain-English answer**  
You must comply with each country’s privacy and labor data laws for employee data, while your ISMS provides a global security baseline. Document local exceptions and maintain a **Jurisdiction Matrix**.

**Applies to**  
- **Primary:** GDPR Article 3; ISO/IEC 27001:2022 Clause 4.3

**Why it matters**  
Ensures legal compliance and consistent security posture globally.

**Do next in our platform**  
- Populate **Jurisdiction Register**.  
- Launch **Multi-Jurisdiction Setup** workflow.

**How our platform will help**  
- **[Register]** Tracks laws per location.  
- **[Workflow]** Local compliance tasks assignment.

**Likely follow-ups**  
- “Do we need local data centers?” (Depends on data residency laws)

**Sources**  
- GDPR Article 3; ISO/IEC 27001:2022 Clause 4.3

**Legal note:** Consult local counsel for employment law specifics.  
