# Graph Modeling (Placeholder)

Decisions
- Start with relational graph in Postgres: requirements, controls, evidence, and link tables.
- Use graph-adjacent expansion in retrieval (one or more hops).

Design Points
- Core entities and relationships; cardinalities and constraints.
- Provenance on edges; versioning when links change.
- Query helpers for adjacency traversal.

Open Questions
- Exact ontology and priority relationships for <PERSON>.
- Future dedicated graph store needs and migration strategy.

Next Steps
- Draft minimal schema for link tables and comments.
- Define expansion rules used by retrieval.

