<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArionComply - Dual Vector Architecture Monitoring</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo-section">
                    <i class="fas fa-project-diagram"></i>
                    <h1>ArionComply Dual-Vector Monitoring</h1>
                </div>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="refreshAll()">
                        <i class="fas fa-sync-alt"></i> Refresh All
                    </button>
                    <button class="btn btn-secondary" onclick="exportDashboard()">
                        <i class="fas fa-download"></i> Export Dashboard
                    </button>
                    <div class="system-status" id="systemStatus">
                        <span class="status-indicator" id="statusIndicator"></span>
                        <span id="statusText">Loading...</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Navigation Tabs -->
        <nav class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('architecture-overview')">
                <i class="fas fa-sitemap"></i> Architecture Overview
            </button>
            <button class="nav-tab" onclick="showTab('hybrid-search')">
                <i class="fas fa-search"></i> Hybrid Search Analytics
            </button>
            <button class="nav-tab" onclick="showTab('vector-stores')">
                <i class="fas fa-database"></i> Vector Store Status
            </button>
            <button class="nav-tab" onclick="showTab('intent-classification')">
                <i class="fas fa-brain"></i> Intent Classification
            </button>
            <button class="nav-tab" onclick="showTab('confidence-tracking')">
                <i class="fas fa-chart-line"></i> Confidence & Escalation
            </button>
            <button class="nav-tab" onclick="showTab('security-audit')">
                <i class="fas fa-shield-alt"></i> Security & Audit
            </button>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Architecture Overview Tab -->
            <div id="architecture-overview" class="tab-content active">
                <div class="section">
                    <div class="section-header">
                        <h2><i class="fas fa-sitemap"></i> Dual-Vector Architecture Status</h2>
                        <div class="section-actions">
                            <button class="btn btn-secondary" onclick="runSystemHealthCheck()">
                                <i class="fas fa-heartbeat"></i> System Health Check
                            </button>
                        </div>
                    </div>

                    <!-- System Architecture Diagram -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Architecture Health Dashboard</h3>
                            <div class="card-actions">
                                <button class="btn-icon" onclick="toggleArchitectureDiagram()" title="Toggle Diagram View">
                                    <i class="fas fa-expand-arrows-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="architecture-diagram" id="architectureDiagram">
                                <div class="architecture-flow">
                                    <!-- User Query Flow -->
                                    <div class="flow-stage" data-stage="input">
                                        <div class="stage-icon"><i class="fas fa-user"></i></div>
                                        <div class="stage-title">User Query</div>
                                        <div class="stage-status" id="inputStageStatus">
                                            <span class="status-badge healthy">Active</span>
                                        </div>
                                    </div>
                                    
                                    <div class="flow-arrow">→</div>
                                    
                                    <!-- Intent Classification -->
                                    <div class="flow-stage" data-stage="intent">
                                        <div class="stage-icon"><i class="fas fa-brain"></i></div>
                                        <div class="stage-title">Intent Classification</div>
                                        <div class="stage-status" id="intentStageStatus">
                                            <span class="status-badge" id="intentStatusBadge">Loading...</span>
                                        </div>
                                        <div class="stage-metrics">
                                            <div class="metric">
                                                <span class="metric-label">Avg Confidence:</span>
                                                <span class="metric-value" id="intentAvgConfidence">--</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="flow-arrow">→</div>
                                    
                                    <!-- Dual Vector Search -->
                                    <div class="flow-stage dual-stage" data-stage="dual-search">
                                        <div class="dual-stores">
                                            <div class="store-branch">
                                                <div class="stage-icon chromadb"><i class="fas fa-book-open"></i></div>
                                                <div class="stage-title">ChromaDB</div>
                                                <div class="stage-subtitle">Public Knowledge</div>
                                                <div class="stage-status" id="chromadbStatus">
                                                    <span class="status-badge" id="chromadbStatusBadge">Loading...</span>
                                                </div>
                                                <div class="stage-metrics">
                                                    <div class="metric">
                                                        <span class="metric-label">Collections:</span>
                                                        <span class="metric-value" id="chromadbCollections">--</span>
                                                    </div>
                                                    <div class="metric">
                                                        <span class="metric-label">Documents:</span>
                                                        <span class="metric-value" id="chromadbDocuments">--</span>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="store-branch">
                                                <div class="stage-icon supabase"><i class="fas fa-building"></i></div>
                                                <div class="stage-title">Supabase Vector</div>
                                                <div class="stage-subtitle">Private Data</div>
                                                <div class="stage-status" id="supabaseStatus">
                                                    <span class="status-badge" id="supabaseStatusBadge">Loading...</span>
                                                </div>
                                                <div class="stage-metrics">
                                                    <div class="metric">
                                                        <span class="metric-label">Organizations:</span>
                                                        <span class="metric-value" id="supabaseOrgs">--</span>
                                                    </div>
                                                    <div class="metric">
                                                        <span class="metric-label">Documents:</span>
                                                        <span class="metric-value" id="supabaseDocuments">--</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="flow-arrow">→</div>
                                    
                                    <!-- Result Fusion -->
                                    <div class="flow-stage" data-stage="fusion">
                                        <div class="stage-icon"><i class="fas fa-compress-arrows-alt"></i></div>
                                        <div class="stage-title">Result Fusion</div>
                                        <div class="stage-status" id="fusionStageStatus">
                                            <span class="status-badge" id="fusionStatusBadge">Loading...</span>
                                        </div>
                                        <div class="stage-metrics">
                                            <div class="metric">
                                                <span class="metric-label">Avg Confidence:</span>
                                                <span class="metric-value" id="fusionAvgConfidence">--</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="flow-arrow">→</div>
                                    
                                    <!-- Tier Escalation -->
                                    <div class="flow-stage" data-stage="escalation">
                                        <div class="stage-icon"><i class="fas fa-layer-group"></i></div>
                                        <div class="stage-title">Tier Routing</div>
                                        <div class="stage-status" id="escalationStageStatus">
                                            <span class="status-badge" id="escalationStatusBadge">Loading...</span>
                                        </div>
                                        <div class="stage-metrics">
                                            <div class="metric">
                                                <span class="metric-label">Tier 1 Rate:</span>
                                                <span class="metric-value" id="tier1Rate">--</span>
                                            </div>
                                            <div class="metric">
                                                <span class="metric-label">Escalation Rate:</span>
                                                <span class="metric-value" id="escalationRate">--</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Real-time Metrics Grid -->
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-header">
                                <h4>Queries per Hour</h4>
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="metric-value" id="queriesPerHour">--</div>
                            <div class="metric-change positive" id="queriesChange">+12% vs last hour</div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-header">
                                <h4>Average Latency</h4>
                                <i class="fas fa-stopwatch"></i>
                            </div>
                            <div class="metric-value" id="avgLatency">-- ms</div>
                            <div class="metric-change" id="latencyChange">-- vs last hour</div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-header">
                                <h4>Success Rate</h4>
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="metric-value" id="successRate">--%</div>
                            <div class="metric-change" id="successChange">-- vs last hour</div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-header">
                                <h4>Active Organizations</h4>
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="metric-value" id="activeOrgs">--</div>
                            <div class="metric-change" id="orgsChange">-- active today</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hybrid Search Analytics Tab -->
            <div id="hybrid-search" class="tab-content">
                <div class="section">
                    <div class="section-header">
                        <h2><i class="fas fa-search"></i> Hybrid Search Analytics</h2>
                        <div class="section-actions">
                            <select id="analyticsTimeframe">
                                <option value="1h">Last Hour</option>
                                <option value="24h" selected>Last 24 Hours</option>
                                <option value="7d">Last 7 Days</option>
                                <option value="30d">Last 30 Days</option>
                            </select>
                        </div>
                    </div>

                    <!-- Search Volume and Performance Charts -->
                    <div class="chart-grid">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>Search Volume by Vector Store</h3>
                            </div>
                            <div class="chart-container">
                                <canvas id="searchVolumeChart"></canvas>
                            </div>
                        </div>
                        
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>Confidence Score Distribution</h3>
                            </div>
                            <div class="chart-container">
                                <canvas id="confidenceDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Hybrid Search Performance -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Hybrid Search Performance Breakdown</h3>
                        </div>
                        <div class="card-content">
                            <div class="performance-breakdown" id="searchPerformanceBreakdown">
                                <!-- Performance data will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Source Distribution Analysis -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Public vs Private Data Usage</h3>
                        </div>
                        <div class="card-content">
                            <div class="source-distribution" id="sourceDistribution">
                                <div class="distribution-chart">
                                    <canvas id="sourceDistributionChart"></canvas>
                                </div>
                                <div class="distribution-details">
                                    <div class="detail-item">
                                        <span class="detail-label">ChromaDB (Public):</span>
                                        <span class="detail-value" id="chromadbUsage">--%</span>
                                        <span class="detail-change" id="chromadbChange">--</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Supabase (Private):</span>
                                        <span class="detail-value" id="supabaseUsage">--%</span>
                                        <span class="detail-change" id="supabaseChange">--</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Hybrid Results:</span>
                                        <span class="detail-value" id="hybridUsage">--%</span>
                                        <span class="detail-change" id="hybridChange">--</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Vector Stores Status Tab -->
            <div id="vector-stores" class="tab-content">
                <div class="section">
                    <div class="section-header">
                        <h2><i class="fas fa-database"></i> Vector Store Management</h2>
                        <div class="section-actions">
                            <button class="btn btn-secondary" onclick="runVectorStoreHealth()">
                                <i class="fas fa-heartbeat"></i> Health Check
                            </button>
                            <button class="btn btn-primary" onclick="optimizeIndexes()">
                                <i class="fas fa-cogs"></i> Optimize Indexes
                            </button>
                        </div>
                    </div>

                    <!-- Vector Store Comparison -->
                    <div class="stores-comparison">
                        <!-- ChromaDB Status -->
                        <div class="store-card chromadb-card">
                            <div class="store-header">
                                <div class="store-icon"><i class="fas fa-book-open"></i></div>
                                <div class="store-info">
                                    <h3>ChromaDB</h3>
                                    <div class="store-subtitle">Public Shared Knowledge</div>
                                </div>
                                <div class="store-status" id="chromadbDetailedStatus">
                                    <span class="status-badge" id="chromadbDetailedBadge">Loading...</span>
                                </div>
                            </div>
                            <div class="store-metrics">
                                <div class="metric-row">
                                    <span class="metric-label">Collections:</span>
                                    <span class="metric-value" id="chromadbDetailedCollections">--</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">Total Documents:</span>
                                    <span class="metric-value" id="chromadbDetailedDocs">--</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">Storage Size:</span>
                                    <span class="metric-value" id="chromadbStorageSize">--</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">Avg Query Time:</span>
                                    <span class="metric-value" id="chromadbQueryTime">-- ms</span>
                                </div>
                            </div>
                            <div class="store-content-breakdown">
                                <h4>Content Types:</h4>
                                <div class="content-tags" id="chromadbContentTypes">
                                    <!-- Content type tags will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- Supabase Vector Status -->
                        <div class="store-card supabase-card">
                            <div class="store-header">
                                <div class="store-icon"><i class="fas fa-building"></i></div>
                                <div class="store-info">
                                    <h3>Supabase Vector</h3>
                                    <div class="store-subtitle">Private Organizational Data</div>
                                </div>
                                <div class="store-status" id="supabaseDetailedStatus">
                                    <span class="status-badge" id="supabaseDetailedBadge">Loading...</span>
                                </div>
                            </div>
                            <div class="store-metrics">
                                <div class="metric-row">
                                    <span class="metric-label">Organizations:</span>
                                    <span class="metric-value" id="supabaseDetailedOrgs">--</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">Total Documents:</span>
                                    <span class="metric-value" id="supabaseDetailedDocs">--</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">Storage Size:</span>
                                    <span class="metric-value" id="supabaseStorageSize">--</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">Avg Query Time:</span>
                                    <span class="metric-value" id="supabaseQueryTime">-- ms</span>
                                </div>
                            </div>
                            <div class="store-content-breakdown">
                                <h4>Security Levels:</h4>
                                <div class="security-breakdown" id="supabaseSecurityLevels">
                                    <!-- Security level breakdown will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Embedding Dimensions Analysis -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Multi-Dimensional Embedding Analysis</h3>
                        </div>
                        <div class="card-content">
                            <div class="dimensions-analysis" id="dimensionsAnalysis">
                                <div class="dimension-chart">
                                    <canvas id="dimensionUsageChart"></canvas>
                                </div>
                                <div class="dimension-details">
                                    <div class="dimension-row">
                                        <span class="dim-label">768-dim (all-mpnet-base-v2):</span>
                                        <span class="dim-count" id="dim768Count">--</span>
                                        <span class="dim-percentage" id="dim768Percentage">--%</span>
                                    </div>
                                    <div class="dimension-row">
                                        <span class="dim-label">1024-dim (BGE-Large-EN-v1.5):</span>
                                        <span class="dim-count" id="dim1024Count">--</span>
                                        <span class="dim-percentage" id="dim1024Percentage">--%</span>
                                    </div>
                                    <div class="dimension-row">
                                        <span class="dim-label">1536-dim (OpenAI-3-small):</span>
                                        <span class="dim-count" id="dim1536Count">--</span>
                                        <span class="dim-percentage" id="dim1536Percentage">--%</span>
                                    </div>
                                    <div class="dimension-row">
                                        <span class="dim-label">3072-dim (OpenAI-3-large):</span>
                                        <span class="dim-count" id="dim3072Count">--</span>
                                        <span class="dim-percentage" id="dim3072Percentage">--%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Intent Classification Tab -->
            <div id="intent-classification" class="tab-content">
                <div class="section">
                    <div class="section-header">
                        <h2><i class="fas fa-brain"></i> Intent Classification Analytics</h2>
                        <div class="section-actions">
                            <button class="btn btn-secondary" onclick="trainIntentModel()">
                                <i class="fas fa-robot"></i> Retrain Model
                            </button>
                        </div>
                    </div>

                    <!-- Intent Distribution -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Intent Category Distribution</h3>
                        </div>
                        <div class="card-content">
                            <div class="intent-distribution">
                                <div class="intent-chart">
                                    <canvas id="intentDistributionChart"></canvas>
                                </div>
                                <div class="intent-legend" id="intentLegend">
                                    <!-- Intent category legend will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Intent Accuracy Metrics -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Classification Performance</h3>
                        </div>
                        <div class="card-content">
                            <div class="accuracy-metrics" id="intentAccuracyMetrics">
                                <!-- Accuracy metrics will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Routing Decisions -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Vector Store Routing Analysis</h3>
                        </div>
                        <div class="card-content">
                            <div class="routing-analysis" id="routingAnalysis">
                                <div class="routing-matrix">
                                    <!-- Intent-to-store routing matrix will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Confidence & Escalation Tab -->
            <div id="confidence-tracking" class="tab-content">
                <div class="section">
                    <div class="section-header">
                        <h2><i class="fas fa-chart-line"></i> Confidence Tracking & Tier Escalation</h2>
                        <div class="section-actions">
                            <button class="btn btn-secondary" onclick="adjustThresholds()">
                                <i class="fas fa-sliders-h"></i> Adjust Thresholds
                            </button>
                        </div>
                    </div>

                    <!-- Tier Distribution -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Multi-Tier Usage Distribution</h3>
                        </div>
                        <div class="card-content">
                            <div class="tier-distribution">
                                <div class="tier-chart">
                                    <canvas id="tierDistributionChart"></canvas>
                                </div>
                                <div class="tier-details">
                                    <div class="tier-item">
                                        <div class="tier-icon tier-1"><i class="fas fa-database"></i></div>
                                        <div class="tier-info">
                                            <span class="tier-label">Tier 1: Dual Vector</span>
                                            <span class="tier-percentage" id="tier1Percentage">--%</span>
                                        </div>
                                    </div>
                                    <div class="tier-item">
                                        <div class="tier-icon tier-2"><i class="fas fa-cloud"></i></div>
                                        <div class="tier-info">
                                            <span class="tier-label">Tier 2: Cloud LLM</span>
                                            <span class="tier-percentage" id="tier2Percentage">--%</span>
                                        </div>
                                    </div>
                                    <div class="tier-item">
                                        <div class="tier-icon tier-3"><i class="fas fa-question-circle"></i></div>
                                        <div class="tier-info">
                                            <span class="tier-label">Tier 3: Clarifying</span>
                                            <span class="tier-percentage" id="tier3Percentage">--%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Confidence Score Trends -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Confidence Score Trends</h3>
                        </div>
                        <div class="card-content">
                            <div class="confidence-trends">
                                <canvas id="confidenceTrendsChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Escalation Triggers -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Escalation Trigger Analysis</h3>
                        </div>
                        <div class="card-content">
                            <div class="escalation-triggers" id="escalationTriggers">
                                <!-- Escalation trigger analysis will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security & Audit Tab -->
            <div id="security-audit" class="tab-content">
                <div class="section">
                    <div class="section-header">
                        <h2><i class="fas fa-shield-alt"></i> Security & Audit Monitoring</h2>
                        <div class="section-actions">
                            <button class="btn btn-secondary" onclick="exportAuditLog()">
                                <i class="fas fa-file-export"></i> Export Audit Log
                            </button>
                        </div>
                    </div>

                    <!-- Data Separation Monitoring -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Data Separation Compliance</h3>
                        </div>
                        <div class="card-content">
                            <div class="separation-status" id="dataSeparationStatus">
                                <div class="separation-metric">
                                    <div class="metric-icon public"><i class="fas fa-unlock"></i></div>
                                    <div class="metric-info">
                                        <span class="metric-label">Public Data Queries</span>
                                        <span class="metric-value" id="publicDataQueries">--</span>
                                        <span class="metric-detail">ChromaDB Only</span>
                                    </div>
                                </div>
                                <div class="separation-metric">
                                    <div class="metric-icon private"><i class="fas fa-lock"></i></div>
                                    <div class="metric-info">
                                        <span class="metric-label">Private Data Queries</span>
                                        <span class="metric-value" id="privateDataQueries">--</span>
                                        <span class="metric-detail">Org-Scoped RLS</span>
                                    </div>
                                </div>
                                <div class="separation-metric">
                                    <div class="metric-icon hybrid"><i class="fas fa-shield-halved"></i></div>
                                    <div class="metric-info">
                                        <span class="metric-label">Cross-Boundary Violations</span>
                                        <span class="metric-value" id="separationViolations">--</span>
                                        <span class="metric-detail">Should be 0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Access Audit Log -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Recent Access Audit Log</h3>
                            <div class="card-actions">
                                <select id="auditLogFilter">
                                    <option value="all">All Activities</option>
                                    <option value="queries">Search Queries</option>
                                    <option value="ingestion">Document Ingestion</option>
                                    <option value="errors">Errors & Violations</option>
                                </select>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="audit-log-table" id="auditLogTable">
                                <!-- Audit log entries will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Security Alerts -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Security Alerts & Anomalies</h3>
                        </div>
                        <div class="card-content">
                            <div class="security-alerts" id="securityAlerts">
                                <!-- Security alerts will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div id="modalOverlay" class="modal-overlay" onclick="closeModal()">
        <div class="modal" onclick="event.stopPropagation()">
            <div class="modal-header">
                <h3 id="modalTitle">Modal Title</h3>
                <button class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content" id="modalContent">
                <!-- Dynamic modal content -->
            </div>
            <div class="modal-footer" id="modalFooter">
                <!-- Dynamic modal footer -->
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container">
        <!-- Toast notifications will appear here -->
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/charts.js"></script>
    <script src="js/dual-vector-monitoring.js"></script>
</body>
</html>