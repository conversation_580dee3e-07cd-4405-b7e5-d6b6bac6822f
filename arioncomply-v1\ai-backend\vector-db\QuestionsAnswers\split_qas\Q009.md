id: Q009
query: >-
  Why are there so many different standards and regulations—can’t I just do one?
packs:
  - "ISO27001:2022"
  - "ISO27701:2019"
  - "GDPR:2016"
  - "CPRA:2023"
  - "NIS2:2023"
  - "EUAI:2024"
primary_ids:
  - "ISO27001:2022/4.3"
overlap_ids:
  - "ISO27701:2019/4.3"
  - "GDPR:2016/Art.24"
capability_tags:
  - "Dashboard"
  - "Report"
  - "Register"
  - "Evidence-Guided"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Context & Scope"
    id: "ISO27001:2022/4.3"
    locator: "Clause 4.3"
  - title: "ISO/IEC 27701:2019 — PIMS Scope"
    id: "ISO27701:2019/4.3"
    locator: "Clause 4.3"
  - title: "GDPR (Regulation 2016/679) — Accountability"
    id: "GDPR:2016/Art.24"
    locator: "Article 24"
ui:
  cards_hint:
    - "Cross-mapping dashboard"
    - "Control overlap report"
  actions:
    - type: "open_register"
      target: "control_crosswalk"
      label: "View Cross-Mapping"
    - type: "start_workflow"
      target: "overlap_analysis"
      label: "Analyze Overlap Wins"
output_mode: "both"
graph_required: true
notes: "Emphasize cross-mapping efficiency"
---
### 9) Why are there so many different standards and regulations—can’t I just do one?

**Standard terms)**
- **Management System Scope (ISO/IEC 27001 Clause 4.3):** defines boundaries of your ISMS.
- **PIMS Scope (ISO/IEC 27701 Clause 4.3):** extends ISMS for privacy governance.
- **Accountability (GDPR Article 24):** legal overlay requiring proof of measures.

**Plain-English answer**
Each framework serves a purpose: ISO 27001/27701 give you a **system** to run security and privacy. GDPR/CPRA/NIS2/EU AI Act set **legal rules** you must follow. By **cross-mapping** once, you can reuse controls and evidence across all.

**Applies to**
- **Primary:** ISO/IEC 27001:2022 Clause 4.3
- **Also relevant/Overlaps:** ISO/IEC 27701:2019 Clause 4.3; GDPR Article 24

**Why it matters**
A single mapping effort saves huge time vs doing each in isolation.

**Do next in our platform**
- Open the **cross-mapping dashboard**.
- Highlight **overlap wins** (controls used by multiple frameworks).
- Allocate evidence to shared controls first.

**How our platform will help**
- **[Dashboard]** Live overlap view.
- **[Report]** Export shared control sets.
- **[Register]** Unified control library.
- **[Evidence-Guided]** Single evidence attachment across frameworks.

**Likely follow-ups**
- “Which controls yield the biggest overlap?” (Identity, logging, incident management)

**Sources**
- ISO/IEC 27001:2022 Clause 4.3
- ISO/IEC 27701:2019 Clause 4.3
- GDPR Article 24
