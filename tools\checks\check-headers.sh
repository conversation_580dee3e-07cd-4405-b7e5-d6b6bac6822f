#!/usr/bin/env bash
# File: tools/checks/check-headers.sh
# File Description: Verifies files start with a repository path header
# Purpose: Enforce header presence: first lines must contain "File: <repo-relative-path>"
# Notes: Skips JSON and binary assets; focuses on code/docs

set -euo pipefail

cd "$(git rev-parse --show-toplevel)" >/dev/null 2>&1 || true

# File globs to include
INCLUDE_EXTS=(ts tsx js jsx py sh sql md html css toml yaml yml mdx Makefile)

missing=()

while IFS= read -r -d '' f; do
  base=$(basename "$f")
  ext="${f##*.}"
  case "$ext" in
    json|png|jpg|jpeg|gif|pdf|svg|ico) continue;;
  esac
  # Only check our include set
  keep=false
  for e in "${INCLUDE_EXTS[@]}"; do
    if [[ "$ext" == "$e" ]] || [[ "$base" == "$e" ]]; then keep=true; break; fi
  done
  $keep || continue

  # Read first 3 logical lines and look for any File: header (exact matching is handled by check-header-paths.py)
  if ! head -n 3 "$f" | rg -q "File:\s+"; then
    missing+=("$f")
  fi
done < <(rg --files --hidden --no-ignore-vcs -g '!**/node_modules/**' -g '!**/.git/**' -g '!**/dist/**' -g '!**/build/**' -z)

if ((${#missing[@]})); then
  printf "Missing required header in files (add: 'File: arioncomply-v1/...'):\n" >&2
  printf "  %s\n" "${missing[@]}" >&2
  exit 1
fi

echo "All checked files have the required header."
