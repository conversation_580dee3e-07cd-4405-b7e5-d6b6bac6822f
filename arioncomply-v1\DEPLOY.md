# Deploy Guide (Edge + Backend + DB)

This guide summarizes how to run and deploy the Edge functions (source-of-truth under `supabase/functions`), the Python AI backend, and the required database migrations for the MVP-Assessment-App.

## Edge Functions (Supabase)

Source of truth: `supabase/functions`
- `ai-conversation-start`
- `ai-conversation-send`
- `ai-conversation-stream`
- `compliance-proxy` (UI proxy; optional)
- `registry-catalog` (serves config/ui_catalog.json)
- `registry-schema` (serves schemas/metadata-v1.json)
- `registry-content` (serves content/{id}/metadata.json)

Environment (Edge)
- `SUPABASE_URL`: main Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY`: service-role key for logging (api_request_logs/api_event_logs)
- `LOGGING_REQUIRED`: `true` (enforce org_id presence in logs)
- `AI_BACKEND_URL`: URL of Python backend `/ai/chat`
- `AI_BACKEND_TIMEOUT_MS`: e.g., `12000`
 - `SYSTEM_ORG_ID` (optional): canonical org used for internal/global event logs when no specific org is in scope (events only; request logs still require org)

Serve (local)
```bash
supabase functions serve ai-conversation-start ai-conversation-send ai-conversation-stream registry-catalog registry-schema registry-content
```

Deploy (remote)
```bash
supabase functions deploy ai-conversation-start ai-conversation-send ai-conversation-stream registry-catalog registry-schema registry-content
```

Notes
- Edge remains thin (auth/JWT parsing for org_id/sub, validation, traceparent, logging, forwarding). No retrieval/anonymization/model calls at Edge.

## AI Backend (Python)

Location: `ai-backend/python-backend`
- Route: `POST /ai/chat` (FastAPI)
- Orchestrates: prompt compile, optional retrieval, event emission (`retrieval_run`, `ai_call`).

Environment (Backend)
- Main project logging: `SUPABASE_URL`, `SUPABASE_SERVICE_ROLE_KEY`, `AC_LOG_TRANSPORT=direct`
- Optional vector project: `VECTOR_SUPABASE_URL`, `VECTOR_SUPABASE_SERVICE_KEY`
- Local SLLM integration: `LOCAL_SLLM_ENDPOINTS_PATH` (JSON config for SmolLM3, Mistral7B, Phi3)
- Copy `.env.example` to `.env` and configure required settings
 - Optional: `SYSTEM_ORG_ID` for internal/global jobs that are not acting on a specific org

Setup (first time)
```bash
cd ai-backend/python-backend
pip install -r requirements.txt
cp .env.example .env
# Edit .env with your settings
```

Run (local)
```bash
# Using provided startup script (recommended)
cd ai-backend/python-backend
./start-backend.sh

# Options:
./start-backend.sh --port 9000 --dev          # Development mode with reload
./start-backend.sh --host 0.0.0.0 --port 8000 # Custom host/port

# Manual uvicorn (alternative)
uvicorn app.main:app --reload --port 9000
```

Stop (local)
```bash
cd ai-backend/python-backend
./stop-backend.sh
```

## Database Migrations

Main Supabase project (app DB)
- Apply `db/migrations/0001 … 0014`
  - 0012 enforces `org_id NOT NULL` and removes NULL visibility in logging policies
  - 0013 creates assessment-system tables
  - 0014 adds `session_id` and `trace_id` to `api_request_logs` and `api_event_logs` with useful indexes

Vector Supabase project (vector DB, optional for MVP)
- Apply `ai-backend/supabase_migrations/vector/0001_vector_schema.sql`
  - Tables: `documents`, `chunks`, `embeddings`
  - RLS: org-scoped via `app_current_org_id()`
  - Function: `match_chunks(p_org, p_query, p_limit, p_min_score)`

## Ingestion (Optional for MVP)

Pipeline stubs: `ai-backend/python-backend/services/ingestion/*`
- Extract → chunk → embed → insert
- Call `ingest_one(org_id, file_path, title?)`
- Emits vector rows for `documents/chunks/embeddings`

## Traceability and Logging

- Edge generates `requestId` and supports W3C `traceparent` (echoed in response headers)
- Edge writes `api_request_logs` (start/end) and `api_event_logs` (including `response_sent`, `stream_finished`)
- Backend emits `retrieval_run` and `ai_call` per request
- Top-level columns for analytics: `org_id`, `request_id`, `trace_id`, `session_id`
- Do not log raw prompts/responses; only counts, hashes, citations

### Internal Jobs (Global vs Org-scoped)
- For org-scoped jobs, include `org_id`, generate a `request_id`, set `direction: "internal"`, and include `session_id` when applicable.
- For global/system jobs (no specific org), set `direction: "internal"` and rely on `SYSTEM_ORG_ID` to satisfy the NOT NULL `org_id` constraint (events only). Include broader scope in `details`.
- `LOGGING_REQUIRED=false` disables the Edge runtime guard, but the database still requires `org_id`. Use `SYSTEM_ORG_ID` where appropriate.

## Quick Verification

1) Start the backend (POST `/ai/chat` returns echo text)
2) Serve Edge functions locally; call `ai-conversation-start` then `ai-conversation-send`
3) Confirm logs:
   - `api_request_logs` rows for each request
   - `api_event_logs` with expected events
4) (Optional) Ingest a doc in vector project and call `ai-conversation-send` with `x-pipeline-mode: retrieval`

## Conventions

- Edge function prefixes by concern: `ai-*` (AI gateway), `app-*` (app DB APIs), `ui-*` (UI helpers)
- Keep shared helpers under `supabase/functions/_shared`
- Backend service modules grouped by concern under `ai-backend/python-backend/services`
