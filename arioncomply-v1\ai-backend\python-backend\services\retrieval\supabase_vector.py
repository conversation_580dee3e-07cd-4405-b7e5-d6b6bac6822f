"""
File: arioncomply-v1/ai-backend/python-backend/services/retrieval/supabase_vector.py
File Description: Supabase Vector retrieval client (skeleton)
Purpose: Query vector project's match_chunks function with org scope
Inputs: org_id (uuid), query_embedding (list[float]), limit, min_score
Outputs: list of chunks [{ chunk_id, doc_id, score, text, metadata }]
Security: Uses service-role key to call REST RPC on the vector project
Notes: Ensure org_id is scoped correctly; do not cross tenant boundaries
"""

import os
import json
from typing import Any, Dict, List
import requests

VECTOR_SUPABASE_URL = os.getenv("VECTOR_SUPABASE_URL")
VECTOR_SUPABASE_SERVICE_KEY = os.getenv("VECTOR_SUPABASE_SERVICE_KEY")


def search(org_id: str, query_embedding: List[float], limit: int = 8, min_score: float = None) -> List[Dict[str, Any]]:
    """Search vector DB via Supabase RPC match_chunks.

    Args:
        org_id: Tenant scope UUID
        query_embedding: Embedding vector to search with
        limit: Max results
        min_score: Optional score threshold
    Returns: List of chunk rows with scores and metadata; [] on error.
    """
    if not VECTOR_SUPABASE_URL or not VECTOR_SUPABASE_SERVICE_KEY:
        return []
    url = f"{VECTOR_SUPABASE_URL}/rest/v1/rpc/match_chunks"
    headers = {
        "apikey": VECTOR_SUPABASE_SERVICE_KEY,
        "Authorization": f"Bearer {VECTOR_SUPABASE_SERVICE_KEY}",
        "Content-Type": "application/json",
        "Prefer": "return=representation",
    }
    payload = {
        "p_org": org_id,
        "p_query": query_embedding,
        "p_limit": limit,
        "p_min_score": min_score,
    }
    try:
        r = requests.post(url, headers=headers, data=json.dumps(payload), timeout=3)
        if r.status_code >= 300:
            return []
        return r.json()
    except Exception:
        return []
