"""
File: arioncomply-v1/ai-backend/python-backend/services/ingestion/metadata_validator.py
File Description: Metadata validator
Purpose: Validate that chunk/document metadata conforms to mapping.md schema
Inputs: dict for document or chunk
Outputs: (ok: bool, errors: list[str])
Notes: Lightweight; extend as needed
"""

from typing import Dict, List, Tuple, Any


def validate_chunk_meta(meta: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """Validate per-chunk metadata shape and types.

    Ensures optional string fields, numeric page, and well-formed actions array.
    Returns (ok, errors).
    """
    errors: List[str] = []
    if not isinstance(meta, dict):
        return False, ["metadata must be an object"]
    for key in ["section", "heading"]:
        if key in meta and meta[key] is not None and not isinstance(meta[key], str):
            errors.append(f"{key} must be string or null")
    if "page" in meta and meta["page"] is not None and not isinstance(meta["page"], (int, float)):
        errors.append("page must be number or null")
    for arr_key in ["framework_ids", "control_ids"]:
        if arr_key in meta and not isinstance(meta[arr_key], list):
            errors.append(f"{arr_key} must be an array")
    if "actions" in meta:
        if not isinstance(meta["actions"], list):
            errors.append("actions must be an array")
        else:
            for i, a in enumerate(meta["actions"]):
                if not isinstance(a, dict):
                    errors.append(f"actions[{i}] must be object")
                    continue
                if a.get("type") not in ("start_workflow", "open_register", "draft_doc"):
                    errors.append(f"actions[{i}].type invalid")
                if not isinstance(a.get("slug"), str):
                    errors.append(f"actions[{i}].slug must be string")
                if "label" in a and a["label"] is not None and not isinstance(a["label"], str):
                    errors.append(f"actions[{i}].label must be string or null")
    return len(errors) == 0, errors


def validate_document_meta(meta: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """Validate document-level metadata (taxonomy/tags arrays). Returns (ok, errors)."""
    errors: List[str] = []
    if not isinstance(meta, dict):
        return False, ["metadata must be an object"]
    if "taxonomy" in meta and not isinstance(meta["taxonomy"], dict):
        errors.append("taxonomy must be object")
    if "tags" in meta and not isinstance(meta["tags"], list):
        errors.append("tags must be array")
    return len(errors) == 0, errors
