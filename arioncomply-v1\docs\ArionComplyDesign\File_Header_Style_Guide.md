<!-- File: arioncomply-v1/docs/ArionComplyDesign/File_Header_Style_Guide.md -->
# File Header and Comment Style Guide (ArionComply)

Purpose
- Set a clear, consistent header and comment standard so a junior developer or engineer can understand, implement, and enhance any file quickly.
- Apply across languages (TS/JS, SQL, Python, Shell, HTML, JSON, Yaml) with small, practical variations.

Core Principles
- Be specific: describe what the file does, not just its name.
- Be actionable: include inputs/outputs, dependencies, security/RLS notes, and error behavior.
- Be brief: 5–10 lines in the header is ideal; use inline comments and function docs for details.
- Be truthful: keep headers updated when responsibilities change.

Universal Header Template
Use this structure at the very top of every file (adapt comment syntax per language):

- File Description: one‑line summary of the file’s responsibility
- Purpose: what this file enables (context, responsibilities)
- Inputs: external inputs (HTTP body/headers, env vars, CLI args)
- Outputs: response shape, side‑effects (DB writes, file writes)
- Dependencies: key modules/services this file relies on
- Security/RLS: auth/tenancy notes, policies impacted
- Notes: constraints, performance caveats, TODO pointers

TypeScript/JavaScript (Edge Functions)
Comment style: `//` at top; use JSDoc on exported functions. Keep API casing and envelopes explicit.

Example (endpoint)
```ts
// File Description: conversation.start endpoint
// Purpose: Initialize a new chat session; validate input; return session stub.
// Input: POST JSON { title?, context? }, headers { Authorization, x-org-id?, x-user-id? }
// Output: ApiSuccess { data: { session, firstMessage }, requestId, timestamp } (camelCase)
// Dependencies: _shared/errors (apiOk/apiError), _shared/logger (request/event logs)
// Security/RLS: inserts performed by service-role client (no user tokens); SELECT via org-scoped RLS
```

Example (shared module)
```ts
// File Description: API envelopes and error helpers
// Purpose: Define standardized success/error response builders (camelCase responses).
// Exports: apiOk<T>(), apiError(), ApiSuccess<T>, ApiError
// Notes: Used by all handlers to ensure consistent envelopes and timestamps.
```

SQL Migrations
Comment style: `--` lines at top. Include migration number, purpose, objects (tables/policies/indexes/functions), RLS implications, and verification hints.

Example
```sql
-- Migration 0011: Application Event Logging
-- Purpose: Persist inbound requests and granular events for audit and debugging.
-- Creates: api_request_logs, api_event_logs; indexes on (request_id), (org_id, created_at)
-- Policies: ENABLE RLS; org-scoped SELECT; admin bypass via app_has_role('admin')
-- Security: Inserts via service-role client; ensure org_id is always set
-- Verify: SELECT count(*) FROM api_request_logs; SELECT count(*) FROM api_event_logs;
```

Python (tools)
Comment style: module docstring at top. Include usage, arguments, inputs/outputs, and side effects.

Example
```py
"""
Build canonical term and phrase sets from curated Markdown corpora.
Usage: python tools/analyze/build_vocab.py --root data/corpus --out outputs/vocab
Inputs: Markdown files with (optional) front matter
Outputs: CSVs in outputs/vocab; summary report
Notes: Designed for batch runs; see CLI help for options
"""
```

Shell Scripts
Comment style: shebang + header comments. Include purpose, usage, env vars, prerequisites.

Example
```sh
#!/bin/bash
# File Description: Deploy selected Supabase Edge functions
# Usage: ./deploy.sh [-s|--skip-function]
# Env: SUPABASE_ACCESS_TOKEN (gh auth), SUPABASE_PROJECT_REF
# Notes: set -e; prints deployed function URLs
```

HTML/Frontend JS (testing harness)
- HTML: top `<!-- ... -->` block with page purpose and how to configure.
- JS: short header describing config shape and target services.

Example (JS)
```js
// File Description: Test harness runtime config
// Purpose: Provide Supabase URL, function URL, anon key for calls
// Shape: { supabaseUrl: string, functionUrl: string, anonKey: string }
```

Inline Comments and Function Docs
- Use JSDoc/TypeDoc for exported functions: parameters, returns, errors thrown.
- Call out logging: whether requestId/correlationId is expected/propagated.
- Note performance assumptions (e.g., streaming, pagination defaults).

Concrete Examples from Repo
- Edge: `_shared/logger.ts` documents request/event logging and service‑role usage.
- Edge: `conversation/start|send|stream` endpoints document inputs/outputs and logging.
- DB: `0011_application_event_logging.sql` describes tables, indexes, RLS.

PR Review Checklist (headers)
- Header present at top with File Description and Purpose.
- Inputs/Outputs clear and correct for public interfaces.
- Dependencies and Security/RLS noted where relevant.
- Examples or usage hints provided (endpoints, CLIs).
- Header aligns with actual code; no stale claims.

Adoption Plan
- New files must include a header on creation.
- Existing files: add headers when touching code, starting with Edge functions, SQL migrations, tools, and testing harness.
- Keep headers brief; move deep details into inline docs or module README when needed.

