# Phase 1 Chat Consultation Workflows
**Subset of Comprehensive Workflows Optimized for MVP Assessment Platform**

*Version 1.0 - September 15, 2025*

---

## Overview

This document defines the operational workflows that support the Phase 1 MVP user journeys. These workflows are specifically designed for:
- **Chat-first interface** with minimal UI complexity
- **Lead generation and customer acquisition** rather than ongoing compliance management
- **Educational approach** for compliance beginners
- **Assessment delivery** as primary value proposition
- **Pilot program enrollment** as conversion goal

---

## Core Chat Consultation Workflows

### **1. Customer Onboarding Workflow**
**Supporting User Journey:** All foundational journeys (discovery, registration, initial consultation)

#### **1.1 Landing Page to Registration Workflow**
**Triggers:** User clicks CTA from marketing landing page
**Systems:** Frontend routing, user management, analytics

```mermaid
graph TD
    A[Marketing Landing Page] --> B{User Clicks CTA}
    B --> C[Analytics Capture]
    C --> D[Registration/Login Page]
    D --> E[Email & Org Info Capture]
    E --> F[Email Validation]
    F --> G[Send Verification Email]
    G --> H[User Confirms Email]
    H --> I[MFA Setup]
    I --> J[Subscription Assignment]
    J --> K[Usage Limits Setup]
    K --> L[Welcome Chat Launch]
    L --> M[Journey Classification Begin]

    C --> N[(Marketing Attribution DB)]
    J --> O[(User Subscriptions DB)]
    K --> P[(Usage Tracking DB)]
```

**Process Flow:**
1. **Landing Page Analytics Capture**
   - Track referral source, campaign data
   - Log visitor session for lead scoring
   - Store UTM parameters and marketing attribution

2. **Registration Intent Capture**
   - Present registration/login page
   - Capture email and basic organizational info
   - Validate email format and domain

3. **MFA Setup Process**
   - Send verification email with secure token
   - Guide through MFA setup (authenticator app or SMS)
   - Validate MFA configuration

4. **Subscription Assignment**
   - Auto-assign "Assessment Subscription" role
   - Set monthly question limits (e.g., 50 questions/month)
   - Initialize usage tracking

5. **Welcome Chat Initiation**
   - Launch chat interface with welcome message
   - Present initial consultation options
   - Begin user journey classification

**Database Operations:**
- Create user record in `users` table
- Initialize subscription in `user_subscriptions` table
- Log onboarding events in `user_activity_log` table

---

#### **1.2 User Intent Classification Workflow**
**Triggers:** First chat interaction after registration
**Systems:** AI intent classifier, user profiling, conversation routing

```mermaid
graph TD
    A[First Chat Message] --> B[AI Intent Analysis]
    B --> C{Classification Results}

    C --> D[Startup Intent]
    C --> E[Non-profit Intent]
    C --> F[Enterprise Intent]
    C --> G[Beginner Education Intent]

    D --> H[Context Gathering - Startup]
    E --> I[Context Gathering - Non-profit]
    F --> J[Context Gathering - Enterprise]
    G --> K[Context Gathering - Beginner]

    H --> L[Startup Reality Check Flow]
    I --> M[Non-profit Guidance Flow]
    J --> N[Enterprise Assessment Flow]
    K --> O[Beginner Education Flow]

    L --> P[Personalization Setup]
    M --> P
    N --> P
    O --> P

    P --> Q[(User Profile DB)]
    P --> R[(Conversation Context DB)]
    P --> S[Knowledge Base Context Init]
```

**Process Flow:**
1. **Initial Conversation Analysis**
   - Process first user messages for intent signals
   - Classify user type (startup, non-profit, enterprise, beginner)
   - Identify primary compliance concerns

2. **Context Gathering**
   - Ask targeted questions based on initial classification
   - Capture industry, company size, existing compliance status
   - Identify specific standards of interest (ISO27001, GDPR, etc.)

3. **Journey Route Assignment**
   - Route to appropriate conversation flow:
     - Startup Reality Check Journey
     - Non-profit Guidance Journey
     - Beginner Education Journey
     - Enterprise Assessment Journey

4. **Personalization Setup**
   - Store user profile data for conversation continuity
   - Set conversation tone and complexity level
   - Initialize relevant knowledge base contexts

**Database Operations:**
- Update user profile with classification in `user_profiles` table
- Log intent classification in `conversation_intents` table
- Initialize conversation context in `chat_sessions` table

---

### **2. Assessment Delivery Workflows**

#### **2.1 Conversational Assessment Workflow**
**Supporting User Journey:** All assessment-focused journeys
**Systems:** QA knowledge base, assessment engine, report generation

```mermaid
graph TD
    A[User Routed to Assessment] --> B[Assessment Initiation]
    B --> C{User Consent?}
    C -->|Yes| D[Dynamic Question Generation]
    C -->|No| E[Continue General Consultation]

    D --> F[(QA Knowledge Base)]
    F --> G[Generate Contextual Questions]
    G --> H[Present Question Conversationally]
    H --> I[User Response]
    I --> J{Response Complete?}
    J -->|No| K[Ask Follow-up Questions]
    K --> I
    J -->|Yes| L[Response Analysis & Scoring]
    L --> M{More Questions?}
    M -->|Yes| G
    M -->|No| N[Assessment Summary]

    N --> O{Offer PDF Report?}
    O -->|Yes| P[Generate PDF Report]
    O -->|No| Q[Present Next Steps]
    P --> Q

    Q --> R{Pilot Interest?}
    R -->|Yes| S[Pilot Program Workflow]
    R -->|No| T[Continue Consultation]

    L --> U[(Assessment Responses DB)]
    L --> V[(Compliance Scores DB)]
    P --> W[(Generated Reports DB)]
```

**Process Flow:**
1. **Assessment Initiation**
   - Present assessment option based on user classification
   - Explain assessment process and expected outcomes
   - Obtain user consent to begin formal assessment

2. **Dynamic Question Generation**
   - Query relevant QA knowledge base sections
   - Generate contextual questions based on:
     - User's industry and company profile
     - Selected compliance standards
     - Conversation history and expressed concerns

3. **Interactive Q&A Session**
   - Present questions in conversational format
   - Accept natural language responses
   - Ask follow-up questions for clarification
   - Provide educational context for complex topics

4. **Response Analysis and Scoring**
   - Process responses using compliance scoring algorithms
   - Identify gaps and strengths in current posture
   - Calculate compliance scores by framework
   - Generate improvement recommendations

5. **Assessment Completion**
   - Summarize key findings in conversation
   - Offer to generate formal PDF report
   - Present next steps options (pilot program, additional consultation)

**Database Operations:**
- Store assessment session in `assessments` table
- Log Q&A interactions in `assessment_responses` table
- Save scoring results in `compliance_scores` table

---

#### **2.2 Professional PDF Report Generation Workflow**
**Supporting User Journey:** Report delivery phase of all assessment journeys
**Systems:** Report generation engine, template management, document delivery

**Process Flow:**
1. **Report Template Selection**
   - Select appropriate template based on:
     - Assessment type and scope
     - User classification (startup, enterprise, etc.)
     - Compliance frameworks covered

2. **Data Compilation**
   - Aggregate assessment responses and scores
   - Include relevant regulatory context from knowledge base
   - Add personalized recommendations based on user profile

3. **Professional Report Generation**
   - Generate executive summary with key findings
   - Include detailed compliance score breakdown
   - Add implementation roadmap recommendations
   - Include regulatory references and next steps

4. **Report Delivery**
   - Generate secure download link
   - Send notification via email and in-chat
   - Log report generation for billing/usage tracking
   - Store report metadata for follow-up conversations

**Database Operations:**
- Store report metadata in `generated_reports` table
- Link report to assessment in `assessment_reports` table
- Update usage tracking in `user_usage_metrics` table

---

### **3. Educational Support Workflows**

#### **3.1 Compliance Education Workflow**
**Supporting User Journey:** Beginner education journey, startup reality check
**Systems:** Educational content management, progressive learning engine

**Process Flow:**
1. **Knowledge Level Assessment**
   - Ask diagnostic questions to gauge current understanding
   - Identify specific knowledge gaps
   - Set appropriate educational starting point

2. **Progressive Content Delivery**
   - Deliver bite-sized educational content in chat
   - Use analogies and examples relevant to user's industry
   - Check understanding before advancing to complex topics

3. **Interactive Learning Support**
   - Encourage questions and provide detailed explanations
   - Offer real-world examples and case studies
   - Connect abstract concepts to user's specific situation

4. **Knowledge Reinforcement**
   - Summarize key learning points
   - Provide takeaway resources and references
   - Suggest practical next steps for implementation

**Database Operations:**
- Track learning progress in `user_education_progress` table
- Log educational interactions in `education_sessions` table
- Store knowledge level assessments in `user_knowledge_profiles` table

---

### **4. Lead Conversion Workflows**

#### **4.1 Pilot Program Enrollment Workflow**
**Supporting User Journey:** Conversion phase of all user journeys
**Systems:** CRM integration, sales pipeline management, subscription management

```mermaid
graph TD
    A[Assessment Completed] --> B[Qualification Assessment]
    B --> C{Meets Pilot Criteria?}

    C -->|Yes| D[Present Pilot Program]
    C -->|No| E[Continue Nurturing]

    D --> F{User Interest?}
    F -->|High| G[Capture Interest Details]
    F -->|Medium| H[Provide More Information]
    F -->|Low| E

    H --> I{Follow-up Interest?}
    I -->|Yes| G
    I -->|No| E

    G --> J[Collect Qualification Info]
    J --> K[Create CRM Lead Record]
    K --> L[Assign Sales Team Member]
    L --> M[Set Follow-up Tasks]
    M --> N[Notify Sales Team]

    N --> O[Maintain Chat Access]
    O --> P[Continue Educational Support]
    P --> Q[Monitor Sales Process]

    E --> R[Long-term Nurturing Campaign]

    K --> S[(CRM System)]
    B --> T[(Lead Scoring DB)]
    G --> U[(Sales Pipeline DB)]
```

**Process Flow:**
1. **Qualification Assessment**
   - Evaluate user's pilot program fit based on:
     - Assessment results and compliance needs
     - Company size and complexity
     - Expressed interest level and timeline

2. **Pilot Program Presentation**
   - Explain pilot program benefits and scope
   - Present pricing and timeline information
   - Address questions and concerns

3. **Interest Capture**
   - Capture expression of interest
   - Schedule follow-up consultation if requested
   - Collect additional qualification information

4. **Sales Pipeline Entry**
   - Create lead record in CRM system
   - Assign to appropriate sales team member
   - Set follow-up tasks and timeline

5. **Transition Preparation**
   - Maintain chat access during sales process
   - Provide continued support and education
   - Prepare for potential full platform onboarding

**Database Operations:**
- Create lead record in `sales_leads` table
- Update user status in `user_subscriptions` table
- Log conversion events in `conversion_tracking` table

---

### **5. Usage Management Workflows**

#### **5.1 Question Limit and Usage Tracking Workflow**
**Supporting User Journey:** All ongoing consultation interactions
**Systems:** Usage tracking, rate limiting, upgrade prompts

**Process Flow:**
1. **Usage Monitoring**
   - Track questions asked per conversation session
   - Monitor monthly usage against subscription limits
   - Log interaction complexity and resource consumption

2. **Limit Enforcement**
   - Present usage status when approaching limits
   - Gracefully handle limit exceeded scenarios
   - Offer upgrade or pilot program options

3. **Usage Analytics**
   - Track engagement patterns and popular topics
   - Identify high-value users for sales priority
   - Monitor system resource utilization

4. **Upgrade Prompting**
   - Present upgrade options based on usage patterns
   - Highlight pilot program benefits for heavy users
   - Facilitate smooth transition to sales process

**Database Operations:**
- Update usage metrics in `user_usage_metrics` table
- Log rate limiting events in `usage_limit_events` table
- Track upgrade prompts in `upgrade_interactions` table

---

## Integration with Backend Systems

### **Knowledge Base Integration**
- **QA Database**: Real-time queries for assessment questions and educational content
- **Standards Database**: Access to ISO27001, GDPR, EU AI Act regulatory content
- **Template Library**: Professional report templates and educational resources

### **User Management Integration**
- **Authentication System**: MFA, session management, security controls
- **Subscription Management**: Usage tracking, limit enforcement, upgrade handling
- **Profile Management**: User classification, preferences, conversation history

### **Analytics and Monitoring**
- **Conversation Analytics**: Intent classification, satisfaction metrics, conversion tracking
- **System Performance**: Response times, error rates, resource utilization
- **Business Metrics**: Lead generation, conversion rates, user engagement

---

## Workflow State Management

### **Conversation State Persistence**
- Maintain conversation context across sessions
- Store user preferences and classification data
- Preserve assessment progress for multi-session completion

### **Error Handling and Recovery**
- Graceful degradation when backend services are unavailable
- Conversation state recovery after interruptions
- User-friendly error messaging with resolution guidance

### **Performance Optimization**
- Caching frequently accessed knowledge base content
- Optimizing assessment question generation
- Efficient report generation and delivery

---

This Phase 1 workflow subset provides the operational foundation needed to support the user journeys while maintaining a clear path to full platform capabilities in later phases.