@startuml Ingest / Index (LLM2.0)
title Ingest / Index Pipeline (LLM 2.0 aligned)
skinparam shadowing false
skinparam monochrome true

actor "Edge Function" as Edge
participant "API (FastAPI)\n/ingest" as API
queue Redis
participant Worker as W
database "App DB" as APPDB
database "Postgres/pgvector" as PG
database "Chroma (optional)" as Chroma
entity "Supabase Storage" as ST
participant "ETL: detect • convert • OCR • normalize • chunk" as ETL
participant "Embedder (local first)" as EMB

Edge -> API : POST /ingest\n(tenant, doc_id, path, meta)
API -> APPDB : create job(status=queued)
API -> Redis : enqueue(job_id)
API --> Edge : 202 Accepted

Redis -> W : job(job_id)
W -> ST : signed URL (download)
W -> ST : download to /tmp
W -> W : checksum • MIME detect • size
W -> APPDB : lookup (doc_id, checksum)
APPDB --> W : exists? (duplicate)

alt duplicate content
  W -> APPDB : update job(status=skipped)\n(reason=duplicate)
else new or changed
  W -> ETL : convert/OCR/normalize/chunk
  ETL --> W : chunks[] + meta\n(pages, sections, tags)
  W -> EMB : embed(chunks)\n(local /v1; fallback provider)
  EMB --> W : vectors
  W -> PG : upsert vectors + metadata
  W -> Chroma : upsert (optional)
  W -> APPDB : update doc + job\n(status=completed, counts, timings)
end

W -> ST : cleanup temp (optional)
W -> API : progress/logs (optional)

@enduml
