id: Q161
query: >-
  What are Standard Contractual Clauses and when do we need them?
packs:
  - "GDPR:2016"
primary_ids:
  - "GDPR:2016/Art.46"
overlap_ids: []
capability_tags:
  - "Register"
  - "Draft Doc"
  - "Workflow"
flags: []
sources:
  - title: "GDPR — Appropriate Safeguards for Transfers"
    id: "GDPR:2016/Art.46"
    locator: "Article 46"
ui:
  cards_hint:
    - "SCC template library"
  actions:
    - type: "start_workflow"
      target: "scc_onboarding"
      label: "Generate SCCs"
    - type: "open_register"
      target: "transfer_register"
      label: "View Transfers"
output_mode: "both"
graph_required: false
notes: "Use SCCs when no adequacy decision exists"
---
### 161) What are Standard Contractual Clauses and when do we need them?

**Standard terms)**  
- **Standard Contractual Clauses (GDPR Art. 46):** EU-approved contract text ensuring EU-level protection when personal data moves outside the EEA.  
- **Adequacy decision:** EU confirmation a destination nation already provides equivalent protection.

**Plain-English answer**  
SCCs are “plug-and-play” legal clauses issued by the European Commission. Sign them with the non-EEA recipient **whenever you transfer EU personal data to a country that lacks an adequacy decision** and you don’t have Binding Corporate Rules (BCRs) or another approved safeguard.

**Applies to**  
- **Primary:** GDPR Article 46

**Why it matters**  
Using SCCs avoids illegal transfers and the multi-million-euro fines that come with them.

**Do next in our platform**  
1. Open **Transfer Register** and flag all destinations lacking adequacy.  
2. Run **SCC Onboarding** workflow to generate, e-sign, and store clauses.  
3. Attach SCCs to each transfer record and set annual re-validation reminders.

**How our platform will help**  
- **[Draft Doc]** Generates latest SCC module combination.  
- **[Register]** Tracks which transfers rely on SCCs.  
- **[Workflow]** Handles legal review, signature routing, and renewal alerts.

**Likely follow-ups**  
- What changed in the 2021 SCC update?  
- Do we need a **Transfer Impact Assessment (TIA)** as well?

**Sources**  
- GDPR Article 46
