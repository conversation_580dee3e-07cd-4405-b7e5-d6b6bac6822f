"""
File: arioncomply-v1/ai-backend/python-backend/services/ingestion/dual_vector_ingestion.py
File Description: Dual-vector ingestion orchestrator for public/private data separation
Purpose: Route documents to ChromaDB (public) or Supabase Vector (private) based on data classification
Inputs: org_id, file_path, data_classification, content_type, metadata
Outputs: dict { doc_id, vector_store, chunks: n, embeddings: n }
Security: Enforces data classification and tenant isolation
Notes: Integrates with multi-pipeline embedding architecture and audit trail
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional, Literal
from enum import Enum
from dataclasses import dataclass

from .locling_extractor import extract_blocks
from .chunker import chunk_blocks
from .embedder import embed_texts_async
from .chromadb_client import chroma_client, CHROMA_ENABLED
from .metadata_validator import validate_document_metadata
from services.retrieval.supabase_vector import search as supabase_vector_search

logger = logging.getLogger(__name__)

VECTOR_SUPABASE_URL = os.getenv("VECTOR_SUPABASE_URL")
VECTOR_SUPABASE_SERVICE_KEY = os.getenv("VECTOR_SUPABASE_SERVICE_KEY")


class DataClassification(str, Enum):
    """Data classification for dual-vector routing."""
    PUBLIC_STANDARDS = "public_standards"      # Standards, regulations, laws → ChromaDB
    PUBLIC_ASSESSMENTS = "public_assessments"  # Assessment questions, frameworks → ChromaDB
    PUBLIC_TEMPLATES = "public_templates"      # Templates, best practices → ChromaDB
    PRIVATE_POLICIES = "private_policies"      # Company policies → Supabase Vector
    PRIVATE_ASSESSMENTS = "private_assessments" # Assessment results → Supabase Vector
    PRIVATE_DOCUMENTS = "private_documents"    # Proprietary content → Supabase Vector


@dataclass
class IngestionRequest:
    """Structured ingestion request with classification metadata."""
    org_id: str
    file_path: str
    classification: DataClassification
    title: Optional[str] = None
    source: Optional[str] = None
    version: Optional[str] = None
    hash_value: Optional[str] = None
    content_metadata: Optional[Dict[str, Any]] = None
    pipeline_name: Optional[str] = None  # Force specific embedding pipeline


@dataclass
class IngestionResult:
    """Structured ingestion result with audit trail."""
    doc_id: str
    vector_store: Literal["chromadb", "supabase_vector"]
    chunks: int
    embeddings: int
    pipeline_used: str
    classification: DataClassification
    embedding_dimension: int
    audit_trail: Dict[str, Any]
    errors: List[str]


class DualVectorIngestionOrchestrator:
    """Orchestrates document ingestion to appropriate vector store based on classification."""
    
    def __init__(self):
        """Initialize orchestrator for dual-vector ingestion flows."""
        self.chromadb_enabled = CHROMA_ENABLED
        self.supabase_enabled = bool(VECTOR_SUPABASE_URL and VECTOR_SUPABASE_SERVICE_KEY)
        
        if not (self.chromadb_enabled or self.supabase_enabled):
            logger.warning("Neither ChromaDB nor Supabase Vector is available")
    
    async def ingest_document(self, request: IngestionRequest) -> IngestionResult:
        """
        Ingest document to appropriate vector store based on classification.
        
        Args:
            request: Structured ingestion request
            
        Returns:
            IngestionResult with complete audit trail
        """
        audit_trail = {
            "start_time": self._get_timestamp(),
            "request": request.__dict__.copy(),
            "steps": []
        }
        errors = []
        
        try:
            # 1. Validate request and metadata
            self._validate_request(request)
            audit_trail["steps"].append("request_validated")
            
            # 2. Extract content blocks
            blocks = extract_blocks(request.file_path)
            audit_trail["steps"].append(f"extracted_{len(blocks)}_blocks")
            
            # 3. Chunk content
            chunks = chunk_blocks(blocks)
            audit_trail["steps"].append(f"created_{len(chunks)}_chunks")
            
            # 4. Generate embeddings with pipeline selection
            texts = [c["text"] for c in chunks]
            embedding_result = await embed_texts_async(
                texts, 
                pipeline_name=request.pipeline_name,
                trace_id=f"ingest_{request.org_id}_{os.path.basename(request.file_path)}"
            )
            audit_trail["steps"].append(f"generated_embeddings_with_{embedding_result.pipeline_name}")
            
            # 5. Route to appropriate vector store
            if self._should_route_to_chromadb(request.classification):
                result = await self._ingest_to_chromadb(request, chunks, embedding_result, audit_trail)
            else:
                result = await self._ingest_to_supabase(request, chunks, embedding_result, audit_trail)
            
            # 6. Finalize audit trail
            audit_trail["end_time"] = self._get_timestamp()
            audit_trail["success"] = True
            result.audit_trail = audit_trail
            
            logger.info(f"Ingestion successful: {result.vector_store} - {result.chunks} chunks")
            return result
            
        except Exception as e:
            error_msg = f"Ingestion failed: {str(e)}"
            errors.append(error_msg)
            logger.error(error_msg, exc_info=True)
            
            audit_trail["end_time"] = self._get_timestamp()
            audit_trail["success"] = False
            audit_trail["error"] = error_msg
            
            # Return error result
            return IngestionResult(
                doc_id="",
                vector_store="none",
                chunks=0,
                embeddings=0,
                pipeline_used="none",
                classification=request.classification,
                embedding_dimension=0,
                audit_trail=audit_trail,
                errors=errors
            )
    
    def _should_route_to_chromadb(self, classification: DataClassification) -> bool:
        """Determine if document should go to ChromaDB (public) or Supabase Vector (private)."""
        public_classifications = {
            DataClassification.PUBLIC_STANDARDS,
            DataClassification.PUBLIC_ASSESSMENTS,
            DataClassification.PUBLIC_TEMPLATES
        }
        return classification in public_classifications and self.chromadb_enabled
    
    async def _ingest_to_chromadb(
        self, 
        request: IngestionRequest, 
        chunks: List[Dict[str, Any]], 
        embedding_result,
        audit_trail: Dict[str, Any]
    ) -> IngestionResult:
        """Ingest document to ChromaDB for public shared knowledge."""
        
        # Determine collection based on classification
        collection_name = self._get_chromadb_collection(request.classification)
        
        # Prepare ChromaDB metadata
        chunk_ids = [f"{request.org_id}_{os.path.basename(request.file_path)}_{c['seq']}" for c in chunks]
        texts = [c["text"] for c in chunks]
        metadatas = []
        
        for i, c in enumerate(chunks):
            metadata = {
                "org_id": request.org_id,  # For cross-org shared data analytics
                "classification": request.classification.value,
                "source": request.source or "upload",
                "version": request.version or "v1",
                "seq": c["seq"],
                "section": c.get("section"),
                "heading": c.get("heading"),
                "page": c.get("page"),
                "pipeline_used": embedding_result.pipeline_name,
                "embedding_dim": embedding_result.embedding_dimension
            }
            
            # Add content-specific metadata
            if request.content_metadata:
                metadata.update(request.content_metadata)
                
            metadatas.append(metadata)
        
        # Upsert to ChromaDB
        success = chroma_client.upsert_chunks(
            org_id="shared",  # Special org_id for shared public data
            collection_name=collection_name,
            chunk_ids=chunk_ids,
            embeddings=embedding_result.embeddings,
            texts=texts,
            metadatas=metadatas
        )
        
        if not success:
            raise Exception("ChromaDB upsert failed")
            
        audit_trail["steps"].append(f"chromadb_upsert_to_{collection_name}")
        
        return IngestionResult(
            doc_id=f"chromadb_{collection_name}_{os.path.basename(request.file_path)}",
            vector_store="chromadb",
            chunks=len(chunks),
            embeddings=len(chunks),
            pipeline_used=embedding_result.pipeline_name,
            classification=request.classification,
            embedding_dimension=embedding_result.embedding_dimension,
            audit_trail=audit_trail,
            errors=[]
        )
    
    async def _ingest_to_supabase(
        self, 
        request: IngestionRequest, 
        chunks: List[Dict[str, Any]], 
        embedding_result,
        audit_trail: Dict[str, Any]
    ) -> IngestionResult:
        """Ingest document to Supabase Vector for private organizational data."""
        
        if not self.supabase_enabled:
            raise Exception("Supabase Vector not configured")
        
        # 1. Insert document
        doc_row = {
            "org_id": request.org_id,
            "title": request.title or os.path.basename(request.file_path),
            "source": request.source or "upload",
            "version": request.version or "v1",
            "hash": request.hash_value,
            "metadata": {
                "classification": request.classification.value,
                "content_metadata": request.content_metadata or {},
                "pipeline_used": embedding_result.pipeline_name,
                "embedding_dimension": embedding_result.embedding_dimension
            }
        }
        
        doc_id = await self._post_supabase("documents", doc_row)
        audit_trail["steps"].append("supabase_document_created")
        
        # 2. Insert chunks and embeddings
        for i, c in enumerate(chunks):
            # Insert chunk
            c_row = {
                "org_id": request.org_id,
                "doc_id": doc_id,
                "seq": c["seq"],
                "text": c["text"],
                "tokens": len(c["text"].split()),  # Rough token count
                "metadata": {
                    "section": c.get("section"),
                    "heading": c.get("heading"),
                    "page": c.get("page"),
                    "classification": request.classification.value
                }
            }
            
            chunk_id = await self._post_supabase("chunks", c_row)
            
            # Insert embedding
            e_row = {
                "org_id": request.org_id,
                "chunk_id": chunk_id,
                "model": embedding_result.pipeline_name,
                "dim": embedding_result.embedding_dimension,
                "embedding": embedding_result.embeddings[i],
            }
            
            await self._post_supabase("embeddings", e_row)
        
        audit_trail["steps"].append("supabase_chunks_and_embeddings_created")
        
        return IngestionResult(
            doc_id=doc_id,
            vector_store="supabase_vector",
            chunks=len(chunks),
            embeddings=len(chunks),
            pipeline_used=embedding_result.pipeline_name,
            classification=request.classification,
            embedding_dimension=embedding_result.embedding_dimension,
            audit_trail=audit_trail,
            errors=[]
        )
    
    def _get_chromadb_collection(self, classification: DataClassification) -> str:
        """Get ChromaDB collection name based on data classification."""
        collection_mapping = {
            DataClassification.PUBLIC_STANDARDS: "standards_knowledge",
            DataClassification.PUBLIC_ASSESSMENTS: "assessment_frameworks", 
            DataClassification.PUBLIC_TEMPLATES: "templates_and_workflows"
        }
        return collection_mapping.get(classification, "general_public_knowledge")
    
    async def _post_supabase(self, table: str, row: Dict[str, Any]) -> str:
        """Post row to Supabase table and return ID."""
        import aiohttp
        
        url = f"{VECTOR_SUPABASE_URL}/rest/v1/{table}"
        headers = {
            "apikey": VECTOR_SUPABASE_SERVICE_KEY,
            "Authorization": f"Bearer {VECTOR_SUPABASE_SERVICE_KEY}",
            "Content-Type": "application/json",
            "Prefer": "return=representation",
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=row) as response:
                if response.status >= 300:
                    raise Exception(f"Supabase POST failed: {response.status}")
                    
                data = await response.json()
                return (data[0]["id"] if isinstance(data, list) and data else data.get("id")) or ""
    
    def _validate_request(self, request: IngestionRequest):
        """Validate ingestion request."""
        if not request.org_id:
            raise ValueError("org_id is required")
        if not request.file_path or not os.path.exists(request.file_path):
            raise ValueError(f"Invalid file_path: {request.file_path}")
        if not isinstance(request.classification, DataClassification):
            raise ValueError(f"Invalid classification: {request.classification}")
    
    def _get_timestamp(self) -> str:
        """Get ISO timestamp for audit trail."""
        from datetime import datetime
        return datetime.utcnow().isoformat() + "Z"


# Convenience functions for backward compatibility
async def ingest_public_document(
    file_path: str,
    classification: DataClassification,
    org_id: str = "shared",
    **kwargs
) -> IngestionResult:
    """Ingest public document to ChromaDB."""
    orchestrator = DualVectorIngestionOrchestrator()
    request = IngestionRequest(
        org_id=org_id,
        file_path=file_path,
        classification=classification,
        **kwargs
    )
    return await orchestrator.ingest_document(request)


async def ingest_private_document(
    org_id: str,
    file_path: str,
    classification: DataClassification,
    **kwargs
) -> IngestionResult:
    """Ingest private organizational document to Supabase Vector."""
    orchestrator = DualVectorIngestionOrchestrator()
    request = IngestionRequest(
        org_id=org_id,
        file_path=file_path,
        classification=classification,
        **kwargs
    )
    return await orchestrator.ingest_document(request)


# Legacy synchronous wrapper
def ingest_one_dual_vector(
    org_id: str, 
    file_path: str, 
    classification: str,
    **kwargs
) -> Dict[str, Any]:
    """
    Legacy synchronous wrapper for dual-vector ingestion.
    
    This provides backward compatibility while routing to the new architecture.
    """
    import asyncio
    
    try:
        # Convert string classification to enum
        classification_enum = DataClassification(classification)
        
        # Create request
        request = IngestionRequest(
            org_id=org_id,
            file_path=file_path,
            classification=classification_enum,
            **kwargs
        )
        
        # Run async ingestion
        orchestrator = DualVectorIngestionOrchestrator()
        loop = asyncio.get_event_loop()
        if loop.is_running():
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(_run_async_ingest, orchestrator, request)
                result = future.result()
        else:
            result = loop.run_until_complete(orchestrator.ingest_document(request))
        
        # Convert to legacy format
        return {
            "doc_id": result.doc_id,
            "vector_store": result.vector_store,
            "chunks": result.chunks,
            "embeddings": result.embeddings,
            "pipeline_used": result.pipeline_used,
            "classification": result.classification.value
        }
        
    except Exception as e:
        logger.error(f"Legacy ingestion wrapper failed: {e}")
        return {
            "doc_id": "",
            "vector_store": "error",
            "chunks": 0,
            "embeddings": 0,
            "error": str(e)
        }


def _run_async_ingest(orchestrator, request):
    """Run async ingestion in new event loop."""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(orchestrator.ingest_document(request))
    finally:
        loop.close()
