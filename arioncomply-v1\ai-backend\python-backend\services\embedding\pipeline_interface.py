# File: arioncomply-v1/ai-backend/python-backend/services/embedding/pipeline_interface.py
# File Description: Core interfaces and data models for the embedding subsystem
# Purpose: Define abstract pipeline contract, metadata/results structures, and health reporting
"""
Core pipeline interface and abstractions for multi-pipeline embedding system.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid


class PipelineStatus(Enum):
    """Pipeline health status."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    FAILED = "failed"
    NOT_LOADED = "not_loaded"


class QualityTier(Enum):
    """Pipeline quality classification."""
    SOTA = "sota"  # State-of-the-art
    HIGH = "high"
    GOOD = "good"
    TESTING_ONLY = "testing_only"


@dataclass
class PipelineMetadata:
    """Complete pipeline metadata with provenance."""
    name: str
    model_name: str
    dimension: int
    quality_tier: QualityTier
    is_local: bool
    
    # Version and provenance
    version: str
    provider: str
    creation_date: datetime
    
    # Technical specifications
    memory_mb: Optional[int] = None
    inference_time_ms: Optional[int] = None
    context_length: Optional[int] = None
    
    # Configuration
    config: Dict[str, Any] = None
    
    def __post_init__(self):
        """Ensure config dict is initialized."""
        if self.config is None:
            self.config = {}


@dataclass
class EmbeddingResult:
    """Complete embedding operation result with audit trail."""
    # Operation metadata
    operation_id: str
    pipeline_name: str
    pipeline_metadata: PipelineMetadata
    timestamp: datetime
    
    # Input/output data
    input_texts: List[str]
    embeddings: List[List[float]]
    
    # Performance metrics
    processing_time_ms: float
    success: bool
    error_message: Optional[str] = None
    
    # Audit trail
    trace_id: Optional[str] = None
    
    def __post_init__(self):
        """Fill missing operation_id/timestamp defaults."""
        if self.operation_id is None:
            self.operation_id = str(uuid.uuid4())
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


@dataclass
class HealthCheckResult:
    """Pipeline health check result."""
    pipeline_name: str
    status: PipelineStatus
    timestamp: datetime
    
    # Performance metrics
    load_time_ms: Optional[float] = None
    test_inference_time_ms: Optional[float] = None
    memory_usage_mb: Optional[float] = None
    
    # Error information
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    
    # Diagnostic info
    is_model_loaded: bool = False
    dependencies_available: bool = False


class EmbeddingPipeline(ABC):
    """Abstract base class for all embedding pipelines."""
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize base pipeline with config and metadata holders."""
        self.config = config or {}
        self._is_loaded = False
        self._metadata: Optional[PipelineMetadata] = None
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Pipeline identifier name."""
        pass
    
    @property
    @abstractmethod
    def metadata(self) -> PipelineMetadata:
        """Get pipeline metadata."""
        pass
    
    @abstractmethod
    async def load(self) -> None:
        """Load the pipeline model and prepare for inference."""
        pass
    
    @abstractmethod
    async def embed_texts(self, texts: List[str], trace_id: Optional[str] = None) -> EmbeddingResult:
        """Generate embeddings for input texts."""
        pass
    
    @abstractmethod
    async def health_check(self) -> HealthCheckResult:
        """Perform pipeline health check."""
        pass
    
    @abstractmethod
    async def unload(self) -> None:
        """Unload the pipeline to free resources."""
        pass
    
    @property
    def is_loaded(self) -> bool:
        """Check if pipeline is loaded and ready."""
        return self._is_loaded
    
    def validate_inputs(self, texts: List[str]) -> None:
        """Validate input texts."""
        if not texts:
            raise ValueError("Input texts list cannot be empty")
        
        for i, text in enumerate(texts):
            if not isinstance(text, str):
                raise ValueError(f"Text at index {i} must be a string, got {type(text)}")
            if len(text.strip()) == 0:
                raise ValueError(f"Text at index {i} cannot be empty or whitespace only")
    
    def _create_embedding_result(
        self,
        operation_id: str,
        input_texts: List[str],
        embeddings: List[List[float]],
        processing_time_ms: float,
        success: bool = True,
        error_message: Optional[str] = None,
        trace_id: Optional[str] = None
    ) -> EmbeddingResult:
        """Helper to create standardized embedding result."""
        return EmbeddingResult(
            operation_id=operation_id,
            pipeline_name=self.name,
            pipeline_metadata=self.metadata,
            timestamp=datetime.utcnow(),
            input_texts=input_texts,
            embeddings=embeddings,
            processing_time_ms=processing_time_ms,
            success=success,
            error_message=error_message,
            trace_id=trace_id
        )
