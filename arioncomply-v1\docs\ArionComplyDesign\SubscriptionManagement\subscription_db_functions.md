# Subscription Management Database Functions and Triggers

## Overview

This document defines the database functions and triggers required to implement the subscription management system for ArionComply. These functions and triggers handle permission checking, usage tracking, limit enforcement, subscription status management, and role assignment.

## Prerequisites

Before implementing these functions and triggers, ensure the following:

1. The database schema from `arioncomply-v1/docs/ApplicationDatabase/Unified-Subscription-Management-Schema-Design.md` has been applied
2. The `organizations` and `users` tables exist and are properly set up with the required fields
3. The PostgreSQL version is 12 or higher (for proper JSONB support)

## Permission Functions

### 1. Check Permission Function

This function checks if a user has a specific permission through any of their assigned roles.

```sql
CREATE OR REPLACE FUNCTION check_permission(
    p_organization_id UUID,
    p_user_id UUID,
    p_permission_code TEXT
) RETURNS BOOLEAN AS $$
DECLARE
    v_has_permission BOOLEAN;
BEGIN
    -- Set context for RLS policies
    PERFORM set_config('app.current_organization_id', p_organization_id::TEXT, true);
    PERFORM set_config('app.current_user_id', p_user_id::TEXT, true);
    
    -- Check if user has the permission through any of their roles
    SELECT EXISTS (
        SELECT 1
        FROM user_roles ur
        JOIN role_permissions rp ON ur.role_id = rp.role_id
        JOIN permissions p ON rp.permission_id = p.permission_id
        WHERE ur.user_id = p_user_id
        AND p.permission_code = p_permission_code
    ) INTO v_has_permission;
    
    RETURN v_has_permission;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION check_permission IS 'Checks if a user has a specific permission through any of their roles';
```

### 2. Check Feature Limit Function

This function checks if an organization has reached the limit for a specific feature based on their subscription plan.

```sql
CREATE OR REPLACE FUNCTION check_feature_limit(
    p_organization_id UUID,
    p_feature_code TEXT
) RETURNS BOOLEAN AS $$
DECLARE
    v_limit INTEGER;
    v_current_count INTEGER;
    v_feature_name TEXT;
    v_table_name TEXT;
BEGIN
    -- Set context for RLS policies
    PERFORM set_config('app.current_organization_id', p_organization_id::TEXT, true);
    
    -- Extract feature name from feature code (e.g., 'create:policy' -> 'policy')
    v_feature_name := SUBSTRING(p_feature_code FROM POSITION(':' IN p_feature_code) + 1);
    
    -- Get the limit from the active subscription's plan configuration
    SELECT (sp.plan_configuration->'feature_limits'->v_feature_name)::INTEGER
    INTO v_limit
    FROM org_subscriptions os
    JOIN sub_plans sp ON os.sub_plan_id = sp.plan_id
    WHERE os.sub_org_id = p_organization_id
    AND os.sub_status = 'active';
    
    -- If no limit is set or it's null, there's no limit
    IF v_limit IS NULL THEN
        RETURN true;
    END IF;
    
    -- Determine the table name based on feature name
    CASE v_feature_name
        WHEN 'policies' THEN v_table_name := 'policies';
        WHEN 'controls' THEN v_table_name := 'controls';
        WHEN 'risks' THEN v_table_name := 'risks';
        WHEN 'documents' THEN v_table_name := 'documents';
        ELSE v_table_name := NULL;
    END CASE;
    
    -- If table name is valid, check the current count
    IF v_table_name IS NOT NULL THEN
        EXECUTE format('
            SELECT COUNT(*) 
            FROM %I 
            WHERE organization_id = %L
        ', v_table_name, p_organization_id) INTO v_current_count;
        
        -- Return true if below limit, false if at or above limit
        RETURN v_current_count < v_limit;
    END IF;
    
    -- Default to true if feature doesn't map to a counted table
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION check_feature_limit IS 'Checks if an organization has reached the limit for a specific feature';
```

### 3. Check Feature Access Function

This function checks if a subscription plan includes access to a specific feature.

```sql
CREATE OR REPLACE FUNCTION check_feature_access(
    p_organization_id UUID,
    p_feature_name TEXT
) RETURNS BOOLEAN AS $$
DECLARE
    v_has_access BOOLEAN;
BEGIN
    -- Set context for RLS policies
    PERFORM set_config('app.current_organization_id', p_organization_id::TEXT, true);
    
    -- Check if the active subscription's plan includes access to the feature
    SELECT (sp.plan_configuration->'feature_access'->p_feature_name)::BOOLEAN
    INTO v_has_access
    FROM org_subscriptions os
    JOIN sub_plans sp ON os.sub_plan_id = sp.plan_id
    WHERE os.sub_org_id = p_organization_id
    AND os.sub_status = 'active';
    
    -- Return the result, defaulting to false if not found
    RETURN COALESCE(v_has_access, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION check_feature_access IS 'Checks if a subscription plan includes access to a specific feature';
```

### 4. Unified Permission Check Function

This function combines permission checking, feature limit checking, and feature access checking.

```sql
CREATE OR REPLACE FUNCTION unified_permission_check(
    p_organization_id UUID,
    p_user_id UUID,
    p_permission_code TEXT
) RETURNS JSONB AS $$
DECLARE
    v_has_permission BOOLEAN;
    v_within_limit BOOLEAN;
    v_feature_access BOOLEAN;
    v_feature_name TEXT;
    v_action TEXT;
    v_result JSONB;
BEGIN
    -- Set context for RLS policies
    PERFORM set_config('app.current_organization_id', p_organization_id::TEXT, true);
    PERFORM set_config('app.current_user_id', p_user_id::TEXT, true);
    
    -- Check if user has the permission
    v_has_permission := check_permission(p_organization_id, p_user_id, p_permission_code);
    
    -- If no permission, return early
    IF NOT v_has_permission THEN
        RETURN jsonb_build_object(
            'allowed', false,
            'reason', 'permission_denied',
            'message', 'User does not have the required permission: ' || p_permission_code
        );
    END IF;
    
    -- Extract action and feature from permission code (e.g., 'create:policy')
    v_action := SUBSTRING(p_permission_code FROM 1 FOR POSITION(':' IN p_permission_code) - 1);
    v_feature_name := SUBSTRING(p_permission_code FROM POSITION(':' IN p_permission_code) + 1);
    
    -- For create actions, check feature limits
    IF v_action = 'create' THEN
        v_within_limit := check_feature_limit(p_organization_id, p_permission_code);
        
        IF NOT v_within_limit THEN
            RETURN jsonb_build_object(
                'allowed', false,
                'reason', 'limit_exceeded',
                'message', 'Organization has reached the limit for: ' || v_feature_name
            );
        END IF;
    END IF;
    
    -- Check if the plan includes feature access (for special features)
    IF v_feature_name IN ('advanced_reporting', 'risk_management', 'audit_trails', 'custom_templates') THEN
        v_feature_access := check_feature_access(p_organization_id, v_feature_name);
        
        IF NOT v_feature_access THEN
            RETURN jsonb_build_object(
                'allowed', false,
                'reason', 'feature_not_available',
                'message', 'Current subscription does not include access to: ' || v_feature_name
            );
        END IF;
    END IF;
    
    -- All checks passed, permission granted
    RETURN jsonb_build_object(
        'allowed', true,
        'reason', 'permission_granted',
        'message', 'User has permission to: ' || p_permission_code
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION unified_permission_check IS 'Checks permission, feature limits, and feature access in one call';
```

## Usage Tracking Functions and Triggers

### 1. Track Feature Usage Function

```sql
CREATE OR REPLACE FUNCTION track_feature_usage(
    p_organization_id UUID,
    p_user_id UUID,
    p_feature_code TEXT,
    p_entity_id UUID,
    p_entity_type TEXT
) RETURNS VOID AS $$
DECLARE
    v_subscription_id UUID;
    v_existing_record BOOLEAN;
BEGIN
    -- Set context for RLS policies
    PERFORM set_config('app.current_organization_id', p_organization_id::TEXT, true);
    PERFORM set_config('app.current_user_id', p_user_id::TEXT, true);
    
    -- Get active subscription
    SELECT sub_id INTO v_subscription_id
    FROM org_subscriptions
    WHERE sub_org_id = p_organization_id
    AND sub_status = 'active'
    LIMIT 1;
    
    -- Skip if no active subscription
    IF v_subscription_id IS NULL THEN
        RETURN;
    END IF;
    
    -- Check if we have an existing record
    SELECT EXISTS (
        SELECT 1 
        FROM feature_usage
        WHERE usage_org_id = p_organization_id
        AND usage_sub_id = v_subscription_id
        AND usage_feature_code = p_feature_code
    ) INTO v_existing_record;
    
    -- Update or insert usage record
    IF v_existing_record THEN
        UPDATE feature_usage
        SET usage_count = usage_count + 1,
            usage_last = NOW(),
            updated_at = NOW()
        WHERE usage_org_id = p_organization_id
        AND usage_sub_id = v_subscription_id
        AND usage_feature_code = p_feature_code;
    ELSE
        INSERT INTO feature_usage (
            usage_org_id,
            usage_sub_id,
            usage_user_id,
            usage_feature_code,
            usage_details
        ) VALUES (
            p_organization_id,
            v_subscription_id,
            p_user_id,
            p_feature_code,
            jsonb_build_object(
                'entity_id', p_entity_id,
                'entity_type', p_entity_type,
                'timestamp', NOW()
            )
        );
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION track_feature_usage IS 'Records feature usage for analytics and limit tracking';
```

### 2. Feature Usage Trigger Functions

These trigger functions automatically track feature usage when entities are created, updated, or deleted.

```sql
-- Example for policies table
CREATE OR REPLACE FUNCTION policy_usage_trigger()
RETURNS TRIGGER AS $$
DECLARE
    v_feature_code TEXT;
    v_organization_id UUID;
    v_user_id UUID;
BEGIN
    -- Determine feature code based on operation
    IF TG_OP = 'INSERT' THEN
        v_feature_code := 'create:policy';
    ELSIF TG_OP = 'UPDATE' THEN
        v_feature_code := 'update:policy';
    ELSIF TG_OP = 'DELETE' THEN
        v_feature_code := 'delete:policy';
    END IF;
    
    -- Get context from settings or NEW/OLD record
    v_organization_id := COALESCE(
        current_setting('app.current_organization_id', true)::UUID,
        CASE WHEN TG_OP = 'DELETE' THEN OLD.organization_id ELSE NEW.organization_id END
    );
    
    v_user_id := COALESCE(
        current_setting('app.current_user_id', true)::UUID,
        CASE WHEN TG_OP = 'DELETE' THEN OLD.updated_by ELSE NEW.updated_by END
    );
    
    -- Track the feature usage
    PERFORM track_feature_usage(
        v_organization_id,
        v_user_id,
        v_feature_code,
        CASE WHEN TG_OP = 'DELETE' THEN OLD.policy_id ELSE NEW.policy_id END,
        'policy'
    );
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION policy_usage_trigger IS 'Tracks policy creation, updates, and deletions';

-- Apply trigger to policies table
CREATE TRIGGER track_policy_usage
AFTER INSERT OR UPDATE OR DELETE ON policies
FOR EACH ROW EXECUTE FUNCTION policy_usage_trigger();

-- Similar triggers would be created for other entity tables (controls, risks, documents, etc.)
```

## Subscription Management Functions

### 1. Calculate Subscription End Date

```sql
CREATE OR REPLACE FUNCTION calculate_subscription_end_date(
    p_plan_id UUID,
    p_start_date TIMESTAMPTZ
) RETURNS TIMESTAMPTZ AS $$
DECLARE
    v_end_date TIMESTAMPTZ;
    v_duration_days INTEGER;
BEGIN
    -- Get plan duration days
    SELECT plan_duration_days INTO v_duration_days
    FROM sub_plans
    WHERE plan_id = p_plan_id;
    
    -- Calculate end date if duration is set, otherwise return NULL (for auto-renewing)
    IF v_duration_days IS NOT NULL THEN
        v_end_date := p_start_date + (v_duration_days || ' days')::INTERVAL;
    ELSE
        v_end_date := NULL;
    END IF;
    
    RETURN v_end_date;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION calculate_subscription_end_date IS 'Calculates subscription end date based on plan duration';
```

### 2. Assign Default Roles from Plan

```sql
CREATE OR REPLACE FUNCTION assign_default_roles_from_plan(
    p_organization_id UUID,
    p_plan_id UUID
) RETURNS VOID AS $$
DECLARE
    v_role_id UUID;
    v_admin_user_id UUID;
BEGIN
    -- Set context for RLS policies
    PERFORM set_config('app.current_organization_id', p_organization_id::TEXT, true);
    
    -- Find the admin user for the organization
    SELECT user_id INTO v_admin_user_id
    FROM users
    WHERE organization_id = p_organization_id
    AND is_admin = true
    LIMIT 1;
    
    -- Assign each role associated with the plan to the admin user
    FOR v_role_id IN (
        SELECT role_id
        FROM plan_roles
        WHERE plan_id = p_plan_id
    ) LOOP
        -- Skip if already assigned
        CONTINUE WHEN EXISTS (
            SELECT 1
            FROM user_roles
            WHERE user_id = v_admin_user_id
            AND role_id = v_role_id
        );
        
        -- Assign role
        INSERT INTO user_roles (
            user_id,
            role_id,
            assigned_by,
            assigned_at
        ) VALUES (
            v_admin_user_id,
            v_role_id,
            v_admin_user_id,  -- Self-assigned for initial setup
            NOW()
        );
    END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION assign_default_roles_from_plan IS 'Assigns default roles from a plan to the organization admin';
```

### 3. Handle Subscription Expiration

```sql
CREATE OR REPLACE FUNCTION handle_subscription_expiration()
RETURNS VOID AS $$
BEGIN
    -- Update expired subscriptions
    UPDATE org_subscriptions
    SET sub_status = 'expired',
        updated_at = NOW()
    WHERE sub_status = 'active'
    AND sub_end_date IS NOT NULL
    AND sub_end_date < NOW();
    
    -- Record transitions for audit purposes
    INSERT INTO sub_transitions (
        transition_org_id,
        transition_from_sub_id,
        transition_to_sub_id,
        transition_from_plan_id,
        transition_to_plan_id,
        transition_reason,
        transition_source,
        transition_notes
    )
    SELECT 
        sub_org_id,
        sub_id,
        NULL,  -- No new subscription
        sub_plan_id,
        NULL,  -- No new plan
        'Subscription expired',
        'system',
        'Automatic expiration due to end date reached'
    FROM org_subscriptions
    WHERE sub_status = 'expired'
    AND updated_at >= NOW() - INTERVAL '5 minutes';  -- Only recent expirations
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION handle_subscription_expiration IS 'Updates subscription status when they expire';
```

### 4. Create Subscription Transition Record

```sql
CREATE OR REPLACE FUNCTION create_subscription_transition(
    p_organization_id UUID,
    p_user_id UUID,
    p_from_sub_id UUID,
    p_to_sub_id UUID,
    p_reason TEXT,
    p_source TEXT,
    p_notes TEXT,
    p_preserved_data TEXT[]
) RETURNS UUID AS $$
DECLARE
    v_from_plan_id UUID;
    v_to_plan_id UUID;
    v_transition_id UUID;
BEGIN
    -- Set context for RLS policies
    PERFORM set_config('app.current_organization_id', p_organization_id::TEXT, true);
    PERFORM set_config('app.current_user_id', p_user_id::TEXT, true);
    
    -- Get plan IDs
    IF p_from_sub_id IS NOT NULL THEN
        SELECT sub_plan_id INTO v_from_plan_id
        FROM org_subscriptions
        WHERE sub_id = p_from_sub_id;
    END IF;
    
    IF p_to_sub_id IS NOT NULL THEN
        SELECT sub_plan_id INTO v_to_plan_id
        FROM org_subscriptions
        WHERE sub_id = p_to_sub_id;
    END IF;
    
    -- Create transition record
    INSERT INTO sub_transitions (
        transition_org_id,
        transition_user_id,
        transition_from_sub_id,
        transition_to_sub_id,
        transition_from_plan_id,
        transition_to_plan_id,
        transition_reason,
        transition_source,
        transition_notes,
        transition_data,
        created_by
    ) VALUES (
        p_organization_id,
        p_user_id,
        p_from_sub_id,
        p_to_sub_id,
        v_from_plan_id,
        v_to_plan_id,
        p_reason,
        p_source,
        p_notes,
        jsonb_build_object(
            'preserved_data', p_preserved_data,
            'transition_metadata', jsonb_build_object(
                'initiated_by', p_user_id::TEXT,
                'timestamp', NOW()
            )
        ),
        p_user_id
    )
    RETURNING transition_id INTO v_transition_id;
    
    RETURN v_transition_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION create_subscription_transition IS 'Creates a record of a subscription transition';
```

## Scheduled Tasks and Triggers

### 1. Subscription Expiration Check Trigger

```sql
-- Create a function that will be called by a scheduled job
CREATE OR REPLACE FUNCTION check_subscription_expirations()
RETURNS VOID AS $$
BEGIN
    -- Call the handler function
    PERFORM handle_subscription_expiration();
END;
$$ LANGUAGE plpgsql;

-- Example of how to schedule this using pg_cron (if available):
-- SELECT cron.schedule('0 0 * * *', 'SELECT check_subscription_expirations()');

COMMENT ON FUNCTION check_subscription_expirations IS 'Scheduled function to check for expired subscriptions';
```

### 2. Subscription Status Change Trigger

```sql
CREATE OR REPLACE FUNCTION subscription_status_change_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Only proceed if status changed
    IF OLD.sub_status = NEW.sub_status THEN
        RETURN NEW;
    END IF;
    
    -- Handle specific status transitions
    IF NEW.sub_status = 'canceled' THEN
        -- Set canceled_at if not already set
        IF NEW.sub_canceled_at IS NULL THEN
            NEW.sub_canceled_at := NOW();
        END IF;
        
        -- Set canceled_by if not already set
        IF NEW.sub_canceled_by IS NULL THEN
            NEW.sub_canceled_by := current_setting('app.current_user_id', true)::UUID;
        END IF;
    END IF;
    
    -- Always update updated_at
    NEW.updated_at := NOW();
    
    -- Record the transition if this is a significant status change
    IF OLD.sub_status IN ('active', 'trial') AND NEW.sub_status IN ('expired', 'canceled', 'replaced') THEN
        PERFORM create_subscription_transition(
            NEW.sub_org_id,
            COALESCE(NEW.sub_canceled_by, current_setting('app.current_user_id', true)::UUID),
            NEW.sub_id,
            NULL,  -- No new subscription
            CASE 
                WHEN NEW.sub_status = 'expired' THEN 'Subscription expired'
                WHEN NEW.sub_status = 'canceled' THEN 'Subscription canceled'
                WHEN NEW.sub_status = 'replaced' THEN 'Subscription replaced'
                ELSE 'Status changed'
            END,
            CASE 
                WHEN NEW.sub_status = 'expired' THEN 'system'
                ELSE 'user_initiated'
            END,
            NEW.sub_cancel_reason,
            ARRAY[]::TEXT[]
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to org_subscriptions table
CREATE TRIGGER subscription_status_change
BEFORE UPDATE OF sub_status ON org_subscriptions
FOR EACH ROW EXECUTE FUNCTION subscription_status_change_trigger();

COMMENT ON FUNCTION subscription_status_change_trigger IS 'Handles subscription status changes';
```

### 3. Prevent Multiple Active Subscriptions Trigger

```sql
CREATE OR REPLACE FUNCTION prevent_multiple_active_subscriptions()
RETURNS TRIGGER AS $$
DECLARE
    v_active_count INTEGER;
BEGIN
    -- Only check for new active subscriptions
    IF NEW.sub_status != 'active' THEN
        RETURN NEW;
    END IF;
    
    -- Count existing active subscriptions for this organization
    SELECT COUNT(*) INTO v_active_count
    FROM org_subscriptions
    WHERE sub_org_id = NEW.sub_org_id
    AND sub_status = 'active'
    AND sub_id != NEW.sub_id;  -- Exclude this one if it's an update
    
    -- If there's already an active subscription, update it to 'replaced'
    IF v_active_count > 0 THEN
        UPDATE org_subscriptions
        SET sub_status = 'replaced',
            updated_at = NOW()
        WHERE sub_org_id = NEW.sub_org_id
        AND sub_status = 'active'
        AND sub_id != NEW.sub_id;
        
        -- Create transition records
        PERFORM create_subscription_transition(
            NEW.sub_org_id,
            COALESCE(current_setting('app.current_user_id', true)::UUID, NEW.created_by),
            (SELECT sub_id FROM org_subscriptions WHERE sub_org_id = NEW.sub_org_id AND sub_status = 'replaced' LIMIT 1),
            NEW.sub_id,
            'Subscription replaced by new plan',
            'system',
            'Automatic replacement due to new subscription activation',
            ARRAY['policies', 'controls', 'risks', 'documents']::TEXT[]
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to org_subscriptions table
CREATE TRIGGER prevent_multiple_active_subscriptions
BEFORE INSERT OR UPDATE OF sub_status ON org_subscriptions
FOR EACH ROW EXECUTE FUNCTION prevent_multiple_active_subscriptions();

COMMENT ON FUNCTION prevent_multiple_active_subscriptions IS 'Ensures only one active subscription per organization';
```

## Stripe Integration Functions

These functions provide integration with the Stripe billing system as mentioned in the master-tracker.md document.

### 1. Create Stripe Customer Function

```sql
CREATE OR REPLACE FUNCTION create_stripe_customer(
    p_organization_id UUID,
    p_email TEXT,
    p_name TEXT
) RETURNS TEXT AS $$
DECLARE
    v_stripe_customer_id TEXT;
BEGIN
    -- In a real implementation, this would make an API call to Stripe
    -- For now, we'll just simulate it with a placeholder
    v_stripe_customer_id := 'cus_' || REPLACE(p_organization_id::TEXT, '-', '');
    
    -- Update the organization with the Stripe customer ID
    UPDATE organizations
    SET 
        stripe_customer_id = v_stripe_customer_id,
        updated_at = NOW()
    WHERE org_id = p_organization_id;
    
    RETURN v_stripe_customer_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION create_stripe_customer IS 'Creates a Stripe customer record for an organization';
```

### 2. Create Stripe Subscription Function

```sql
CREATE OR REPLACE FUNCTION create_stripe_subscription(
    p_organization_id UUID,
    p_plan_id UUID,
    p_user_id UUID
) RETURNS TEXT AS $$
DECLARE
    v_stripe_customer_id TEXT;
    v_stripe_subscription_id TEXT;
    v_plan_code TEXT;
    v_plan_billing_cycle TEXT;
    v_new_sub_id UUID;
BEGIN
    -- Set context for RLS policies
    PERFORM set_config('app.current_organization_id', p_organization_id::TEXT, true);
    PERFORM set_config('app.current_user_id', p_user_id::TEXT, true);
    
    -- Get the Stripe customer ID
    SELECT stripe_customer_id INTO v_stripe_customer_id
    FROM organizations
    WHERE org_id = p_organization_id;
    
    -- Create customer if not exists
    IF v_stripe_customer_id IS NULL THEN
        SELECT email, name INTO v_email, v_name
        FROM organizations
        WHERE org_id = p_organization_id;
        
        SELECT create_stripe_customer(p_organization_id, v_email, v_name)
        INTO v_stripe_customer_id;
    END IF;
    
    -- Get plan details
    SELECT plan_code, plan_billing_cycle
    INTO v_plan_code, v_plan_billing_cycle
    FROM sub_plans
    WHERE plan_id = p_plan_id;
    
    -- In a real implementation, this would make an API call to Stripe
    -- For now, we'll just simulate it with a placeholder
    v_stripe_subscription_id := 'sub_' || REPLACE(uuid_generate_v4()::TEXT, '-', '');
    
    -- Create the subscription record
    INSERT INTO org_subscriptions (
        sub_org_id,
        sub_plan_id,
        sub_status,
        sub_start_date,
        sub_end_date,
        sub_auto_renew,
        sub_billing_reference,
        created_by
    ) VALUES (
        p_organization_id,
        p_plan_id,
        'active',
        NOW(),
        calculate_subscription_end_date(p_plan_id, NOW()),
        v_plan_billing_cycle != 'one_time',
        v_stripe_subscription_id,
        p_user_id
    )
    RETURNING sub_id INTO v_new_sub_id;
    
    -- Assign default roles
    PERFORM assign_default_roles_from_plan(p_organization_id, p_plan_id);
    
    RETURN v_stripe_subscription_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION create_stripe_subscription IS 'Creates a Stripe subscription and local subscription record';
```

## Summary

This document defines the essential database functions and triggers for the ArionComply subscription management system. These functions and triggers handle:

1. **Permission Checking**: Functions to check if a user has permission, if an organization is within feature limits, and if their plan includes access to specific features
2. **Usage Tracking**: Automatic tracking of feature usage for analytics and limit enforcement
3. **Subscription Management**: Functions to handle subscription creation, status changes, and expiration
4. **Stripe Integration**: Functions to integrate with the Stripe billing system

## Next Steps

After implementing these database functions and triggers, the next steps are:

1. Implement the metadata-driven API as described in `arioncomply-v1/docs/ApplicationDatabase/Unified-Subscription-Management-Metadata-Implementation.md`
2. Create the edge function router and handlers as described in `arioncomply-v1/docs/API/Subscription-Management-Edge-Functions.md`
3. Develop the React UI components as described in `arioncomply-v1/docs/Frontend/Subscription-Management-React-Components.md`
4. Define the subscription workflows as described in `arioncomply-v1/docs/Workflows/Subscription-Management-Workflows.md`

## Implementation Notes for Junior Developers

1. **Function Dependencies**: Implement the functions in order, as later functions may depend on earlier ones
2. **Context Setting**: Always set the app.current_organization_id and app.current_user_id context variables for RLS policies
3. **Error Handling**: Add appropriate error handling in production code
4. **Testing**: Test each function individually before implementing triggers
5. **Documentation**: Add detailed comments to explain complex logic
