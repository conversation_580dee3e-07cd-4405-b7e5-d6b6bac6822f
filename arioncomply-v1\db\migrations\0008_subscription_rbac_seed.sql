-- File: arioncomply-v1/db/migrations/0008_subscription_rbac_seed.sql
-- Migration 0008: Subscription/RBAC alignment and seed data
-- Purpose: Add missing plan_roles + feature_usage tables (if not present) and seed
--          standard permissions, roles, plans, and mappings for a fresh instance.

BEGIN;

-- Add plan_roles if not present (map plans to roles)
CREATE TABLE IF NOT EXISTS plan_roles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  plan_id uuid NOT NULL REFERENCES sub_plans(plan_id) ON DELETE CASCADE,
  role_id uuid NOT NULL REFERENCES roles(role_id) ON DELETE CASCADE,
  UNIQUE (plan_id, role_id)
);

CREATE INDEX IF NOT EXISTS idx_plan_roles_plan ON plan_roles (plan_id);
CREATE INDEX IF NOT EXISTS idx_plan_roles_role ON plan_roles (role_id);

ALTER TABLE plan_roles ENABLE ROW LEVEL SECURITY;
CREATE POLICY plan_roles_select ON plan_roles FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM sub_plans sp WHERE sp.plan_id = plan_roles.plan_id AND (
      sp.organization_id IS NULL OR sp.organization_id = app_current_org_id() OR app_has_role('admin')
    )
  )
);
CREATE POLICY plan_roles_cud ON plan_roles FOR ALL USING (
  app_has_role('admin') OR EXISTS (
    SELECT 1 FROM sub_plans sp WHERE sp.plan_id = plan_roles.plan_id AND (
      sp.organization_id = app_current_org_id()
    )
  )
) WITH CHECK (
  app_has_role('admin') OR EXISTS (
    SELECT 1 FROM sub_plans sp WHERE sp.plan_id = plan_roles.plan_id AND (
      sp.organization_id = app_current_org_id()
    )
  )
);

-- Add feature_usage if not present (basic usage metering)
CREATE TABLE IF NOT EXISTS feature_usage (
  usage_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  usage_org_id uuid NOT NULL REFERENCES organizations(org_id) ON DELETE CASCADE,
  usage_sub_id uuid NOT NULL REFERENCES org_subscriptions(sub_id) ON DELETE CASCADE,
  usage_user_id uuid,
  usage_feature_code text NOT NULL,
  usage_count integer DEFAULT 1,
  usage_first timestamptz NOT NULL DEFAULT now(),
  usage_last timestamptz NOT NULL DEFAULT now(),
  usage_details jsonb,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now(),
  CONSTRAINT chk_feature_usage_details CHECK (usage_details IS NULL OR jsonb_typeof(usage_details) = 'object')
);

CREATE INDEX IF NOT EXISTS idx_feature_usage_org_feature ON feature_usage(usage_org_id, usage_feature_code);
CREATE INDEX IF NOT EXISTS idx_feature_usage_sub ON feature_usage(usage_sub_id);
CREATE INDEX IF NOT EXISTS idx_feature_usage_user ON feature_usage(usage_user_id);

ALTER TABLE feature_usage ENABLE ROW LEVEL SECURITY;
CREATE POLICY feature_usage_rw ON feature_usage
  FOR ALL USING (usage_org_id = app_current_org_id() OR app_has_role('admin'))
  WITH CHECK (usage_org_id = app_current_org_id() OR app_has_role('admin'));

CREATE TRIGGER feature_usage_set_updated_at
  BEFORE UPDATE ON feature_usage
  FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

-- Seed permissions if table is empty (idempotent-ish via NOT EXISTS)
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM permissions) THEN
    INSERT INTO permissions (permission_code, permission_description, feature_area) VALUES
      ('view:dashboard', 'View dashboard', 'dashboard'),
      ('create:policy', 'Create policy', 'policies'),
      ('view:policy', 'View policy', 'policies'),
      ('update:policy', 'Update policy', 'policies'),
      ('delete:policy', 'Delete policy', 'policies'),
      ('create:control', 'Create control', 'controls'),
      ('view:control', 'View control', 'controls'),
      ('update:control', 'Update control', 'controls'),
      ('delete:control', 'Delete control', 'controls'),
      ('create:risk', 'Create risk', 'risks'),
      ('view:risk', 'View risk', 'risks'),
      ('update:risk', 'Update risk', 'risks'),
      ('delete:risk', 'Delete risk', 'risks'),
      ('export:report', 'Export report', 'reports'),
      ('view:report', 'View report', 'reports'),
      ('create:document', 'Create document', 'documents'),
      ('view:document', 'View document', 'documents'),
      ('update:document', 'Update document', 'documents'),
      ('delete:document', 'Delete document', 'documents'),
      ('manage:users', 'Manage users', 'administration'),
      ('manage:roles', 'Manage roles', 'administration'),
      ('manage:subscriptions', 'Manage subscriptions', 'administration'),
      ('view:analytics', 'View analytics', 'analytics');
  END IF;
END $$;

-- Seed system roles (fixed UUIDs for portability) if missing
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM roles WHERE role_is_system) THEN
    INSERT INTO roles (role_id, role_name, role_description, role_is_system)
    VALUES
      ('11111111-1111-1111-1111-111111111111', 'Admin', 'Full administrative access', true),
      ('*************-2222-2222-************', 'Demo User', 'Limited access for demo users', true),
      ('*************-3333-3333-************', 'Basic User', 'Standard user access', true),
      ('*************-4444-4444-************', 'Auditor', 'Read-only access for auditing', true);
  END IF;
END $$;

-- Admin: grant all permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT '11111111-1111-1111-1111-111111111111', p.permission_id
FROM permissions p
ON CONFLICT DO NOTHING;

-- Demo User: limited set
INSERT INTO role_permissions (role_id, permission_id)
SELECT '*************-2222-2222-************', p.permission_id
FROM permissions p
WHERE p.permission_code IN (
  'view:dashboard','view:policy','create:policy','view:control','create:control','view:risk','view:document','create:document','view:report'
)
ON CONFLICT DO NOTHING;

-- Basic User: anything not manage:* (no admin)
INSERT INTO role_permissions (role_id, permission_id)
SELECT '*************-3333-3333-************', p.permission_id
FROM permissions p
WHERE p.permission_code NOT LIKE 'manage:%'
ON CONFLICT DO NOTHING;

-- Auditor: read-only (view:*)
INSERT INTO role_permissions (role_id, permission_id)
SELECT '*************-4444-4444-************', p.permission_id
FROM permissions p
WHERE p.permission_code LIKE 'view:%'
ON CONFLICT DO NOTHING;

-- Seed plans (system-wide) if not present
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM sub_plans) THEN
    INSERT INTO sub_plans (
      plan_id, plan_code, plan_name, plan_description, plan_type,
      plan_is_active, plan_is_public, plan_duration_days, plan_billing_cycle,
      plan_price_amount, plan_price_currency, plan_user_limit, plan_storage_limit_gb, plan_configuration
    ) VALUES
      ('55555555-5555-5555-5555-555555555555','demo-30','30-Day Demo','Demo with limited records','demo',true,true,30,'one_time',0.00,'USD',5,10,
       '{"feature_limits": {"policies": 10, "controls": 25, "risks": 15, "documents": 20}, "feature_access": {"advanced_reporting": false, "risk_management": true, "audit_trails": false, "custom_templates": false}}'),
      ('66666666-6666-6666-6666-666666666666','basic-monthly','Basic Plan - Monthly','Essential compliance management','basic',true,true,NULL,'monthly',99.99,'USD',10,50,
       '{"feature_limits": {"policies": 50, "controls": 100, "risks": 75, "documents": 100}, "feature_access": {"advanced_reporting": false, "risk_management": true, "audit_trails": true, "custom_templates": false}}'),
      ('77777777-7777-7777-7777-777777777777','professional-monthly','Professional Plan - Monthly','Advanced compliance management','professional',true,true,NULL,'monthly',199.99,'USD',25,100,
       '{"feature_limits": {"policies": 100, "controls": 250, "risks": 150, "documents": 250}, "feature_access": {"advanced_reporting": true, "risk_management": true, "audit_trails": true, "custom_templates": true}}'),
      ('88888888-8888-8888-8888-888888888888','enterprise-annual','Enterprise Plan - Annual','Comprehensive for large orgs','enterprise',true,true,NULL,'annual',4999.99,'USD',NULL,500,
       '{"feature_limits": null, "feature_access": {"advanced_reporting": true, "risk_management": true, "audit_trails": true, "custom_templates": true, "custom_integrations": true}}');
  END IF;
END $$;

-- Map plans to roles
INSERT INTO plan_roles (plan_id, role_id) VALUES
  ('55555555-5555-5555-5555-555555555555','*************-2222-2222-************')
ON CONFLICT DO NOTHING;

INSERT INTO plan_roles (plan_id, role_id) VALUES
  ('66666666-6666-6666-6666-666666666666','*************-3333-3333-************')
ON CONFLICT DO NOTHING;

INSERT INTO plan_roles (plan_id, role_id) VALUES
  ('77777777-7777-7777-7777-777777777777','*************-3333-3333-************'),
  ('77777777-7777-7777-7777-777777777777','*************-4444-4444-************')
ON CONFLICT DO NOTHING;

INSERT INTO plan_roles (plan_id, role_id) VALUES
  ('88888888-8888-8888-8888-888888888888','*************-3333-3333-************'),
  ('88888888-8888-8888-8888-888888888888','*************-4444-4444-************'),
  ('88888888-8888-8888-8888-888888888888','11111111-1111-1111-1111-111111111111')
ON CONFLICT DO NOTHING;

COMMIT;
-- File: arioncomply-v1/db/migrations/0008_subscription_rbac_seed.sql
