// File: arioncomply-v1/supabase/functions/_shared/trace.ts
// File Description: W3C Trace Context helpers for Edge
// Purpose: Parse and generate traceparent header values and IDs

export type TraceParts = {
  traceId: string;
  spanId: string;
  parentSpanId?: string | null;
  flags?: string | null;
  header: string;
};

function isHex(s: string, len: number) {
  return s.length === len && /^[0-9a-f]+$/.test(s);
}

/** Parse a W3C traceparent header into trace parts; returns null if invalid. */
export function parseTraceparent(header: string | null | undefined): TraceParts | null {
  if (!header) return null;
  // Format: version-traceId-spanId-flags (parent not in header per spec; we track previous span as parentSpanId when generating)
  const parts = header.trim().split("-");
  if (parts.length < 4) return null;
  const [_ver, traceId, spanId, flags] = parts;
  if (!isHex(traceId, 32) || !isHex(spanId, 16) || !isHex(flags, 2)) return null;
  return { traceId, spanId, flags, header };
}

function randomHex(bytes: number) {
  const buf = new Uint8Array(bytes);
  crypto.getRandomValues(buf);
  return Array.from(buf).map((b) => b.toString(16).padStart(2, "0")).join("");
}

/** Generate a new trace context, optionally continuing a parent span. */
export function generateTraceContext(parent?: TraceParts | null): TraceParts {
  const traceId = parent?.traceId ?? randomHex(16); // 16 bytes = 32 hex
  const spanId = randomHex(8); // 8 bytes = 16 hex
  const flags = parent?.flags ?? "01"; // sampled
  const header = `00-${traceId}-${spanId}-${flags}`;
  return { traceId, spanId, parentSpanId: parent?.spanId ?? null, flags, header };
}
