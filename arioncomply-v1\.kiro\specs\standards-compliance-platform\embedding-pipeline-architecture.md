File: arioncomply-v1/.kiro/specs/standards-compliance-platform/embedding-pipeline-architecture.md
# Dual-Vector Multi-Pipeline Embedding Architecture

**Design Document**: ArionComply Standards Compliance Platform  
**Section**: AI Backend Architecture  
**Version**: 2.0 - Dual-Vector Multi-Pipeline Architecture  
**Date**: 2025-09-13

---

## Dual-Vector Multi-Pipeline Embedding Architecture

### Overview

The ArionComply platform implements a **dual-vector multi-pipeline embedding architecture** that provides flexible, scalable, and secure text-to-vector conversion capabilities for both public shared knowledge and private organizational data. This architecture supports multiple embedding models with runtime selection, fallback mechanisms, and different optimization strategies for various deployment scenarios while maintaining strict data separation.

### Design Principles

1. **Security-First**: Local-first approach with optional cloud services for enhanced quality
2. **Data Separation**: Clear distinction between public shared knowledge and private organizational data
3. **Quality Flexibility**: Multiple quality tiers to balance performance, cost, and accuracy
4. **CPU Optimization**: Quantized models optimized for CPU-only deployment scenarios
5. **Runtime Selection**: Dynamic pipeline selection based on availability and requirements
6. **Fallback Resilience**: Automatic fallback to ensure system availability
7. **Multi-Dimensional Support**: Handle different embedding dimensions seamlessly
8. **Dual-Vector Integration**: Seamless orchestration between ChromaDB and Supabase Vector stores

### Dual-Vector Architecture Overview

```mermaid
graph TB
    subgraph "Embedding Pipeline Registry"
        REGISTRY[Pipeline Registry]
        SELECTOR[Pipeline Selector]
        HEALTH[Health Monitor]
        CLASSIFIER[Data Classifier<br/>Public vs Private]
    end
    
    subgraph "Primary Pipelines"
        BGE[BGE-Large-EN-v1.5 + ONNX<br/>1024-dim, SOTA Quality]
        MPNET[all-mpnet-base-v2<br/>768-dim, Good Quality]
    end
    
    subgraph "Optional Pipelines"  
        OPENAI[OpenAI Embeddings<br/>1536/3072-dim, High Quality]
        PLACEHOLDER[Placeholder<br/>768-dim, Testing Only]
    end
    
    subgraph "Dual Vector Storage"
        CHROMA_PUB[(ChromaDB<br/>📚 Public Shared Knowledge<br/>Standards, Regulations, Assessments)]
        SUPABASE_PRIV[(Supabase Vector<br/>🏢 Private Organizational Data<br/>Company Policies, Results)]
    end
    
    subgraph "Hybrid Search Orchestration"
        PARALLEL[Parallel Query Engine]
        FUSION[Result Fusion & Scoring]
        CONFIDENCE[Confidence Evaluation]
    end
    
    REGISTRY --> SELECTOR
    REGISTRY --> CLASSIFIER
    
    SELECTOR --> BGE
    SELECTOR --> MPNET
    SELECTOR --> OPENAI
    SELECTOR --> PLACEHOLDER
    
    CLASSIFIER -->|Public Data| CHROMA_PUB
    CLASSIFIER -->|Private Data| SUPABASE_PRIV
    
    BGE --> CHROMA_PUB
    BGE --> SUPABASE_PRIV
    MPNET --> CHROMA_PUB
    MPNET --> SUPABASE_PRIV
    OPENAI --> SUPABASE_PRIV
    PLACEHOLDER --> CHROMA_PUB
    
    CHROMA_PUB --> PARALLEL
    SUPABASE_PRIV --> PARALLEL
    
    PARALLEL --> FUSION
    FUSION --> CONFIDENCE
```

---

## Pipeline Implementations

### 1. BGE-Large-EN-v1.5 + ONNX Pipeline (Primary)

**Purpose**: State-of-the-art embedding quality with CPU optimization  
**Model**: BAAI/bge-large-en-v1.5 with ONNX Runtime quantization  
**Dimension**: 1024  
**Quality Tier**: SOTA (State-of-the-Art)

#### Technical Specifications
```yaml
Model: "BAAI/bge-large-en-v1.5"
Quantization: "INT8 via ONNX Runtime"
Memory: "~350MB (quantized from ~1.3GB)"
Inference Time: "25-30ms per batch"
Context Length: "512 tokens"
Normalization: "L2 normalized, CLS pooling"
```

#### Implementation Details
- **Optimization Framework**: Hugging Face Optimum + ONNX Runtime
- **Quantization**: Automatic INT8 quantization during model conversion
- **CPU Targeting**: CPUExecutionProvider for maximum compatibility
- **Memory Efficiency**: 75% memory reduction vs unquantized model
- **Quality Retention**: <2% quality degradation from quantization

#### Use Cases
- **Production deployments** requiring high-quality embeddings
- **Compliance-specific content** needing domain understanding
- **CPU-only environments** without GPU acceleration
- **Multi-tenant scenarios** with resource constraints

### 2. all-mpnet-base-v2 Pipeline (Secondary)

**Purpose**: Reliable local embeddings with good quality/performance balance  
**Model**: sentence-transformers/all-mpnet-base-v2  
**Dimension**: 768  
**Quality Tier**: Good

#### Technical Specifications
```yaml
Model: "sentence-transformers/all-mpnet-base-v2"
Memory: "~400MB"
Inference Time: "50-100ms per batch"
Context Length: "384 tokens"
Normalization: "Mean pooling + L2 normalization"
```

#### Implementation Details
- **Framework**: sentence-transformers library
- **Device Targeting**: CPU with configurable device selection
- **Caching**: Model caching for faster subsequent loads
- **Batch Processing**: Optimized for batch embedding operations

#### Use Cases
- **Development environments** needing reliable embeddings
- **Fallback scenarios** when BGE-ONNX unavailable
- **Resource-constrained** environments
- **CI/CD pipelines** requiring stable dependencies

### 3. OpenAI Embeddings Pipeline (Optional)

**Purpose**: Cloud-based high-quality embeddings with security considerations  
**Models**: text-embedding-3-small (1536-dim), text-embedding-3-large (3072-dim)  
**Quality Tier**: High  
**Security Status**: ⚠️ Data Exposure Risk

#### Technical Specifications
```yaml
Models:
  - "text-embedding-3-small": 1536-dim, $0.02/1M tokens
  - "text-embedding-3-large": 3072-dim, $0.13/1M tokens
API: "OpenAI Embeddings v1"
Context Length: "8192 tokens"
Rate Limits: "3000 RPM / 1M TPM (tier dependent)"
```

#### Security Considerations
- **Data Exposure**: All text sent to OpenAI servers
- **GDPR Compliance**: May violate data residency requirements
- **Enterprise Restrictions**: Many clients prohibit cloud AI services
- **Audit Requirements**: External data processing complicates compliance

#### Implementation Details
- **Client Library**: Official OpenAI Python client
- **Error Handling**: Comprehensive retry logic with exponential backoff
- **Rate Limiting**: Built-in rate limit handling
- **Configuration**: Environment-based API key management

#### Use Cases
- **High-quality requirements** where security permits
- **Clients explicitly allowing** cloud AI services
- **Quality benchmarking** against local models
- **Research and development** scenarios

### 4. Placeholder Pipeline (Testing/Fallback)

**Purpose**: ⚠️ Testing and system fallback only - NO SEMANTIC UNDERSTANDING  
**Model**: Deterministic character hashing  
**Dimension**: 768  
**Quality Tier**: Testing Only

#### Technical Specifications
```yaml
Algorithm: "Character ASCII value hashing"
Memory: "0MB (no model loading)"
Inference Time: "1-2ms per batch"
Deterministic: "Same input always produces same output"
```

#### Implementation Details
- **Character Mapping**: ASCII values converted to normalized floats
- **Hash Distribution**: Multiple hash functions for better vector distribution
- **L2 Normalization**: Standard vector normalization for consistency
- **No Dependencies**: Pure Python implementation

#### ⚠️ Limitations
- **No Semantic Understanding**: "backup" and "recovery" get unrelated vectors
- **Poor Search Quality**: Results based on character similarity, not meaning
- **Testing Only**: Never use for production user queries

#### Use Cases
- **Pipeline development** without model dependencies
- **CI/CD environments** with fast startup requirements
- **System fallback** when all real models fail to load
- **Performance baseline** measurement
- **LLM isolation testing** with poor context

---

## Pipeline Selection and Management

### Selection Strategies

#### 1. Quality-First Strategy (Default)
```yaml
Priority Order:
  1. "bge-large-onnx"      # Best quality + CPU optimized
  2. "all-mpnet-base-v2"   # Good local fallback
  3. "placeholder"         # System availability fallback
  
Exclusions:
  - "openai"               # Disabled for security by default
```

#### 2. Security-First Strategy
```yaml
Priority Order:
  1. "bge-large-onnx"      # Local, high quality
  2. "all-mpnet-base-v2"   # Local, reliable
  3. "placeholder"         # Local, fallback
  
Exclusions:
  - "openai"               # Cloud services excluded
```

#### 3. Performance-First Strategy
```yaml  
Priority Order:
  1. "placeholder"         # Fastest (testing scenarios)
  2. "bge-large-onnx"      # Fast + quality
  3. "all-mpnet-base-v2"   # Moderate speed
  4. "openai"              # Network latency
```

### Configuration Management

#### Environment Variables
```bash
# Primary pipeline selection
EMBEDDING_PIPELINE_PRIMARY="bge-large-onnx"
EMBEDDING_PIPELINE_FALLBACK="all-mpnet-base-v2,placeholder"
EMBEDDING_PIPELINE_STRATEGY="quality-first"

# Model-specific configuration
BGE_QUANTIZATION_LEVEL="int8"
MPNET_DEVICE="cpu"
OPENAI_EMBEDDING_MODEL="text-embedding-3-small"

# System behavior
EMBEDDING_AUTO_FALLBACK="true"
EMBEDDING_HEALTH_CHECK_INTERVAL="300"
```

#### Runtime Configuration
```json
{
  "embedding_pipelines": {
    "default_pipeline": "bge-large-onnx",
    "selection_strategy": "quality-first",
    "auto_fallback": true,
    "health_check_interval": 300,
    
    "pipelines": {
      "bge-large-onnx": {
        "enabled": true,
        "model_name": "BAAI/bge-large-en-v1.5",
        "quantization": "int8",
        "cache_dir": "~/llms/models/bge"
      },
      "all-mpnet-base-v2": {
        "enabled": true,
        "model_name": "sentence-transformers/all-mpnet-base-v2",
        "device": "cpu"
      },
      "openai": {
        "enabled": false,
        "model_name": "text-embedding-3-small",
        "security_warning": "Data exposure risk - enable only if explicitly approved"
      },
      "placeholder": {
        "enabled": true,
        "warning": "Testing only - no semantic understanding"
      }
    }
  }
}
```

---

## Dual Vector Storage Integration

### ChromaDB Public Knowledge Store

**Purpose**: Shared non-proprietary data accessible across organizations
**Content Types**:
- Standards (ISO 27001, GDPR, CCPA, etc.)
- Regulations and legal frameworks
- Best practices and templates
- Assessment question banks
- Follow-up suggestion templates
- Workflow templates and roadmaps

```python
# ChromaDB collection structure for public knowledge
class ChromaDBPublicManager:
    def create_standards_collection(self, standard_name: str):
        """Create collection for specific standard (e.g., ISO27001, GDPR)"""
        collection_name = f"standards_{standard_name.lower()}"
        return self.client.get_or_create_collection(
            name=collection_name,
            metadata={
                "type": "public_standards",
                "standard": standard_name,
                "content_types": ["regulations", "assessments", "templates"]
            }
        )
    
    def create_assessment_collection(self):
        """Create collection for assessment questions and frameworks"""
        return self.client.get_or_create_collection(
            name="assessment_frameworks",
            metadata={
                "type": "public_assessments",
                "content_types": ["questions", "rubrics", "scoring"]
            }
        )
```

### Supabase Vector Private Store

**Purpose**: Organization-scoped private data with strict tenant isolation
**Content Types**:
- Company-specific policies and procedures
- Internal assessment results
- Proprietary implementations
- Organizational context and decisions

### Multi-Dimensional Schema Support

The dual vector storage layer supports multiple embedding dimensions through flexible schema design:

```sql
-- Supabase Vector: Private organizational embeddings
CREATE TABLE embeddings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL,  -- Strict tenant isolation
    chunk_id UUID NOT NULL REFERENCES chunks(id),
    
    -- Pipeline identification
    pipeline_name TEXT NOT NULL,
    model_name TEXT NOT NULL,
    embedding_dimension INTEGER NOT NULL,
    data_classification TEXT NOT NULL DEFAULT 'private', -- 'private' for org data
    
    -- Vector storage (flexible dimensions)
    embedding vector(1024),  -- Adjust based on primary pipeline
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints and indexes
    CONSTRAINT valid_dimension CHECK (
        embedding_dimension = vector_dims(embedding)
    ),
    CONSTRAINT valid_classification CHECK (
        data_classification IN ('private', 'internal')
    )
);

-- RLS for strict org isolation
ALTER TABLE embeddings ENABLE ROW LEVEL SECURITY;
CREATE POLICY embeddings_org_isolation ON embeddings 
  FOR ALL USING (org_id = app_current_org_id());

-- Pipeline-specific indexes
CREATE INDEX idx_embeddings_pipeline ON embeddings(pipeline_name, org_id);
CREATE INDEX idx_embeddings_dimension ON embeddings(embedding_dimension);
CREATE INDEX idx_embeddings_classification ON embeddings(data_classification, org_id);
```

### Dual Vector Store Orchestration

```python
# Hybrid search orchestration across both vector stores
class DualVectorOrchestrator:
    def __init__(self, chromadb_client, supabase_vector_client):
        self.chromadb = chromadb_client      # Public knowledge
        self.supabase = supabase_vector_client # Private org data
        
    async def hybrid_search(
        self, 
        org_id: str, 
        query_embedding: List[float], 
        pipeline_name: str,
        limit: int = 8
    ) -> HybridSearchResult:
        """Parallel search across both vector stores"""
        
        # Parallel queries
        public_task = self._search_chromadb(
            query_embedding, pipeline_name, limit
        )
        private_task = self._search_supabase(
            org_id, query_embedding, pipeline_name, limit
        )
        
        # Await both results
        public_results, private_results = await asyncio.gather(
            public_task, private_task, return_exceptions=True
        )
        
        # Fuse and score results
        return self._fuse_results(
            public_results, private_results, query_embedding
        )
        
    def _fuse_results(self, public_results, private_results, query_embedding):
        """Intelligent fusion of public standards and private org data"""
        fused_chunks = []
        
        # Add source attribution for transparency
        for result in public_results:
            result['source_type'] = 'public_standards'
            result['confidence_boost'] = 0.1  # Boost for authoritative sources
            fused_chunks.append(result)
            
        for result in private_results:
            result['source_type'] = 'private_organization'
            result['confidence_boost'] = 0.05  # Slight boost for org-specific
            fused_chunks.append(result)
            
        # Re-rank by hybrid score (semantic similarity + source authority)
        return self._rerank_hybrid_results(fused_chunks, query_embedding)
```

---

## Performance Characteristics

### Benchmark Results (Estimated)

| Pipeline | Load Time | Memory | Inference | Quality | Use Case |
|----------|-----------|---------|-----------|---------|----------|
| BGE-ONNX | 2-3s | 350MB | 25-30ms | 95% | Production |
| all-mpnet | 1-2s | 400MB | 50-100ms | 85% | Development |
| OpenAI | 0s | 0MB | 200-500ms | 90% | Cloud (if permitted) |
| Placeholder | 0s | 0MB | 1-2ms | 0% | Testing only |

### Scaling Characteristics

```yaml
Concurrent Requests:
  BGE-ONNX: "10-20 concurrent (CPU bound)"
  all-mpnet: "5-15 concurrent (CPU bound)"
  OpenAI: "Limited by rate limits (3000 RPM)"
  Placeholder: "100+ concurrent (compute minimal)"

Memory Usage:
  Single Pipeline: "350-400MB per model"
  Multi Pipeline: "Lazy loading, 1 active at a time by default"
  
Startup Time:
  Cold Start: "2-5s for model loading"
  Warm Start: "50-200ms for loaded models"
```

---

## Integration with Existing Architecture

### Router Integration

The embedding pipelines integrate with the existing router at Stage 6 (RAG Preparation):

```python
# Stage 6: Enhanced query embedding (when no deterministic match found)
def handle_chat(messages, meta, hints, explainability):
    # ... preprocessing stages 1-5 ...
    
    if not preprocessing_result.deterministic_match:
        # Use selected embedding pipeline
        embedding_service = get_embedding_service()
        enhanced_query = build_enhanced_query(last_user_message, preprocessing_result)
        
        # Generate embeddings using active pipeline
        query_vector = embedding_service.embed_texts([enhanced_query])[0]
        
        # Proceed with hybrid retrieval
        search_results = hybrid_search(meta.org_id, query_vector, limit=8)
```

### Dual-Vector Retrieval Integration

```python
# Enhanced retrieval layer for dual-vector architecture
class DualVectorRetrieval:
    def __init__(self):
        self.orchestrator = DualVectorOrchestrator(
            chromadb_client, supabase_vector_client
        )
        
    async def multi_tier_search(
        self, 
        org_id: str, 
        query_embedding: List[float], 
        pipeline_name: str,
        tier_config: TierConfig
    ) -> MultiTierResult:
        """Multi-tier search with confidence-based escalation"""
        
        # Tier 1: Dual Vector Parallel Search
        hybrid_result = await self.orchestrator.hybrid_search(
            org_id, query_embedding, pipeline_name, limit=8
        )
        
        # Evaluate confidence
        confidence_score = self._calculate_hybrid_confidence(hybrid_result)
        
        if confidence_score >= tier_config.tier1_threshold:
            return MultiTierResult(
                tier="dual_vector",
                results=hybrid_result.chunks,
                confidence=confidence_score,
                source_breakdown={
                    "public_standards": len([r for r in hybrid_result.chunks 
                                            if r['source_type'] == 'public_standards']),
                    "private_organization": len([r for r in hybrid_result.chunks 
                                                if r['source_type'] == 'private_organization'])
                }
            )
            
        # Tier 2: Cloud LLM Enhanced (if confidence too low)
        if confidence_score >= tier_config.tier2_threshold:
            return await self._escalate_to_cloud_llm(
                org_id, query_embedding, hybrid_result
            )
            
        # Tier 3: Generate clarifying questions
        return await self._generate_clarifying_questions(
            org_id, hybrid_result
        )
        
    def _calculate_hybrid_confidence(self, hybrid_result: HybridSearchResult) -> float:
        """Calculate confidence based on dual-vector fusion"""
        if not hybrid_result.chunks:
            return 0.0
            
        # Base semantic similarity score
        base_score = sum(chunk['score'] for chunk in hybrid_result.chunks) / len(hybrid_result.chunks)
        
        # Authority boost for public standards
        authority_boost = sum(
            0.1 for chunk in hybrid_result.chunks 
            if chunk.get('source_type') == 'public_standards'
        ) / len(hybrid_result.chunks)
        
        # Coverage boost if both public and private data found
        coverage_boost = 0.15 if self._has_both_source_types(hybrid_result.chunks) else 0.0
        
        return min(1.0, base_score + authority_boost + coverage_boost)
```

---

## Development and Deployment Considerations

### Development Workflow

1. **Local Development**: Use `all-mpnet-base-v2` for fast iteration
2. **Quality Testing**: Switch to `bge-large-onnx` for quality validation
3. **Performance Testing**: Use `placeholder` for pipeline performance isolation
4. **Security Testing**: Validate with local-only pipelines

### Deployment Strategy

#### Phase 1 (MVP-Assessment-App)
```yaml
Primary: "bge-large-onnx"
Fallback: "all-mpnet-base-v2"
Security: "Local-only (no OpenAI)"
Focus: "Quality + Security"
```

#### Phase 2 (MVP-Demo-Light-App)  
```yaml
Primary: "bge-large-onnx"
Secondary: "all-mpnet-base-v2"
Optional: "openai" (client-specific)
Focus: "Flexibility + Demos"
```

#### Phase 3 (MVP-Pilot)
```yaml
Production: "bge-large-onnx"
Development: "all-mpnet-base-v2"  
Quality Benchmark: "openai"
System Fallback: "placeholder"
Focus: "Full pipeline maturity"
```

### Dual-Vector Monitoring and Observability

```python
# Enhanced monitoring for dual-vector architecture
def get_dual_vector_system_status():
    return {
        "active_pipeline": embedding_service.current_pipeline,
        "vector_stores": {
            "chromadb": {
                "status": chromadb_client.health_check(),
                "collections": chromadb_client.list_collections(),
                "total_documents": chromadb_client.get_document_count(),
                "data_types": ["standards", "regulations", "assessments"]
            },
            "supabase_vector": {
                "status": supabase_vector_client.health_check(),
                "organizations": supabase_vector_client.get_org_count(),
                "total_documents": supabase_vector_client.get_document_count(),
                "data_types": ["policies", "assessments", "proprietary"]
            }
        },
        "pipeline_health": {
            pipeline: registry.get_pipeline(pipeline).health_check()
            for pipeline in registry.list_pipeline_names()
        },
        "hybrid_search_metrics": {
            "avg_confidence_score": get_avg_confidence(),
            "public_private_ratio": get_source_distribution(),
            "tier_escalation_rate": get_escalation_rate(),
            "cross_source_coverage": get_coverage_rate()
        },
        "performance_metrics": {
            "dual_search_latency_ms": get_dual_search_latency(),
            "chromadb_latency_ms": get_chromadb_latency(),
            "supabase_latency_ms": get_supabase_latency(),
            "success_rate": get_success_rate(),
            "error_rate": get_error_rate()
        },
        "resource_usage": {
            "memory_mb": get_memory_usage(),
            "cpu_utilization": get_cpu_usage(),
            "storage_breakdown": {
                "chromadb_gb": get_chromadb_storage(),
                "supabase_vector_gb": get_supabase_storage()
            }
        }
    }
```

---

## Future Considerations

### Potential Enhancements

1. **Fine-tuning Support**: Domain-specific fine-tuning for compliance content
2. **Multi-Model Ensemble**: Combine multiple embeddings for improved quality
3. **Dynamic Quantization**: Runtime quantization level adjustment
4. **GPU Acceleration**: Optional GPU support for higher throughput
5. **Embedding Caching**: Intelligent caching for repeated content

### Research Directions

1. **Domain Adaptation**: Compliance-specific embedding training
2. **Multilingual Support**: International compliance standards
3. **Temporal Embeddings**: Version-aware document embeddings
4. **Federated Learning**: Privacy-preserving embedding improvements

---

## Conclusion

The dual-vector multi-pipeline embedding architecture provides ArionComply with:

- **Security**: Local-first approach with strict data separation and optional cloud enhancement
- **Data Governance**: Clear separation between public standards knowledge and private organizational data
- **Quality**: State-of-the-art embeddings with hybrid search across both knowledge domains
- **Flexibility**: Runtime pipeline selection based on requirements and data classification
- **Reliability**: Automatic fallback ensures system availability across both vector stores
- **Scalability**: CPU-optimized for efficient deployment with dual storage optimization
- **Intelligence**: Confidence-based progressive escalation from dual-vector to cloud LLM
- **Transparency**: Full traceability of data sources, pipeline usage, and confidence scoring

This architecture supports the platform's core principles of security, quality, and operational excellence while enabling sophisticated multi-tier retrieval that leverages both shared standards knowledge and private organizational context for superior compliance guidance.
File: arioncomply-v1/.kiro/specs/standards-compliance-platform/embedding-pipeline-architecture.md
