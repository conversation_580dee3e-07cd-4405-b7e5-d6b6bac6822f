```yaml
id: Q215
question: What if our business model changes — does that affect our compliance requirements?
packs: ["ISO27001:2022","GDPR:2016","ISO27701:2019"]
primary_ids: ["ISO27001:2022/Cl.4.1","ISO27001:2022/Cl.4.2","ISO27001:2022/Cl.4.3","ISO27001:2022/Cl.6.1","GDPR:2016/Art.6"]
overlap_ids: ["GDPR:2016/Art.13","GDPR:2016/Art.14","GDPR:2016/Art.24"]
capability_tags: ["Register","Draft Doc","Versioning","Approval","Workflow","Report"]
ui:
  actions:
    - target: "workflow"
      action: "open"
      args: { key: "scope_and_role_reassessment" }
    - target: "register"
      action: "open"
      args: { key: "risk" }
cards_hint:
  - Reassess scope, roles, lawful bases, risk.
  - Update SoA, notices, contracts, records.
  - Re-run training if roles change.
graph_required: false
```

### 215) What if our business model changes—does that affect our compliance requirements?

**Standard term(s)**

- **Context & scope (Cl. 4.1–4.3); Lawful basis (GDPR Art. 6).**

**Plain-English answer**\
Yes—revisit **scope**, **roles**, **lawful bases**, and **risk**; update SoA, notices, contracts, and records.

**Applies to**

- **Primary:** ISO/IEC 27001 **Cl. 4–6**; GDPR **Arts. 6, 13–14, 24**.
- **Also relevant/Overlaps:** ISO/IEC 27701.

**Why it matters**\
Wrong assumptions create **gaps**.

**Do next in our platform**

- Run a **scope & role reassessment**; update RoPA/SoA/contracts; refresh risk treatment where needed.

**How our platform will help**

- **[Register] [Draft Doc] [Versioning] [Approval] [Workflow] [Report]** — Guided reassessment with auto-generated updates.

**Likely follow-ups**

- “Do we need to re-consent users?” → **[LOCAL LAW CHECK]**

**Sources**

- ISO/IEC 27001 **Cl. 4–6**; GDPR **Arts. 6, 13–14, 24**.