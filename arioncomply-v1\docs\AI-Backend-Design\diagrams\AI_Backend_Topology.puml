' refresh: trigger render
@startuml AI Backend Topology
' trigger: workflow refresh
title AI Backend Topology
skinparam shadowing false
skinparam monochrome true

actor User
node "Web App" as UI

cloud "Supabase" as SUPA {
  node "Edge Functions\n(Deno)" as Edge
  database "Postgres\n(App DB)" as APPDB
  database "Postgres\n(pgvector)" as PG
  node "Storage" as Storage
}

node "AI Backend" as Backend {
  component "API (FastAPI)" as API
  component "Worker (RQ/Celery)" as WORK
  queue "Redis" as Redis
}

node "Local LLMs" as LLMs {
  [OpenAI-compatible endpoints]
}

database "Chroma (optional)" as Chroma

User --> UI
UI --> Edge : HTTPS
Edge --> API : forward requests (JWT)
API --> Redis : enqueue jobs
Redis --> WORK : job

WORK --> Storage : download/upload artifacts (signed URLs)
WORK --> PG : embeddings upsert/search
WORK --> APPDB : job updates (optional)

API --> APPDB : configs/status/logs
Edge --> APPDB : minimal request logs (optional)

API --> LLMs : OpenAI /v1/* (chat)
WORK --> LLMs : embeddings (optional)
WORK --> Chroma : local vector (optional)

note bottom of LLMs
  Selection via $HOME/llms/config/local_sllm_endpoints.json
  Fallback to cloud provider if none healthy
end note

note right of Storage
  Edge issues signed URLs; Worker uses
  them to read/write artifacts
end note

@enduml
