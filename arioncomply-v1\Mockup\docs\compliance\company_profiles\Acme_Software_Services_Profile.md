# Acme Software Services - Company Profile

This profile captures key information collected during a mock onboarding session for Acme Software Services. It provides background context for preparing ISO 27001 and ISO 27701 documentation templates.

## Basic Information

- **Headquarters:** New York, NY, USA
- **Additional Offices:** Prague, Czech Republic (EU)
- **Number of Employees:** Approximately 50
- **Industry Focus:**
  - Custom software development
  - IT and digital transformation consulting
  - AI implementation and automation services
- **Key Technologies:** Cloud platforms, AI/ML frameworks, web and mobile development stacks

## Business Activities

Acme Software Services designs and builds software solutions for external clients. The company also provides strategic consulting around digitalization initiatives, system integration, and adoption of AI-based tools. Internal teams leverage AI to assist software development, manage administrative tasks, and support sales/marketing operations. Services are delivered from both the U.S. and EU locations.

## Security and Privacy Objectives

- Achieve ISO 27001 certification to demonstrate robust information security management practices.
- Implement ISO 27701 controls to strengthen privacy governance and support compliance with global data protection regulations (e.g., GDPR).
- Maintain a security and privacy posture suitable for handling client data from diverse industries, including those with heightened regulatory requirements.

## Infrastructure Overview (Assumptions)

- Predominantly cloud-based environment using a major IaaS/PaaS provider.
- Centralized code repository on GitHub with CI/CD workflows.
- Productivity and communications via SaaS platforms (email, document collaboration, ticketing systems).
- Minimal on-premises infrastructure; most staff work remotely or in small office spaces.

## Data Handling & Processing

- Acme develops software that may process personal data on behalf of clients. The company acts primarily as a data processor when hosting or maintaining client solutions.
- Internal HR records and customer relationship data are stored in cloud services.
- AI tools are used to generate code, automate testing, and support business administration. Strict access controls and logging are required for these tools.

## Compliance Approach

1. **Define ISMS Scope:** Include all business units involved in software development, consulting, and AI-based solutions delivered from both U.S. and Czech Republic locations.
2. **Assign Roles:** Establish an information security officer and privacy lead to oversee ISO 27001 and ISO 27701 programs.
3. **Risk Assessment:** Identify risks related to cloud infrastructure, remote work, use of AI, and handling of client data across jurisdictions.
4. **Policy Development:** Document information security policies, privacy policies, and procedures aligned with ISO requirements.
5. **Training & Awareness:** Provide ongoing security and privacy training to all employees, emphasizing responsible AI usage and cross-border data handling.
6. **Monitoring & Improvement:** Utilize centralized logging, vulnerability management, and regular internal audits to ensure continuous improvement.

## Notes

This profile is a starting point for completing the various templates in the `iso27001` and `iso27701` directories. It should be reviewed and expanded as the implementation project progresses.
