<!--
File: arioncomply-v1/testing/workflow-gui/testGUIDevelopmentPlan.md
File Description: Development roadmap for SLLM workflow testing GUI integration
Purpose: Track implementation phases and priorities for local LLM testing capabilities
Inputs: Current GUI state, local LLM endpoints, MVP-Assessment requirements
Outputs: Structured development phases with specific deliverables
Dependencies: local_sllm_endpoints.json, Python backend router, existing GUI
Security/RLS: N/A (local dev tooling only)
Notes: Update as phases are completed or priorities change
-->

# Test GUI Development Plan: SLLM Workflow Integration

## Current State Analysis

### Existing Components:
- **GUI**: Workflow testing interface for Supabase Edge Functions
  - Conversation start/send/stream endpoints
  - Provider comparison (OpenAI/Claude)
  - Configuration persistence in localStorage
- **Local LLMs**: Configured and discoverable via `collectSLLMinfo.sh`
  - SmolLM3 (port 8081)
  - Mistral7B (port 8082) 
  - Phi3 (port 8083)
  - Status: JSON endpoint discovery at `local_sllm_endpoints.json`
- **Python Backend**: Router skeleton with service stubs
  - `services/router.py` - Chat orchestration skeleton
  - `services/logging/events.py` - Event emission framework
  - `services/retrieval/` - Vector search stubs
  - `services/ingestion/` - Document processing stubs

### MVP-Assessment Requirements:
- Backend stub for chat requests → local SLLM path
- Event logging (retrieval_run, ai_call) with traceability
- Basic document ingestion pipeline (extract→chunk→embed→insert)
- UI correlation ID display and trace visualization

## Implementation Phases

### Phase 1: Direct SLLM Integration (Week 1)

**Objective**: Enable direct testing of local LLM endpoints from GUI

**Tasks**:
1. **Endpoint Discovery Module**
   - Read `local_sllm_endpoints.json` via fetch API
   - Parse model configurations (id, port, base, health status)
   - Handle file not found / parsing errors gracefully

2. **Health Check Interface**
   - Implement `/health` and `/v1/models` endpoint probes
   - Display real-time status (ok/starting/down) per model
   - Add manual refresh capability

3. **Direct Chat Interface**
   - Model selection dropdown populated from endpoints JSON
   - Simple prompt input with compliance examples
   - Direct HTTP POST to `http://127.0.0.1:PORT/v1/chat/completions`
   - Response display with timing and token information

4. **Response Comparison**
   - Side-by-side testing across multiple models
   - Performance metrics (latency, tokens/sec)
   - Basic response quality indicators

**Deliverables**:
- Extended HTML with SLLM testing section
- JavaScript functions for endpoint discovery and health checks
- Direct model invocation without backend router
- Response comparison interface

### Phase 2: Backend Router Integration (Week 2)

**Objective**: Route GUI requests through Python backend for logging and orchestration

**Tasks**:
1. **Backend Router Enhancement**
   - Complete `services/router.py` implementation
   - Wire local LLM endpoints from JSON config
   - Add proper error handling and timeouts

2. **Logging Integration**
   - Implement event emission (retrieval_run, ai_call)
   - Add correlation ID propagation
   - Create log viewing interface in GUI

3. **API Bridge**
   - Create local HTTP server wrapper for router.py
   - Handle CORS for browser → backend communication
   - Add request/response envelope standardization

4. **Trace Visualization** 
   - Display request IDs, correlation IDs, trace headers
   - Show event log timeline per request
   - Add performance profiling view

**Deliverables**:
- Working Python HTTP server for router
- GUI integration with backend endpoints
- Real-time logging and tracing display
- Event log persistence and viewing

### Phase 3: MVP-Assessment Workflow (Week 3)

**Objective**: Complete assessment workflow with document generation and compliance scoring

**Tasks**:
1. **Assessment Scenarios**
   - Pre-built question sets for ISO 27001, GDPR compliance
   - Scenario selection and context injection
   - Multi-turn conversation support

2. **Document Ingestion Pipeline**
   - Implement extract→chunk→embed→insert workflow
   - Test with sample compliance documents
   - Vector search integration with local embeddings

3. **Response Scoring & Analysis**
   - Basic compliance assessment logic
   - Citation extraction and validation
   - Confidence scoring for responses

4. **Document Generation**
   - Template-based policy creation
   - Integration with assessment results
   - Export capabilities (PDF, Word, etc.)

**Deliverables**:
- Complete assessment workflow UI
- Working document ingestion pipeline
- Policy generation templates and engine
- Full logging and traceability chain

## Technical Specifications

### API Interfaces:
- **SLLM Direct**: OpenAI-compatible `/v1/chat/completions`
- **Backend Router**: `POST /ai/chat` with ArionComply envelope
- **Health Checks**: `GET /health`, `GET /v1/models`
- **Logging**: Event emission via Supabase or local storage

### Data Formats:
- **Endpoint Config**: JSON from `local_sllm_endpoints.json`
- **Request Envelope**: `{messages[], meta{orgId, userId, sessionId}, hints{}, explainability{}}`
- **Response Envelope**: `{provider, model, text, usage, citations[], suggestions[]}`
- **Event Logs**: W3C trace format with ArionComply extensions

### Performance Targets:
- **Response Time**: < 5s for simple queries on local LLMs
- **Health Checks**: < 1s per endpoint probe
- **UI Responsiveness**: < 200ms for interface updates
- **Memory Usage**: < 100MB for GUI + backend combined

## Dependencies & Risks

### Critical Dependencies:
- Local LLM services must be running and healthy
- Python backend requires proper environment setup
- Browser CORS policies for local development
- File system access for endpoint discovery JSON

### Risk Mitigation:
- Graceful degradation when LLMs are unavailable
- Fallback to mock responses for development
- Clear error messages for configuration issues
- Health check automation and alerts

## Success Criteria

### Phase 1 Complete:
- [ ] Can discover and display local LLM endpoints
- [ ] Can send direct requests to any healthy model
- [ ] Can compare responses across models side-by-side
- [ ] Health status updates in real-time

### Phase 2 Complete:
- [ ] All requests route through Python backend
- [ ] Event logs captured and displayed in GUI
- [ ] Correlation IDs propagate end-to-end
- [ ] Performance metrics visible per request

### Phase 3 Complete:
- [ ] Can run complete assessment scenarios
- [ ] Document ingestion pipeline functional
- [ ] Policy generation working from templates
- [ ] Full compliance workflow demonstrable

## Next Actions

**Immediate (This Week)**:
1. Extend GUI HTML with SLLM testing section
2. Add JavaScript endpoint discovery functions
3. Implement direct model health checking
4. Test basic chat flow with local models

**Short Term (Next 2 Weeks)**:
1. Complete Python backend router implementation
2. Add logging and tracing infrastructure
3. Build assessment scenario framework
4. Test end-to-end workflow

**Medium Term (Next Month)**:
1. Document ingestion and vector search
2. Policy generation templates
3. Compliance scoring algorithms
4. Production readiness improvements