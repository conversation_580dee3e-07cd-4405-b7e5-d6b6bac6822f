# Database Design Type System and Validators

## Overview
This document establishes consistent principles for type systems and validation across all system components in ArionComply. It directly builds upon and implements the core standards defined in `database_schema_design_principles.md` and integrates with the API approach outlined in `metadata_driven_api_design_principles_v1.md`.

**MANDATORY:** All database schema, API implementations, and UI components MUST follow this type system architecture.

## 1. Type System Foundation

### Core Type Definitions

**Standard: Define a consistent set of core types used across all layers**

| Core Type | PostgreSQL Type | API Type | UI Type | Description |
|-----------|----------------|----------|---------|-------------|
| `identifier` | UUID | string | string | Unique identifier |
| `text` | TEXT | string | string/text | General text content |
| `long_text` | TEXT | string | textarea | Multi-line text content |
| `code` | TEXT | string | code | Code or technical identifier |
| `number` | NUMERIC | number | number | Precise numeric value |
| `integer` | INTEGER | number | number | Whole number value |
| `boolean` | BOOLEAN | boolean | checkbox/toggle | True/false value |
| `date` | DATE | string (ISO) | date | Date without time |
| `datetime` | TIMESTAMPTZ | string (ISO) | datetime | Date and time with timezone |
| `enum` | TEXT | string | select | Value from predefined set |
| `json` | JSONB | object | object/json | Structured JSON data |
| `array` | ARRAY | array | array/multi-select | Collection of values |
| `email` | TEXT | string | email | Email address |
| `url` | TEXT | string | url | Web URL |
| `file` | TEXT | string | file | File reference (path/URL) |
| `money` | MONEY | string | currency | Monetary value |
| `percentage` | NUMERIC | number | percentage | Percentage value (0-100) |
| `color` | TEXT | string | color | Color value (hex/RGB) |
| `relation` | UUID | string | relation | Reference to another entity |

**Rationale:**
- Consistent type mapping across all system layers
- Clear expectations for data formats and validation
- Simplified conversion between database, API, and UI

### Type Registry

**Standard: Maintain a type registry in the database**

```sql
CREATE TABLE system_metadata.type_definitions (
    type_name TEXT PRIMARY KEY,
    description TEXT,
    pg_type TEXT NOT NULL,
    api_type TEXT NOT NULL,
    ui_type TEXT NOT NULL,
    validation_schema JSONB NOT NULL,
    conversion_rules JSONB,
    metadata JSONB DEFAULT '{}'::jsonb,
    organization_id UUID REFERENCES organizations(id), -- NULL for system-wide types
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- Enable RLS for multi-tenant isolation
ALTER TABLE system_metadata.type_definitions ENABLE ROW LEVEL SECURITY;

-- Policy for type definitions - system types visible to all, org-specific types restricted
CREATE POLICY tenant_isolation_type_definitions ON system_metadata.type_definitions
USING (
    organization_id IS NULL OR -- System-wide definitions
    organization_id = current_setting('app.current_organization_id')::UUID -- Org-specific
);
```

Example type definition:
```json
{
  "type_name": "email",
  "description": "Email address with validation",
  "pg_type": "TEXT",
  "api_type": "string",
  "ui_type": "email",
  "validation_schema": {
    "type": "string",
    "format": "email",
    "maxLength": 255
  },
  "conversion_rules": {
    "db_to_api": {
      "transform": "LOWER(value)"
    },
    "api_to_db": {
      "transform": "LOWER(value)"
    }
  },
  "metadata": {
    "searchable": true,
    "indexable": true
  }
}
```

**Rationale:**
- Centralized registry enables consistent type handling
- Self-documenting system with explicit conversions
- Extensible for custom types and validation
- Multi-tenant support with RLS policies

## 2. Type Validation Framework

### Validation Functions

**Standard: Implement type-specific validation functions**

General validation function:
```sql
CREATE OR REPLACE FUNCTION validate_field_type(
    value ANYELEMENT,
    type_name TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    validation_schema JSONB;
    json_value JSONB;
BEGIN
    -- Get validation schema
    SELECT td.validation_schema INTO validation_schema
    FROM system_metadata.type_definitions td
    WHERE td.type_name = validate_field_type.type_name
    AND (td.organization_id IS NULL OR 
         td.organization_id = current_setting('app.current_organization_id', true)::UUID);
    
    IF validation_schema IS NULL THEN
        RAISE EXCEPTION 'Unknown type: %', type_name;
    END IF;
    
    -- Convert value to JSON for schema validation
    json_value := to_jsonb(value);
    
    -- Perform validation
    RETURN validate_json_schema(validation_schema, json_value);
END;
$$ LANGUAGE plpgsql IMMUTABLE;
```

Type-specific validation functions:
```sql
CREATE OR REPLACE FUNCTION validate_email(email TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN validate_field_type(email, 'email');
END;
$$ LANGUAGE plpgsql IMMUTABLE;

CREATE OR REPLACE FUNCTION validate_url(url TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN validate_field_type(url, 'url');
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Additional type validators...
```

**Rationale:**
- Reusable validation functions for consistent checks
- Type-specific functions improve readability
- Centralized validation logic reduces duplication
- Multi-tenant aware validation with organization context

### Database Constraints

**Standard: Use type validation in database constraints**

```sql
CREATE TABLE contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    email TEXT NOT NULL,
    website_url TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    
    CONSTRAINT valid_email CHECK (validate_email(email)),
    CONSTRAINT valid_website_url CHECK (
        website_url IS NULL OR validate_url(website_url)
    )
);

-- Enable RLS for multi-tenant isolation (following database_schema_design_principles.md)
ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;

-- Standard tenant isolation policy
CREATE POLICY tenant_isolation_contacts ON contacts
USING (organization_id = current_setting('app.current_organization_id')::UUID);
```

**Rationale:**
- Database-level validation ensures data integrity
- Consistent validation regardless of access path
- Self-documenting constraints with clear purpose
- Multi-tenant isolation with RLS policies

**Rejected Alternatives:**
- Application-only validation: Bypassed by direct database access
- Regex-based constraints: More difficult to maintain and understand
- Trigger-based validation: More complex and potentially less performant

## 3. Type Conversion System

### Conversion Functions

**Standard: Implement consistent type conversion functions**

Database to API conversion:
```sql
CREATE OR REPLACE FUNCTION convert_to_api(
    value ANYELEMENT,
    type_name TEXT
)
RETURNS JSONB AS $$
DECLARE
    conversion_rules JSONB;
    transform_expr TEXT;
    result JSONB;
BEGIN
    -- Get conversion rules
    SELECT td.conversion_rules->'db_to_api' INTO conversion_rules
    FROM system_metadata.type_definitions td
    WHERE td.type_name = convert_to_api.type_name
    AND (td.organization_id IS NULL OR 
         td.organization_id = current_setting('app.current_organization_id', true)::UUID);
    
    -- Convert value to JSON
    result := to_jsonb(value);
    
    -- Apply transformation if defined
    IF conversion_rules IS NOT NULL AND conversion_rules ? 'transform' THEN
        transform_expr := conversion_rules->>'transform';
        
        -- Replace placeholder with actual value
        transform_expr := replace(
            transform_expr, 
            'value', 
            quote_literal(value)
        );
        
        -- Execute transformation
        EXECUTE 'SELECT ' || transform_expr INTO result;
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql STABLE;
```

API to database conversion:
```sql
CREATE OR REPLACE FUNCTION convert_to_db(
    value JSONB,
    type_name TEXT
)
RETURNS TEXT AS $$
DECLARE
    conversion_rules JSONB;
    transform_expr TEXT;
    pg_type TEXT;
    result TEXT;
BEGIN
    -- Get conversion rules and PostgreSQL type
    SELECT 
        td.conversion_rules->'api_to_db',
        td.pg_type
    INTO 
        conversion_rules,
        pg_type
    FROM system_metadata.type_definitions td
    WHERE td.type_name = convert_to_db.type_name
    AND (td.organization_id IS NULL OR 
         td.organization_id = current_setting('app.current_organization_id', true)::UUID);
    
    -- Extract value as text
    result := value#>>'{}';
    
    -- Apply transformation if defined
    IF conversion_rules IS NOT NULL AND conversion_rules ? 'transform' THEN
        transform_expr := conversion_rules->>'transform';
        
        -- Replace placeholder with actual value
        transform_expr := replace(
            transform_expr, 
            'value', 
            quote_literal(result)
        );
        
        -- Execute transformation
        EXECUTE 'SELECT ' || transform_expr INTO result;
    END IF;
    
    -- Cast to appropriate PostgreSQL type
    EXECUTE format('SELECT %L::%s', result, pg_type) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql STABLE;
```

**Rationale:**
- Consistent conversion between database and API types
- Dynamic transformations based on type registry
- Type-safe conversions with appropriate casting
- Organization-aware conversions for multi-tenant support

### Integration with Field Mapping System

**Standard: Integrate with field_mappings table defined in database_schema_design_principles.md**

```sql
-- Function to generate field_mappings entries from type_definitions and entity_fields
CREATE OR REPLACE FUNCTION generate_field_mappings(entity_name TEXT)
RETURNS VOID AS $$
DECLARE
    db_to_api JSONB := '{}'::jsonb;
    api_to_db JSONB := '{}'::jsonb;
    field_record RECORD;
BEGIN
    -- Generate mappings for each field
    FOR field_record IN
        SELECT 
            ef.field_name,
            snake_to_camel(ef.field_name) AS camel_field_name
        FROM 
            system_metadata.entity_fields ef
        WHERE 
            ef.entity_name = generate_field_mappings.entity_name
    LOOP
        -- Build db_to_api mapping
        db_to_api := jsonb_set(
            db_to_api, 
            ARRAY[field_record.field_name], 
            to_jsonb(field_record.camel_field_name)
        );
        
        -- Build api_to_db mapping
        api_to_db := jsonb_set(
            api_to_db, 
            ARRAY[field_record.camel_field_name], 
            to_jsonb(field_record.field_name)
        );
    END LOOP;
    
    -- Update field_mappings table
    INSERT INTO field_mappings (
        table_name,
        db_to_api_mapping,
        api_to_db_mapping
    ) VALUES (
        entity_name,
        db_to_api,
        api_to_db
    )
    ON CONFLICT (table_name) DO UPDATE SET
        db_to_api_mapping = EXCLUDED.db_to_api_mapping,
        api_to_db_mapping = EXCLUDED.api_to_db_mapping,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Helper function for snake_case to camelCase conversion
CREATE OR REPLACE FUNCTION snake_to_camel(snake_case TEXT)
RETURNS TEXT AS $$
DECLARE
    result TEXT := '';
    parts TEXT[];
    i INTEGER;
BEGIN
    parts := string_to_array(snake_case, '_');
    result := parts[1];
    
    FOR i IN 2..array_length(parts, 1) LOOP
        result := result || initcap(parts[i]);
    END LOOP;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql IMMUTABLE;
```

**Rationale:**
- Maintains consistency with field mapping system
- Automates snake_case ↔ camelCase conversion
- Single source of truth for field mappings
- Reduces duplication and potential inconsistencies

### Bulk Data Conversion

Function for converting entire records:
```sql
CREATE OR REPLACE FUNCTION convert_record_to_api(
    record_data ANYELEMENT,
    entity_name TEXT
)
RETURNS JSONB AS $$
DECLARE
    result JSONB := '{}'::jsonb;
    field_name TEXT;
    field_type TEXT;
    field_value JSONB;
    db_to_api_mapping JSONB;
    api_field_name TEXT;
BEGIN
    -- Get field mappings
    SELECT fm.db_to_api_mapping INTO db_to_api_mapping
    FROM field_mappings fm
    WHERE fm.table_name = entity_name;
    
    IF db_to_api_mapping IS NULL THEN
        RAISE EXCEPTION 'No field mappings found for entity: %', entity_name;
    END IF;
    
    -- Get entity field types
    FOR field_name, field_type IN
        SELECT ef.field_name, ef.field_type
        FROM system_metadata.entity_fields ef
        WHERE ef.entity_name = convert_record_to_api.entity_name
        AND (ef.organization_id IS NULL OR 
             ef.organization_id = current_setting('app.current_organization_id', true)::UUID)
    LOOP
        -- Skip fields not present in record
        CONTINUE WHEN NOT (record_data::jsonb ? field_name);
        
        -- Convert field
        field_value := convert_to_api(
            (record_data::jsonb->>field_name)::text, 
            field_type
        );
        
        -- Get API field name from mapping
        api_field_name := db_to_api_mapping->>field_name;
        
        -- Add to result
        result := jsonb_set(result, ARRAY[api_field_name], field_value);
    END LOOP;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql STABLE;
```

**Rationale:**
- Efficient conversion of entire records
- Entity-aware field mapping
- Consistent handling of all fields
- Properly handles camelCase conversion

## 4. Field Type Mapping

### Entity Field Registry

**Standard: Register field types for all entities**

```sql
CREATE TABLE system_metadata.entity_fields (
    entity_name TEXT NOT NULL,
    field_name TEXT NOT NULL,
    field_type TEXT NOT NULL REFERENCES system_metadata.type_definitions(type_name),
    is_required BOOLEAN NOT NULL DEFAULT false,
    is_primary_key BOOLEAN NOT NULL DEFAULT false,
    is_unique BOOLEAN NOT NULL DEFAULT false,
    description TEXT,
    validation_rules JSONB,
    ui_properties JSONB,
    organization_id UUID REFERENCES organizations(id), -- NULL for system-wide fields
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    
    PRIMARY KEY (entity_name, field_name)
);

-- Enable RLS for multi-tenant isolation
ALTER TABLE system_metadata.entity_fields ENABLE ROW LEVEL SECURITY;

-- Policy for entity fields - system fields visible to all, org-specific fields restricted
CREATE POLICY tenant_isolation_entity_fields ON system_metadata.entity_fields
USING (
    organization_id IS NULL OR -- System-wide fields
    organization_id = current_setting('app.current_organization_id')::UUID -- Org-specific
);

-- Trigger to update field_mappings when entity_fields change
CREATE OR REPLACE FUNCTION update_field_mappings_on_entity_field_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Re-generate field mappings for this entity
    PERFORM generate_field_mappings(NEW.entity_name);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER entity_fields_update_field_mappings
AFTER INSERT OR UPDATE OR DELETE ON system_metadata.entity_fields
FOR EACH ROW EXECUTE FUNCTION update_field_mappings_on_entity_field_change();
```

Example entity field definitions:
```sql
INSERT INTO system_metadata.entity_fields (
    entity_name, field_name, field_type, 
    is_required, is_primary_key, is_unique,
    description, validation_rules, ui_properties
)
VALUES
(
    'subscriptions', 'id', 'identifier',
    true, true, true,
    'Unique subscription identifier', 
    NULL,
    '{"visible": false}'::jsonb
),
(
    'subscriptions', 'organization_id', 'relation',
    true, false, false,
    'Organization that owns this subscription',
    NULL,
    '{"visible": false}'::jsonb
),
(
    'subscriptions', 'plan_id', 'relation',
    true, false, false,
    'Subscription plan reference',
    NULL,
    '{"component": "PlanSelector", "label": "Subscription Plan"}'::jsonb
),
(
    'subscriptions', 'start_date', 'datetime',
    true, false, false,
    'When the subscription begins',
    '{"min": "now()"}'::jsonb,
    '{"label": "Start Date"}'::jsonb
),
(
    'subscriptions', 'status', 'enum',
    true, false, false,
    'Current subscription status',
    '{"values": ["active", "cancelled", "past_due", "pending"]}'::jsonb,
    '{"component": "StatusBadge", "label": "Status"}'::jsonb
);
```

**Rationale:**
- Explicit field type mapping for all entities
- Self-documenting schema with UI hints
- Field-specific validation rules beyond type validation
- Automatic field mapping integration
- Multi-tenant support with organization_id

### Field Generation

Generate table definitions from entity field registry:
```sql
CREATE OR REPLACE FUNCTION generate_table_definition(entity_name TEXT)
RETURNS TEXT AS $$
DECLARE
    result TEXT := format('CREATE TABLE %I (', entity_name);
    fields TEXT[];
    constraints TEXT[];
    field_name TEXT;
    field_type TEXT;
    pg_type TEXT;
    is_required BOOLEAN;
    is_primary_key BOOLEAN;
    is_unique BOOLEAN;
    validation_rules JSONB;
BEGIN
    -- Collect field definitions
    FOR field_name, field_type, pg_type, is_required, is_primary_key, is_unique, validation_rules IN
        SELECT 
            ef.field_name, 
            ef.field_type, 
            td.pg_type,
            ef.is_required,
            ef.is_primary_key,
            ef.is_unique,
            ef.validation_rules
        FROM 
            system_metadata.entity_fields ef
            JOIN system_metadata.type_definitions td ON ef.field_type = td.type_name
        WHERE 
            ef.entity_name = generate_table_definition.entity_name
        AND (ef.organization_id IS NULL OR 
             ef.organization_id = current_setting('app.current_organization_id', true)::UUID)
    LOOP
        -- Build field definition
        fields := array_append(
            fields,
            format('    %I %s%s', 
                field_name, 
                pg_type,
                CASE WHEN is_required THEN ' NOT NULL' ELSE '' END
            )
        );
        
        -- Add constraints
        IF is_primary_key THEN
            constraints := array_append(
                constraints,
                format('    CONSTRAINT %I_pkey PRIMARY KEY (%I)', 
                    entity_name, field_name
                )
            );
        END IF;
        
        IF is_unique THEN
            constraints := array_append(
                constraints,
                format('    CONSTRAINT %I_%I_unique UNIQUE (%I)', 
                    entity_name, field_name, field_name
                )
            );
        END IF;
        
        -- Add validation constraint if needed
        IF validation_rules IS NOT NULL THEN
            constraints := array_append(
                constraints,
                format('    CONSTRAINT %I_%I_valid CHECK (validate_field_type(%I, %L))', 
                    entity_name, field_name, field_name, field_type
                )
            );
        END IF;
    END LOOP;
    
    -- Add standard audit fields if not already defined
    IF NOT array_to_string(fields, ',') ~ 'created_at' THEN
        fields := array_append(fields, '    created_at TIMESTAMPTZ NOT NULL DEFAULT now()');
    END IF;
    
    IF NOT array_to_string(fields, ',') ~ 'updated_at' THEN
        fields := array_append(fields, '    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()');
    END IF;
    
    IF NOT array_to_string(fields, ',') ~ 'created_by' THEN
        fields := array_append(fields, '    created_by UUID REFERENCES users(id)');
    END IF;
    
    IF NOT array_to_string(fields, ',') ~ 'updated_by' THEN
        fields := array_append(fields, '    updated_by UUID REFERENCES users(id)');
    END IF;
    
    -- Combine fields and constraints
    result := result || E'\n' || array_to_string(fields, E',\n');
    
    IF array_length(constraints, 1) > 0 THEN
        result := result || E',\n' || array_to_string(constraints, E',\n');
    END IF;
    
    result := result || E'\n);';
    
    -- Add RLS configuration
    result := result || E'\n\n-- Enable RLS for multi-tenant isolation';
    result := result || E'\nALTER TABLE ' || entity_name || ' ENABLE ROW LEVEL SECURITY;';
    result := result || E'\n\n-- Standard tenant isolation policy';
    result := result || E'\nCREATE POLICY tenant_isolation_' || entity_name || ' ON ' || entity_name;
    result := result || E'\nUSING (organization_id = current_setting(''app.current_organization_id'')::UUID);';
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;
```

**Rationale:**
- Automated table generation ensures consistency
- Constraint generation based on registry
- Single source of truth for schema definition
- Includes RLS policies for multi-tenant isolation
- Enforces standard audit fields

## 5. JSON Schema Integration

### JSON Schema Validation

**Standard: Use JSON Schema for structured data validation**

```sql
CREATE OR REPLACE FUNCTION validate_json_schema(
    schema JSONB,
    data JSONB
)
RETURNS BOOLEAN AS $$
DECLARE
    result BOOLEAN := true;
    field RECORD;
    required_fields TEXT[];
    additional_properties BOOLEAN;
    pattern TEXT;
    format TEXT;
    enum_values JSONB;
    min_value NUMERIC;
    max_value NUMERIC;
    min_length INTEGER;
    max_length INTEGER;
BEGIN
    -- Check type
    IF schema ? 'type' AND jsonb_typeof(data) != schema->>'type' THEN
        -- Handle special cases like integers in JSON
        IF NOT (
            schema->>'type' = 'integer' AND 
            jsonb_typeof(data) = 'number' AND 
            data::text ~ '^\d+\.0*$'
        ) THEN
            RETURN false;
        END IF;
    END IF;
    
    -- Required properties
    IF schema ? 'required' AND jsonb_typeof(data) = 'object' THEN
        required_fields := ARRAY(
            SELECT jsonb_array_elements_text(schema->'required')
        );
        
        FOREACH field IN ARRAY required_fields
        LOOP
            IF NOT data ? field THEN
                RETURN false;
            END IF;
        END LOOP;
    END IF;
    
    -- Object properties
    IF schema ? 'properties' AND jsonb_typeof(data) = 'object' THEN
        -- Check each property against its schema
        FOR field IN SELECT * FROM jsonb_each(schema->'properties')
        LOOP
            IF data ? field.key THEN
                result := result AND validate_json_schema(
                    field.value::jsonb,
                    data->field.key
                );
                
                IF NOT result THEN
                    RETURN false;
                END IF;
            END IF;
        END LOOP;
        
        -- additionalProperties
        additional_properties := COALESCE(
            (schema->>'additionalProperties')::boolean,
            true
        );
        
        IF NOT additional_properties THEN
            FOR field IN SELECT * FROM jsonb_each(data)
            LOOP
                IF NOT schema->'properties' ? field.key THEN
                    RETURN false;
                END IF;
            END LOOP;
        END IF;
    END IF;
    
    -- Array items
    IF schema ? 'items' AND jsonb_typeof(data) = 'array' THEN
        FOR field IN SELECT * FROM jsonb_array_elements(data)
        LOOP
            result := result AND validate_json_schema(
                schema->'items',
                field.value::jsonb
            );
            
            IF NOT result THEN
                RETURN false;
            END IF;
        END LOOP;
    END IF;
    
    -- String patterns
    IF schema ? 'pattern' AND jsonb_typeof(data) = 'string' THEN
        pattern := schema->>'pattern';
        
        IF NOT (data#>>'{}' ~ pattern) THEN
            RETURN false;
        END IF;
    END IF;
    
    -- String formats
    IF schema ? 'format' AND jsonb_typeof(data) = 'string' THEN
        format := schema->>'format';
        
        CASE format
            WHEN 'email' THEN
                IF NOT (data#>>'{}' ~ '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$') THEN
                    RETURN false;
                END IF;
            WHEN 'uri' THEN
                IF NOT (data#>>'{}' ~ '^https?://') THEN
                    RETURN false;
                END IF;
            WHEN 'date-time' THEN
                BEGIN
                    PERFORM (data#>>'{}')::timestamptz;
                EXCEPTION WHEN OTHERS THEN
                    RETURN false;
                END;
            WHEN 'date' THEN
                BEGIN
                    PERFORM (data#>>'{}')::date;
                EXCEPTION WHEN OTHERS THEN
                    RETURN false;
                END;
            WHEN 'uuid' THEN
                IF NOT (data#>>'{}' ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$') THEN
                    RETURN false;
                END IF;
            ELSE
                -- Unknown format, return true (assume valid)
                NULL;
        END CASE;
    END IF;
    
    -- Enum values
    IF schema ? 'enum' AND jsonb_typeof(schema->'enum') = 'array' THEN
        enum_values := schema->'enum';
        
        IF NOT enum_values @> jsonb_build_array(data) THEN
            RETURN false;
        END IF;
    END IF;
    
    -- Numeric constraints
    IF jsonb_typeof(data) = 'number' THEN
        -- Minimum
        IF schema ? 'minimum' THEN
            min_value := (schema->>'minimum')::numeric;
            
            IF (data#>>'{}')::numeric < min_value THEN
                RETURN false;
            END IF;
        END IF;
        
        -- Maximum
        IF schema ? 'maximum' THEN
            max_value := (schema->>'maximum')::numeric;
            
            IF (data#>>'{}')::numeric > max_value THEN
                RETURN false;
            END IF;
        END IF;
    END IF;
    
    -- String length constraints
    IF jsonb_typeof(data) = 'string' THEN
        -- minLength
        IF schema ? 'minLength' THEN
            min_length := (schema->>'minLength')::integer;
            
            IF length(data#>>'{}') < min_length THEN
                RETURN false;
            END IF;
        END IF;
        
        -- maxLength
        IF schema ? 'maxLength' THEN
            max_length := (schema->>'maxLength')::integer;
            
            IF length(data#>>'{}') > max_length THEN
                RETURN false;
            END IF;
        END IF;
    END IF;
    
    -- Array length constraints
    IF jsonb_typeof(data) = 'array' THEN
        -- minItems
        IF schema ? 'minItems' THEN
            min_length := (schema->>'minItems')::integer;
            
            IF jsonb_array_length(data) < min_length THEN
                RETURN false;
            END IF;
        END IF;
        
        -- maxItems
        IF schema ? 'maxItems' THEN
            max_length := (schema->>'maxItems')::integer;
            
            IF jsonb_array_length(data) > max_length THEN
                RETURN false;
            END IF;
        END IF;
    END IF;
    
    RETURN true;
END;
$$ LANGUAGE plpgsql IMMUTABLE;
```

**Rationale:**
- Comprehensive JSON Schema validation in the database
- Support for complex nested structures
- Rich validation options beyond simple type checking

**Rejected Alternatives:**
- External validation services: Adds latency and dependency
- Limited validation functions: Insufficient for complex data
- Application-only validation: Bypassed by direct database access

### Schema Generation

Generate JSON Schema from entity field registry:
```sql
CREATE OR REPLACE FUNCTION generate_json_schema(entity_name TEXT)
RETURNS JSONB AS $$
DECLARE
    result JSONB := jsonb_build_object(
        'type', 'object',
        'properties', '{}'::jsonb,
        'required', '[]'::jsonb
    );
    field_name TEXT;
    field_type TEXT;
    field_schema JSONB;
    is_required BOOLEAN;
    validation_rules JSONB;
BEGIN
    -- Collect field schemas
    FOR field_name, field_type, is_required, validation_rules IN
        SELECT 
            ef.field_name, 
            ef.field_type,
            ef.is_required,
            ef.validation_rules
        FROM 
            system_metadata.entity_fields ef
        WHERE 
            ef.entity_name = generate_json_schema.entity_name
        AND (ef.organization_id IS NULL OR 
             ef.organization_id = current_setting('app.current_organization_id', true)::UUID)
    LOOP
        -- Get base schema for type
        SELECT td.validation_schema INTO field_schema
        FROM system_metadata.type_definitions td
        WHERE td.type_name = field_type
        AND (td.organization_id IS NULL OR 
             td.organization_id = current_setting('app.current_organization_id', true)::UUID);
        
        -- Merge with field-specific validation rules
        IF validation_rules IS NOT NULL THEN
            field_schema := field_schema || validation_rules;
        END IF;
        
        -- Add to properties
        result := jsonb_set(
            result,
            ARRAY['properties', field_name],
            field_schema
        );
        
        -- Add to required if needed
        IF is_required THEN
            result := jsonb_set(
                result,
                ARRAY['required'],
                (result->'required') || jsonb_build_array(field_name)
            );
        END IF;
    END LOOP;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;
```

**Rationale:**
- Automatic schema generation ensures consistency
- Field-specific rules combined with type-level validation
- Single source of truth for validation schemas
- Organization-aware for multi-tenant support

## 6. UI Type Mapping

### UI Component Registry

**Standard: Register UI components for field types**

```sql
CREATE TABLE system_metadata.ui_components (
    component_name TEXT PRIMARY KEY,
    component_type TEXT NOT NULL,
    applicable_types TEXT[] NOT NULL,
    default_props JSONB DEFAULT '{}'::jsonb,
    description TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    organization_id UUID REFERENCES organizations(id), -- NULL for system-wide components
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- Enable RLS for multi-tenant isolation
ALTER TABLE system_metadata.ui_components ENABLE ROW LEVEL SECURITY;

-- Policy for UI components - system components visible to all, org-specific components restricted
CREATE POLICY tenant_isolation_ui_components ON system_metadata.ui_components
USING (
    organization_id IS NULL OR -- System-wide components
    organization_id = current_setting('app.current_organization_id')::UUID -- Org-specific
);
```

Example components:
```sql
INSERT INTO system_metadata.ui_components (
    component_name, component_type, applicable_types,
    default_props, description
)
VALUES
(
    'TextField', 'input', ARRAY['text', 'code', 'email', 'url'],
    '{"variant": "outlined", "fullWidth": true}'::jsonb,
    'Basic text input field'
),
(
    'DatePicker', 'input', ARRAY['date', 'datetime'],
    '{"variant": "outlined", "fullWidth": true}'::jsonb,
    'Date selection component'
),
(
    'Select', 'input', ARRAY['enum'],
    '{"variant": "outlined", "fullWidth": true}'::jsonb,
    'Dropdown selection component'
),
(
    'Checkbox', 'input', ARRAY['boolean'],
    '{}'::jsonb,
    'Boolean checkbox component'
),
(
    'StatusBadge', 'display', ARRAY['enum', 'text'],
    '{"colorMap": {"active": "success", "cancelled": "error", "past_due": "warning", "pending": "info"}}'::jsonb,
    'Status indicator with color coding'
);
```

### UI Schema Generation

Generate UI schema from entity field registry:
```sql
CREATE OR REPLACE FUNCTION generate_ui_schema(entity_name TEXT)
RETURNS JSONB AS $$
DECLARE
    result JSONB := jsonb_build_object(
        'entity', entity_name,
        'fields', '{}'::jsonb
    );
    field_name TEXT;
    field_type TEXT;
    ui_properties JSONB;
    component_name TEXT;
    default_props JSONB;
    api_field_name TEXT;
    db_to_api_mapping JSONB;
BEGIN
    -- Get field mappings for camelCase conversion
    SELECT fm.db_to_api_mapping INTO db_to_api_mapping
    FROM field_mappings fm
    WHERE fm.table_name = entity_name;
    
    IF db_to_api_mapping IS NULL THEN
        RAISE EXCEPTION 'No field mappings found for entity: %', entity_name;
    END IF;
    
    -- Collect field UI schemas
    FOR field_name, field_type, ui_properties IN
        SELECT 
            ef.field_name, 
            ef.field_type,
            ef.ui_properties
        FROM 
            system_metadata.entity_fields ef
        WHERE 
            ef.entity_name = generate_ui_schema.entity_name
        AND (ef.organization_id IS NULL OR 
             ef.organization_id = current_setting('app.current_organization_id', true)::UUID)
    LOOP
        -- Skip hidden fields
        IF ui_properties IS NOT NULL AND 
           (ui_properties->>'visible')::boolean = false THEN
            CONTINUE;
        END IF;
        
        -- Get default component for type
        SELECT 
            uc.component_name,
            uc.default_props
        INTO 
            component_name,
            default_props
        FROM 
            system_metadata.ui_components uc
        WHERE 
            field_type = ANY(uc.applicable_types) AND
            uc.component_type = 'input'
        AND (uc.organization_id IS NULL OR 
             uc.organization_id = current_setting('app.current_organization_id', true)::UUID)
        LIMIT 1;
        
        -- Override with field-specific component
        IF ui_properties IS NOT NULL AND ui_properties ? 'component' THEN
            component_name := ui_properties->>'component';
        END IF;
        
        -- Get API field name (camelCase)
        api_field_name := db_to_api_mapping->>field_name;
        
        -- Build field UI schema
        result := jsonb_set(
            result,
            ARRAY['fields', api_field_name],
            jsonb_build_object(
                'type', field_type,
                'component', component_name,
                'label', COALESCE(
                    ui_properties->>'label',
                    initcap(replace(field_name, '_', ' '))
                ),
                'dbField', field_name,
                'props', COALESCE(default_props, '{}'::jsonb) || 
                         COALESCE(ui_properties->'props', '{}'::jsonb)
            )
        );
    END LOOP;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;
```

**Rationale:**
- Automatic UI schema generation ensures consistency
- Field-specific customization with sensible defaults
- Component-type mapping simplifies form generation
- Respects camelCase for API/UI and snake_case for database
- Organization-aware for multi-tenant support

## 7. Type System Implementation

### Implementation in Edge Functions with Dart

Example of type validation in Dart-based edge functions:
```dart
// arioncomply-v1/supabase/functions/entity_api/lib/validators.dart

import 'dart:convert';
import 'package:supabase/supabase.dart';

// Validate request body against entity schema
Future<bool> validateRequestBody(SupabaseClient supabase, String entityName, Map<String, dynamic> body) async {
  try {
    // Get entity JSON schema
    final response = await supabase.rpc('generate_json_schema', 
      params: {'entity_name': entityName}
    );
    
    if (response.error != null) {
      throw ValidationException(
        'SCHEMA_ERROR',
        'Error loading schema: ${response.error!.message}'
      );
    }
    
    // Validate body against schema
    final Map<String, dynamic> schema = response.data;
    final validationResult = validateSchema(schema, body);
    
    if (!validationResult.valid) {
      throw ValidationException(
        'VALIDATION_ERROR',
        'Invalid request data',
        errors: validationResult.errors
      );
    }
    
    return true;
  } catch (e) {
    rethrow;
  }
}

// Convert API data to database format
Future<Map<String, dynamic>> convertToDatabaseFormat(
  SupabaseClient supabase, 
  String entityName, 
  Map<String, dynamic> apiData,
  String organizationId
) async {
  final result = <String, dynamic>{};
  
  try {
    // Set organization context for RLS
    await supabase.rpc('set_config', params: {
      'setting_name': 'app.current_organization_id',
      'setting_value': organizationId,
      'is_local': true
    });
    
    // Get API to DB field mappings
    final mappingsResponse = await supabase
      .from('field_mappings')
      .select('api_to_db_mapping')
      .eq('table_name', entityName)
      .single();
    
    if (mappingsResponse.error != null) {
      throw DatabaseException(
        'MAPPING_ERROR',
        'Error loading field mappings: ${mappingsResponse.error!.message}'
      );
    }
    
    final apiToDbMapping = mappingsResponse.data['api_to_db_mapping'] as Map<String, dynamic>;
    
    // Get entity field types
    final fieldsResponse = await supabase
      .from('system_metadata.entity_fields')
      .select('field_name, field_type')
      .eq('entity_name', entityName);
    
    if (fieldsResponse.error != null) {
      throw DatabaseException(
        'CONVERSION_ERROR',
        'Error loading field types: ${fieldsResponse.error!.message}'
      );
    }
    
    final fields = fieldsResponse.data as List<dynamic>;
    
    // Convert each field using mappings
    for (final apiFieldName in apiData.keys) {
      // Skip if no mapping exists
      final dbFieldName = apiToDbMapping[apiFieldName];
      if (dbFieldName == null) continue;
      
      final fieldDef = fields.firstWhere(
        (f) => f['field_name'] == dbFieldName, 
        orElse: () => null
      );
      
      if (fieldDef == null) continue;
      
      final convertResponse = await supabase.rpc(
        'convert_to_db',
        params: {
          'value': jsonEncode(apiData[apiFieldName]),
          'type_name': fieldDef['field_type']
        }
      );
      
      if (convertResponse.error != null) {
        throw ValidationException(
          'TYPE_CONVERSION_ERROR',
          'Error converting $apiFieldName: ${convertResponse.error!.message}',
          field: apiFieldName,
          value: apiData[apiFieldName]
        );
      }
      
      result[dbFieldName] = convertResponse.data;
    }
    
    // Add audit fields
    result['updated_at'] = DateTime.now().toIso8601String();
    
    // Only add created_at for new records
    if (!apiData.containsKey('id')) {
      result['created_at'] = DateTime.now().toIso8601String();
    }
    
    return result;
  } catch (e) {
    rethrow;
  }
}

// Validation result class
class ValidationResult {
  final bool valid;
  final Map<String, dynamic>? errors;
  
  ValidationResult(this.valid, [this.errors]);
}

// Schema validation
ValidationResult validateSchema(Map<String, dynamic> schema, Map<String, dynamic> data) {
  // This is a simplified validation - production code would implement full JSON schema validation
  final errors = <String, dynamic>{};
  
  // Validate required fields
  if (schema.containsKey('required')) {
    final requiredFields = schema['required'] as List<dynamic>;
    for (final field in requiredFields) {
      if (!data.containsKey(field) || data[field] == null) {
        errors[field.toString()] = 'Field is required';
      }
    }
  }
  
  // Validate field types and formats
  if (schema.containsKey('properties')) {
    final properties = schema['properties'] as Map<String, dynamic>;
    
    for (final entry in properties.entries) {
      final fieldName = entry.key;
      final fieldSchema = entry.value as Map<String, dynamic>;
      
      if (!data.containsKey(fieldName)) continue;
      
      final value = data[fieldName];
      
      // Type validation
      if (fieldSchema.containsKey('type')) {
        final fieldType = fieldSchema['type'];
        
        switch (fieldType) {
          case 'string':
            if (value != null && value is! String) {
              errors[fieldName] = 'Must be a string';
            } else if (value is String) {
              // Length validation
              if (fieldSchema.containsKey('maxLength') && 
                  value.length > (fieldSchema['maxLength'] as int)) {
                errors[fieldName] = 'Exceeds maximum length of ${fieldSchema['maxLength']}';
              }
              
              if (fieldSchema.containsKey('minLength') && 
                  value.length < (fieldSchema['minLength'] as int)) {
                errors[fieldName] = 'Below minimum length of ${fieldSchema['minLength']}';
              }
              
              // Format validation
              if (fieldSchema.containsKey('format')) {
                final format = fieldSchema['format'];
                if (format == 'email' && !isValidEmail(value)) {
                  errors[fieldName] = 'Invalid email format';
                }
              }
            }
            break;
            
          case 'number':
          case 'integer':
            if (value != null && value is! num) {
              errors[fieldName] = 'Must be a number';
            } else if (value is num) {
              if (fieldSchema.containsKey('minimum') && 
                  value < (fieldSchema['minimum'] as num)) {
                errors[fieldName] = 'Below minimum value of ${fieldSchema['minimum']}';
              }
              
              if (fieldSchema.containsKey('maximum') && 
                  value > (fieldSchema['maximum'] as num)) {
                errors[fieldName] = 'Exceeds maximum value of ${fieldSchema['maximum']}';
              }
            }
            break;
            
          case 'boolean':
            if (value != null && value is! bool) {
              errors[fieldName] = 'Must be a boolean';
            }
            break;
            
          case 'object':
            if (value != null && value is! Map) {
              errors[fieldName] = 'Must be an object';
            }
            break;
            
          case 'array':
            if (value != null && value is! List) {
              errors[fieldName] = 'Must be an array';
            }
            break;
        }
      }
      
      // Enum validation
      if (fieldSchema.containsKey('enum') && value != null) {
        final enumValues = fieldSchema['enum'] as List<dynamic>;
        if (!enumValues.contains(value)) {
          errors[fieldName] = 'Must be one of: ${enumValues.join(', ')}';
        }
      }
    }
  }
  
  return ValidationResult(errors.isEmpty, errors.isEmpty ? null : errors);
}

// Email validation helper
bool isValidEmail(String email) {
  final emailRegExp = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}

**Rationale:**
- Consistent validation and conversion in edge functions
- Reuse of database validation logic
- Strong typing for API requests and responses
- Multi-tenant support with organization context
- Proper field mapping integration

### Implementation in Flutter UI Components

Example Flutter implementation for type-based form generation:
```dart
// arioncomply-v1/frontend-flutter/lib/services/typed_form_service.dart

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

// TypedFormController - Handles data management for dynamically generated forms
class TypedFormController {
  final String entityName;
  final SupabaseClient supabase = Supabase.instance.client;
  final _secureStorage = const FlutterSecureStorage();
  
  Map<String, dynamic>? uiSchema;
  Map<String, dynamic> formData = {};
  Map<String, String> errors = {};
  bool isLoading = true;
  
  TypedFormController({required this.entityName});
  
  // Load UI schema from database
  Future<void> loadSchema() async {
    try {
      isLoading = true;
      
      // Set organization context for RLS
      final orgId = await _secureStorage.read(key: 'organization_id');
      await supabase.rpc('set_config', params: {
        'setting_name': 'app.current_organization_id',
        'setting_value': orgId,
        'is_local': true
      });
      
      // Get UI schema
      final response = await supabase.rpc('generate_ui_schema', 
        params: {'entity_name': entityName}
      );
      
      if (response.error != null) {
        throw response.error!;
      }
      
      uiSchema = response.data;
      
      // Initialize form data with defaults
      final fields = uiSchema?['fields'] as Map<String, dynamic>? ?? {};
      for (final entry in fields.entries) {
        final fieldName = entry.key;
        final fieldSchema = entry.value;
        
        if (fieldSchema['props'] != null && 
            fieldSchema['props']['defaultValue'] != null) {
          formData[fieldName] = fieldSchema['props']['defaultValue'];
        }
      }
      
    } catch (e) {
      print('Error loading schema: $e');
    } finally {
      isLoading = false;
    }
  }
  
  // Handle field changes
  void handleChange(String fieldName, dynamic value) {
    formData[fieldName] = value;
    
    // Clear field error
    if (errors.containsKey(fieldName)) {
      errors.remove(fieldName);
    }
  }
  
  // Validate form
  Future<bool> validate() async {
    if (uiSchema == null) return false;
    
    try {
      final response = await supabase.rpc(
        'validate_entity_data',
        params: {
          'entity_name': entityName,
          'entity_data': formData
        }
      );
      
      if (response.error != null) {
        throw response.error!;
      }
      
      if (!(response.data['valid'] as bool)) {
        final validationErrors = response.data['errors'] as Map<String, dynamic>;
        errors = validationErrors.map((key, value) => 
          MapEntry(key, value.toString()));
        return false;
      }
      
      errors = {};
      return true;
    } catch (e) {
      print('Validation error: $e');
      return false;
    }
  }
  
  // Submit form
  Future<Map<String, dynamic>?> handleSubmit() async {
    final isValid = await validate();
    
    if (!isValid) return null;
    
    try {
      // Convert to database format
      final convertResponse = await supabase.rpc(
        'convert_entity_data_to_db',
        params: {
          'entity_name': entityName,
          'entity_data': formData
        }
      );
      
      if (convertResponse.error != null) {
        throw convertResponse.error!;
      }
      
      final convertedData = convertResponse.data;
      
      // Submit to database
      final operation = formData.containsKey('id') ? 'update' : 'insert';
      dynamic result;
      
      if (operation == 'insert') {
        result = await supabase
          .from(entityName)
          .insert(convertedData)
          .select()
          .single();
      } else {
        result = await supabase
          .from(entityName)
          .update(convertedData)
          .eq('id', formData['id'])
          .select()
          .single();
      }
      
      if (result.error != null) {
        throw result.error!;
      }
      
      // Convert result back to API format
      final apiResponse = await supabase.rpc(
        'convert_record_to_api',
        params: {
          'record_data': result.data,
          'entity_name': entityName
        }
      );
      
      if (apiResponse.error != null) {
        throw apiResponse.error!;
      }
      
      return apiResponse.data;
    } catch (e) {
      print('Submission error: $e');
      return null;
    }
  }
}

// TypedFormWidget - Dynamic form generation based on type definitions
class TypedFormWidget extends StatefulWidget {
  final String entityName;
  final Function(Map<String, dynamic>)? onSubmit;
  
  const TypedFormWidget({
    Key? key, 
    required this.entityName,
    this.onSubmit
  }) : super(key: key);
  
  @override
  State<TypedFormWidget> createState() => _TypedFormWidgetState();
}

class _TypedFormWidgetState extends State<TypedFormWidget> {
  late TypedFormController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = TypedFormController(entityName: widget.entityName);
    _loadSchema();
  }
  
  Future<void> _loadSchema() async {
    await _controller.loadSchema();
    setState(() {});
  }
  
  @override
  Widget build(BuildContext context) {
    if (_controller.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    
    if (_controller.uiSchema == null) {
      return const Center(child: Text('Failed to load form schema'));
    }
    
    final fields = _controller.uiSchema!['fields'] as Map<String, dynamic>;
    
    return Column(
      children: [
        ...fields.entries.map((entry) {
          final fieldName = entry.key;
          final fieldSchema = entry.value as Map<String, dynamic>;
          return _buildField(fieldName, fieldSchema);
        }).toList(),
        
        const SizedBox(height: 20),
        
        ElevatedButton(
          onPressed: () async {
            final result = await _controller.handleSubmit();
            if (result != null && widget.onSubmit != null) {
              widget.onSubmit!(result);
            }
          },
          child: const Text('Submit'),
        ),
      ],
    );
  }
  
  Widget _buildField(String fieldName, Map<String, dynamic> fieldSchema) {
    final fieldType = fieldSchema['type'] as String;
    final component = fieldSchema['component'] as String?;
    final label = fieldSchema['label'] as String;
    final props = fieldSchema['props'] as Map<String, dynamic>? ?? {};
    final errorText = _controller.errors[fieldName];
    
    // Build different widgets based on field type and component
    switch (component) {
      case 'TextField':
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: TextFormField(
            decoration: InputDecoration(
              labelText: label,
              errorText: errorText,
              border: const OutlineInputBorder(),
            ),
            onChanged: (value) => setState(() {
              _controller.handleChange(fieldName, value);
            }),
            initialValue: _controller.formData[fieldName]?.toString(),
          ),
        );
        
      case 'DatePicker':
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: InkWell(
            onTap: () async {
              final DateTime? picked = await showDatePicker(
                context: context,
                initialDate: _controller.formData[fieldName] != null
                    ? DateTime.parse(_controller.formData[fieldName])
                    : DateTime.now(),
                firstDate: DateTime(2000),
                lastDate: DateTime(2100),
              );
              
              if (picked != null) {
                setState(() {
                  _controller.handleChange(fieldName, picked.toIso8601String());
                });
              }
            },
            child: InputDecorator(
              decoration: InputDecoration(
                labelText: label,
                errorText: errorText,
                border: const OutlineInputBorder(),
              ),
              child: Text(
                _controller.formData[fieldName] != null
                    ? DateTime.parse(_controller.formData[fieldName]).toString().split(' ')[0]
                    : 'Select date',
              ),
            ),
          ),
        );
        
      case 'Select':
        final options = fieldSchema['validation_schema']?['enum'] as List<dynamic>? ?? [];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: label,
              errorText: errorText,
              border: const OutlineInputBorder(),
            ),
            value: _controller.formData[fieldName]?.toString(),
            items: options.map((option) {
              return DropdownMenuItem<String>(
                value: option.toString(),
                child: Text(option.toString()),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _controller.handleChange(fieldName, value);
                });
              }
            },
          ),
        );
        
      case 'Checkbox':
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: CheckboxListTile(
            title: Text(label),
            subtitle: errorText != null ? Text(errorText, style: TextStyle(color: Theme.of(context).colorScheme.error)) : null,
            value: _controller.formData[fieldName] == true,
            onChanged: (value) {
              setState(() {
                _controller.handleChange(fieldName, value ?? false);
              });
            },
          ),
        );
        
      case 'StatusBadge':
        // This is a display-only component, not editable
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: InputDecorator(
            decoration: InputDecoration(
              labelText: label,
              border: const OutlineInputBorder(),
            ),
            child: Text(_controller.formData[fieldName]?.toString() ?? 'Unknown'),
          ),
        );
        
      default:
        // Fallback to basic text field
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: TextFormField(
            decoration: InputDecoration(
              labelText: label,
              errorText: errorText,
              border: const OutlineInputBorder(),
            ),
            onChanged: (value) => setState(() {
              _controller.handleChange(fieldName, value);
            }),
            initialValue: _controller.formData[fieldName]?.toString(),
          ),
        );
    }
  }
}
```

**Rationale:**
- Type-driven form generation simplifies UI development
- Consistent validation across all form components
- Automatic field mapping based on entity registry
- Multi-tenant support with organization context
- Proper handling of camelCase in UI and snake_case in database

## 8. Migration and Data Integrity

### Type System Migration

```sql
-- Migration script for existing tables
DO $$
DECLARE
    table_record RECORD;
    column_record RECORD;
    type_mapping JSONB := '{
        "subscription_status": "subscription_status",
        "plan_amount": "currency_amount", 
        "contact_email": "email_address"
    }';
BEGIN
    -- Add validation version tracking to existing tables
    FOR table_record IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name LIKE '%subscription%'
    LOOP
        -- Add validation tracking columns
        EXECUTE format('ALTER TABLE %I ADD COLUMN IF NOT EXISTS validation_version INTEGER DEFAULT 1', table_record.table_name);
        EXECUTE format('ALTER TABLE %I ADD COLUMN IF NOT EXISTS validation_errors JSONB DEFAULT ''[]''::jsonb', table_record.table_name);
        
        -- Update constraints based on type mappings
        FOR column_record IN 
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = table_record.table_name 
            AND table_schema = 'public'
        LOOP
            IF type_mapping ? column_record.column_name THEN
                -- Add typed constraint
                EXECUTE format('ALTER TABLE %I ADD CONSTRAINT %I_type_check CHECK (validate_field_type(%I, %L))',
                    table_record.table_name,
                    column_record.column_name,
                    column_record.column_name,
                    type_mapping->>column_record.column_name
                );
            END IF;
        END LOOP;
    END LOOP;
END $$;
```

### Data Validation Report

```sql
-- Generate validation report for existing data
CREATE OR REPLACE FUNCTION generate_validation_report()
RETURNS TABLE(
    table_name TEXT,
    column_name TEXT,
    type_name TEXT,
    invalid_count BIGINT,
    total_count BIGINT,
    invalid_percentage DECIMAL
) AS $$
DECLARE
    table_rec RECORD;
    col_rec RECORD;
BEGIN
    FOR table_rec IN 
        SELECT t.table_name
        FROM information_schema.tables t
        WHERE t.table_schema = 'public'
    LOOP
        FOR col_rec IN 
            SELECT c.column_name, td.type_name
            FROM information_schema.columns c
            JOIN system_metadata.entity_fields ef ON ef.entity_name = table_rec.table_name AND ef.field_name = c.column_name
            JOIN system_metadata.type_definitions td ON td.type_name = ef.field_type
            WHERE c.table_name = table_rec.table_name AND c.table_schema = 'public'
        LOOP
            RETURN QUERY EXECUTE format('
                SELECT %L::TEXT, %L::TEXT, %L::TEXT,
                       COUNT(*) FILTER (WHERE NOT validate_field_type(%I, %L)) as invalid_count,
                       COUNT(*) as total_count,
                       (COUNT(*) FILTER (WHERE NOT validate_field_type(%I, %L)) * 100.0 / COUNT(*)) as invalid_percentage
                FROM %I.%I
            ', table_rec.table_name, col_rec.column_name, col_rec.type_name,
               col_rec.column_name, col_rec.type_name,
               col_rec.column_name, col_rec.type_name,
               'public', table_rec.table_name);
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

## Implementation Guidelines

### Database Layer
1. **Type Registry**: Centralized type definitions with SQL, JSON Schema, and UI mappings
2. **Validation Functions**: Generic validation functions that work with type metadata
3. **Enhanced Constraints**: Type-aware CHECK constraints on all columns
4. **Migration Support**: Automated migration tools for existing data
5. **Multi-tenant Support**: Proper RLS policies and organization_id fields

### API Layer
1. **Type Conversion**: Automatic conversion between database and API formats
2. **Validation Endpoints**: Real-time validation services
3. **Error Handling**: Detailed validation error messages
4. **Performance**: Cached type definitions for optimal performance
5. **Field Mapping Integration**: Automatic snake_case ↔ camelCase conversion

### UI Layer
1. **Typed Components**: Components that automatically adapt based on type definitions
2. **Real-time Validation**: Client-side validation using the same rules as server-side
3. **Dynamic Forms**: Forms that generate based on type metadata
4. **Error Display**: Consistent error message formatting
5. **Organization Context**: Proper handling of multi-tenant data

## Migration Strategy

### Phase 1: Type System Infrastructure
- [ ] Create system_metadata schema
- [ ] Create type_definitions table
- [ ] Implement validation functions
- [ ] Deploy type conversion services

### Phase 2: Table Migration
- [ ] Add type constraints to existing tables
- [ ] Migrate existing data to conform to types
- [ ] Update API endpoints to use type conversion

### Phase 3: UI Integration
- [ ] Implement typed form components
- [ ] Update existing forms to use typed fields
- [ ] Add client-side validation

## Assumptions
- PostgreSQL with JSONB support for schema definitions
- JSON Schema standard for validation rules
- TypeScript/Deno for Edge Functions
- Dart for Flutter frontend (web and native)
- Python for AI backend
- Supabase for database access and RLS policies

## Review Flags
- [ ] Database validation function performance impact
- [ ] Type conversion overhead in API responses  
- [ ] Client-side validation bundle size
- [ ] Migration complexity for existing data

## Completeness Checklist
- [ ] Type registry schema and implementation
- [ ] SQL validation framework
- [ ] Type conversion layer
- [ ] Field mapping integration
- [ ] UI component integration
- [ ] Supabase edge function services
- [ ] Migration strategy and data integrity
- [ ] Testing framework specification
- [ ] Implementation guidelines
);
  return emailRegExp.hasMatch(email);
}

// Custom exceptions
class ValidationException implements Exception {
  final String code;
  final String message;
  final Map<String, dynamic>? errors;
  final String? field;
  final dynamic value;
  
  ValidationException(this.code, this.message, {this.errors, this.field, this.value});
  
  @override
  String toString() => '$code: $message';
}

class DatabaseException implements Exception {
  final String code;
  final String message;
  
  DatabaseException(this.code, this.message);
  
  @override
  String toString() => '$code: $message';
}
```

**Rationale:**
- Consistent validation and conversion in edge functions
- Reuse of database validation logic
- Strong typing for API requests and responses
- Multi-tenant support with organization context
- Proper field mapping integration

### Implementation in Flutter UI Components

Example Flutter implementation for type-based form generation:
```dart
// arioncomply-v1/frontend-flutter/lib/services/typed_form_service.dart

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

// TypedFormController - Handles data management for dynamically generated forms
class TypedFormController {
  final String entityName;
  final SupabaseClient supabase = Supabase.instance.client;
  final _secureStorage = const FlutterSecureStorage();
  
  Map<String, dynamic>? uiSchema;
  Map<String, dynamic> formData = {};
  Map<String, String> errors = {};
  bool isLoading = true;
  
  TypedFormController({required this.entityName});
  
  // Load UI schema from database
  Future<void> loadSchema() async {
    try {
      isLoading = true;
      
      // Set organization context for RLS
      final orgId = await _secureStorage.read(key: 'organization_id');
      await supabase.rpc('set_config', params: {
        'setting_name': 'app.current_organization_id',
        'setting_value': orgId,
        'is_local': true
      });
      
      // Get UI schema
      final response = await supabase.rpc('generate_ui_schema', 
        params: {'entity_name': entityName}
      );
      
      if (response.error != null) {
        throw response.error!;
      }
      
      uiSchema = response.data;
      
      // Initialize form data with defaults
      final fields = uiSchema?['fields'] as Map<String, dynamic>? ?? {};
      for (final entry in fields.entries) {
        final fieldName = entry.key;
        final fieldSchema = entry.value;
        
        if (fieldSchema['props'] != null && 
            fieldSchema['props']['defaultValue'] != null) {
          formData[fieldName] = fieldSchema['props']['defaultValue'];
        }
      }
      
    } catch (e) {
      print('Error loading schema: $e');
    } finally {
      isLoading = false;
    }
  }
  
  // Handle field changes
  void handleChange(String fieldName, dynamic value) {
    formData[fieldName] = value;
    
    // Clear field error
    if (errors.containsKey(fieldName)) {
      errors.remove(fieldName);
    }
  }
  
  // Validate form
  Future<bool> validate() async {
    if (uiSchema == null) return false;
    
    try {
      final response = await supabase.rpc(
        'validate_entity_data',
        params: {
          'entity_name': entityName,
          'entity_data': formData
        }
      );
      
      if (response.error != null) {
        throw response.error!;
      }
      
      if (!(response.data['valid'] as bool)) {
        final validationErrors = response.data['errors'] as Map<String, dynamic>;
        errors = validationErrors.map((key, value) => 
          MapEntry(key, value.toString()));
        return false;
      }
      
      errors = {};
      return true;
    } catch (e) {
      print('Validation error: $e');
      return false;
    }
  }
  
  // Submit form
  Future<Map<String, dynamic>?> handleSubmit() async {
    final isValid = await validate();
    
    if (!isValid) return null;
    
    try {
      // Convert to database format
      final convertResponse = await supabase.rpc(
        'convert_entity_data_to_db',
        params: {
          'entity_name': entityName,
          'entity_data': formData
        }
      );
      
      if (convertResponse.error != null) {
        throw convertResponse.error!;
      }
      
      final convertedData = convertResponse.data;
      
      // Submit to database
      final operation = formData.containsKey('id') ? 'update' : 'insert';
      dynamic result;
      
      if (operation == 'insert') {
        result = await supabase
          .from(entityName)
          .insert(convertedData)
          .select()
          .single();
      } else {
        result = await supabase
          .from(entityName)
          .update(convertedData)
          .eq('id', formData['id'])
          .select()
          .single();
      }
      
      if (result.error != null) {
        throw result.error!;
      }
      
      // Convert result back to API format
      final apiResponse = await supabase.rpc(
        'convert_record_to_api',
        params: {
          'record_data': result.data,
          'entity_name': entityName
        }
      );
      
      if (apiResponse.error != null) {
        throw apiResponse.error!;
      }
      
      return apiResponse.data;
    } catch (e) {
      print('Submission error: $e');
      return null;
    }
  }
}

// TypedFormWidget - Dynamic form generation based on type definitions
class TypedFormWidget extends StatefulWidget {
  final String entityName;
  final Function(Map<String, dynamic>)? onSubmit;
  
  const TypedFormWidget({
    Key? key, 
    required this.entityName,
    this.onSubmit
  }) : super(key: key);
  
  @override
  State<TypedFormWidget> createState() => _TypedFormWidgetState();
}

class _TypedFormWidgetState extends State<TypedFormWidget> {
  late TypedFormController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = TypedFormController(entityName: widget.entityName);
    _loadSchema();
  }
  
  Future<void> _loadSchema() async {
    await _controller.loadSchema();
    setState(() {});
  }
  
  @override
  Widget build(BuildContext context) {
    if (_controller.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    
    if (_controller.uiSchema == null) {
      return const Center(child: Text('Failed to load form schema'));
    }
    
    final fields = _controller.uiSchema!['fields'] as Map<String, dynamic>;
    
    return Column(
      children: [
        ...fields.entries.map((entry) {
          final fieldName = entry.key;
          final fieldSchema = entry.value as Map<String, dynamic>;
          return _buildField(fieldName, fieldSchema);
        }).toList(),
        
        const SizedBox(height: 20),
        
        ElevatedButton(
          onPressed: () async {
            final result = await _controller.handleSubmit();
            if (result != null && widget.onSubmit != null) {
              widget.onSubmit!(result);
            }
          },
          child: const Text('Submit'),
        ),
      ],
    );
  }
  
  Widget _buildField(String fieldName, Map<String, dynamic> fieldSchema) {
    final fieldType = fieldSchema['type'] as String;
    final component = fieldSchema['component'] as String?;
    final label = fieldSchema['label'] as String;
    final props = fieldSchema['props'] as Map<String, dynamic>? ?? {};
    final errorText = _controller.errors[fieldName];
    
    // Build different widgets based on field type and component
    switch (component) {
      case 'TextField':
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: TextFormField(
            decoration: InputDecoration(
              labelText: label,
              errorText: errorText,
              border: const OutlineInputBorder(),
            ),
            onChanged: (value) => setState(() {
              _controller.handleChange(fieldName, value);
            }),
            initialValue: _controller.formData[fieldName]?.toString(),
          ),
        );
        
      case 'DatePicker':
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: InkWell(
            onTap: () async {
              final DateTime? picked = await showDatePicker(
                context: context,
                initialDate: _controller.formData[fieldName] != null
                    ? DateTime.parse(_controller.formData[fieldName])
                    : DateTime.now(),
                firstDate: DateTime(2000),
                lastDate: DateTime(2100),
              );
              
              if (picked != null) {
                setState(() {
                  _controller.handleChange(fieldName, picked.toIso8601String());
                });
              }
            },
            child: InputDecorator(
              decoration: InputDecoration(
                labelText: label,
                errorText: errorText,
                border: const OutlineInputBorder(),
              ),
              child: Text(
                _controller.formData[fieldName] != null
                    ? DateTime.parse(_controller.formData[fieldName]).toString().split(' ')[0]
                    : 'Select date',
              ),
            ),
          ),
        );
        
      case 'Select':
        final options = fieldSchema['validation_schema']?['enum'] as List<dynamic>? ?? [];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: label,
              errorText: errorText,
              border: const OutlineInputBorder(),
            ),
            value: _controller.formData[fieldName]?.toString(),
            items: options.map((option) {
              return DropdownMenuItem<String>(
                value: option.toString(),
                child: Text(option.toString()),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _controller.handleChange(fieldName, value);
                });
              }
            },
          ),
        );
        
      case 'Checkbox':
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: CheckboxListTile(
            title: Text(label),
            subtitle: errorText != null ? Text(errorText, style: TextStyle(color: Theme.of(context).colorScheme.error)) : null,
            value: _controller.formData[fieldName] == true,
            onChanged: (value) {
              setState(() {
                _controller.handleChange(fieldName, value ?? false);
              });
            },
          ),
        );
        
      case 'StatusBadge':
        // This is a display-only component, not editable
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: InputDecorator(
            decoration: InputDecoration(
              labelText: label,
              border: const OutlineInputBorder(),
            ),
            child: Text(_controller.formData[fieldName]?.toString() ?? 'Unknown'),
          ),
        );
        
      default:
        // Fallback to basic text field
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: TextFormField(
            decoration: InputDecoration(
              labelText: label,
              errorText: errorText,
              border: const OutlineInputBorder(),
            ),
            onChanged: (value) => setState(() {
              _controller.handleChange(fieldName, value);
            }),
            initialValue: _controller.formData[fieldName]?.toString(),
          ),
        );
    }
  }
}
```

**Rationale:**
- Type-driven form generation simplifies UI development
- Consistent validation across all form components
- Automatic field mapping based on entity registry
- Multi-tenant support with organization context
- Proper handling of camelCase in UI and snake_case in database

## 8. Migration and Data Integrity

### Type System Migration

```sql
-- Migration script for existing tables
DO $$
DECLARE
    table_record RECORD;
    column_record RECORD;
    type_mapping JSONB := '{
        "subscription_status": "subscription_status",
        "plan_amount": "currency_amount", 
        "contact_email": "email_address"
    }';
BEGIN
    -- Add validation version tracking to existing tables
    FOR table_record IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name LIKE '%subscription%'
    LOOP
        -- Add validation tracking columns
        EXECUTE format('ALTER TABLE %I ADD COLUMN IF NOT EXISTS validation_version INTEGER DEFAULT 1', table_record.table_name);
        EXECUTE format('ALTER TABLE %I ADD COLUMN IF NOT EXISTS validation_errors JSONB DEFAULT ''[]''::jsonb', table_record.table_name);
        
        -- Update constraints based on type mappings
        FOR column_record IN 
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = table_record.table_name 
            AND table_schema = 'public'
        LOOP
            IF type_mapping ? column_record.column_name THEN
                -- Add typed constraint
                EXECUTE format('ALTER TABLE %I ADD CONSTRAINT %I_type_check CHECK (validate_field_type(%I, %L))',
                    table_record.table_name,
                    column_record.column_name,
                    column_record.column_name,
                    type_mapping->>column_record.column_name
                );
            END IF;
        END LOOP;
    END LOOP;
END $$;
```

### Data Validation Report

```sql
-- Generate validation report for existing data
CREATE OR REPLACE FUNCTION generate_validation_report()
RETURNS TABLE(
    table_name TEXT,
    column_name TEXT,
    type_name TEXT,
    invalid_count BIGINT,
    total_count BIGINT,
    invalid_percentage DECIMAL
) AS $$
DECLARE
    table_rec RECORD;
    col_rec RECORD;
BEGIN
    FOR table_rec IN 
        SELECT t.table_name
        FROM information_schema.tables t
        WHERE t.table_schema = 'public'
    LOOP
        FOR col_rec IN 
            SELECT c.column_name, td.type_name
            FROM information_schema.columns c
            JOIN system_metadata.entity_fields ef ON ef.entity_name = table_rec.table_name AND ef.field_name = c.column_name
            JOIN system_metadata.type_definitions td ON td.type_name = ef.field_type
            WHERE c.table_name = table_rec.table_name AND c.table_schema = 'public'
        LOOP
            RETURN QUERY EXECUTE format('
                SELECT %L::TEXT, %L::TEXT, %L::TEXT,
                       COUNT(*) FILTER (WHERE NOT validate_field_type(%I, %L)) as invalid_count,
                       COUNT(*) as total_count,
                       (COUNT(*) FILTER (WHERE NOT validate_field_type(%I, %L)) * 100.0 / COUNT(*)) as invalid_percentage
                FROM %I.%I
            ', table_rec.table_name, col_rec.column_name, col_rec.type_name,
               col_rec.column_name, col_rec.type_name,
               col_rec.column_name, col_rec.type_name,
               'public', table_rec.table_name);
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

## Implementation Guidelines

### Database Layer
1. **Type Registry**: Centralized type definitions with SQL, JSON Schema, and UI mappings
2. **Validation Functions**: Generic validation functions that work with type metadata
3. **Enhanced Constraints**: Type-aware CHECK constraints on all columns
4. **Migration Support**: Automated migration tools for existing data
5. **Multi-tenant Support**: Proper RLS policies and organization_id fields

### API Layer
1. **Type Conversion**: Automatic conversion between database and API formats
2. **Validation Endpoints**: Real-time validation services
3. **Error Handling**: Detailed validation error messages
4. **Performance**: Cached type definitions for optimal performance
5. **Field Mapping Integration**: Automatic snake_case ↔ camelCase conversion

### UI Layer
1. **Typed Components**: Components that automatically adapt based on type definitions
2. **Real-time Validation**: Client-side validation using the same rules as server-side
3. **Dynamic Forms**: Forms that generate based on type metadata
4. **Error Display**: Consistent error message formatting
5. **Organization Context**: Proper handling of multi-tenant data

## Migration Strategy

### Phase 1: Type System Infrastructure
- [ ] Create system_metadata schema
- [ ] Create type_definitions table
- [ ] Implement validation functions
- [ ] Deploy type conversion services

### Phase 2: Table Migration
- [ ] Add type constraints to existing tables
- [ ] Migrate existing data to conform to types
- [ ] Update API endpoints to use type conversion

### Phase 3: UI Integration
- [ ] Implement typed form components
- [ ] Update existing forms to use typed fields
- [ ] Add client-side validation

## Assumptions
- PostgreSQL with JSONB support for schema definitions
- JSON Schema standard for validation rules
- Dart for type safety in application layer
- Flutter for UI components (both web and native)
- Supabase for database access and RLS policies

## Review Flags
- [ ] Database validation function performance impact
- [ ] Type conversion overhead in API responses  
- [ ] Client-side validation bundle size
- [ ] Migration complexity for existing data

## Completeness Checklist
- [ ] Type registry schema and implementation
- [ ] SQL validation framework
- [ ] Type conversion layer
- [ ] Field mapping integration
- [ ] UI component integration
- [ ] Supabase edge function services
- [ ] Migration strategy and data integrity
- [ ] Testing framework specification
- [ ] Implementation guidelines