# Logging and Observability (Placeholder)

Decisions
- Mandatory persistence: All events must be written to DB (no exceptions).
- Use `api_request_logs` and `api_event_logs` for traceability.
- Capture `ai_call`, `db_read`, `db_write`, `response_sent`, `stream_finished`.
- Avoid logging sensitive content; store counts, hashes, and metadata.

Design Points
- Token usage/cost per `ai_call`; model, provider, latency, status.
- Linkage: requestId, sessionId, processId, correlationId.
- Redaction flags and truncation policy.

Open Questions
- SLOs and alert thresholds; dashboards and queries.

Next Steps
- Enforce `LOGGING_REQUIRED=true` in Edge and Backend; fail fast if env is missing.
- Extend logger utilities to emit the new event types with minimal retry.
- Add example SQL queries and a quickstart dashboard recipe.
