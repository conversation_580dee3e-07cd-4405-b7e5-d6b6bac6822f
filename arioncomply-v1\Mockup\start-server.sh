#!/bin/bash
#
# File: arioncomply-v1/Mockup/start-server.sh
# File Description: Start the ArionComply UI Mockup server for design and functionality preview
# Purpose: Serve static HTML/CSS/JS mockup files for UI design validation and user testing
#
# Usage: bash start-server.sh [--port PORT] [--host HOST]
#
# Setup Instructions:
#   1. Run from Mockup directory: cd arioncomply-v1/Mockup
#   2. Execute script: bash start-server.sh
#   3. Server will be available at http://localhost:8080
#   4. Main entry point: index.html (ArionComply dashboard)
#
# Options:
#   --port PORT    Set server port (default: 8080)
#   --host HOST    Set server host (default: 127.0.0.1)
#   --help         Show usage information
#
# Available Mockup Pages:
#   /                     Main dashboard (index.html)
#   /chatInterface.html   Chat interface mockup
#   /dashboard.html       Analytics dashboard
#   /documentEditor.html  Document editor interface
#   /workflowEngine.html  Workflow management
#   /listView.html        Data list views
#   /searchInterface.html Search functionality
#
# Dependencies: Python 3.x (for http.server), curl (for health checks)
# Security/RLS: Static files only - no backend integration
# Notes: Mockup represents potential functional design; stays as simple HTML/CSS/JS
#

set -euo pipefail

# Always run from this script's directory for proper file serving
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd "$SCRIPT_DIR"

# Default server configuration
DEFAULT_HOST="127.0.0.1"
DEFAULT_PORT="8080"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --port)
            if [[ $# -gt 1 && "$2" =~ ^[0-9]+$ ]]; then
                DEFAULT_PORT="$2"
                shift 2
            else
                echo "❌ --port requires a numeric value"
                exit 1
            fi
            ;;
        --host)
            if [[ $# -gt 1 ]]; then
                DEFAULT_HOST="$2"
                shift 2
            else
                echo "❌ --host requires a hostname or IP"
                exit 1
            fi
            ;;
        --help|-h)
            echo "Usage: $0 [--port PORT] [--host HOST]"
            echo "Start ArionComply UI Mockup server"
            echo ""
            echo "Options:"
            echo "  --port PORT    Server port (default: 8080)"
            echo "  --host HOST    Server host (default: 127.0.0.1)"
            echo "  --help         Show this help message"
            echo ""
            echo "Available mockup pages:"
            echo "  /                     Main dashboard"
            echo "  /chatInterface.html   Chat interface"
            echo "  /dashboard.html       Analytics dashboard"
            echo "  /documentEditor.html  Document editor"
            echo "  /workflowEngine.html  Workflow management"
            echo "  /listView.html        Data views"
            exit 0
            ;;
        *)
            echo "❌ Unknown argument: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

echo "🎨 Starting ArionComply UI Mockup server..."

# Verify required files exist
if [ ! -f "index.html" ]; then
    echo "❌ index.html not found!"
    echo "💡 Run from Mockup directory: cd arioncomply-v1/Mockup"
    exit 1
fi

if [ ! -f "mystyles.css" ]; then
    echo "❌ mystyles.css not found!"
    echo "💡 Ensure you're in the correct Mockup directory"
    exit 1
fi

echo "✅ Mockup files found"

# Count available mockup pages for user information
HTML_FILES_COUNT=$(find . -maxdepth 1 -name "*.html" | wc -l | xargs)
echo "📄 Found $HTML_FILES_COUNT mockup pages"

# Kill any existing server on the target port
echo "🧹 Cleaning up any existing servers on port $DEFAULT_PORT..."
lsof -ti:"$DEFAULT_PORT" | xargs kill -9 2>/dev/null || true

# Start HTTP server in background
echo "🌐 Starting mockup server on http://$DEFAULT_HOST:$DEFAULT_PORT..."
python3 -m http.server "$DEFAULT_PORT" --bind "$DEFAULT_HOST" > .mockup-server.log 2>&1 &
SERVER_PID=$!

# Wait for server to start
sleep 2

# Verify server is responding
HEALTH_URL="http://$DEFAULT_HOST:$DEFAULT_PORT/"
if curl -s --connect-timeout 3 "$HEALTH_URL" > /dev/null 2>&1; then
    echo "✅ Server health check passed"
else
    echo "❌ Failed to start mockup server"
    kill -9 "$SERVER_PID" 2>/dev/null || true
    exit 1
fi

# Save process ID for cleanup
echo "$SERVER_PID" > .mockup-server.pid

# Open browser automatically
URL="http://$DEFAULT_HOST:$DEFAULT_PORT"
echo "🌐 Opening browser..."
if command -v open >/dev/null 2>&1; then
    open "$URL"
elif command -v xdg-open >/dev/null 2>&1; then
    xdg-open "$URL" >/dev/null 2>&1 || true
else
    echo "ℹ️  Please open your browser to: $URL"
fi

echo ""
echo "✅ ArionComply UI Mockup running on $URL"
echo "✅ Browser opened automatically"
echo ""
echo "🎯 Available mockup interfaces:"
echo "   📊 Main Dashboard:     $URL"
echo "   💬 Chat Interface:     $URL/chatInterface.html"
echo "   📈 Analytics:          $URL/dashboard.html"
echo "   📝 Document Editor:    $URL/documentEditor.html"
echo "   ⚙️  Workflow Engine:    $URL/workflowEngine.html"
echo "   📋 List Views:         $URL/listView.html"
echo "   🔍 Search Interface:   $URL/searchInterface.html"
echo ""
echo "🔧 Development testing with other services:"
echo "   🧪 Workflow GUI:      http://localhost:10000 (testing)"
echo "   🤖 AI Backend:        http://localhost:9000 (when running)"
echo "   📡 Local LLMs:        ports 8081-8083"
echo ""
echo "💡 Server logs: tail -f .mockup-server.log"
echo "💡 To stop server: bash ./stop-server.sh"
echo "💡 Process ID: $SERVER_PID"
echo ""