// File: arioncomply-v1/supabase/functions/_shared/supabase.ts
// File Description: Supabase admin client bootstrap
// Purpose: Create service-role Supabase client for Edge logging/DB ops.
// Env: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY
// Notes: Only defined when both env vars are present.
import { createClient, SupabaseClient } from "https://esm.sh/@supabase/supabase-js@2";

const url = Deno.env.get("SUPABASE_URL");
const key = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
const loggingRequired = (Deno.env.get("LOGGING_REQUIRED") ?? "true").toLowerCase() === "true";

let cachedAdmin: SupabaseClient | undefined;

/** Create or return cached service-role Supabase admin client. */
export function getSupabaseAdmin(): SupabaseClient {
  if (cachedAdmin) return cachedAdmin;
  if (!url || !key) {
    const msg = "Supabase admin env missing (SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY)";
    if (loggingRequired) {
      throw new Error(msg);
    } else {
      console.warn(msg + "; LOGGING_REQUIRED=false allows continued execution.");
    }
  }
  cachedAdmin = createClient(url!, key!, {
    auth: { autoRefreshToken: false, persistSession: false },
  });
  return cachedAdmin;
}

// Backwards compatibility export
export const supabaseAdmin = ((): SupabaseClient | undefined => {
  try {
    return getSupabaseAdmin();
  } catch {
    return undefined;
  }
})();
