/**
 * API communication layer for Ingestion Pipeline Management GUI
 */

class APIClient {
    constructor(baseURL = '/api/v1') {
        this.baseURL = baseURL;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
        };
        
        // Request interceptors
        this.requestInterceptors = [];
        this.responseInterceptors = [];
        
        // Add default error handling
        this.addResponseInterceptor(
            response => response,
            error => this.handleError(error)
        );
    }
    
    /**
     * Add request interceptor
     * @param {Function} fulfilled - Success handler
     * @param {Function} rejected - Error handler
     */
    addRequestInterceptor(fulfilled, rejected) {
        this.requestInterceptors.push({ fulfilled, rejected });
    }
    
    /**
     * Add response interceptor
     * @param {Function} fulfilled - Success handler
     * @param {Function} rejected - Error handler
     */
    addResponseInterceptor(fulfilled, rejected) {
        this.responseInterceptors.push({ fulfilled, rejected });
    }
    
    /**
     * Apply request interceptors
     * @param {Object} config - Request configuration
     * @returns {Object} Modified configuration
     */
    async applyRequestInterceptors(config) {
        let modifiedConfig = { ...config };
        
        for (const interceptor of this.requestInterceptors) {
            try {
                modifiedConfig = await interceptor.fulfilled(modifiedConfig);
            } catch (error) {
                if (interceptor.rejected) {
                    return interceptor.rejected(error);
                }
                throw error;
            }
        }
        
        return modifiedConfig;
    }
    
    /**
     * Apply response interceptors
     * @param {Response} response - Fetch response
     * @returns {Response} Modified response
     */
    async applyResponseInterceptors(response) {
        let modifiedResponse = response;
        
        for (const interceptor of this.responseInterceptors) {
            try {
                if (response.ok) {
                    modifiedResponse = await interceptor.fulfilled(modifiedResponse);
                } else {
                    if (interceptor.rejected) {
                        modifiedResponse = await interceptor.rejected(modifiedResponse);
                    }
                }
            } catch (error) {
                if (interceptor.rejected) {
                    return interceptor.rejected(error);
                }
                throw error;
            }
        }
        
        return modifiedResponse;
    }
    
    /**
     * Make HTTP request
     * @param {string} endpoint - API endpoint
     * @param {Object} options - Request options
     * @returns {Promise} Response promise
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        
        const config = {
            method: 'GET',
            headers: { ...this.defaultHeaders },
            ...options,
            url
        };
        
        // Apply request interceptors
        const modifiedConfig = await this.applyRequestInterceptors(config);
        
        try {
            const response = await fetch(modifiedConfig.url, {
                method: modifiedConfig.method,
                headers: modifiedConfig.headers,
                body: modifiedConfig.body
            });
            
            // Apply response interceptors
            const modifiedResponse = await this.applyResponseInterceptors(response);
            
            if (!modifiedResponse.ok) {
                throw new APIError(
                    `HTTP ${modifiedResponse.status}: ${modifiedResponse.statusText}`,
                    modifiedResponse.status,
                    endpoint
                );
            }
            
            return modifiedResponse;
        } catch (error) {
            if (error instanceof APIError) {
                throw error;
            }
            throw new APIError(error.message, 0, endpoint);
        }
    }
    
    /**
     * GET request
     * @param {string} endpoint - API endpoint
     * @param {Object} params - Query parameters
     * @returns {Promise} Response promise
     */
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        
        const response = await this.request(url);
        return response.json();
    }
    
    /**
     * POST request
     * @param {string} endpoint - API endpoint
     * @param {Object} data - Request body data
     * @returns {Promise} Response promise
     */
    async post(endpoint, data = {}) {
        const response = await this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
        return response.json();
    }
    
    /**
     * PUT request
     * @param {string} endpoint - API endpoint
     * @param {Object} data - Request body data
     * @returns {Promise} Response promise
     */
    async put(endpoint, data = {}) {
        const response = await this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
        return response.json();
    }
    
    /**
     * DELETE request
     * @param {string} endpoint - API endpoint
     * @returns {Promise} Response promise
     */
    async delete(endpoint) {
        const response = await this.request(endpoint, {
            method: 'DELETE'
        });
        return response.json();
    }
    
    /**
     * Handle API errors
     * @param {Error} error - Error object
     * @returns {Promise} Rejected promise
     */
    handleError(error) {
        console.error('API Error:', error);
        
        // Show toast notification
        if (window.showToast) {
            window.showToast(error.message, 'error');
        }
        
        // Emit error event
        if (window.eventEmitter) {
            window.eventEmitter.emit('apiError', error);
        }
        
        return Promise.reject(error);
    }
}

/**
 * Custom API Error class
 */
class APIError extends Error {
    constructor(message, status = 0, endpoint = '') {
        super(message);
        this.name = 'APIError';
        this.status = status;
        this.endpoint = endpoint;
    }
}

/**
 * Embedding Pipeline API
 */
class EmbeddingAPI extends APIClient {
    constructor() {
        super('/api/v1/embedding');
    }
    
    /**
     * Get system status
     * @returns {Promise<Object>} System status
     */
    async getSystemStatus() {
        return this.get('/status');
    }
    
    /**
     * Get current pipeline info
     * @returns {Promise<Object>} Current pipeline information
     */
    async getCurrentPipeline() {
        return this.get('/pipeline/current');
    }
    
    /**
     * List available pipelines
     * @returns {Promise<Array>} Available pipelines
     */
    async listPipelines() {
        return this.get('/pipelines');
    }
    
    /**
     * Get pipeline details
     * @param {string} pipelineName - Pipeline name
     * @returns {Promise<Object>} Pipeline details
     */
    async getPipeline(pipelineName) {
        return this.get(`/pipelines/${pipelineName}`);
    }
    
    /**
     * Switch to pipeline
     * @param {string} pipelineName - Pipeline name
     * @returns {Promise<Object>} Switch result
     */
    async switchPipeline(pipelineName) {
        return this.post('/pipeline/switch', { pipeline_name: pipelineName });
    }
    
    /**
     * Perform health check on all pipelines
     * @returns {Promise<Object>} Health check results
     */
    async healthCheck() {
        return this.get('/health');
    }
    
    /**
     * Perform health check on specific pipeline
     * @param {string} pipelineName - Pipeline name
     * @returns {Promise<Object>} Health check result
     */
    async healthCheckPipeline(pipelineName) {
        return this.get(`/pipelines/${pipelineName}/health`);
    }
    
    /**
     * Generate test embedding
     * @param {Array<string>} texts - Text to embed
     * @param {string} pipelineName - Pipeline name (optional)
     * @param {string} traceId - Trace ID (optional)
     * @returns {Promise<Object>} Embedding result
     */
    async generateEmbedding(texts, pipelineName = null, traceId = null) {
        const data = { texts };
        if (pipelineName) data.pipeline_name = pipelineName;
        if (traceId) data.trace_id = traceId;
        
        return this.post('/generate', data);
    }
    
    /**
     * Compare embeddings across pipelines
     * @param {Array<string>} texts - Texts to embed
     * @param {Array<string>} pipelineNames - Pipeline names to compare (optional)
     * @returns {Promise<Object>} Comparison results
     */
    async compareEmbeddings(texts, pipelineNames = null) {
        const data = { texts };
        if (pipelineNames) data.pipeline_names = pipelineNames;
        
        return this.post('/compare', data);
    }
    
    /**
     * Get performance metrics
     * @param {string} timeRange - Time range (1h, 6h, 24h, 7d, 30d)
     * @returns {Promise<Object>} Performance metrics
     */
    async getMetrics(timeRange = '24h') {
        return this.get('/metrics', { time_range: timeRange });
    }
    
    /**
     * Get pipeline configuration
     * @returns {Promise<Object>} Configuration
     */
    async getConfiguration() {
        return this.get('/config');
    }
    
    /**
     * Update pipeline configuration
     * @param {Object} config - New configuration
     * @returns {Promise<Object>} Update result
     */
    async updateConfiguration(config) {
        return this.put('/config', config);
    }
}

/**
 * Document Ingestion API
 */
class IngestionAPI extends APIClient {
    constructor() {
        super('/api/v1/ingestion');
    }
    
    /**
     * Get ingestion queue status
     * @returns {Promise<Object>} Queue status
     */
    async getQueueStatus() {
        return this.get('/queue/status');
    }
    
    /**
     * Get ingestion history
     * @param {number} limit - Number of items to return
     * @param {number} offset - Offset for pagination
     * @returns {Promise<Object>} Ingestion history
     */
    async getHistory(limit = 50, offset = 0) {
        return this.get('/history', { limit, offset });
    }
    
    /**
     * Upload document for ingestion
     * @param {FormData} formData - Form data with files
     * @returns {Promise<Object>} Upload result
     */
    async uploadDocument(formData) {
        // Override content-type to let browser set it with boundary
        const response = await this.request('/upload', {
            method: 'POST',
            headers: {}, // Let browser set Content-Type
            body: formData
        });
        return response.json();
    }
    
    /**
     * Get ingestion job status
     * @param {string} jobId - Job ID
     * @returns {Promise<Object>} Job status
     */
    async getJobStatus(jobId) {
        return this.get(`/jobs/${jobId}`);
    }
    
    /**
     * Cancel ingestion job
     * @param {string} jobId - Job ID
     * @returns {Promise<Object>} Cancellation result
     */
    async cancelJob(jobId) {
        return this.post(`/jobs/${jobId}/cancel`);
    }
    
    /**
     * Retry failed ingestion job
     * @param {string} jobId - Job ID
     * @returns {Promise<Object>} Retry result
     */
    async retryJob(jobId) {
        return this.post(`/jobs/${jobId}/retry`);
    }
    
    /**
     * Get processing statistics
     * @param {string} timeRange - Time range (1h, 6h, 24h, 7d, 30d)
     * @returns {Promise<Object>} Processing statistics
     */
    async getStatistics(timeRange = '24h') {
        return this.get('/statistics', { time_range: timeRange });
    }
}

/**
 * Vector Storage API
 */
class StorageAPI extends APIClient {
    constructor() {
        super('/api/v1/storage');
    }
    
    /**
     * Get storage overview
     * @returns {Promise<Object>} Storage overview
     */
    async getOverview() {
        return this.get('/overview');
    }
    
    /**
     * Get storage statistics by dimension
     * @returns {Promise<Object>} Dimension statistics
     */
    async getDimensionStats() {
        return this.get('/dimensions');
    }
    
    /**
     * Get storage statistics by pipeline
     * @returns {Promise<Object>} Pipeline statistics
     */
    async getPipelineStats() {
        return this.get('/pipelines');
    }
    
    /**
     * Get database health
     * @returns {Promise<Object>} Database health status
     */
    async getHealth() {
        return this.get('/health');
    }
    
    /**
     * Run database maintenance
     * @param {string} operation - Maintenance operation
     * @returns {Promise<Object>} Maintenance result
     */
    async runMaintenance(operation) {
        return this.post('/maintenance', { operation });
    }
}

/**
 * System API
 */
class SystemAPI extends APIClient {
    constructor() {
        super('/api/v1/system');
    }
    
    /**
     * Get system information
     * @returns {Promise<Object>} System information
     */
    async getInfo() {
        return this.get('/info');
    }
    
    /**
     * Get system health
     * @returns {Promise<Object>} System health
     */
    async getHealth() {
        return this.get('/health');
    }
    
    /**
     * Get system logs
     * @param {string} level - Log level
     * @param {number} limit - Number of entries
     * @returns {Promise<Object>} System logs
     */
    async getLogs(level = 'info', limit = 100) {
        return this.get('/logs', { level, limit });
    }
    
    /**
     * Get system configuration
     * @returns {Promise<Object>} System configuration
     */
    async getConfiguration() {
        return this.get('/config');
    }
    
    /**
     * Update system configuration
     * @param {Object} config - New configuration
     * @returns {Promise<Object>} Update result
     */
    async updateConfiguration(config) {
        return this.put('/config', config);
    }
}

// Create global API instances
const embeddingAPI = new EmbeddingAPI();
const ingestionAPI = new IngestionAPI();
const storageAPI = new StorageAPI();
const systemAPI = new SystemAPI();

// Add authentication interceptor
function addAuthInterceptor(token) {
    const authInterceptor = (config) => {
        config.headers['Authorization'] = `Bearer ${token}`;
        return config;
    };
    
    embeddingAPI.addRequestInterceptor(authInterceptor);
    ingestionAPI.addRequestInterceptor(authInterceptor);
    storageAPI.addRequestInterceptor(authInterceptor);
    systemAPI.addRequestInterceptor(authInterceptor);
}

// Add retry interceptor for failed requests
function addRetryInterceptor(maxRetries = 3) {
    const retryInterceptor = async (error) => {
        const config = error.config;
        
        if (!config.retryCount) {
            config.retryCount = 0;
        }
        
        if (config.retryCount < maxRetries) {
            config.retryCount += 1;
            
            // Exponential backoff
            const delay = Math.pow(2, config.retryCount) * 1000;
            await new Promise(resolve => setTimeout(resolve, delay));
            
            // Retry the request
            return fetch(config.url, {
                method: config.method,
                headers: config.headers,
                body: config.body
            });
        }
        
        return Promise.reject(error);
    };
    
    embeddingAPI.addResponseInterceptor(null, retryInterceptor);
    ingestionAPI.addResponseInterceptor(null, retryInterceptor);
    storageAPI.addResponseInterceptor(null, retryInterceptor);
    systemAPI.addResponseInterceptor(null, retryInterceptor);
}

// Export for module systems (if used)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        APIClient,
        APIError,
        EmbeddingAPI,
        IngestionAPI,
        StorageAPI,
        SystemAPI,
        embeddingAPI,
        ingestionAPI,
        storageAPI,
        systemAPI,
        addAuthInterceptor,
        addRetryInterceptor
    };
}