id: Q180
query: >-
  What if we can’t explain how our AI system made a particular decision?
packs:
  - "GDPR:2016"
  - "EUAI:2024"
primary_ids:
  - "GDPR:2016/Art.22"
  - "EUAI:2024/Art.52"
overlap_ids: []
capability_tags:
  - "Report"
  - "Draft Doc"
flags:
  - "LOCAL LAW_CHECK"
sources:
  - title: "GDPR — Automated Decision-Making"
    id: "GDPR:2016/Art.22"
    locator: "Article 22"
  - title: "EU AI Act — Right to Explanation"
    id: "EUAI:2024/Art.52"
    locator: "Article 52"
ui:
  cards_hint:
    - "Explanation obligations"
  actions:
    - type: "start_workflow"
      target: "ai_explanation"
      label: "Generate Explanation"
    - type: "open_register"
      target: "explanation_registry"
      label: "View Explanations"
output_mode: "both"
graph_required: false
notes: "Document decision logic, fallback processes, and human review options"
---
### 180) What if we can’t explain how our AI system made a particular decision?

**Standard terms)**  
- **Automated decision-making (GDPR Art. 22):** the right not to be subject to solely automated decisions with legal or similarly significant effects.  
- **Right to explanation (EU AI Act Art. 52):** requirement to provide meaningful information about the logic, significance, and intended consequences of AI-driven decisions.

**Plain-English answer**  
Both GDPR and the EU AI Act require you to explain impactful automated decisions to affected individuals. If your model is a “black box,” you can:  
1. Implement **post-hoc explanation** techniques (e.g., LIME, SHAP) to approximate decision logic.  
2. Maintain a **human-in-the-loop** process for high-risk decisions.  
3. Document fallback procedures, decision thresholds, and appeal mechanisms.

**Applies to**  
- **Primary:** GDPR Article 22; EU AI Act Article 52

**Why it matters**  
Ensures transparency, builds user trust, and avoids legal penalties or invalidated decisions.

**Do next in our platform**  
1. Run the **Generate Explanation** workflow.  
2. Store each explanation in the **Explanation Registry**.  
3. Assign human review tasks for any decision flagged as high-impact.

**How our platform will help**  
- **[Report]** Auto-generates explanation summaries with key features and caveats.  
- **[Draft Doc]** Produces user-facing explanation templates.  
- **[Workflow]** Orchestrates human reviews and logs outcomes for audit.

**Likely follow-ups**  
- Which explanation methods satisfy regulators?  
- How do we protect IP while giving meaningful explanations?

**Sources**  
- GDPR Article 22; EU AI Act Article 52

**Legal note:** This is not legal advice. Validate explanation adequacy with qualified counsel and refer to supervisory authority guidance.  

