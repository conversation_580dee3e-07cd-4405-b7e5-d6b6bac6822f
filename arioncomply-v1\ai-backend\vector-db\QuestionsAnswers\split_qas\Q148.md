id: Q148
query: >-
  What if auditors ask for something we don't have or don't understand?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/9.2"
overlap_ids:
  - "ISO27001:2022/7.5"
capability_tags:
  - "Tracker"
  - "Workflow"
  - "Evidence-Guided"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Internal Audit"
    id: "ISO27001:2022/9.2"
    locator: "Clause 9.2"
  - title: "ISO/IEC 27001:2022 — Documented Information"
    id: "ISO27001:2022/7.5"
    locator: "Clause 7.5"
ui:
  cards_hint:
    - "Audit request tracker"
  actions:
    - type: "open_tracker"
      target: "evidence_requests"
      label: "Log Auditor Request"
    - type: "start_workflow"
      target: "audit_request_resolution"
      label: "Resolve Audit Request"
output_mode: "both"
graph_required: false
notes: "Capture all auditor requests, route for clarification, and document resolution or corrective actions."
---
### 148) What if auditors ask for something we don't have or don't understand?

**Standard term(s)**  
- **Internal audit (ISO 27001 Cl. 9.2):** process to check compliance and readiness for certification.  
- **Documented information (ISO 27001 Cl. 7.5):** required records and evidence.

**Plain-English answer**  
First, log the request in an **audit request tracker** and ask the auditor for clarification if the scope or intent is unclear. If you truly don’t have the item, determine whether it’s a required control or evidence, then either produce it promptly or record a corrective action to address the gap. Always respond professionally, showing you are taking steps to resolve the issue.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 9.2  
- **Also relevant/Overlaps:** ISO/IEC 27001:2022 Clause 7.5

**Why it matters**  
Transparent handling of requests builds trust with auditors and avoids nonconformities escalating.

**Do next in our platform**  
- Record the request in the **Evidence Requests** tracker.  
- Launch the **Audit Request Resolution** workflow to assign owners and deadlines.

**How our platform will help**  
- **[Tracker]** Central log for all evidence and clarification requests.  
- **[Workflow]** Structured resolution process with escalation paths.  
- **[Evidence-Guided]** Prompts for required documents and evidence sources.

**Likely follow-ups**  
- “What if the request is outside audit scope?” (Escalate to lead auditor for scope check) [LOCAL LAW CHECK]

**Sources**  
- ISO/IEC 27001:2022 Clause 9.2  
- ISO/IEC 27001:2022 Clause 7.5
