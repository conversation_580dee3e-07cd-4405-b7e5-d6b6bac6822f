-- File: arioncomply-v1/db/migrations/0010_realtime_publication.sql
-- File Description: Migration 0010 adds tables to the Supabase Realtime publication
-- Purpose: Ensure key tables (sessions/messages/documents) are broadcast by Realtime when enabled
-- Security/RLS: No policy changes; operates only if 'supabase_realtime' publication exists

DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_publication WHERE pubname = 'supabase_realtime') THEN
    EXECUTE 'ALTER PUBLICATION supabase_realtime ADD TABLE 
      conversation_sessions,
      conversation_messages,
      documents,
      document_versions';
  END IF;
END $$;
-- File: arioncomply-v1/db/migrations/0010_realtime_publication.sql
