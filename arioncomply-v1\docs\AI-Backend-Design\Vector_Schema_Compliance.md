# Vector Schema Compliance (Placeholder)

Decisions
- All vector tables must include `org_id`, audit fields, and RLS.

Design Points
- Tables: documents, chunks, embeddings; metadata jsonb; foreign keys; provenance.
- Policies: select/insert/update/delete scoping by `org_id` and roles.

Open Questions
- Migration strategy from current AI backend schemas to compliant ones.

Next Steps
- Draft compliant migrations in `ai-backend/supabase_migrations` with RLS and comments.
- Add tests to validate RLS and audit behavior.

