id: Q136
query: >-
  How do we explain automated decision-making to customers in plain English?
packs:
  - "GDPR:2016"
primary_ids:
  - "GDPR:2016/Art.13"
  - "GDPR:2016/Art.22"
overlap_ids: []
capability_tags:
  - "Draft Doc"
flags: []
sources:
  - title: "GDPR — Information to Data Subjects"
    id: "GDPR:2016/Art.13"
    locator: "Article 13"
  - title: "GDPR — Automated Decisions"
    id: "GDPR:2016/Art.22"
    locator: "Article 22"
ui:
  cards_hint:
    - "Transparency statement builder"
  actions:
    - type: "start_workflow"
      target: "transparency_statement"
      label: "Generate Statement"
output_mode: "both"
graph_required: false
notes: "Use simple examples and avoid legal jargon"
---
### 136) How do we explain automated decision-making to customers in plain English?

**Standard terms)**  
- **Transparency (GDPR Art. 13):** must inform about logic involved.  
- **Automated decisions (Art. 22):** rights to human intervention and explanation.

**Plain-English answer**  
Say, for example: “Our system uses your usage data to personalize offers. If you prefer a human review, just let us know.” Avoid legalese—explain what data you use, how it affects them, and how they can opt out or request review.

**Applies to**  
- **Primary:** GDPR Articles 13; 22

**Why it matters**  
Clear communication builds trust and meets legal requirements.

**Do next in our platform**  
- Launch **Transparency Statement** builder.  
- Customize for each AI feature.

**How our platform will help**  
- **[Draft Doc]** Pre-built plain-language templates.  
- **[Workflow]** Review and approval routing.

**Likely follow-ups**  
- “How do we handle requests for human review?” (Define process in policies)

**Sources**  
- GDPR Articles 13; 22
