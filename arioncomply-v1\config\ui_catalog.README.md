<!-- File: arioncomply-v1/config/ui_catalog.README.md -->
# UI Catalog JSON Reference

- File: `arioncomply-v1/config/ui_catalog.json`
- Purpose: Defines UI component metadata used by testing/demo UIs
- Structure: JSON object keyed by component/view with fields such as `name`, `props`, `dataBindings`
- Usage: Read-only at runtime by testing harness; not user-editable
- Notes: Non-breaking additions only for Phase 2; keep keys stable

