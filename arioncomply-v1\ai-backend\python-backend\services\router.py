"""
File: arioncomply-v1/ai-backend/python-backend/services/router.py
File Description: Backend chat router with sophisticated multi-tier AI retrieval orchestration
Purpose: Orchestrate intent classification → dual-vector retrieval → confidence scoring → model calls with progressive escalation
Inputs: messages[], Meta (request/org/user/session/trace), hints, explainability
Outputs: dict { provider, model, text, usage, suggestions }
Dependencies: HybridSearchOrchestrator, embedding services, Supabase logging, ChromaDB/Vector databases
Security/RLS: Org-scoped retrieval, secure API key handling, audit trail logging
Notes: Uses HybridSearchOrchestrator for multi-tier progressive escalation (Tier 0-3), confidence-based routing, dual-vector architecture
"""

from dataclasses import dataclass
from typing import Any, Dict, List, Optional
import time
import asyncio

from .logging.events import log_event
from .ingestion.embedder import embed_texts
from .retrieval import supabase_vector
from .retrieval.hybrid_search import hybrid_search
from .retrieval.hybrid_search_orchestration import HybridSearchOrchestrator, SearchParameters
from .prompts import registry as prompts
from .preprocessing.query_preprocessor import QueryPreprocessor
from .preprocessing.graph_crawler import GraphCrawler


@dataclass
class Meta:
    request_id: str
    org_id: Optional[str]
    user_id: Optional[str]
    session_id: Optional[str] = None
    traceparent: Optional[str] = None


async def handle_chat(
    *,
    messages: List[Dict[str, str]],
    meta: Meta,
    hints: Dict[str, Any],
    explainability: Dict[str, Any],
) -> Dict[str, Any]:
    # Basic guards
    if not meta.org_id:
        # In production: raise or return error envelope; logs require org_id
        return {"provider": "backend", "model": "n/a", "text": "Missing org_id"}
    
    # Initialize deterministic preprocessing pipeline
    query_preprocessor = QueryPreprocessor()
    last_user_message = next((m["content"] for m in reversed(messages) if m.get("role") == "user"), "")
    framework_hint = hints.get("framework") or hints.get("standard")
    
    # Initialize sophisticated search orchestrator
    search_orchestrator = HybridSearchOrchestrator()
    
    # Stage 0: Deterministic Query Preprocessing
    # This pipeline can resolve many queries without expensive RAG/LLM calls
    preprocessing_result = await query_preprocessor.preprocess_query(
        query=last_user_message,
        framework_hint=framework_hint,
        request_id=meta.request_id
    )
    
    # Log preprocessing pipeline execution
    log_event(
        request_id=meta.request_id,
        org_id=meta.org_id,
        user_id=meta.user_id,
        event_type="deterministic_preprocessing",
        direction="internal",
        details={
            "query": last_user_message,
            "frameworkHint": framework_hint,
            "stages": preprocessing_result.stages_executed,
            "deterministic_match": preprocessing_result.deterministic_match is not None,
            "synonym_expansions": len(preprocessing_result.synonym_expansions or []),
            "paraphrase_matches": len(preprocessing_result.paraphrase_matches or []),
            "e_pmi_associations": len(preprocessing_result.e_pmi_associations or []),
            "graph_crawl_nodes": len(preprocessing_result.graph_crawl_result.paths if preprocessing_result.graph_crawl_result else []),
            "pipeline_mode": "deterministic_first",
            "sessionId": meta.session_id,
        },
        session_id=meta.session_id,
        trace_id=meta.traceparent,
    )
    
    # If we found a deterministic match, return immediately without RAG/LLM
    if preprocessing_result.deterministic_match:
        # Determine output mode from hints and content metadata
        output_mode = _determine_output_mode(hints, preprocessing_result.deterministic_match)
        
        deterministic_response = _format_deterministic_response(
            match=preprocessing_result.deterministic_match,
            preprocessing_result=preprocessing_result,
            output_mode=output_mode,
            meta=meta
        )
        
        # Log successful deterministic resolution
        log_event(
            request_id=meta.request_id,
            org_id=meta.org_id,
            user_id=meta.user_id,
            event_type="deterministic_match_resolved",
            direction="internal",
            details={
                "match_type": preprocessing_result.deterministic_match.match_type,
                "canonical_id": preprocessing_result.deterministic_match.canonical_id,
                "confidence_score": preprocessing_result.deterministic_match.confidence_score,
                "source_stage": preprocessing_result.deterministic_match.source_stage,
                "avoided_rag": True,
                "avoided_llm": True,
                "sessionId": meta.session_id,
            },
            session_id=meta.session_id,
            trace_id=meta.traceparent,
        )
        
        return deterministic_response

    # 1) Enhanced Retrieval (hybrid: preprocessing results + vector top-k)
    # No deterministic match found, proceed with augmented RAG pipeline
    pipeline_mode = (hints.get("pipelineMode") or "retrieval").lower()
    retrieval_backend = (hints.get("retrievalBackend") or "supabase").lower()
    t0 = time.time()
    citations = []
    suggestions = []
    
    if pipeline_mode == "retrieval" and meta.org_id:
        # Use preprocessing results to enhance retrieval query
        enhanced_query = _build_enhanced_query(last_user_message, preprocessing_result)
        
        # Embed enhanced query (includes synonyms, paraphrases, E-PMI terms)
        qvec = embed_texts([enhanced_query])[0]

        # Use sophisticated orchestrator instead of simple hybrid_search
        search_params = SearchParameters(
            org_id=meta.org_id,
            query_text=enhanced_query,
            query_embedding=qvec,
            limit=8,
            include_audit_trail=True
        )
        orchestrated_result = await search_orchestrator.hybrid_search(search_params)

        # Convert orchestrator results to legacy format for compatibility
        rows = []
        for chunk in orchestrated_result.chunks:
            rows.append({
                "doc_id": chunk.doc_id,
                "chunk_id": chunk.chunk_id,
                "score": chunk.score,
                "text": chunk.text,
                "metadata": chunk.metadata
            })

        t1 = time.time()
        for r in rows:
            citations.append({
                "id": r.get("doc_id"),
                "type": "chunk",
                "score": r.get("score"),
                "version": "v1",
                "ts": int(time.time()),
            })
            md = r.get("metadata") or {}
            for a in (md.get("actions") or []):
                label = a.get("label") or a.get("slug")
                suggestions.append({"text": label, "action": {"type": a.get("type"), "target": a.get("slug")}})
        log_event(
            request_id=meta.request_id,
            org_id=meta.org_id,
            user_id=meta.user_id,
            event_type="retrieval_run",
            direction="internal",
            details={
                "pipelineMode": pipeline_mode,
                "retrievalBackend": "orchestrated_hybrid",
                "topK": len(rows),
                "latencyMs": int(orchestrated_result.total_latency_ms),
                "citations": citations,
                "graphHops": len(preprocessing_result.graph_crawl_result.paths if preprocessing_result.graph_crawl_result else []),
                "enhancedQuery": enhanced_query != last_user_message,
                "preprocessingStages": preprocessing_result.stages_executed,
                "sessionId": meta.session_id,
                # Enhanced orchestrator metadata
                "orchestrator": {
                    "intent_category": orchestrated_result.intent_classification.category.value,
                    "intent_confidence": orchestrated_result.intent_classification.confidence_score,
                    "tier_used": orchestrated_result.tier_used.value,
                    "confidence_score": orchestrated_result.confidence_score,
                    "search_breakdown": orchestrated_result.search_breakdown,
                    "escalation_reason": orchestrated_result.escalation_reason,
                    "query_hash": orchestrated_result.query_hash,
                    "requires_public": orchestrated_result.intent_classification.requires_public_knowledge,
                    "requires_private": orchestrated_result.intent_classification.requires_private_knowledge
                }
            },

            session_id=meta.session_id,
            trace_id=meta.traceparent,
        )

    # 2) AI Call (SLLM primary; stubbed)
    model = "llama3-8b-int8"
    provider = "sllm"
    # Compile prompt (for auditing; stubbed generation still echoes)
    last_user = next((m["content"] for m in reversed(messages) if m.get("role") == "user"), "")
    retrieval_text = None
    if citations:
        # Minimal join of retrieved texts for transparency (avoid long payloads)
        retrieval_text = "\n\n".join([f"[{c['id']}] …" for c in citations[:3]])
    compiled = prompts.compile_messages(template_id="qna", retrieval_context=retrieval_text, user_text=last_user)
    tmpl_meta = prompts.template_meta("qna")
    p0 = time.time()
    # Simulate processing
    time.sleep(0.02)
    p1 = time.time()
    text = _simple_echo(messages)
    usage = {"inputTokens": 64, "outputTokens": 32, "costUsd": 0.0}
    log_event(
        request_id=meta.request_id,
        org_id=meta.org_id,
        user_id=meta.user_id,
        event_type="ai_call",
        direction="internal",
        details={
            "provider": provider,
            "model": model,
            "anonymized": False,
            "inputTokens": usage["inputTokens"],
            "outputTokens": usage["outputTokens"],
            "costUsd": usage["costUsd"],
            "latencyMs": int((p1 - p0) * 1000),
            "promptTemplate": tmpl_meta,
            "promptHash": "stub",
            "fallback": {"used": False},
            "sessionId": meta.session_id,
        },
        session_id=meta.session_id,
        trace_id=meta.traceparent,
    )

    # 3) Deterministic rules (Pilot+; stub)
    # Here we can evaluate rules; we log a placeholder outcome
    log_event(
        request_id=meta.request_id,
        org_id=meta.org_id,
        user_id=meta.user_id,
        event_type="deterministic_rules_applied",
        direction="internal",
        details={
            "rules": [{"id": "rule:coverage-basic", "version": "v0", "outcome": "n/a", "reason": "stub"}],
            "coverage": 0,
            "durationMs": 1,
            "sessionId": meta.session_id,
        },
        session_id=meta.session_id,
        trace_id=meta.traceparent,
    )

    # Ensure we always return up to 4 suggestions; provide educational defaults if none
    if not suggestions:
        suggestions = [
            {"text": "Start an assessment", "action": {"type": "nav", "target": "assessment"}},
            {"text": "What is ISO 27001 and where do I start?", "action": {"type": "nav", "target": "standards/iso27001"}},
            {"text": "Show recommended next steps for my organization"},
            {"text": "Explore compliance registers", "action": {"type": "nav", "target": "registers"}},
        ]

    # Determine output mode for RAG response
    output_mode = _determine_output_mode(hints, None)
    
    # Format response based on output_mode (cards, prose, or both)
    response = _format_rag_response(
        provider=provider,
        model=model,
        text=text,
        usage=usage,
        suggestions=suggestions[:4],
        citations=citations,
        preprocessing_result=preprocessing_result,
        output_mode=output_mode,
        enhanced_query=enhanced_query if 'enhanced_query' in locals() else None,
        last_user_message=last_user_message,
        meta=meta
    )
    
    return response


def _simple_echo(messages: List[Dict[str, str]]) -> str:
    """Return a simple echo of the last user message.

    Args:
        messages: Conversation messages including roles and content.

    Returns:
        Echo string prefixed with 'Echo: ' or empty string when no user message.
    """
    last_user = next((m["content"] for m in reversed(messages) if m.get("role") == "user"), "")
    return f"Echo: {last_user}" if last_user else ""


def _format_deterministic_response(match, preprocessing_result, output_mode: str, meta) -> Dict[str, Any]:
    """Format deterministic match into response format without LLM call.
    
    Args:
        match: DeterministicMatch with canonical_id, match_type, confidence_score, content
        preprocessing_result: Complete PreprocessingResult with all stage data
        
    Returns:
        Dict with provider/model/text/usage matching standard response format
    """
    # Extract UI actions from match metadata for suggestions
    suggestions = []
    if hasattr(match, 'metadata') and match.metadata:
        ui_actions = match.metadata.get('ui', {}).get('actions', [])
        for action in ui_actions[:4]:  # Limit to top 4 suggestions
            suggestions.append({
                "text": action.get('label', action.get('target', 'Unknown')),
                "action": {
                    "type": action.get('type'),
                    "target": action.get('target')
                }
            })
    
    # Build deterministic response text with explainability
    response_parts = []
    if match.content:
        response_parts.append(match.content)
    
    if preprocessing_result.graph_crawl_result and preprocessing_result.graph_crawl_result.paths:
        response_parts.append(f"\n\nRelated: Found {len(preprocessing_result.graph_crawl_result.paths)} related concepts through graph traversal.")
    
    response_text = "\n".join(response_parts) if response_parts else f"Found direct match: {match.canonical_id}"
    
    # Build base response
    base_response = {
        "provider": "deterministic",
        "model": f"preprocessing-{match.source_stage}",
        "usage": {
            "inputTokens": len(preprocessing_result.original_query.split()),
            "outputTokens": len(response_text.split()),
            "costUsd": 0.0  # Deterministic responses are free
        },
        "suggestions": suggestions,
        "deterministic": {
            "match_type": match.match_type,
            "canonical_id": match.canonical_id,
            "confidence_score": match.confidence_score,
            "source_stage": match.source_stage,
            "graph_paths": len(preprocessing_result.graph_crawl_result.paths if preprocessing_result.graph_crawl_result else []),
        },
        "output_mode": output_mode
    }
    
    # Add output_mode specific formatting
    if output_mode == 'cards' or output_mode == 'both':
        # Cards format: structured evidence with UI actions
        cards_data = _build_cards_from_match(match, preprocessing_result)
        base_response['evidence'] = cards_data['evidence']
        base_response['cards_hint'] = cards_data.get('cards_hint', [])
        
    if output_mode == 'prose' or output_mode == 'both':
        # Prose format: natural language response
        base_response['text'] = response_text
        base_response['typing_simulation'] = True  # Deterministic responses support typing sim
    
    return base_response


def _build_enhanced_query(original_query: str, preprocessing_result) -> str:
    """Build enhanced query from preprocessing results for better vector retrieval.
    
    Args:
        original_query: User's original query text
        preprocessing_result: PreprocessingResult with expansions and associations
        
    Returns:
        Enhanced query string with synonyms, paraphrases, and E-PMI terms
    """
    query_parts = [original_query]
    
    # Add top synonym expansions (limit to avoid embedding size issues)
    if preprocessing_result.synonym_expansions:
        top_synonyms = [exp.expanded_term for exp in preprocessing_result.synonym_expansions[:3]]
        query_parts.extend(top_synonyms)
    
    # Add high-confidence paraphrase matches
    if preprocessing_result.paraphrase_matches:
        top_paraphrases = [
            match.matched_phrase for match in preprocessing_result.paraphrase_matches 
            if match.confidence_score > 0.8
        ][:2]
        query_parts.extend(top_paraphrases)
    
    # Add strong E-PMI associations
    if preprocessing_result.e_pmi_associations:
        strong_associations = [
            assoc.associated_term for assoc in preprocessing_result.e_pmi_associations
            if assoc.pmi_score > 0.5
        ][:2]
        query_parts.extend(strong_associations)
    
    # Join with original query weighted higher
    enhanced = f"{original_query} {' '.join(query_parts[1:])}".strip()
    return enhanced


def _determine_output_mode(hints: Dict[str, Any], match=None) -> str:
    """
    Determine output mode from hints and content metadata.
    
    Args:
        hints: Request hints that may contain output_mode preference
        match: Optional DeterministicMatch with content metadata
        
    Returns:
        Output mode: 'cards', 'prose', or 'both' (default: 'both')
    """
    # Check explicit output_mode in hints first
    if "output_mode" in hints:
        mode = hints["output_mode"].lower()
        if mode in ["cards", "prose", "both"]:
            return mode
    
    # Check content metadata if available
    if match and hasattr(match, 'metadata') and match.metadata:
        content_mode = match.metadata.get('output_mode')
        if content_mode and content_mode.lower() in ["cards", "prose", "both"]:
            return content_mode.lower()
    
    # Default to 'both' for maximum flexibility
    return 'both'


def _build_cards_from_match(match, preprocessing_result) -> Dict[str, Any]:
    """
    Build Cards format data from deterministic match.
    
    Args:
        match: DeterministicMatch with canonical_id and metadata
        preprocessing_result: Complete PreprocessingResult with stage data
        
    Returns:
        Dict with evidence array and cards_hint array
    """
    evidence = []
    cards_hint = []
    
    # Build evidence item from match
    evidence_item = {
        "id": match.canonical_id,
        "type": "deterministic_match",
        "title": getattr(match, 'title', match.canonical_id),
        "content": getattr(match, 'content', ''),
        "confidence": match.confidence_score,
        "source": match.source_stage,
        "timestamp": int(time.time()),
        "metadata": {
            "match_type": match.match_type,
            "canonical_id": match.canonical_id,
            "deterministic": True
        }
    }
    
    # Add graph traversal data if available
    if preprocessing_result.graph_crawl_result and preprocessing_result.graph_crawl_result.paths:
        evidence_item["graph_paths"] = [
            {
                "path": path.node_ids,
                "score": path.aggregated_score,
                "hops": len(path.node_ids) - 1
            }
            for path in preprocessing_result.graph_crawl_result.paths[:3]  # Top 3 paths
        ]
    
    evidence.append(evidence_item)
    
    # Extract cards_hint from metadata
    if hasattr(match, 'metadata') and match.metadata:
        ui_data = match.metadata.get('ui', {})
        if 'cards_hint' in ui_data:
            cards_hint = ui_data['cards_hint'][:3]  # Max 3 hints as per design
    
    # Generate default cards_hint if none found
    if not cards_hint:
        cards_hint = [
            f"{match.match_type.replace('_', ' ').title()}",
            f"Confidence: {match.confidence_score:.0%}"
        ]
    
    return {
        "evidence": evidence,
        "cards_hint": cards_hint
    }


def _format_rag_response(
    provider: str,
    model: str,
    text: str,
    usage: Dict[str, Any],
    suggestions: List[Dict[str, Any]],
    citations: List[Dict[str, Any]],
    preprocessing_result,
    output_mode: str,
    enhanced_query: Optional[str],
    last_user_message: str,
    meta
) -> Dict[str, Any]:
    """
    Format RAG response based on output_mode (cards, prose, or both).
    
    Args:
        provider: LLM provider name
        model: Model identifier  
        text: Generated response text
        usage: Token usage statistics
        suggestions: UI action suggestions
        citations: Retrieved citations/evidence
        preprocessing_result: PreprocessingResult with stage data
        output_mode: 'cards', 'prose', or 'both'
        enhanced_query: Enhanced query string (optional)
        last_user_message: Original user message
        meta: Request metadata
        
    Returns:
        Formatted response dict with output_mode specific fields
    """
    # Build base response
    base_response = {
        "provider": provider,
        "model": model,
        "usage": usage,
        "suggestions": suggestions,
        "output_mode": output_mode,
        "preprocessing": {
            "stages_executed": preprocessing_result.stages_executed,
            "deterministic_resolved": False,  # RAG path means no deterministic match
            "query_enhanced": enhanced_query != last_user_message if enhanced_query else False,
        }
    }
    
    # Add output_mode specific formatting
    if output_mode == 'cards' or output_mode == 'both':
        # Cards format: structured evidence from citations
        cards_data = _build_cards_from_citations(citations, preprocessing_result)
        base_response['evidence'] = cards_data['evidence']
        base_response['cards_hint'] = cards_data.get('cards_hint', [])
        
    if output_mode == 'prose' or output_mode == 'both':
        # Prose format: natural language with typing simulation support
        base_response['text'] = text
        # Note: typing_simulation would be added by frontend based on user preference
        
    return base_response


def _build_cards_from_citations(citations: List[Dict[str, Any]], preprocessing_result) -> Dict[str, Any]:
    """
    Build Cards format data from RAG citations.
    
    Args:
        citations: List of citation dicts from vector search
        preprocessing_result: PreprocessingResult with stage data
        
    Returns:
        Dict with evidence array and cards_hint array
    """
    evidence = []
    cards_hint = []
    
    # Build evidence items from citations
    for citation in citations:
        evidence_item = {
            "id": citation.get("id"),
            "type": citation.get("type", "chunk"),
            "score": citation.get("score", 0.0),
            "version": citation.get("version", "v1"),
            "timestamp": citation.get("ts", int(time.time())),
            "metadata": {
                "retrieval_source": "vector_search",
                "deterministic": False
            }
        }
        
        # Add any available metadata
        if "metadata" in citation:
            evidence_item["source_metadata"] = citation["metadata"]
            
            # Extract cards_hint from citation metadata
            md = citation["metadata"] or {}
            if "ui" in md and "cards_hint" in md["ui"]:
                cards_hint.extend(md["ui"]["cards_hint"][:2])  # Add up to 2 hints per citation
        
        evidence.append(evidence_item)
    
    # Remove duplicate hints and limit to 3 total
    cards_hint = list(dict.fromkeys(cards_hint))[:3]
    
    # Generate default cards_hint if none found
    if not cards_hint and evidence:
        cards_hint = [
            f"Found {len(evidence)} relevant items",
            f"Best match: {evidence[0]['score']:.0%}" if evidence[0]['score'] else "Vector search results"
        ]
    
    return {
        "evidence": evidence,
        "cards_hint": cards_hint
    }
