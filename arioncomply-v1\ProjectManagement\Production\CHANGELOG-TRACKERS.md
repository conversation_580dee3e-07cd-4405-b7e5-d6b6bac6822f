# Tracker Alignment: Data-Driven MVP + Logging Readiness

## Summary

This change documents and consolidates the tracker updates aligning our MVP phases (MVP-Assessment-App, MVP-Demo-Light-App, MVP-Pilot) with data-driven API/UI principles and end-to-end logging requirements.

## Key Updates (reflected in trackers)

- Standardized phase terminology across trackers and master tracker.
- Added Testing Readiness checklists (DB → Edge → API) with explicit deployment steps.
- MVP-Assessment-App scope: conversation + assessment endpoints; logging with JWT-derived orgId/userId; audit read optional.
- MVP-Demo-Light-App scope: documents.generate, scenarios, registration, metadata registry (minimal), ListView/Form APIs, assistant_router (MVP), runtime validation, FieldMapper.
- MVP-Pilot scope: registers, plans/tasks, certification document set.
- Logging tasks: tighten RLS on logs, ensure org_id set; emit response_sent, stream_finished, db_read/db_write, ai_call; audit endpoints and OpenAPI.

## Files Updated Previously

- `ProjectManagement/Production/database-tracker.md`
- `ProjectManagement/Production/edge-functions-tracker.md`
- `ProjectManagement/Production/api-tracker.md`
- `ProjectManagement/Production/EventSystem/eventSystemTaskTracker.md`
- `ProjectManagement/Production/ai-backend-tracker.md`
- `ProjectManagement/Production/master-tracker.md`
- `ProjectManagement/ongoingActivities.md`

## Next Steps

- Apply DB migrations 0001–0011; seed demo tenant and minimal templates.
- Deploy conversation endpoints; verify request/event logging.
- Add minimal audit reader; wire testing harness non-destructively.

---

## 2025-09-08 – Edge as Gateway, Trace Context, Transparency/Explainability Docs

Changes
- Edge functions simplified to gateway-only for AI: forward to backend with timeout; removed provider/anonymization/retrieval from Edge.
- Mandatory logging at Edge: `LOGGING_REQUIRED=true` enforced; minimal retry on failures.
- W3C Trace Context: parse/generate `traceparent`, echo to clients; enrich `api_event_logs.details` with `traceId/spanId/parentSpanId/correlationId` (MVP JSON path).
- Event coverage: added `response_sent` to start/send, `stream_finished` to stream.
- Docs added: Explainable AI and Decision Chain; Transparency/Traceability contract expanded; Traceability IDs and DB model proposal.

Trackers Updated
- Edge Functions Tracker: phase alignment note; Edge gateway role; tracing and events status.
- AI Backend Tracker: responsibilities clarified (provider routing, anonymization GLLM-only, retrieval, explainability, logging events) with phased tasks.
- API Tracker: phase alignment note for conversation API and audit read.
- ActionsToDo: new section enumerating AI Backend and Logging/Traceability tasks per phase.

Phase Alignment
- MVP-Assessment: requestId + correlationId, sessionId after start, `traceparent` supported; Edge logs lifecycle and response events.
- MVP-Demo-Light: backend emits `retrieval_run` and `ai_call`; minimal app telemetry; adopt `traceparent` end-to-end.
- MVP-Pilot: add trace/session/process columns; `process_runs` and `app_telemetry` tables; export traces.
