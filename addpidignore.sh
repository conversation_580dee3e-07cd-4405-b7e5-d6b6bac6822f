#!/usr/bin/env bash
# add_pid_ignore.sh
set -euo pipefail

# Move to repo root
repo_root="$(git rev-parse --show-toplevel 2>/dev/null || pwd)"
cd "$repo_root"

block_header="# Added by add_pid_ignore.sh: ignore server PID files"
rule="*.pid"

updated=0

add_block() {
  local file="$1"
  # Ensure file ends with a newline
  tail -c1 "$file" | read -r _ || echo >> "$file"
  {
    echo ""
    echo "$block_header"
    echo "$rule"
  } >> "$file"
  echo "Updated: $file"
  updated=$((updated+1))
}

# Ensure a root .gitignore exists
if [ ! -f ".gitignore" ]; then
  echo -e "$block_header\n$rule" > .gitignore
  echo "Created: $repo_root/.gitignore"
  updated=$((updated+1))
else
  if ! grep -Eq '(^|\s)\*\.pid(\s|$)' .gitignore && ! grep -Eq '(^|\s)\.server\.pid(\s|$)' .gitignore; then
    add_block ".gitignore"
  fi
fi

# Find and process all other .gitignore files
while IFS= read -r -d '' f; do
  # Skip the root one we handled
  [ "$f" = "./.gitignore" ] && continue
  if ! grep -Eq '(^|\s)\*\.pid(\s|$)' "$f" && ! grep -Eq '(^|\s)\.server\.pid(\s|$)' "$f"; then
    add_block "$f"
  fi
done < <(find . -type f -name ".gitignore" -print0)

echo "Done. Files updated: $updated"
echo "Next:"
echo "  git add .gitignore **/.gitignore"
echo "  git commit -m \"Ignore PID files (*.pid) across repo\""
#!/bin/bash
# File Description: Add .pidignore pattern to gitignore-like files
# Purpose: Prevent accidental PID files from being tracked by VCS
# Usage: ./addpidignore.sh [path]
# Notes: Idempotent; appends only if entry is missing
