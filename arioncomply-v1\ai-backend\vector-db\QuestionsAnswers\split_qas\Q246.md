id: Q246
query: >-
  What documentation do we need to keep about incidents and our response?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/7.5"
  - "GDPR:2016/Art.33"
overlap_ids: []
capability_tags:
  - "Register"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Documented Information"
    id: "ISO27001:2022/7.5"
    locator: "Clause 7.5"
  - title: "GDPR — Breach Notification"
    id: "GDPR:2016/Art.33"
    locator: "Article 33"
ui:
  cards_hint:
    - "Incident record template"
  actions:
    - type: "open_register"
      target: "incident_register"
      label: "View Incident Docs"
output_mode: "both"
graph_required: false
notes: "Keep timelines, decisions, notifications, root-cause analysis, and closure evidence"
---
### 246) What documentation do we need to keep about incidents and our response?

**Standard terms)**  
- **Documented information (ISO27001 Cl.7.5):** versioned records.  
- **Breach logs (GDPR Art.33):** details of breach and notifications.

**Plain-English answer**  
Retain an **Incident Register** containing:  
- Date/time of discovery and resolution  
- Root-cause analysis  
- All communications (authorities, customers)  
- Evidence of corrective actions  
- Audit trail of approvals and sign-off

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 7.5; GDPR Article 33

**Why it matters**  
Complete records demonstrate due diligence and ease future audits.

**Do next in our platform**  
- Open the **Incident Register**.  
- Attach all relevant documents and timestamps.

**How our platform will help**  
- **[Register]** Structured incident log template.  
- **[Dashboard]** Filters by status, severity, and type.

**Likely follow-ups**  
- How long must we retain these records?

**Sources**  
- ISO/IEC 27001:2022 Clause 7.5; GDPR Article 33
