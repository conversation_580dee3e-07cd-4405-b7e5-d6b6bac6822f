id: Q044
query: >-
  What if compliance requirements conflict with how we currently operate?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/4.1"
  - "ISO27001:2022/4.2"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Register"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Context & Needs"
    id: "ISO27001:2022/4.1"
    locator: "Clause 4.1"
  - title: "ISO/IEC 27001:2022 — Leadership & Policies"
    id: "ISO27001:2022/4.2"
    locator: "Clause 4.2"
ui:
  cards_hint:
    - "Conflict-resolution log"
  actions:
    - type: "open_register"
      target: "conflicts"
      label: "Record Conflicts"
output_mode: "both"
graph_required: false
notes: "Use risk-based approach to resolve conflicts"
---
### 44) What if compliance requirements conflict with how we currently operate?

**Standard terms)**  
- **Context (Cl. 4.1):** understand internal/external issues.  
- **Policies (Cl. 4.2):** define how you’ll address requirements.

**Plain-English answer**  
Conflicts are resolved by a **risk-based analysis**: document the gap, assess risk, then decide to adapt process or apply compensating controls. This ensures alignment without blind compliance.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clauses 4.1; 4.2

**Why it matters**  
A structured approach prevents ad-hoc fixes and audit findings.

**Do next in our platform**  
- Report **conflicts** in the register.  
- Run **risk evaluation** workflow.

**How our platform will help**  
- **[Register]** Conflict log.  
- **[Workflow]** Risk-based resolution steps.

**Likely follow-ups**  
- “What are common compensating controls?” (Encryption, monitoring)

**Sources**  
- ISO/IEC 27001:2022 Clauses 4.1; 4.2
