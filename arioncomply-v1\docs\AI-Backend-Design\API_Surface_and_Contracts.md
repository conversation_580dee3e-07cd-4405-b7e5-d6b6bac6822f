# API Surface and Contracts (Placeholder)

Decisions
- Public API: `conversation.start`, `conversation.send`, `conversation.stream`.
- Envelopes: `apiOk`/`apiError` with camelCase data and `requestId`/`timestamp`.

Design Points
- Validation and error codes; consistent casing (camel API, snake DB).
- Versioning strategy and deprecation policy.
- SSE event schema: start/token/end payloads and error handling.

Open Questions
- Additional endpoints (audit read, prompts registry) and access control.

Next Steps
- Document request/response examples; add JSON Schemas.
- Implement audit read endpoint once logs are live.

