# LLM 2.0 Preprocessing (Placeholder)

Intent
- Apply <PERSON>’s LLM 2.0 concept: shift effort to preprocessing and structured reasoning so the LLM mainly reformats, verifies, and explains.

Approach
- Knowledge extraction: derive facts, rules, mappings from documents into structured tables/metadata.
- Deterministic functions: implement rule checks and transformations outside the model where possible.
- Retrieval-first: assemble authoritative context and candidate answers before LLM step.
- LLM role: summarize, resolve ambiguities, generate human-friendly prose, and propose edits.

Open Questions
- Minimal fact schema for MVP; priority entities and relationships.
- Rule authoring workflow; provenance and testing.

Next Steps
- Design a small evidence/requirement/control fact schema with provenance.
- Build pre-answer generators for common queries (e.g., policy coverage), feeding the LLM only for prose.
- Evaluate quality/latency vs generation-from-scratch baselines.

