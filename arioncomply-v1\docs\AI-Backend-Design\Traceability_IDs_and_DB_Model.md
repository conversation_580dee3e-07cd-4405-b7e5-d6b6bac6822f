<!-- File: arioncomply-v1/docs/AI-Backend-Design/Traceability_IDs_and_DB_Model.md -->
# Traceability: ID Generation and DB Model (Proposal)

Goals
- Define how IDs are generated and propagated, and which DB records ensure end-to-end and top-to-bottom traceability.

ID Strategy
- requestId: UUIDv4 generated at Edge per request; stored in `api_request_logs.request_id` and `api_event_logs.request_id`.
- sessionId: ULID recommended (sortable), generated by Back<PERSON> on `conversation.start`; persisted in `conversation_sessions.session_id` and attached to `conversation_messages.session_id`.
- processId: ULID generated by Backend at the beginning of long workflows (e.g., ingestion, paraphrase batch, proposal pipeline). Persisted in `process_runs.process_id` and referenced by events.
- correlationId: client generated per UI session/task; flows via `x-correlation-id`; stored with request logs and app telemetry.

DB Additions (Option A: dedicated columns)
- api_request_logs: add `session_id uuid`, `process_id text` (ULID), `correlation_id text` (already present) for easier filtering.
- api_event_logs: add `session_id uuid`, `process_id text`.
- conversation_sessions: add `external_correlation_id text` (optional) for client correlation.
- conversation_messages: ensure `session_id uuid` present; add `request_id uuid` for precise linkage, and `process_id text` for long flows.
- process_runs (new): records long-running operations per org.
  - Columns: id (uuid pk), org_id (uuid), process_id (text ULID), kind (text), status (text), created_at, finished_at, details (jsonb)
- app_telemetry (new): client-side events (ui_action/navigation/error_client) for correlation.
  - Columns: id (uuid pk), org_id, user_id, correlation_id, session_id, kind, route, details (jsonb), created_at

DB Additions (Option B: details JSON only)
- Keep current schemas; write `sessionId` and `processId` into `api_event_logs.details` and `api_request_logs.request_headers`.
- Pros: no migration now; Cons: slower queries and less explicit constraints.

Recommendation
- Adopt Option A for `api_event_logs` and `api_request_logs` columns in Pilot to enable efficient queries; use Option B in MVP for speed.

Generation Points
- Edge: requestId always; copy `x-correlation-id` into `api_request_logs.correlation_id`.
- Backend: sessionId on `conversation.start`; processId on first step of any multi-step operation; both returned to client and attached to subsequent events.

Join Patterns
- request timeline: join `api_request_logs` and `api_event_logs` on `request_id`.
- session trace: filter `api_event_logs.session_id = :sid` and join `conversation_messages` by `session_id`.
- process trace: filter `api_event_logs.process_id = :pid` and join `process_runs` and related domain tables.
- UI correlation: filter `app_telemetry.correlation_id = :cid`, then cross-reference `api_request_logs.correlation_id`.

Retention and Integrity
- Apply RLS by `org_id` everywhere; deny NULL.
- Consider append-only on `api_event_logs` with a trigger in Pilot+.
- Define retention windows per table (e.g., logs 180 days, telemetry 90 days) with legal hold overrides.

Open Questions
- Finalize ULID vs UUID for `sessionId` and `processId`.
- Which app telemetry events are in-scope for MVP-Assessment.
- Whether `process_runs` should be split by domain (ingestion/proposals/etc.) or a single table with `kind` discriminator.

