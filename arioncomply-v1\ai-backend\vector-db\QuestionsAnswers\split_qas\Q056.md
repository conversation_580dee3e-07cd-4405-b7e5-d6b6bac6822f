id: Q056
query: >-
  Can we get "conditionally certified" or is it all-or-nothing?
packs:
  - "ISO17021-1:2015"
  - "ISO27001:2022"
primary_ids:
  - "ISO17021-1:2015/8.2"
overlap_ids: []
capability_tags:
  - "Report"
  - "Register"
flags: []
sources:
  - title: "ISO/IEC 17021-1:2015 — Nonconformity Classification"
    id: "ISO17021-1:2015/8.2"
    locator: "Clause 8.2"
  - title: "ISO/IEC 27001:2022 — Audit Requirements"
    id: "ISO27001:2022/9.1"
    locator: "Clause 9.1"
ui:
  cards_hint:
    - "Certification decision log"
  actions:
    - type: "open_register"
      target: "certification_status"
      label: "View Certification Status"
output_mode: "both"
graph_required: false
notes: "No conditional certificates—CB issues pass/fail with NCs to resolve"
---
### 56) Can we get "conditionally certified" or is it all-or-nothing?

**Standard terms**  
- **Nonconformities (ISO 17021-1 Cl. 8.2):** classification of major vs minor.  
- **Audit requirements (ISO 27001 Cl. 9.1):** determines pass/fail.

**Plain-English answer**  
Certificates are **pass/fail**. You can pass with **minor nonconformities** (requiring CAPs) but major NCs mean no certificate. There is no provisional or conditional certification.

**Applies to**  
- **Primary:** ISO/IEC 17021-1:2015 Clause 8.2; ISO/IEC 27001:2022 Clause 9.1

**Why it matters**  
Clarifies certification outcomes and planning for CAP closure.

**Do next in our platform**  
- Track **NC severity** in register.  
- Plan **CAP** workflows promptly.

**How our platform will help**  
- **[Register]** NC classification log.  
- **[Report]** Certification readiness status.

**Likely follow-ups**  
- “How do we resolve minor vs major NCs?” (CB guidance differs)

**Sources**  
- ISO/IEC 17021-1:2015 Clause 8.2  
- ISO/IEC 27001:2022 Clause 9.1
