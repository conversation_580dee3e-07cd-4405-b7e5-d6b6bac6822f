id: Q173
query: >-
  How do we handle privacy compliance when using AI that learns from customer data?
packs:
  - "EUAI:2024"
  - "GDPR:2016"
primary_ids:
  - "EUAI:2024/Art.22"
  - "GDPR:2016/Rec.71"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Register"
flags: []
sources:
  - title: "EU AI Act — Data Governance"
    id: "EUAI:2024/Art.22"
    locator: "Article 22"
  - title: "GDPR Recital 71 — Safeguards"
    id: "GDPR:2016/Rec.71"
    locator: "Recital 71"
ui:
  cards_hint:
    - "AI data governance"
  actions:
    - type: "start_workflow"
      target: "ai_data_governance"
      label: "Implement Data Governance"
    - type: "open_register"
      target: "data_governance"
      label: "View Data Governance Register"
output_mode: "both"
graph_required: false
notes: "Establish data minimization, quality, and retention for learning systems"
---
### 173) How do we handle privacy compliance when using AI that learns from customer data?

**Standard term(s)**  
- **Data governance (EU AI Act Art. 22):** requirements for managing data used in training, validating, and testing AI systems, ensuring quality and relevance.  
- **Safeguards (GDPR Recital 71):** protections for individuals subject to automated decision-making, including transparency, human oversight, and the right to contest decisions.

**Plain-English answer**  
You must ensure that any customer data used by AI is collected and processed lawfully, with explicit purposes, and complies with GDPR principles such as minimization and accuracy. Under the EU AI Act, you must implement robust data governance for training and testing datasets, including checks for bias, quality, and relevance. For AI systems that make automated decisions, GDPR requires safeguards like human review, clear explanations, and the ability for individuals to challenge outcomes.

**Applies to**  
- **Primary:** EU AI Act Article 22; GDPR Recital 71

**Why it matters**  
Combining AI and personal data processing raises both privacy and ethical risks, with strict regulatory requirements for lawful and fair use.

**Do next in our platform**  
- Launch the **AI Data Governance** workflow to define policies for training and operational datasets.  
- Record all AI data sources and governance measures in the **Data Governance Register**.

**How our platform will help**  
- **[Workflow]** Step-by-step implementation of AI data governance controls.  
- **[Register]** Maintain authoritative records of datasets, processing purposes, and retention rules.

**Likely follow-ups**  
- “Can we use anonymized customer data for AI training without consent?” (Depends on whether re-identification risk exists) [LOCAL LAW CHECK]

**Sources**  
- EU AI Act Article 22  
- GDPR Recital 71

