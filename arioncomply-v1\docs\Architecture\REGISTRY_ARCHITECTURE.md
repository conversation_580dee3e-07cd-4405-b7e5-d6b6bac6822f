# Registry Architecture Documentation

## JSON-First Registry System

**IMPORTANT**: The ArionComply metadata registry is **JSON-based**, NOT database tables.

### Registry Data Location
- **Main Registry**: `config/ui_catalog.json` - defines registers, workflows, templates, trackers
- **Validation Schema**: `schemas/metadata-v1.json` - 610-line JSON schema for validation
- **Content Metadata**: `content/*/metadata.json` - content instance metadata

### Why JSON-First Architecture?
1. **Version Control**: All metadata changes are tracked in Git
2. **Schema Validation**: JSON Schema provides strict validation
3. **Deployment Independence**: No database migrations needed for registry changes
4. **Approval Workflows**: Git-based review process for metadata changes

## Database vs JSON Data Distribution

### JSON Registry Data (Version Controlled)
- UI component definitions and actions
- Workflow parameter schemas
- Template and register definitions
- Content metadata and relationships
- Evidence bucket classifications

### Database Data (Operational)
- User sessions and conversations
- Event logs and audit trails
- Vector embeddings (separate project)
- User-generated content

## API Layer

### Current Status
- ❌ **MISSING**: Edge functions to serve JSON registry data
- ✅ **EXISTS**: Conversation APIs (`ai-conversation-start`, `ai-conversation-send`)
- ✅ **EXISTS**: Backend AI router (`/ai/chat`)

### Required Registry Endpoints
```
GET /functions/v1/registry-catalog  → serves config/ui_catalog.json
GET /functions/v1/registry-schema   → serves schemas/metadata-v1.json  
GET /functions/v1/registry-content/{id} → serves content/{id}/metadata.json
```

## Integration Points

### Workflow GUI Integration
The `testing/workflow-gui` should call registry endpoints to:
- Populate workflow dropdowns from ui_catalog.json
- Validate forms against metadata-v1.json schema
- Load content metadata for UI actions

### Vector Database Integration
Content metadata includes vector-specific fields:
```json
{
  "vector_db_metadata": {
    "chromadb_collection": "ariocomply-qa-dev",
    "supabase_org_id": "uuid-here",
    "embedding_model": "text-embedding-3-large",
    "chunk_strategy": "semantic"
  }
}
```

## Migration Path

### Phase 1-2 (Current)
- Serve JSON files via Edge functions
- Client-side validation against JSON schemas
- Static registry definitions

### Phase 3-4 (Future)
- Optional: Database tables for dynamic registry updates
- Keep JSON as source-of-truth, sync to DB for performance
- Maintain backward compatibility with JSON APIs

## Developer Guidelines

### Adding New Registry Items
1. Update appropriate JSON file (`ui_catalog.json` or content metadata)
2. Validate against `schemas/metadata-v1.json`
3. Submit PR with schema validation passing
4. No database migration required

### Finding Registry Data
- **DON'T** look for `metadata_entities`, `metadata_fields` tables
- **DO** look in `config/ui_catalog.json` and `schemas/metadata-v1.json`
- **DON'T** expect database CRUD for registry
- **DO** expect file-based versioning and Git workflow