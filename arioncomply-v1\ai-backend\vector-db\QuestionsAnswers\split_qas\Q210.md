id: Q210
query: >-
  What if we outgrow our current consultants — how do we transition to new help?
packs: []
primary_ids: []
overlap_ids: []
capability_tags:
  - "Planner"
  - "Workflow"
flags: []
sources: []
ui:
  cards_hint:
    - "Consultant offboarding plan"
  actions:
    - type: "start_workflow"
      target: "consultant_offboarding"
      label: "Plan Offboarding"
    - type: "open_register"
      target: "new_consultants"
      label: "Shortlist Replacements"
output_mode: "both"
graph_required: false
notes: "Document current engagements, knowledge transfer requirements, and contract exit terms"
---
### 210) What if we outgrow our current consultants — how do we transition to new help?

**Standard terms**
_None_

**Plain-English answer**
Use a structured **Offboarding Plan**: review active deliverables, document knowledge-transfer sessions, ensure all materials are stored in the platform, and initiate a replacement engagement via shortlisting.

**Applies to**
_None_

**Why it matters**
Prevents loss of institutional knowledge and maintains compliance momentum.

**Do next in our platform**
- Run **Consultant Offboarding** workflow.
- Populate **New Consultants** shortlist register.

**How our platform will help**
- **[Planner]** Alignment of exit and onboarding timelines.
- **[Workflow]** Checklist for handover and validation.

**Likely follow-ups**
- How to evaluate the success of offboarding?
"""






---
### 221) How do we know if our compliance program is actually effective vs. just checking boxes?

**Standard terms)**  
- **Management review (ISO 27001 Cl.9.3):** top-level assessment of ISMS performance.

**Plain-English answer**  
Track outcome metrics—incident rates, audit findings severity, risk reduction over time—instead of merely logging completed policies. Use dashboards to correlate controls with real-world security events.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 9.3

**Why it matters**  
Effective programs reduce incidents and fines, not just paperwork.

**Do next in our platform**  
- Open **Effectiveness Metrics** register.  
- Configure thresholds for key outcome indicators.

**How our platform will help**  
- **[Report]** Automated charts of incident trends vs control coverage.  
- **[Dashboard]** Alerts when metrics deviate from targets.

**Likely follow-ups**  
- Which leading indicators predict compliance breakdowns?

**Sources**  
- ISO/IEC 27001:2022 Clause 9.3
