id: Q067
query: >-
  What's the time impact on our IT team for implementing security controls?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/A.9"
overlap_ids:
  - "ISO27002:2022/9.1"
capability_tags:
  - "Planner"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Access Control"
    id: "ISO27001:2022/A.9"
    locator: "Annex A.9"
  - title: "ISO/IEC 27002:2022 — User Access Management"
    id: "ISO27002:2022/9.1"
    locator: "Section 9.1"
ui:
  cards_hint:
    - "IT effort estimator"
  actions:
    - type: "start_workflow"
      target: "it_control_implementation"
      label: "Estimate IT Effort"
output_mode: "both"
graph_required: false
notes: "Estimating ~10–20 % of IT capacity during rollout"
---
### 67) What's the time impact on our IT team for implementing security controls?

**Standard terms**  
- **Access control (ISO 27001 Annex A.9):** implementing identity and access management.  
- **User access management (ISO 27002 Sect. 9.1):** operational guidance.

**Plain-English answer**  
Rollout of MFA, role-based access, patch automation, and logging typically consumes **10–20 % of IT bandwidth** over the first 3–6 months.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.9  
- **Also relevant/Overlaps:** ISO/IEC 27002:2022 Section 9.1

**Why it matters**  
Setting realistic IT project plans avoids overload and ensures quality.

**Do next in our platform**  
- Run the **IT Effort Estimator**.  
- Balance tasks across team members.

**How our platform will help**  
- **[Planner]** Control rollout scheduler.  
- **[Report]** IT capacity dashboard.

**Likely follow-ups**  
- “Which controls are most time-intensive?” (MFA and patch management)

**Sources**  
- ISO/IEC 27001:2022 Annex A.9  
- ISO/IEC 27002:2022 Section 9.1
