"""
# File: arioncomply-v1/ai-backend/python-backend/services/embedding/pipelines/placeholder_pipeline.py
# File Description: Deterministic placeholder embedding pipeline for tests/fallback
# Purpose: Provide fast deterministic vectors without semantic meaning for testing flows

Placeholder embedding pipeline for testing and system fallback.

⚠️ WARNING: This pipeline provides NO SEMANTIC UNDERSTANDING
- Uses deterministic character-based hashing
- Same input always produces same output (good for testing)
- Results based on character similarity, NOT meaning
- Never use for production user queries
"""

import asyncio
import hashlib
import math
import time
import uuid
from datetime import datetime
from typing import List, Optional

from ..pipeline_interface import (
    EmbeddingPipeline,
    PipelineMetadata,
    EmbeddingResult,
    HealthCheckResult,
    PipelineStatus,
    QualityTier
)


class PlaceholderPipeline(EmbeddingPipeline):
    """
    Placeholder pipeline using deterministic character hashing.
    
    ⚠️ NO SEMANTIC UNDERSTANDING - Testing and fallback only!
    """
    
    DIMENSION = 768
    
    def __init__(self, config=None):
        """Initialize placeholder pipeline (no-op)."""
        super().__init__(config)
        self._name = "placeholder"
        self._metadata = PipelineMetadata(
            name=self._name,
            model_name="character-ascii-hash-v1.0",
            dimension=self.DIMENSION,
            quality_tier=QualityTier.TESTING_ONLY,
            is_local=True,
            version="1.0.0",
            provider="ArionComply",
            creation_date=datetime(2025, 9, 13),
            memory_mb=0,  # No model loading required
            inference_time_ms=2,
            context_length=None,  # No limit for character hashing
            config={
                "algorithm": "character-ascii-hash",
                "normalization": "l2",
                "deterministic": True,
                "warning": "NO SEMANTIC UNDERSTANDING - Testing only!"
            }
        )
    
    @property
    def name(self) -> str:
        """Return pipeline name."""
        return self._name
    
    @property
    def metadata(self) -> PipelineMetadata:
        """Return static metadata for placeholder pipeline."""
        return self._metadata
    
    async def load(self) -> None:
        """Load the pipeline (no-op for placeholder)."""
        # Simulate minimal loading time
        await asyncio.sleep(0.001)
        self._is_loaded = True
    
    async def embed_texts(self, texts: List[str], trace_id: Optional[str] = None) -> EmbeddingResult:
        """
        Generate deterministic placeholder embeddings.
        
        ⚠️ WARNING: NO SEMANTIC UNDERSTANDING
        """
        start_time = time.time()
        operation_id = str(uuid.uuid4())
        
        try:
            # Validate inputs
            self.validate_inputs(texts)
            
            # Generate placeholder embeddings
            embeddings = []
            for text in texts:
                vector = self._generate_placeholder_embedding(text)
                embeddings.append(vector)
            
            processing_time_ms = (time.time() - start_time) * 1000
            
            return self._create_embedding_result(
                operation_id=operation_id,
                input_texts=texts,
                embeddings=embeddings,
                processing_time_ms=processing_time_ms,
                success=True,
                trace_id=trace_id
            )
            
        except Exception as e:
            processing_time_ms = (time.time() - start_time) * 1000
            return self._create_embedding_result(
                operation_id=operation_id,
                input_texts=texts,
                embeddings=[],
                processing_time_ms=processing_time_ms,
                success=False,
                error_message=str(e),
                trace_id=trace_id
            )
    
    def _generate_placeholder_embedding(self, text: str) -> List[float]:
        """
        Generate deterministic embedding from text characters.
        
        Uses multiple hash functions for better vector distribution.
        """
        # Initialize vector
        vector = [0.0] * self.DIMENSION
        
        # Method 1: Direct character mapping (first half of vector)
        for i, char in enumerate(text[:self.DIMENSION // 2]):
            ascii_val = ord(char)
            # Normalize ASCII value to [0, 1] range
            vector[i] = (ascii_val % 128) / 128.0
        
        # Method 2: Hash-based distribution (second half of vector)
        text_bytes = text.encode('utf-8')
        for i in range(self.DIMENSION // 2, self.DIMENSION):
            # Use different hash seeds for different positions
            seed = f"{i}_{text}".encode('utf-8')
            hash_val = hashlib.md5(seed).hexdigest()
            # Convert hex to float in [0, 1]
            hex_int = int(hash_val[:8], 16)
            vector[i] = (hex_int % 10000) / 10000.0
        
        # Method 3: Text statistics (overlay on existing values)
        text_stats = self._calculate_text_stats(text)
        stats_indices = [0, 10, 20, 30, 40, 50]  # Spread across vector
        for i, stat in enumerate(text_stats):
            if i < len(stats_indices):
                idx = stats_indices[i]
                vector[idx] = (vector[idx] + stat) / 2.0  # Blend with existing
        
        # L2 normalization
        norm = math.sqrt(sum(x * x for x in vector) or 1.0)
        return [x / norm for x in vector]
    
    def _calculate_text_stats(self, text: str) -> List[float]:
        """Calculate basic text statistics for additional vector features."""
        if not text:
            return [0.0] * 6
        
        # Basic statistics normalized to [0, 1]
        stats = [
            len(text) / 1000.0,  # Length (capped at 1000)
            sum(1 for c in text if c.isalpha()) / len(text),  # Alpha ratio
            sum(1 for c in text if c.isdigit()) / len(text),  # Digit ratio
            sum(1 for c in text if c.isspace()) / len(text),  # Space ratio
            sum(1 for c in text if c.isupper()) / len(text),  # Uppercase ratio
            len(set(text.lower())) / len(text)  # Character diversity
        ]
        
        return stats
    
    async def health_check(self) -> HealthCheckResult:
        """Perform health check on placeholder pipeline."""
        start_time = time.time()
        
        try:
            # Test embedding generation
            test_result = await self.embed_texts(["test embedding generation"])
            test_time_ms = (time.time() - start_time) * 1000
            
            if test_result.success and len(test_result.embeddings) == 1:
                status = PipelineStatus.HEALTHY
                error_message = None
            else:
                status = PipelineStatus.FAILED
                error_message = test_result.error_message or "Test embedding failed"
            
            return HealthCheckResult(
                pipeline_name=self.name,
                status=status,
                timestamp=datetime.utcnow(),
                load_time_ms=1.0,  # Minimal load time
                test_inference_time_ms=test_time_ms,
                memory_usage_mb=1.0,  # Minimal memory
                error_message=error_message,
                is_model_loaded=self._is_loaded,
                dependencies_available=True  # No external dependencies
            )
            
        except Exception as e:
            return HealthCheckResult(
                pipeline_name=self.name,
                status=PipelineStatus.FAILED,
                timestamp=datetime.utcnow(),
                error_message=str(e),
                error_details={"exception_type": type(e).__name__},
                is_model_loaded=self._is_loaded,
                dependencies_available=True
            )
    
    async def unload(self) -> None:
        """Unload the pipeline (no-op for placeholder)."""
        self._is_loaded = False
