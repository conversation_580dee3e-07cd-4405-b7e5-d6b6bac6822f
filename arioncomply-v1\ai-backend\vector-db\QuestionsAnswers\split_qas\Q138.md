id: Q138
query: >-
  How do we handle data subject rights like deletion when AI has learned from the data?
packs:
  - "GDPR:2016"
  - "EUAI:2024"
primary_ids:
  - "GDPR:2016/Art.17"
  - "EUAI:2024/Art.22"
overlap_ids: []
capability_tags:
  - "Report"
  - "Workflow"
flags:
  - "LOCAL LAW CHECK"
sources:
  - title: "GDPR — Right to Erasure"
    id: "GDPR:2016/Art.17"
    locator: "Article 17"
  - title: "EU AI Act — Data Governance"
    id: "EUAI:2024/Art.22"
    locator: "Article 22"
ui:
  cards_hint:
    - "AI retraining guide"
  actions:
    - type: "start_workflow"
      target: "ai_dsar_handling"
      label: "Manage AI DSARs"
output_mode: "both"
graph_required: false
notes: "May require model retraining or data isolation—document process"
---
### 138) How do we handle data subject rights like deletion when AI has learned from the data?

**Standard terms**  
- **Erasure (GDPR Art. 17):** remove personal data.  
- **Data governance (EU AI Act Art. 22):** obligations on data quality and lifecycle.

**Plain-English answer**  
You must delete the subject’s data and retrain or fine-tune models to remove its influence. Use data isolation techniques or differential privacy. Document each retraining and maintain versioned models.

**Applies to**  
- **Primary:** GDPR Article 17; EU AI Act Article 22

**Why it matters**  
Ensures right-to-erasure compliance without compromising model integrity.

**Do next in our platform**  
- Launch **AI DSAR Handling** workflow.  
- Log retraining activities and version changes.

**How our platform will help**  
- **[Workflow]** Steps for data removal and model retraining.  
- **[Report]** Audit trail of model versions and data deleted.

**Likely follow-ups**  
- “How long does retraining take?” (Depends on model size—estimate in workflow)

**Sources**  
- GDPR Article 17; EU AI Act Article 22

**Legal note:** Check local guidance on AI-specific DSAR practices.  
