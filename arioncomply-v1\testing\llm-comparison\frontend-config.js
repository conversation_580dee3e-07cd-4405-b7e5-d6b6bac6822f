// File: arioncomply-v1/testing/llm-comparison/frontend-config.js
// File Description: Test harness runtime config
// Purpose: Provide Supabase and Edge function endpoints + anon key to the UI
// Shape: { supabaseUrl: string, functionUrl: string, anonKey: string }
// Auto-generated configuration
window.COMPLIANCE_CONFIG = {
    supabaseUrl: 'https://zjhfgskhizkjilbsetiq.supabase.co',
    functionUrl: 'https://zjhfgskhizkjilbsetiq.supabase.co/functions/v1/compliance-proxy',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpqaGZnc2toaXpramlsYnNldGlxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ2NjU3MjgsImV4cCI6MjA3MDI0MTcyOH0.1pafbKFWXUS-MDLisAKbC1obSc2zG5umb-Ysx3jABCw'
};
