"""
File: arioncomply-v1/ai-backend/python-backend/services/ingestion/embedder.py
File Description: Multi-pipeline embedding interface for ingestion
Purpose: Generate embeddings for text chunks with automatic pipeline selection
Inputs: list of strings
Outputs: list[list[float]] with dimensions based on active pipeline
Notes: Uses multi-pipeline architecture with fallback and audit trail
"""

from typing import List, Optional
import asyncio
import logging

from services.embedding import embed_texts as async_embed_texts

logger = logging.getLogger(__name__)


# Legacy synchronous interface for backward compatibility
def embed_texts(texts: List[str], pipeline_name: Optional[str] = None) -> List[List[float]]:
    """
    Generate embeddings for a list of texts (synchronous interface).
    
    This function provides backward compatibility for the ingestion pipeline
    while leveraging the new multi-pipeline embedding architecture.
    
    Args:
        texts: List of texts to embed
        pipeline_name: Optional specific pipeline to use
        
    Returns:
        List of embedding vectors
        
    Note: This is a synchronous wrapper around the async multi-pipeline system.
    For new code, prefer using the async interface directly.
    """
    if not texts:
        return []
    
    try:
        # Run async function in event loop
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If we're already in an async context, create a new thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(_run_async_embed, texts, pipeline_name)
                result = future.result()
        else:
            # Run in current thread's event loop
            result = loop.run_until_complete(_async_embed_wrapper(texts, pipeline_name))
        
        return result.embeddings
        
    except Exception as e:
        logger.error(f"Embedding failed, falling back to placeholder: {e}")
        # Fallback to deterministic placeholder for compatibility
        return _fallback_placeholder_embeddings(texts)


async def _async_embed_wrapper(texts: List[str], pipeline_name: Optional[str] = None):
    """Wrapper for async embedding function."""
    return await async_embed_texts(texts, pipeline_name=pipeline_name)


def _run_async_embed(texts: List[str], pipeline_name: Optional[str] = None):
    """Run async embedding in a new event loop."""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_async_embed_wrapper(texts, pipeline_name))
    finally:
        loop.close()


def _fallback_placeholder_embeddings(texts: List[str]) -> List[List[float]]:
    """
    Fallback placeholder embeddings for emergency compatibility.
    
    This provides the same deterministic behavior as the original embedder
    in case the multi-pipeline system is not available.
    """
    import math
    
    DIM = 768
    out: List[List[float]] = []
    
    for t in texts:
        vec = [0.0] * DIM
        for i, ch in enumerate(t[:DIM]):
            vec[i] = ((ord(ch) % 53) / 53.0)
        # L2 normalize
        norm = math.sqrt(sum(x * x for x in vec) or 1.0)
        out.append([x / norm for x in vec])
    
    return out


# Async interface for new code
async def embed_texts_async(texts: List[str], pipeline_name: Optional[str] = None, trace_id: Optional[str] = None):
    """
    Generate embeddings using the multi-pipeline system (async interface).
    
    This is the preferred interface for new code as it provides:
    - Full access to pipeline metadata and audit trail
    - Better error handling and fallback
    - Performance benefits of async operation
    
    Args:
        texts: List of texts to embed  
        pipeline_name: Optional specific pipeline to use
        trace_id: Optional trace ID for operation tracking
        
    Returns:
        EmbeddingResult with complete audit trail
    """
    return await async_embed_texts(texts, pipeline_name=pipeline_name, trace_id=trace_id)
