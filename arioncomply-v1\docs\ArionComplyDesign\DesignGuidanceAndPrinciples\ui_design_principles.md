# UI Design Principles

## Overview
This document establishes unified design principles for UI widget interfaces, resolving inconsistencies between DataDriveUISchemaAndFunctions metadata schema and SubscriptionManagement implementation. Flutter Web and Flutter Native front end shall always be assumed.

## Current State Analysis

### Inconsistencies Identified
- **Widget Parameters**: Mismatch between UI config schema and Flutter Web/Native widget properties
- **Field Mapping**: Database field names don't align with widget display names
- **Data Requirements**: Widgets have specific needs not accounted for in metadata schema
- **State Management**: Inconsistent approach to form state and validation

## Unified UI Widget Architecture

### 1. Metadata-Driven Widget Schema

#### Base Widget Configuration
```json
{
  "widget_registry": {
    "subscription_card": {
      "type": "display_card",
      "category": "subscription_management",
      "data_source": {
        "table": "org_subscriptions",
        "primary_key": "subscription_id",
        "required_fields": ["plan_name", "status", "next_billing_date"],
        "computed_fields": ["days_until_renewal", "usage_percentage"]
      },
      "ui_config": {
        "layout": "card",
        "responsive": true,
        "actions": ["edit", "cancel", "upgrade"],
        "conditional_display": {
          "show_cancel": "status != 'cancelled'",
          "show_upgrade": "plan_tier != 'enterprise'"
        }
      },
      "field_mappings": {
        "plan_name": {
          "display_name": "Subscription Plan",
          "widget": "text",
          "format": "title_case",
          "required": true
        },
        "status": {
          "display_name": "Status",
          "widget": "status_badge",
          "color_mapping": {
            "active": "green",
            "pending": "yellow",
            "cancelled": "red"
          }
        }
      }
    }
  }
}
```

#### Form Widget Configuration
```json
{
  "subscription_form": {
    "type": "form",
    "category": "subscription_management", 
    "data_source": {
      "table": "org_subscriptions",
      "validation_rules": "subscription_validation_schema"
    },
    "form_config": {
      "layout": "vertical",
      "submit_endpoint": "/api/subscriptions",
      "validation": "client_and_server",
      "auto_save": false
    },
    "sections": [
      {
        "section_id": "plan_selection",
        "title": "Choose Your Plan",
        "fields": [
          {
            "field_name": "plan_id",
            "display_name": "Subscription Plan",
            "widget": "dropdown",
            "required": true,
            "data_source": {
              "table": "subscription_plans",
              "value_field": "plan_id",
              "display_field": "plan_name",
              "filter": "is_active = true"
            },
            "validation": {
              "required": true,
              "custom_validator": "validatePlanEligibility"
            }
          }
        ]
      }
    ]
  }
}
```

### 2. Flutter Widget Architecture

#### Base Metadata-Driven Widget
```dart
// widgets/metadata_widget.dart
class MetadataWidget extends StatefulWidget {
  final String widgetId;
  final String organizationId;
  final String? recordId;
  final Map<String, dynamic>? overrides;
  final Function(String action, Map<String, dynamic> data)? onAction;

  const MetadataWidget({
    Key? key,
    required this.widgetId,
    required this.organizationId,
    this.recordId,
    this.overrides,
    this.onAction,
  }) : super(key: key);

  @override
  _MetadataWidgetState createState() => _MetadataWidgetState();
}

class _MetadataWidgetState extends State<MetadataWidget> {
  late Future<WidgetConfigData> _configDataFuture;

  @override
  void initState() {
    super.initState();
    _configDataFuture = _loadWidgetConfigAndData();
  }

  Future<WidgetConfigData> _loadWidgetConfigAndData() async {
    final config = await WidgetDataService.getWidgetConfig(widget.widgetId);
    final data = await WidgetDataService.getWidgetData(
      widgetId: widget.widgetId,
      organizationId: widget.organizationId,
      recordId: widget.recordId,
    );
    
    // Merge with overrides if provided
    final mergedConfig = widget.overrides != null
        ? {...config, ...widget.overrides!}
        : config;
    
    return WidgetConfigData(config: mergedConfig, data: data);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<WidgetConfigData>(
      future: _configDataFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const WidgetSkeleton();
        }

        if (snapshot.hasError) {
          return ErrorDisplay(error: snapshot.error.toString());
        }

        final configData = snapshot.data!;
        
        // Dynamically render widget based on type
        switch (configData.config['type']) {
          case 'display_card':
            return DisplayCard(
              config: configData.config,
              data: configData.data,
              onAction: widget.onAction,
            );
          case 'form':
            return DynamicForm(
              config: configData.config,
              data: configData.data,
              onAction: widget.onAction,
            );
          case 'data_table':
            return DataTable(
              config: configData.config,
              data: configData.data,
              onAction: widget.onAction,
            );
          default:
            return Text('Unknown widget type: ${configData.config['type']}');
        }
      },
    );
  }
}

class WidgetConfigData {
  final Map<String, dynamic> config;
  final Map<String, dynamic> data;

  WidgetConfigData({required this.config, required this.data});
}
```

#### Subscription-Specific Widgets
```dart
// widgets/subscription/subscription_card.dart
class SubscriptionCard extends StatelessWidget {
  final String subscriptionId;
  final String organizationId;
  final String variant;
  final List<String> actions;

  const SubscriptionCard({
    Key? key,
    required this.subscriptionId,
    required this.organizationId,
    this.variant = 'detailed',
    this.actions = const ['edit', 'cancel'],
  }) : super(key: key);

  void _handleAction(String action, Map<String, dynamic> data) {
    switch (action) {
      case 'edit':
        // Navigate to edit form
        Navigator.pushNamed(
          context,
          '/subscription/edit',
          arguments: {'subscriptionId': subscriptionId},
        );
        break;
      case 'cancel':
        // Show cancellation dialog
        _showCancellationDialog();
        break;
      case 'upgrade':
        // Navigate to upgrade flow
        Navigator.pushNamed(
          context,
          '/subscription/upgrade',
          arguments: {'subscriptionId': subscriptionId},
        );
        break;
    }
  }

  void _showCancellationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Subscription'),
        content: const Text('Are you sure you want to cancel this subscription?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Perform cancellation logic
            },
            child: const Text('Yes'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MetadataWidget(
      widgetId: 'subscription_card',
      organizationId: organizationId,
      recordId: subscriptionId,
      overrides: {
        'ui_config': {
          'layout': variant,
          'actions': actions,
        },
      },
      onAction: _handleAction,
    );
  }
}
```

### 3. Data Binding and Field Mapping

#### Field Mapping Utility
```dart
// utils/field_mapper.dart
import 'package:intl/intl.dart';

class FieldMapping {
  final String displayName;
  final String widget;
  final String? format;
  final List<ValidationRule>? validation;
  final String? computed; // Dart expression for computed fields

  FieldMapping({
    required this.displayName,
    required this.widget,
    this.format,
    this.validation,
    this.computed,
  });
}

class FieldMapper {
  /// Transform raw data based on field mappings
  static Map<String, dynamic> mapFields(
    Map<String, dynamic> rawData,
    Map<String, FieldMapping> fieldMappings,
  ) {
    final mappedData = <String, dynamic>{};

    fieldMappings.forEach((fieldName, mapping) {
      var value = rawData[fieldName];

      // Handle computed fields
      if (mapping.computed != null) {
        value = _evaluateExpression(mapping.computed!, rawData);
      }

      // Apply formatting
      if (mapping.format != null) {
        value = _formatValue(value, mapping.format!);
      }

      mappedData[fieldName] = {
        'value': value,
        'displayName': mapping.displayName,
        'widget': mapping.widget,
        'validation': mapping.validation ?? <ValidationRule>[],
      };
    });

    return mappedData;
  }

  /// Safe evaluation of Dart expressions for computed fields
  static dynamic _evaluateExpression(String expression, Map<String, dynamic> context) {
    // For security reasons, implement a safe expression evaluator
    // This is a simplified version - in production, use a proper expression parser
    try {
      // Basic math expressions and field references
      if (expression.contains('+') || expression.contains('-') || 
          expression.contains('*') || expression.contains('/')) {
        // Handle simple arithmetic with field substitution
        String evaluableExpression = expression;
        context.forEach((key, value) {
          if (value is num) {
            evaluableExpression = evaluableExpression.replaceAll(key, value.toString());
          }
        });
        // In production, use a proper expression parser library
        return evaluableExpression; // Placeholder
      }
      return context[expression] ?? expression;
    } catch (e) {
      return null;
    }
  }

  /// Format value based on format specification
  static dynamic _formatValue(dynamic value, String format) {
    if (value == null) return null;

    switch (format) {
      case 'currency':
        if (value is num) {
          return NumberFormat.currency(locale: 'en_US', symbol: '\$').format(value);
        }
        return value;
      
      case 'date':
        if (value is String) {
          try {
            final date = DateTime.parse(value);
            return DateFormat('MM/dd/yyyy').format(date);
          } catch (e) {
            return value;
          }
        }
        return value;
      
      case 'title_case':
        return value.toString().split(' ').map((word) => 
          word.isNotEmpty 
              ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
              : ''
        ).join(' ');
      
      default:
        return value;
    }
  }
}

class ValidationRule {
  final String type;
  final String message;
  final dynamic value;

  ValidationRule({
    required this.type,
    required this.message,
    this.value,
  });
}
```

### 4. Form State Management

#### Metadata-Driven Form Widget
```dart
// widgets/metadata_form.dart
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class MetadataForm extends StatefulWidget {
  final Map<String, dynamic> config;
  final Map<String, dynamic>? initialData;
  final Function(Map<String, dynamic> data)? onSubmit;

  const MetadataForm({
    Key? key,
    required this.config,
    this.initialData,
    this.onSubmit,
  }) : super(key: key);

  @override
  _MetadataFormState createState() => _MetadataFormState();
}

class _MetadataFormState extends State<MetadataForm> {
  final _formKey = GlobalKey<FormState>();
  late Map<String, dynamic> _formData;
  int _currentSection = 0;
  FormSubmitStatus _submitStatus = FormSubmitStatus.idle;

  @override
  void initState() {
    super.initState();
    _formData = Map.from(widget.initialData ?? {});
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _submitStatus = FormSubmitStatus.submitting;
      });

      try {
        final response = await http.post(
          Uri.parse(widget.config['form_config']['submit_endpoint']),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode(_formData),
        );

        if (response.statusCode != 200) {
          throw Exception('HTTP ${response.statusCode}: ${response.reasonPhrase}');
        }

        setState(() {
          _submitStatus = FormSubmitStatus.success;
        });

        if (widget.onSubmit != null) {
          widget.onSubmit!(_formData);
        }
      } catch (error) {
        print('Form submission error: $error');
        setState(() {
          _submitStatus = FormSubmitStatus.error;
        });
      }
    }
  }

  void _nextSection() {
    final sections = widget.config['sections'] as List;
    if (_currentSection < sections.length - 1) {
      setState(() {
        _currentSection++;
      });
    }
  }

  void _previousSection() {
    if (_currentSection > 0) {
      setState(() {
        _currentSection--;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final sections = widget.config['sections'] as List;
    final currentSectionConfig = sections[_currentSection];

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            currentSectionConfig['title'],
            style: Theme.of(context).textTheme.headline6,
          ),
          const SizedBox(height: 16),
          
          // Render form fields based on section configuration
          ...currentSectionConfig['fields'].map<Widget>((fieldConfig) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: _buildFormField(fieldConfig),
            );
          }).toList(),
          
          const SizedBox(height: 24),
          
          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (_currentSection > 0)
                ElevatedButton(
                  onPressed: _previousSection,
                  child: const Text('Previous'),
                ),
              
              if (_currentSection < sections.length - 1)
                ElevatedButton(
                  onPressed: _nextSection,
                  child: const Text('Next'),
                )
              else
                ElevatedButton(
                  onPressed: _submitStatus == FormSubmitStatus.submitting 
                      ? null 
                      : _submitForm,
                  child: _submitStatus == FormSubmitStatus.submitting
                      ? const CircularProgressIndicator()
                      : const Text('Submit'),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFormField(Map<String, dynamic> fieldConfig) {
    final fieldName = fieldConfig['field_name'];
    
    switch (fieldConfig['widget']) {
      case 'text':
        return TextFormField(
          decoration: InputDecoration(
            labelText: fieldConfig['display_name'],
            border: const OutlineInputBorder(),
          ),
          initialValue: _formData[fieldName]?.toString() ?? '',
          validator: (value) => _validateField(value, fieldConfig),
          onChanged: (value) {
            setState(() {
              _formData[fieldName] = value;
            });
          },
        );
        
      case 'dropdown':
        return DropdownButtonFormField<String>(
          decoration: InputDecoration(
            labelText: fieldConfig['display_name'],
            border: const OutlineInputBorder(),
          ),
          value: _formData[fieldName]?.toString(),
          items: _getDropdownItems(fieldConfig),
          validator: (value) => _validateField(value, fieldConfig),
          onChanged: (value) {
            setState(() {
              _formData[fieldName] = value;
            });
          },
        );
        
      default:
        return Text('Unsupported field type: ${fieldConfig['widget']}');
    }
  }

  List<DropdownMenuItem<String>> _getDropdownItems(Map<String, dynamic> fieldConfig) {
    // In a real implementation, fetch dropdown items from data source
    return [
      const DropdownMenuItem(value: '1', child: Text('Option 1')),
      const DropdownMenuItem(value: '2', child: Text('Option 2')),
    ];
  }

  String? _validateField(String? value, Map<String, dynamic> fieldConfig) {
    final validation = fieldConfig['validation'];
    
    if (validation?['required'] == true && (value == null || value.isEmpty)) {
      return 'This field is required';
    }
    
    return null;
  }
}

enum FormSubmitStatus { idle, submitting, success, error }
```

### 5. Widget Library Integration

#### Base UI Widgets
```dart
// widgets/ui/dynamic_field.dart
import 'package:flutter/material.dart';

class DynamicField extends StatelessWidget {
  final FieldMapping fieldConfig;
  final dynamic value;
  final Function(dynamic)? onChange;
  final String? error;
  final bool disabled;

  const DynamicField({
    Key? key,
    required this.fieldConfig,
    required this.value,
    this.onChange,
    this.error,
    this.disabled = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          fieldConfig.displayName,
          style: Theme.of(context).textTheme.subtitle1,
        ),
        const SizedBox(height: 8),
        _renderField(),
        if (error != null) ...[
          const SizedBox(height: 4),
          Text(
            error!,
            style: const TextStyle(color: Colors.red, fontSize: 12.0),
          ),
        ],
      ],
    );
  }

  Widget _renderField() {
    switch (fieldConfig.widget) {
      case 'text':
        return TextField(
          controller: TextEditingController(text: value?.toString() ?? ''),
          onChanged: onChange,
          enabled: !disabled,
          decoration: InputDecoration(
            errorText: error,
            border: const OutlineInputBorder(),
            filled: disabled,
            fillColor: disabled ? Colors.grey[100] : null,
          ),
        );
      
      case 'dropdown':
        return DropdownButton<String>(
          value: value?.toString(),
          onChanged: disabled ? null : (newValue) {
            if (onChange != null) {
              onChange!(newValue);
            }
          },
          isExpanded: true,
          items: _getDropdownItems(),
          hint: const Text('Select an option'),
        );
      
      case 'status_badge':
        final colorMapping = fieldConfig.colorMapping ?? {};
        final color = _getStatusColor(value?.toString() ?? '', colorMapping);
        
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16.0),
            border: Border.all(color: color),
          ),
          child: Text(
            value?.toString() ?? '',
            style: TextStyle(color: color, fontWeight: FontWeight.w500),
          ),
        );
      
      default:
        return Text(value?.toString() ?? '');
    }
  }

  List<DropdownMenuItem<String>> _getDropdownItems() {
    // In a real implementation, get options from fieldConfig
    return [
      const DropdownMenuItem(value: '1', child: Text('Option 1')),
      const DropdownMenuItem(value: '2', child: Text('Option 2')),
    ];
  }

  Color _getStatusColor(String status, Map<String, String> colorMapping) {
    final colorName = colorMapping[status] ?? 'grey';
    
    switch (colorName) {
      case 'green':
        return Colors.green;
      case 'yellow':
        return Colors.amber;
      case 'red':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
```

### 6. API Integration Layer

#### Widget Data Service
```dart
// services/widget_data_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;

class WidgetDataRequest {
  final String widgetId;
  final String organizationId;
  final String? recordId;
  final Map<String, dynamic>? filters;

  WidgetDataRequest({
    required this.widgetId,
    required this.organizationId,
    this.recordId,
    this.filters,
  });
}

class WidgetDataService {
  static const String _baseUrl = '/api/widgets';

  /// Get widget configuration
  static Future<Map<String, dynamic>> getWidgetConfig(String widgetId) async {
    final response = await http.get(Uri.parse('$_baseUrl/$widgetId/config'));
    
    if (response.statusCode != 200) {
      throw Exception('Failed to fetch widget config: ${response.statusCode}');
    }
    
    return jsonDecode(response.body);
  }

  /// Get widget data
  static Future<Map<String, dynamic>> getWidgetData({
    required String widgetId,
    required String organizationId,
    String? recordId,
    Map<String, dynamic>? filters,
  }) async {
    final queryParams = <String, String>{
      'organization_id': organizationId,
    };
    
    if (recordId != null) {
      queryParams['record_id'] = recordId;
    }
    
    if (filters != null) {
      queryParams['filters'] = jsonEncode(filters);
    }

    final uri = Uri.parse('$_baseUrl/$widgetId/data')
        .replace(queryParameters: queryParams);
    
    final response = await http.get(uri);
    
    if (response.statusCode != 200) {
      throw Exception('Failed to fetch widget data: ${response.statusCode}');
    }
    
    return jsonDecode(response.body);
  }

  /// Submit widget action
  static Future<Map<String, dynamic>> submitWidgetAction(
    String widgetId,
    String action,
    Map<String, dynamic> data,
  ) async {
    final response = await http.post(
      Uri.parse('$_baseUrl/$widgetId/actions/$action'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(data),
    );
    
    if (response.statusCode != 200) {
      throw Exception('Failed to execute widget action: ${response.statusCode}');
    }
    
    return jsonDecode(response.body);
  }
}
```

### 7. Supabase Edge Function Implementation

#### Widget Data Handler
```typescript
// supabase/functions/widget-data/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "@supabase/supabase-js";

serve(async (req) => {
  const url = new URL(req.url);
  const widgetId = url.pathname.split('/')[2];
  const action = url.searchParams.get('action') || 'get_data';

  switch (action) {
    case 'get_config':
      return await getWidgetConfig(widgetId);
    case 'get_data':
      return await getWidgetData(widgetId, req);
    case 'submit_action':
      return await submitWidgetAction(widgetId, req);
    default:
      return new Response('Invalid action', { status: 400 });
  }
});

async function getWidgetConfig(widgetId: string): Promise<Response> {
  const { data, error } = await supabase
    .from('system_metadata.widget_registry')
    .select('widget_config')
    .eq('widget_id', widgetId)
    .single();

  if (error) {
    return new Response(`Widget not found: ${error.message}`, { status: 404 });
  }

  return new Response(JSON.stringify(data.widget_config));
}

async function getWidgetData(widgetId: string, req: Request): Promise<Response> {
  const url = new URL(req.url);
  const organizationId = url.searchParams.get('organization_id');
  const recordId = url.searchParams.get('record_id');

  // Get widget configuration
  const config = await getWidgetConfig(widgetId);
  const widgetConfig = await config.json();

  // Build dynamic query based on widget configuration
  let query = supabase
    .from(widgetConfig.data_source.table)
    .select(widgetConfig.data_source.required_fields.join(', '));

  // Apply organization filter
  if (organizationId) {
    query = query.eq('organization_id', organizationId);
  }

  // Apply record filter if specified
  if (recordId) {
    query = query.eq(widgetConfig.data_source.primary_key, recordId);
  }

  const { data, error } = await query;

  if (error) {
    return new Response(`Data fetch error: ${error.message}`, { status: 500 });
  }

  return new Response(JSON.stringify(data));
}
```

## Implementation Guidelines

### Database Integration
1. **Widget Registry**: Store widget configurations in metadata tables
2. **Field Mapping**: Maintain consistent field name mappings
3. **Validation Rules**: Centralize validation logic in database functions

### Flutter Architecture
1. **Metadata-Driven**: All widgets derive configuration from metadata
2. **Reusable Base**: Common base widgets for different UI patterns  
3. **Type Safety**: Strong Dart typing for widget configs

### API Design
1. **RESTful Endpoints**: Consistent API structure for widget operations
2. **Dynamic Queries**: Build queries based on widget metadata
3. **Action Handling**: Standardized action execution framework

## Testing Framework

### Widget Testing Strategy
```typescript
// __tests__/MetadataComponent.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { MetadataComponent } from '../components/MetadataComponent';

describe('MetadataComponent', () => {
  const mockConfig = {
    type: 'display_card',
    data_source: { table: 'test_table' },
    field_mappings: {
      name: { display_name: 'Name', component: 'text' }
    }
  };

  it('renders component based on metadata configuration', async () => {
    render(
      <MetadataComponent 
        componentId="test_component"
        organizationId="org-123"
      />
    );

    expect(screen.getByText('Name')).toBeInTheDocument();
  });

  it('handles component actions correctly', async () => {
    const mockOnAction = jest.fn();
    
    render(
      <MetadataComponent 
        componentId="subscription_card"
        organizationId="org-123"
        onAction={mockOnAction}
      />
    );

    fireEvent.click(screen.getByText('Edit'));
    expect(mockOnAction).toHaveBeenCalledWith('edit', expect.any(Object));
  });
});
```

## Migration Strategy

### Phase 1: Component Registry Setup
- [ ] Create component metadata tables
- [ ] Define base component configurations
- [ ] Implement metadata loading services

### Phase 2: Component Refactoring  
- [ ] Refactor existing components to use metadata
- [ ] Update component props to match schema
- [ ] Implement field mapping system

### Phase 3: Integration Testing
- [ ] Test component rendering with metadata
- [ ] Verify form submission workflows
- [ ] Performance testing with dynamic components

## Assumptions
- Flutter 3.0+ with StatefulWidget/StatelessWidget for widget state management
- Dart for type safety and interfaces
- Supabase Edge Functions for widget data services
- Flutter themes for widget styling

## Review Flags
- [ ] Widget rendering performance with complex metadata
- [ ] Package size impact of dynamic widget loading
- [ ] Accessibility compliance for generated widgets
- [ ] SEO implications of metadata-driven widgets

## Completeness Checklist
- [x] Widget metadata schema definition
- [x] Flutter widget architecture
- [x] Field mapping and data binding system
- [x] Form state management framework
- [x] API integration layer
- [x] Supabase edge function implementation
- [x] Testing framework specification
- [x] Migration strategy outline