# UI Design Principles

## Overview
This document establishes unified design principles for UI widget interfaces, resolving inconsistencies between DataDriveUISchemaAndFunctions metadata schema and SubscriptionManagement. 

### Frontend Technology Progression
- **MVP-Assessment-App & MVP-Demo-Light-App**: Flutter Web implementation for rapid deployment
- **MVP-Pilot & Production**: Flutter Native (iOS/Android) for enhanced mobile experience
- **Chat-Driven Interface**: Primary assessment method through natural language conversation
- **Forms**: Secondary/optional for structured data collection only

## Current State Analysis

### Inconsistencies Identified
- **Widget Parameters**: Mismatch between UI config schema and Flutter Web/Native elements
- **Field Mapping**: Database field names don't align with widget display names
- **Data Requirements**: Widgets have specific needs not accounted for in metadata schema
- **State Management**: Inconsistent approach to form state and validation

## Unified UI Widget Architecture

### 1. Metadata-Driven Widget Schema

#### Base Widget Configuration
```json
{
  "widget_registry": {
    "subscription_card": {
      "type": "display_card",
      "category": "subscription_management",
      "data_source": {
        "table": "org_subscriptions",
        "primary_key": "subscription_id",
        "required_fields": ["plan_name", "status", "next_billing_date"],
        "computed_fields": ["days_until_renewal", "usage_percentage"]
      },
      "ui_config": {
        "layout": "card",
        "responsive": true,
        "actions": ["edit", "cancel", "upgrade"],
        "conditional_display": {
          "show_cancel": "status != 'cancelled'",
          "show_upgrade": "plan_tier != 'enterprise'"
        }
      },
      "field_mappings": {
        "plan_name": {
          "display_name": "Subscription Plan",
          "widget": "text",
          "format": "title_case",
          "required": true
        },
        "status": {
          "display_name": "Status",
          "widget": "status_badge",
          "color_mapping": {
            "active": "green",
            "pending": "yellow",
            "cancelled": "red"
          }
        }
      }
    }
  }
}
```

#### Form Widget Configuration
```json
{
  "subscription_form": {
    "type": "form",
    "category": "subscription_management", 
    "data_source": {
      "table": "org_subscriptions",
      "validation_rules": "subscription_validation_schema"
    },
    "form_config": {
      "layout": "vertical",
      "submit_endpoint": "/api/subscriptions",
      "validation": "client_and_server",
      "auto_save": false
    },
    "sections": [
      {
        "section_id": "plan_selection",
        "title": "Choose Your Plan",
        "fields": [
          {
            "field_name": "plan_id",
            "display_name": "Subscription Plan",
            "widget": "dropdown",
            "required": true,
            "data_source": {
              "table": "subscription_plans",
              "value_field": "plan_id",
              "display_field": "plan_name",
              "filter": "is_active = true"
            },
            "validation": {
              "required": true,
              "custom_validator": "validatePlanEligibility"
            }
          }
        ]
      }
    ]
  }
}
```

### 2. Flutter Widget Architecture

#### Base Metadata-Driven Widget
```dart
// Pseudo-code for MetadataWidget
class MetadataWidget extends StatefulWidget {
  final String widgetId;
  final String organizationId;
  final String? recordId;
  final Map<String, dynamic>? overrides;
  final Function(String action, Map<String, dynamic> data)? onAction;

  MetadataWidget({
    required this.widgetId,
    required this.organizationId,
    this.recordId,
    this.overrides,
    this.onAction
  });

  @override
  _MetadataWidgetState createState() => _MetadataWidgetState();
}

class _MetadataWidgetState extends State<MetadataWidget> {
  late Future<Map<String, dynamic>> _configFuture;
  late Future<Map<String, dynamic>> _dataFuture;

  @override
  void initState() {
    super.initState();
    _configFuture = _loadWidgetConfig();
    _dataFuture = _loadWidgetData();
  }

  Future<Map<String, dynamic>> _loadWidgetConfig() async {
    // Load widget configuration from backend
    // Merge with overrides if provided
    return {...};
  }

  Future<Map<String, dynamic>> _loadWidgetData() async {
    // Load widget data based on configuration
    return {...};
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: Future.wait([_configFuture, _dataFuture]),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return WidgetSkeleton();
        }

        if (snapshot.hasError) {
          return ErrorDisplay(error: snapshot.error.toString());
        }

        final config = snapshot.data![0];
        final data = snapshot.data![1];
        
        // Dynamically render widget based on type
        switch (config['type']) {
          case 'display_card':
            return DisplayCard(
              config: config,
              data: data,
              onAction: widget.onAction
            );
          case 'form':
            return DynamicForm(
              config: config,
              data: data,
              onAction: widget.onAction
            );
          case 'data_table':
            return DataTable(
              config: config,
              data: data,
              onAction: widget.onAction
            );
          default:
            return Text('Unknown widget type: ${config['type']}');
        }
      }
    );
  }
}
```

#### Subscription-Specific Widgets
```dart
// Pseudo-code for SubscriptionCard
class SubscriptionCard extends StatelessWidget {
  final String subscriptionId;
  final String organizationId;
  final String variant;
  final List<String> actions;

  SubscriptionCard({
    required this.subscriptionId,
    required this.organizationId,
    this.variant = 'detailed',
    this.actions = const ['edit', 'cancel']
  });

  void _handleAction(String action, Map<String, dynamic> data) {
    switch (action) {
      case 'edit':
        // Navigate to edit form
        break;
      case 'cancel':
        // Show cancellation dialog
        break;
      case 'upgrade':
        // Navigate to upgrade flow
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return MetadataWidget(
      widgetId: 'subscription_card',
      organizationId: organizationId,
      recordId: subscriptionId,
      overrides: {
        'ui_config': {
          'layout': variant,
          'actions': actions
        }
      },
      onAction: _handleAction
    );
  }
}
```

### 3. Data Binding and Field Mapping

#### Field Mapping Utility
```dart
// Pseudo-code for field mapping utility
class FieldMapper {
  // Transform raw data based on field mappings
  static Map<String, dynamic> mapFields(
    Map<String, dynamic> rawData,
    Map<String, dynamic> fieldMappings
  ) {
    final mappedData = <String, dynamic>{};
    
    fieldMappings.forEach((fieldName, mapping) {
      var value = rawData[fieldName];
      
      // Handle computed fields
      if (mapping['computed'] != null) {
        value = _evaluateExpression(mapping['computed'], rawData);
      }
      
      // Apply formatting
      if (mapping['format'] != null) {
        value = _formatValue(value, mapping['format']);
      }
      
      mappedData[fieldName] = {
        'value': value,
        'displayName': mapping['display_name'],
        'widget': mapping['widget'],
        'validation': mapping['validation'] ?? []
      };
    });
    
    return mappedData;
  }

  // Utility methods
  static dynamic _evaluateExpression(String expression, Map<String, dynamic> context) {
    // Evaluate expressions using dart:mirrors or a simple expression parser
    // This is simplified for the pseudo-code
    return null; // Placeholder
  }

  static dynamic _formatValue(dynamic value, String format) {
    switch (format) {
      case 'currency':
        return NumberFormat.currency(locale: 'en_US', symbol: '\$').format(value);
      case 'date':
        return DateFormat('MM/dd/yyyy').format(DateTime.parse(value));
      case 'title_case':
        return value?.toString().split(' ').map((word) => 
          word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}' : ''
        ).join(' ');
      default:
        return value;
    }
  }
}
```

### 4. Form State Management

#### Metadata-Driven Form
```dart
// Pseudo-code for metadata-driven form
class MetadataForm extends StatefulWidget {
  final Map<String, dynamic> config;
  final Map<String, dynamic>? initialData;
  final Function(Map<String, dynamic> data)? onSubmit;

  MetadataForm({
    required this.config,
    this.initialData,
    this.onSubmit
  });

  @override
  _MetadataFormState createState() => _MetadataFormState();
}

class _MetadataFormState extends State<MetadataForm> {
  final _formKey = GlobalKey<FormState>();
  late Map<String, dynamic> _formData;
  int _currentSection = 0;
  String _submitStatus = 'idle';

  @override
  void initState() {
    super.initState();
    _formData = widget.initialData ?? {};
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _submitStatus = 'submitting';
      });
      
      try {
        // Submit form data to API endpoint
        final response = await http.post(
          Uri.parse(widget.config['form_config']['submit_endpoint']),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode(_formData)
        );

        if (response.statusCode != 200) {
          throw Exception('HTTP ${response.statusCode}: ${response.reasonPhrase}');
        }

        setState(() {
          _submitStatus = 'success';
        });
        
        if (widget.onSubmit != null) {
          widget.onSubmit!(_formData);
        }
      } catch (error) {
        print('Form submission error: $error');
        setState(() {
          _submitStatus = 'error';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final sections = widget.config['sections'] as List;
    final currentSectionConfig = sections[_currentSection];
    
    return Form(
      key: _formKey,
      child: Column(
        children: [
          Text(
            currentSectionConfig['title'],
            style: Theme.of(context).textTheme.headline6
          ),
          // Render form fields based on section configuration
          ...currentSectionConfig['fields'].map<Widget>((fieldConfig) {
            return _buildFormField(fieldConfig);
          }).toList(),
          
          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (_currentSection > 0)
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _currentSection--;
                    });
                  },
                  child: Text('Previous')
                ),
              
              if (_currentSection < sections.length - 1)
                ElevatedButton(
                  onPressed: () {
                    if (_validateCurrentSection()) {
                      setState(() {
                        _currentSection++;
                      });
                    }
                  },
                  child: Text('Next')
                )
              else
                ElevatedButton(
                  onPressed: _submitStatus == 'submitting' ? null : _submitForm,
                  child: _submitStatus == 'submitting' 
                    ? CircularProgressIndicator() 
                    : Text('Submit')
                )
            ]
          )
        ]
      )
    );
  }

  Widget _buildFormField(Map<String, dynamic> fieldConfig) {
    // Build appropriate form field based on field configuration
    // This is simplified for pseudo-code
    final fieldName = fieldConfig['field_name'];
    
    switch (fieldConfig['widget']) {
      case 'text':
        return TextFormField(
          decoration: InputDecoration(
            labelText: fieldConfig['display_name']
          ),
          initialValue: _formData[fieldName]?.toString() ?? '',
          validator: (value) => _validateField(value, fieldConfig),
          onChanged: (value) {
            setState(() {
              _formData[fieldName] = value;
            });
          }
        );
        
      case 'dropdown':
        return DropdownButtonFormField<String>(
          decoration: InputDecoration(
            labelText: fieldConfig['display_name']
          ),
          value: _formData[fieldName]?.toString(),
          items: _getDropdownItems(fieldConfig),
          validator: (value) => _validateField(value, fieldConfig),
          onChanged: (value) {
            setState(() {
              _formData[fieldName] = value;
            });
          }
        );
        
      default:
        return Text('Unsupported field type: ${fieldConfig['widget']}');
    }
  }

  List<DropdownMenuItem<String>> _getDropdownItems(Map<String, dynamic> fieldConfig) {
    // Fetch dropdown items from data source
    // Simplified for pseudo-code
    return [
      DropdownMenuItem(value: '1', child: Text('Option 1')),
      DropdownMenuItem(value: '2', child: Text('Option 2'))
    ];
  }

  String? _validateField(String? value, Map<String, dynamic> fieldConfig) {
    // Validate field based on validation rules
    if (fieldConfig['validation']?['required'] == true && (value == null || value.isEmpty)) {
      return 'This field is required';
    }
    
    // Additional validation logic
    return null;
  }

  bool _validateCurrentSection() {
    // Validate only the fields in the current section
    // Simplified for pseudo-code
    return true;
  }
}
```

### 5. Widget Library Integration

#### Base UI Widgets
```dart
// Pseudo-code for DynamicField widget
class DynamicField extends StatelessWidget {
  final Map<String, dynamic> fieldConfig;
  final dynamic value;
  final Function(dynamic)? onChange;
  final String? error;
  final bool disabled;

  DynamicField({
    required this.fieldConfig,
    required this.value,
    this.onChange,
    this.error,
    this.disabled = false
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          fieldConfig['display_name'],
          style: Theme.of(context).textTheme.subtitle1
        ),
        _renderField(),
        if (error != null)
          Text(
            error!,
            style: TextStyle(color: Colors.red, fontSize: 12.0)
          )
      ]
    );
  }

  Widget _renderField() {
    switch (fieldConfig['widget']) {
      case 'text':
        return TextField(
          controller: TextEditingController(text: value?.toString() ?? ''),
          onChanged: onChange,
          enabled: !disabled,
          decoration: InputDecoration(
            errorText: error,
            border: OutlineInputBorder()
          )
        );
      
      case 'dropdown':
        return DropdownButton<String>(
          value: value?.toString(),
          onChanged: disabled ? null : (newValue) {
            if (onChange != null) {
              onChange!(newValue);
            }
          },
          items: fieldConfig['options']?.map<DropdownMenuItem<String>>((option) {
            return DropdownMenuItem<String>(
              value: option['value'],
              child: Text(option['label'])
            );
          })?.toList() ?? []
        );
      
      case 'status_badge':
        final colorMapping = fieldConfig['color_mapping'] ?? {};
        final color = _getStatusColor(value, colorMapping);
        
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16.0),
            border: Border.all(color: color)
          ),
          child: Text(
            value?.toString() ?? '',
            style: TextStyle(color: color)
          )
        );
      
      default:
        return Text(value?.toString() ?? '');
    }
  }

  Color _getStatusColor(String status, Map<String, dynamic> colorMapping) {
    final colorName = colorMapping[status] ?? 'grey';
    
    switch (colorName) {
      case 'green':
        return Colors.green;
      case 'yellow':
        return Colors.amber;
      case 'red':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
```

### 6. API Integration Layer

#### Widget Data Service
```dart
// Pseudo-code for WidgetDataService
class WidgetDataService {
  // Get widget configuration
  Future<Map<String, dynamic>> getWidgetConfig(String widgetId) async {
    final response = await http.get(Uri.parse('/api/widgets/$widgetId/config'));
    
    if (response.statusCode != 200) {
      throw Exception('Failed to fetch widget config');
    }
    
    return jsonDecode(response.body);
  }

  // Get widget data
  Future<Map<String, dynamic>> getWidgetData({
    required String widgetId,
    required String organizationId,
    String? recordId,
    Map<String, dynamic>? filters
  }) async {
    final queryParams = {
      'organization_id': organizationId,
      if (recordId != null) 'record_id': recordId,
      if (filters != null) 'filters': jsonEncode(filters)
    };
    
    final uri = Uri.parse('/api/widgets/$widgetId/data')
      .replace(queryParameters: queryParams);
    
    final response = await http.get(uri);
    
    if (response.statusCode != 200) {
      throw Exception('Failed to fetch widget data');
    }
    
    return jsonDecode(response.body);
  }

  // Submit widget action
  Future<Map<String, dynamic>> submitWidgetAction(
    String widgetId,
    String action,
    Map<String, dynamic> data
  ) async {
    final response = await http.post(
      Uri.parse('/api/widgets/$widgetId/actions/$action'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(data)
    );
    
    if (response.statusCode != 200) {
      throw Exception('Failed to execute widget action');
    }
    
    return jsonDecode(response.body);
  }
}
```

### 7. Supabase Edge Function Implementation

#### Widget Data Handler
```dart
// Pseudo-code for Supabase Edge Function (actually implemented in TypeScript)
// This represents the server-side logic that would be implemented in the Edge Function

// Handle request
async function handleRequest(req) {
  const url = new URL(req.url);
  const widgetId = url.pathname.split('/')[2];
  const action = url.searchParams.get('action') || 'get_data';

  switch (action) {
    case 'get_config':
      return await getWidgetConfig(widgetId);
    case 'get_data':
      return await getWidgetData(widgetId, req);
    case 'submit_action':
      return await submitWidgetAction(widgetId, req);
    default:
      return new Response('Invalid action', { status: 400 });
  }
}

// Get widget configuration
async function getWidgetConfig(widgetId) {
  const { data, error } = await supabase
    .from('system_metadata.widget_registry')
    .select('widget_config')
    .eq('widget_id', widgetId)
    .single();

  if (error) {
    return new Response(`Widget not found: ${error.message}`, { status: 404 });
  }

  return new Response(JSON.stringify(data.widget_config));
}

// Get widget data
async function getWidgetData(widgetId, req) {
  const url = new URL(req.url);
  const organizationId = url.searchParams.get('organization_id');
  const recordId = url.searchParams.get('record_id');

  // Get widget configuration
  const config = await getWidgetConfig(widgetId);
  const widgetConfig = await config.json();

  // Build dynamic query based on widget configuration
  let query = supabase
    .from(widgetConfig.data_source.table)
    .select(widgetConfig.data_source.required_fields.join(', '));

  // Apply organization filter
  if (organizationId) {
    query = query.eq('organization_id', organizationId);
  }

  // Apply record filter if specified
  if (recordId) {
    query = query.eq(widgetConfig.data_source.primary_key, recordId);
  }

  const { data, error } = await query;

  if (error) {
    return new Response(`Data fetch error: ${error.message}`, { status: 500 });
  }

  return new Response(JSON.stringify(data));
}
```

## Implementation Guidelines

### Database Integration
1. **Widget Registry**: Store widget configurations in metadata tables
2. **Field Mapping**: Maintain consistent field name mappings
3. **Validation Rules**: Centralize validation logic in database functions

### Flutter Architecture
1. **Metadata-Driven**: All widgets derive configuration from metadata
2. **Reusable Base**: Common base widgets for different UI patterns  
3. **Type Safety**: Strong Dart typing for widget configs

### API Design
1. **RESTful Endpoints**: Consistent API structure for widget operations
2. **Dynamic Queries**: Build queries based on widget metadata
3. **Action Handling**: Standardized action execution framework

## Testing Framework

### Widget Testing Strategy
```dart
// Pseudo-code for widget tests
void main() {
  testWidgets('MetadataWidget renders based on configuration', (WidgetTester tester) async {
    // Mock widget configuration
    final mockConfig = {
      'type': 'display_card',
      'data_source': {'table': 'test_table'},
      'field_mappings': {
        'name': {'display_name': 'Name', 'widget': 'text'}
      }
    };

    // Mock API service to return configuration
    // Set up widget test environment
    await tester.pumpWidget(
      MaterialApp(
        home: MetadataWidget(
          widgetId: 'test_widget',
          organizationId: 'org-123'
        )
      )
    );

    // Wait for async operations to complete
    await tester.pumpAndSettle();

    // Verify widget rendering
    expect(find.text('Name'), findsOneWidget);
  });

  testWidgets('Widget handles actions correctly', (WidgetTester tester) async {
    // Setup action handler
    bool actionCalled = false;
    void mockOnAction(String action, Map<String, dynamic> data) {
      actionCalled = true;
      expect(action, equals('edit'));
    }

    // Set up widget test environment
    await tester.pumpWidget(
      MaterialApp(
        home: SubscriptionCard(
          subscriptionId: 'sub-123',
          organizationId: 'org-123',
          onAction: mockOnAction
        )
      )
    );

    // Wait for async operations to complete
    await tester.pumpAndSettle();

    // Tap on edit button
    await tester.tap(find.text('Edit'));
    await tester.pumpAndSettle();

    // Verify action was called
    expect(actionCalled, isTrue);
  });
}
```

## Migration Strategy

### Phase 1: Widget Registry Setup
- [ ] Create widget metadata tables
- [ ] Define base widget configurations
- [ ] Implement metadata loading services

### Phase 2: Widget Refactoring  
- [ ] Refactor existing widgets to use metadata
- [ ] Update widget parameters to match schema
- [ ] Implement field mapping system

### Phase 3: Integration Testing
- [ ] Test widget rendering with metadata
- [ ] Verify form submission workflows
- [ ] Performance testing with dynamic widgets

## Assumptions
- Flutter 3.0+ with StatefulWidget/StatelessWidget for widget state management
- Dart for type safety and interfaces
- Supabase Edge Functions for widget data services
- Flutter themes for widget styling

## Review Flags
- [ ] Widget rendering performance with complex metadata
- [ ] Package size impact of dynamic widget loading
- [ ] Accessibility compliance for generated widgets
- [ ] SEO implications of metadata-driven widgets

## Completeness Checklist
- [x] Widget metadata schema definition
- [x] Flutter widget architecture
- [x] Field mapping and data binding system
- [x] Form state management framework
- [x] API integration layer
- [x] Supabase edge function implementation
- [x] Testing framework specification
- [x] Migration strategy outline