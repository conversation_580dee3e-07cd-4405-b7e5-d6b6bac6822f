id: Q100
query: >-
  Do we need to hire cybersecurity experts or can IT staff learn this?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/7.2"
overlap_ids: []
capability_tags:
  - "Report"
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Awareness & Competence"
    id: "ISO27001:2022/7.2"
    locator: "Clause 7.2"
ui:
  cards_hint:
    - "Skill development plan"
  actions:
    - type: "open_register"
      target: "training_plan"
      label: "View Training Plan"
    - type: "start_workflow"
      target: "skills_development"
      label: "Launch Skills Program"
output_mode: "both"
graph_required: false
notes: "Our platform’s templates and training modules enable IT staff to cover ~80% of routine tasks"
---
### 100) Do we need to hire cybersecurity experts or can IT staff learn this?

**Standard terms**  
- **Awareness & competence (Cl. 7.2):** training and skills attainment.

**Plain-English answer**  
Most routine controls and documentation can be handled by trained IT staff using our guided training modules. You may only need external experts for specialized tasks like penetration tests or legal interpretations.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 7.2

**Why it matters**  
Balancing internal capability vs. expert costs optimizes budgets.

**Do next in our platform**  
- Review **Training Plan** for IT.  
- Launch **Skills Program** workflow for targeted upskilling.

**How our platform will help**  
- **[Report]** Skill-gap analysis.  
- **[Workflow]** Microlearning and certification paths.

**Likely follow-ups**  
- “Which specialized tasks still need experts?”  

**Sources**  
- ISO/IEC 27001:2022 Clause 7.2
That completes Q091–Q100. Let me know if you’d like any adjustments or to proceed further!









Sources
You said:
BTW since we skipped 78 can you generate a completely new Q78 so we don't have a gap?


ChatGPT said:
