id: Q099
query: >-
  What's vulnerability management and how often do we need to scan for problems?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/A.12.6.1"
overlap_ids:
  - "NIST:800-40"
capability_tags:
  - "Workflow"
  - "Tracker"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Annex A.12.6.1 Technical Vulnerability Management"
    id: "ISO27001:2022/A.12.6.1"
    locator: "Annex A.12.6.1"
ui:
  cards_hint:
    - "Vulnerability scan schedule"
  actions:
    - type: "start_workflow"
      target: "vuln_management"
      label: "Initiate VM Program"
    - type: "open_register"
      target: "vuln_register"
      label: "View Vulnerability Log"
output_mode: "both"
graph_required: false
notes: "Best practice: monthly scans and quarterly comprehensive assessments"
---
### 99) What's vulnerability management and how often do we need to scan for problems?

**Standard terms**  
- **Technical vulnerability management (A.12.6.1):** identify and address software flaws.

**Plain-English answer**  
Vulnerability management is a continuous cycle: discover, classify, remediate, and verify fixes. Run **monthly automated scans** for known issues and **quarterly deep scans** (including authenticated checks).

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.12.6.1

**Why it matters**  
Regular scanning prevents attackers from exploiting known weaknesses.

**Do next in our platform**  
- Kick off **VM Program** workflow.  
- Review **Vulnerability Log** for trending issues.

**How our platform will help**  
- **[Tracker]** Vulnerability register with remediation status.  
- **[Workflow]** Automated scan reminders and reporting.

**Likely follow-ups**  
- “What tools integrate with our platform for scanning?”  

**Sources**  
- ISO/IEC 27001:2022 Annex A.12.6.1
