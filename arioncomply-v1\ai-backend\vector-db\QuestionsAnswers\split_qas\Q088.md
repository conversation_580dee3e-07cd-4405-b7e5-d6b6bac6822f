id: Q088
query: >-
  What technology do we need vs. what can we do with existing systems?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/A.12"
  - "GDPR:2016/Art.32"
overlap_ids: []
capability_tags:
  - "Register"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Operations Security"
    id: "ISO27001:2022/A.12"
    locator: "Annex A.12"
  - title: "GDPR — Security of Processing"
    id: "GDPR:2016/Art.32"
    locator: "Article 32"
ui:
  cards_hint:
    - "Technology gap register"
  actions:
    - type: "open_register"
      target: "tech_inventory"
      label: "View Tech Inventory"
    - type: "start_workflow"
      target: "tech_gap_analysis"
      label: "Run Tech Gap Analysis"
output_mode: "both"
graph_required: false
notes: "Leverage built-in registers/workflows before buying new tools"
---
### 88) What technology do we need vs. what can we do with existing systems?

**Standard terms**  
- **Operations security (ISO 27001 A.12):** technical controls for operations.  
- **Security of processing (GDPR Art. 32):** encryption, access control.

**Plain-English answer**  
Core needs—MFA, encryption, backup, logging—often exist in cloud suites (Office 365, AWS). Use our built-in registers, workflows, and dashboards first; add specialized tools (SIEM, DLP) only for advanced requirements.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.12; GDPR Article 32

**Why it matters**  
Maximizing existing investments reduces cost and complexity.

**Do next in our platform**  
- Inventory **current tech** in the Tech Inventory.  
- Run **Tech Gap Analysis**.

**How our platform will help**  
- **[Register]** Tech & tool inventory.  
- **[Report]** Gap analysis with purchase recommendations.

**Likely follow-ups**  
- “Can we integrate existing SIEM with platform?” (Yes—via APIs)

**Sources**  
- ISO/IEC 27001:2022 Annex A.12  
- GDPR Article 32
