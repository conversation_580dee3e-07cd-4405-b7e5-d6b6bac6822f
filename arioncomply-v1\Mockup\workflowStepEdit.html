<!-- File: arioncomply-v1/Mockup/workflowStepEdit.html -->
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Edit Workflow Step</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link href="mystyles.css" rel="stylesheet" />
  </head>
  <body>
    <div class="app-container">
      <main class="main-content">
        <div class="content">
          <div class="page-header">
            <div>
              <h1 class="page-title">Edit Step</h1>
            </div>
          </div>
          <form id="step-form" class="form-grid">
            <div class="form-group">
              <label class="form-label" for="step-title">Title</label>
              <input id="step-title" class="form-input" type="text" />
            </div>
            <div class="form-group">
              <label class="form-label" for="step-desc">Description</label>
              <textarea id="step-desc" class="form-input"></textarea>
            </div>
            <div class="form-group">
              <button type="submit" class="btn btn-primary">Save</button>
              <a href="#" id="cancel" class="btn btn-secondary">Cancel</a>
            </div>
          </form>
        </div>
      </main>
    </div>
    <script src="navigation-config.js"></script>
    <script src="sidebar-component.js"></script>
    <script src="layout-manager.js"></script>
    <script src="workflowModel.js"></script>
    <script src="workflowEditor.js"></script>
    <script src="workflowStepEdit.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        LayoutManager.initializePage("workflowStepEdit.html");
        WorkflowStepEdit.init();
      });
    </script>
  </body>
</html>
<!-- File: arioncomply-v1/Mockup/workflowStepEdit.html -->
