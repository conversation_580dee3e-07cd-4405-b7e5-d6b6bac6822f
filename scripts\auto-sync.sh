#!/bin/bash
#
# File: scripts/auto-sync.sh
# File Description: Periodic auto-commit and push script for GitHub synchronization
# Purpose: Automatically commit and push uncommitted changes at regular intervals
#
# Usage: ./scripts/auto-sync.sh [interval_minutes]
#
# Setup Instructions:
#   1. Created by Claude Code for device-independent sync
#   2. Made executable with: chmod +x scripts/auto-sync.sh
#   3. Added to crontab for automatic execution:
#      */15 * * * * cd /Users/<USER>/LocalProjects/GitHubProjectsDocuments/xLLMArionComply && ./scripts/auto-sync.sh >/dev/null 2>&1
#   4. Runs every 15 minutes to catch uncommitted changes
#
# How it works:
#   - Checks for uncommitted changes using git diff-index
#   - If changes found, stages all files with git add -A
#   - Creates timestamped commit with Claude Code attribution
#   - Pushes to GitHub origin automatically
#   - Logs all activities to .git/auto-sync.log
#   - Complements post-commit hook for complete coverage
#
# Multi-device workflow:
#   - Works on any device with git and GitHub access
#   - No dependency on VSCode or specific editors
#   - Ensures work is always backed up to GitHub
#   - Prevents conflicts when switching devices
#
# Dependencies: Git, GitHub remote origin configured, cron (for automation)
# Security/RLS: Uses existing git credentials and SSH keys
# Notes: Part of comprehensive auto-sync strategy with post-commit hook
#

cd "$(dirname "$0")/.." || exit 1

INTERVAL=${1:-15}  # Default 15 minutes (used for logging, actual interval set by cron)
LOG_FILE=".git/auto-sync.log"

log_msg() {
    echo "$(date '+%Y-%m-%d %H:%M:%S'): $*" | tee -a "$LOG_FILE"
}

# First, check for and pull any remote changes
BRANCH=$(git branch --show-current)
log_msg "Checking for remote changes on $BRANCH..."

# Fetch latest remote refs
git fetch origin "$BRANCH" 2>/dev/null

# Check if remote has new commits
LOCAL_HASH=$(git rev-parse HEAD)
REMOTE_HASH=$(git rev-parse "origin/$BRANCH" 2>/dev/null || echo "$LOCAL_HASH")

if [[ "$LOCAL_HASH" != "$REMOTE_HASH" ]]; then
    log_msg "Remote changes detected, pulling..."
    if git pull origin "$BRANCH" 2>/dev/null; then
        log_msg "Successfully pulled changes from origin/$BRANCH"
    else
        log_msg "Failed to pull from origin/$BRANCH (may need manual merge)"
    fi
else
    log_msg "Local branch up to date with remote"
fi

# Check if there are changes (staged, unstaged, or untracked)
if ! git diff-index --quiet HEAD --; then
    log_msg "Changes detected, committing..."
    
    # Add all changes (modified, new, deleted)
    git add -A
    
    # Commit with timestamp and automation indicator
    COMMIT_MSG="Auto-sync: $(date '+%Y-%m-%d %H:%M:%S') [automated]"
    git commit -m "$COMMIT_MSG"
    
    # Push to remote origin
    BRANCH=$(git branch --show-current)
    if git push origin "$BRANCH" 2>/dev/null; then
        log_msg "Successfully pushed to origin/$BRANCH"
    else
        log_msg "Failed to push to origin/$BRANCH"
    fi
else
    log_msg "No changes to sync"
fi