
```yaml
id: Q219
question: What if we want to stop being certified — can we just let it expire?
packs: ["ISO17021-1:2015","ISO27001:2022"]
primary_ids: ["ISO17021-1:2015/Certificate.Status","ISO27001:2022/Cl.9","ISO27001:2022/Cl.10"]
overlap_ids: []
capability_tags: ["Workflow","Versioning","Approval","Report"]
ui:
  actions:
    - target: "workflow"
      action: "open"
      args: { key: "decertification" }
cards_hint:
  - Cease claims/marks after lapse.
  - Update websites/policies; inform customers if contracted.
  - Keep running controls if useful.
graph_required: false
```

### 219) What if we want to stop being certified—can we just let it expire?

**Standard term(s)**

- **Certificate/mark rules:** once lapsed, stop **claiming** certification; remove marks/scope statements. **[CB POLICY VARIES]**

**Plain-English answer**\
Yes, you can let it lapse—then **cease claims** and update any contracts or materials that require/mention certification.

**Applies to**

- **Primary:** ISO/IEC **17021-1** (certificate/mark rules); ISO/IEC 27001 **Cl. 9–10** (ongoing ISMS).

**Why it matters**\
Improper claims post-expiry can trigger **contract** issues.

**Do next in our platform**

- Run a **de-certification** workflow: remove claims, update sites/policies, notify customers if contracted.

**How our platform will help**

- **[Workflow] [Versioning] [Approval] [Report]** — Templates, sign-offs, and confirmation logs.

**Likely follow-ups**

- “Can we keep the controls?” → Yes—the cert is the attestation.

**Sources**

- ISO/IEC **17021-1**; ISO/IEC 27001 **Cl. 9–10**.