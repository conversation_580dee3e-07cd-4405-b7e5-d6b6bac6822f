id: Q149
query: >-
  How do we balance being thorough vs. not overwhelming auditors with too much information?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/9.2"
overlap_ids: []
capability_tags:
  - "Report"
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Internal Audit"
    id: "ISO27001:2022/9.2"
    locator: "Clause 9.2"
ui:
  cards_hint:
    - "Evidence prioritization guide"
  actions:
    - type: "start_workflow"
      target: "evidence_pruning"
      label: "Prioritize Evidence"
output_mode: "both"
graph_required: false
notes: "Provide key artifacts first; offer deeper evidence on request"
---
### 149) How do we balance being thorough vs. not overwhelming auditors with too much information?

**Standard terms**  
- **Internal audit (Cl.9.2):** focus on evidence effectiveness.

**Plain-English answer**  
Prepare a **top-layer summary** for each control (policy, status, metrics). Keep detailed logs available on request. This “just enough” approach satisfies auditors without clutter.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 9.2

**Why it matters**  
Efficient audits save time and reduce friction.

**Do next in our platform**  
- Run **Evidence Pruning** workflow.  
- Tag summary vs. detailed documents in register.

**How our platform will help**  
- **[Report]** Summary dashboards with drill-down links.  
- **[Workflow]** Evidence classification and packaging.

**Likely follow-ups**  
- “What’s the ideal summary length?” (1–2 pages per control)

**Sources**  
- ISO/IEC 27001:2022 Clause 9.2  
