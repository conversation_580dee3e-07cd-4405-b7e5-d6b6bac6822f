id: Q176
query: >-
  How do we explain automated decision-making to customers in plain English?
packs:
  - "GDPR:2016"
primary_ids:
  - "GDPR:2016/Art.13"
  - "GDPR:2016/Art.22"
overlap_ids: []
capability_tags:
  - "Draft Doc"
flags: []
sources:
  - title: "GDPR — Transparent Information"
    id: "GDPR:2016/Art.13"
    locator: "Article 13"
  - title: "GDPR — Automated Decisions"
    id: "GDPR:2016/Art.22"
    locator: "Article 22"
ui:
  cards_hint:
    - "Transparency statement builder"
  actions:
    - type: "start_workflow"
      target: "transparency_statement"
      label: "Generate Statement"
output_mode: "both"
graph_required: false
notes: "Use simple language, examples, and opt‑out options"
---
### 176) How do we explain automated decision-making to customers in plain English?

**Standard term(s)**  
- **Transparent information (GDPR Art. 13):** requirement to inform individuals how their data is processed in a clear, accessible way.  
- **Automated decisions (GDPR Art. 22):** decisions made solely by automated means that have legal or significant effects on individuals.

**Plain-English answer**  
Explain what the decision-making system does, the type of data it uses, and how it affects the customer — in clear, non-technical language. Provide examples (e.g., “We use your transaction history to automatically approve or decline a loan application”). Include the key factors considered, the role of AI or automation, and whether human review is available. Inform customers about their rights to request human intervention, express their views, or contest the decision.

**Applies to**  
- **Primary:** GDPR Articles 13 & 22

**Why it matters**  
Transparency builds trust, meets GDPR obligations, and reduces confusion or complaints.

**Do next in our platform**  
- Launch the **Transparency Statement** workflow to create plain-language explanations for all automated decision-making processes.

**How our platform will help**  
- **[Draft Doc]** Generate clear, compliant statements tailored to your services.  
- Provide templates with placeholders for examples, rights, and contact details.

**Likely follow-ups**  
- “Do we need to explain the algorithm itself?” (Not the code, but the logic and key factors influencing the decision)

**Sources**  
- GDPR Article 13  
- GDPR Article 22
