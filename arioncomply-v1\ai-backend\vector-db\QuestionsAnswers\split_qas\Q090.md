id: Q090
query: >-
  What does "governance" mean in practical terms for a small company?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/5.1"
overlap_ids:
  - "ISO27001:2022/4.1"
capability_tags:
  - "Register"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Leadership & Commitment"
    id: "ISO27001:2022/5.1"
    locator: "Clause 5.1"
  - title: "ISO/IEC 27001:2022 — Context (4.1)"
    id: "ISO27001:2022/4.1"
    locator: "Clause 4.1"
ui:
  cards_hint:
    - "Governance charters"
  actions:
    - type: "open_register"
      target: "governance"
      label: "Set Up Governance Register"
    - type: "start_workflow"
      target: "governance_framework"
      label: "Define Governance"
output_mode: "both"
graph_required: false
notes: "Scaled-down roles & committees; clear ownership and reporting lines"
---
### 90) What does "governance" mean in practical terms for a small company?

**Standard terms**  
- **Leadership & commitment (Cl. 5.1):** top management’s role.  
- **Context (Cl. 4.1):** organizational environment.

**Plain-English answer**  
Governance means assigning clear owners for security and privacy, setting up a lightweight steering committee (e.g., monthly one-hour meeting), defining reporting lines, and establishing decision-making processes—scaled to your size.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clauses 5.1; 4.1

**Why it matters**  
Proper governance aligns security initiatives with business goals and ensures accountability.

**Do next in our platform**  
- Create your **Governance Register**.  
- Launch **Governance Framework** workflow.

**How our platform will help**  
- **[Register]** Owner and committee definitions.  
- **[Workflow]** Governance charter setup.  
- **[Report]** Governance meeting dashboards.

**Likely follow-ups**  
- “Who should be on the steering committee?” (Leadership plus key process owners)
Let me know when you’re ready for Q091–Q100!

**Sources**  
- ISO/IEC 27001:2022 Clause 5.1  
- ISO/IEC 27001:2022 Clause 4.1