# Event and Workflow Design Principles

## Overview
This document establishes unified design principles for event and workflow systems, addressing inconsistencies between DataDriveUISchemaAndFunctions general event system and SubscriptionManagement specific triggers.

## Current State Analysis

### Inconsistencies Identified
- **DataDriveUISchemaAndFunctions**: General-purpose event system with hooks
- **SubscriptionManagement**: Specific database triggers for subscription events
- **Missing Integration**: No clear mapping between database triggers and metadata-driven events

## Unified Event System Architecture

### 1. Event Registry Structure
```json
{
  "event_types": {
    "subscription.created": {
      "schema": "subscription_event_v1",
      "triggers": ["db_trigger", "api_hook"],
      "handlers": ["workflow_engine", "notification_service"],
      "metadata": {
        "source": "subscription_management",
        "category": "business_critical",
        "retention_days": 365
      }
    }
  },
  "event_schemas": {
    "subscription_event_v1": {
      "type": "object",
      "required": ["organization_id", "subscription_id", "event_data"],
      "properties": {
        "organization_id": {"type": "string", "format": "uuid"},
        "subscription_id": {"type": "string", "format": "uuid"},
        "event_data": {"$ref": "#/definitions/subscription_payload"}
      }
    }
  }
}
```

### 2. Database Trigger Integration

#### Standardized Trigger Function
```sql
-- Generic metadata-aware trigger function
CREATE OR REPLACE FUNCTION emit_metadata_event()
RETURNS TRIGGER AS $$
DECLARE
    event_config JSONB;
    event_payload JSONB;
BEGIN
    -- Retrieve event configuration from metadata registry
    SELECT event_definition INTO event_config
    FROM system_metadata.event_registry 
    WHERE table_name = TG_TABLE_NAME 
    AND trigger_event = TG_OP
    AND (organization_id IS NULL OR 
         organization_id = current_setting('app.current_organization_id', true)::UUID);
    
    IF event_config IS NULL THEN
        RETURN COALESCE(NEW, OLD);
    END IF;
    
    -- Build standardized event payload
    event_payload = jsonb_build_object(
        'event_type', event_config->>'event_type',
        'organization_id', COALESCE(NEW.organization_id, OLD.organization_id),
        'table_name', TG_TABLE_NAME,
        'operation', TG_OP,
        'timestamp', NOW(),
        'old_data', CASE WHEN TG_OP IN ('UPDATE', 'DELETE') THEN row_to_json(OLD) END,
        'new_data', CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN row_to_json(NEW) END
    );
    
    -- Emit event to event bus
    PERFORM pg_notify('system_events', event_payload::text);
    
    -- Log to audit trail
    PERFORM log_event_to_audit(
        event_config->>'event_type', 
        event_payload, 
        'emitted'
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Add function to log event processing to audit trail
CREATE OR REPLACE FUNCTION log_event_to_audit(
    event_type TEXT,
    event_payload JSONB,
    processing_result TEXT
) RETURNS VOID AS $$
DECLARE
    organization_id UUID;
    entity_id UUID;
    audit_tier TEXT;
BEGIN
    -- Extract organization_id from event payload
    organization_id := (event_payload->>'organization_id')::UUID;
    
    -- Determine audit tier based on event type
    SELECT determine_audit_tier('event_processing', 'EVENT', ARRAY[event_type]) 
    INTO audit_tier;
    
    -- Log to appropriate audit tier
    IF audit_tier = 'critical' THEN
        INSERT INTO critical_audit_trails (
            action_type,
            entity_table,
            entity_id,
            after_state,
            performed_by,
            organization_id,
            regulatory_impact
        ) VALUES (
            'EVENT_PROCESSED',
            'system_metadata.event_registry',
            event_payload->>'id',
            event_payload,
            current_setting('app.current_user_id', true)::UUID,
            organization_id,
            ARRAY['compliance_activity_log']
        );
    ELSE
        INSERT INTO routine_audit_trails (
            action_type,
            entity_table,
            entity_id,
            changed_fields,
            performed_by,
            organization_id
        ) VALUES (
            'EVENT_PROCESSED',
            'system_metadata.event_registry',
            event_payload->>'id',
            jsonb_build_object('processing_result', processing_result),
            current_setting('app.current_user_id', true)::UUID,
            organization_id
        );
    END IF;
END;
$$ LANGUAGE plpgsql;
```

#### Metadata-Driven Trigger Registration
```sql
-- Event registry table
CREATE TABLE system_metadata.event_registry (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id), -- For multi-tenant isolation
    table_name TEXT NOT NULL,
    trigger_event TEXT NOT NULL, -- INSERT, UPDATE, DELETE
    event_type TEXT NOT NULL,
    event_definition JSONB NOT NULL,
    payload_type TEXT REFERENCES system_metadata.type_definitions(type_name),
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Standard audit fields
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_by UUID REFERENCES users(id),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Validation constraint
    CONSTRAINT event_registry_event_definition_check CHECK (jsonb_typeof(event_definition) = 'object')
);

-- RLS policy for multi-tenant isolation
ALTER TABLE system_metadata.event_registry ENABLE ROW LEVEL SECURITY;

CREATE POLICY tenant_isolation_event_registry ON system_metadata.event_registry
USING (
    organization_id IS NULL OR -- System-wide events
    organization_id = current_setting('app.current_organization_id')::UUID -- Org-specific
);

-- Documentation
COMMENT ON TABLE system_metadata.event_registry IS 
'Stores event type definitions and their configurations for the event system.
Each record defines an event type that can be triggered by database operations or API actions.
Used by: event processing system, workflow engine, notification service.
Related components: workflow_definitions, notification_templates.';

COMMENT ON COLUMN system_metadata.event_registry.event_type IS
'Unique identifier for the event type using dot notation (e.g., subscription.created).
Format should be [domain].[action] for consistency.
Used for routing events to appropriate handlers and triggering workflows.
Required for all event processing.';

-- Register subscription events
INSERT INTO system_metadata.event_registry (
    organization_id, 
    table_name, 
    trigger_event, 
    event_type, 
    event_definition,
    payload_type,
    created_by
) VALUES 
(NULL, 'org_subscriptions', 'INSERT', 'subscription.created', 
 '{"handlers": ["workflow_engine", "notification"], "priority": "high"}',
 'event_payload',
 (SELECT id FROM users WHERE email = '<EMAIL>')),
(NULL, 'org_subscriptions', 'UPDATE', 'subscription.updated',
 '{"handlers": ["workflow_engine"], "conditions": ["plan_id_changed", "status_changed"]}',
 'event_payload',
 (SELECT id FROM users WHERE email = '<EMAIL>'));
```

### 3. Type System Integration

```sql
-- Add event payload types to type_definitions
INSERT INTO system_metadata.type_definitions (
    type_name, 
    description, 
    pg_type, 
    api_type, 
    ui_type, 
    validation_schema,
    created_by
) VALUES (
    'event_payload', 
    'Standard event message payload',
    'JSONB',
    'object',
    'json',
    '{
        "type": "object",
        "required": ["event_type", "organization_id", "timestamp"],
        "properties": {
            "event_type": {"type": "string"},
            "organization_id": {"type": "string", "format": "uuid"},
            "timestamp": {"type": "string", "format": "date-time"}
        }
    }',
    (SELECT id FROM users WHERE email = '<EMAIL>')
);

-- Add field mappings for event payloads
INSERT INTO field_mappings (table_name, db_to_api_mapping, api_to_db_mapping) VALUES 
('system_metadata.event_registry', 
 '{
    "event_type": "eventType",
    "organization_id": "organizationId", 
    "table_name": "tableName",
    "trigger_event": "triggerEvent",
    "event_definition": "eventDefinition",
    "is_active": "isActive",
    "created_at": "createdAt",
    "created_by": "createdBy",
    "updated_at": "updatedAt",
    "updated_by": "updatedBy"
 }',
 '{
    "eventType": "event_type",
    "organizationId": "organization_id",
    "tableName": "table_name",
    "triggerEvent": "trigger_event",
    "eventDefinition": "event_definition",
    "isActive": "is_active",
    "createdAt": "created_at",
    "createdBy": "created_by",
    "updatedAt": "updated_at",
    "updatedBy": "updated_by"
 }'
);
```

### 4. Workflow Engine Integration

#### Workflow Definition Schema
```json
{
  "workflow_id": "subscription_lifecycle",
  "triggers": ["subscription.created", "subscription.updated"],
  "steps": [
    {
      "step_id": "validate_subscription",
      "type": "validation",
      "handler": "subscription_validator",
      "on_success": "send_welcome_email",
      "on_failure": "rollback_subscription"
    },
    {
      "step_id": "send_welcome_email",
      "type": "notification",
      "handler": "email_service",
      "template": "subscription_welcome",
      "on_complete": "update_user_status"
    }
  ],
  "error_handling": {
    "max_retries": 3,
    "retry_delay": "exponential",
    "dead_letter_queue": "failed_workflows"
  }
}
```

#### Workflow Database Schema
```sql
-- Workflow definitions table
CREATE TABLE system_metadata.workflow_definitions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id),
    workflow_id TEXT NOT NULL,
    version INTEGER NOT NULL DEFAULT 1,
    triggers TEXT[] NOT NULL,
    steps JSONB NOT NULL,
    error_handling JSONB NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Standard audit fields
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_by UUID REFERENCES users(id),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Validation constraint
    CONSTRAINT workflow_definitions_steps_check CHECK (jsonb_typeof(steps) = 'array'),
    CONSTRAINT workflow_definitions_error_handling_check CHECK (jsonb_typeof(error_handling) = 'object'),
    
    UNIQUE(workflow_id, version, organization_id)
);

-- RLS policy for multi-tenant isolation
ALTER TABLE system_metadata.workflow_definitions ENABLE ROW LEVEL SECURITY;

CREATE POLICY tenant_isolation_workflow_definitions ON system_metadata.workflow_definitions
USING (
    organization_id IS NULL OR -- System-wide workflows
    organization_id = current_setting('app.current_organization_id')::UUID -- Org-specific
);

-- Documentation
COMMENT ON TABLE system_metadata.workflow_definitions IS 
'Stores workflow definitions that respond to system events.
Each workflow consists of a series of steps that are executed in response to specific event triggers.
Used by: workflow engine, event processing system.
Related components: event_registry, notification_templates.';

-- Workflow execution tracking
CREATE TABLE workflow_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    workflow_id TEXT NOT NULL,
    workflow_version INTEGER NOT NULL,
    trigger_event_id UUID,
    status TEXT NOT NULL,
    current_step_id TEXT,
    start_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    end_time TIMESTAMPTZ,
    execution_result JSONB,
    error_details JSONB,
    retry_count INTEGER NOT NULL DEFAULT 0,
    
    -- Standard audit fields
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_by UUID REFERENCES users(id),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT workflow_executions_status_check CHECK (status IN ('pending', 'running', 'completed', 'failed', 'retrying'))
);

-- RLS policy for multi-tenant isolation
ALTER TABLE workflow_executions ENABLE ROW LEVEL SECURITY;

CREATE POLICY tenant_isolation_workflow_executions ON workflow_executions
USING (organization_id = current_setting('app.current_organization_id')::UUID);

-- Documentation
COMMENT ON TABLE workflow_executions IS 
'Tracks execution of workflows in response to system events.
Records all state changes, errors, and outcomes for auditing and monitoring.
Used by: workflow engine, monitoring dashboard, audit system.
Related components: workflow_definitions, event_registry.';
```

#### Workflow Execution Engine (Dart)
```dart
// Pseudo-code for Workflow Engine in Dart
// arioncomply-v1/ai-backend/lib/workflow/workflow_engine.dart

import 'package:supabase/supabase.dart';
import '../error/error_classes.dart';
import '../type_system/client.dart';
import '../utils/field_mapper.dart';

class WorkflowStep {
  final String stepId;
  final String type;
  final String handler;
  final Map<String, dynamic>? config;
  final String? onSuccess;
  final String? onFailure;
  final int? timeoutSeconds;
  
  WorkflowStep({
    required this.stepId,
    required this.type,
    required this.handler,
    this.config,
    this.onSuccess,
    this.onFailure,
    this.timeoutSeconds,
  });
  
  factory WorkflowStep.fromJson(Map<String, dynamic> json) {
    return WorkflowStep(
      stepId: json['step_id'],
      type: json['type'],
      handler: json['handler'],
      config: json['config'],
      onSuccess: json['on_success'],
      onFailure: json['on_failure'],
      timeoutSeconds: json['timeout_seconds'],
    );
  }
}

class WorkflowDefinition {
  final String workflowId;
  final List<String> triggers;
  final List<WorkflowStep> steps;
  final Map<String, dynamic> errorHandling;
  
  WorkflowDefinition({
    required this.workflowId,
    required this.triggers,
    required this.steps,
    required this.errorHandling,
  });
  
  factory WorkflowDefinition.fromJson(Map<String, dynamic> json) {
    return WorkflowDefinition(
      workflowId: json['workflow_id'],
      triggers: List<String>.from(json['triggers']),
      steps: (json['steps'] as List).map((step) => WorkflowStep.fromJson(step)).toList(),
      errorHandling: json['error_handling'],
    );
  }
}

class WorkflowEngine {
  final SupabaseClient supabase;
  final TypeSystemClient typeSystem;
  final Map<String, Function> stepHandlers = {};
  
  WorkflowEngine(this.supabase, this.typeSystem) {
    _registerHandlers();
  }
  
  void _registerHandlers() {
    // Register step handlers
    stepHandlers['subscription_validator'] = _validateSubscription;
    stepHandlers['email_service'] = _sendEmail;
    // Add more handlers as needed
  }
  
  Future<void> executeWorkflow(String eventType, Map<String, dynamic> eventData) async {
    final String organizationId = eventData['organization_id'];
    
    // Set organization context for RLS
    await supabase.rpc('set_config', params: {
      'setting_name': 'app.current_organization_id',
      'setting_value': organizationId,
      'is_local': true
    });
    
    // Get workflows for this event type
    final response = await supabase
      .from('system_metadata.workflow_definitions')
      .select()
      .eq('is_active', true)
      .contains('triggers', [eventType]);
      
    if (response.error != null) {
      throw DatabaseError(
        'DATABASE_ERROR',
        'Failed to fetch workflows: ${response.error!.message}',
        { 'event_type': eventType }
      );
    }
    
    final workflows = (response.data as List)
      .map((w) => WorkflowDefinition.fromJson(w))
      .toList();
      
    // Execute each matching workflow
    for (final workflow in workflows) {
      await runWorkflow(workflow, eventData);
    }
  }
  
  Future<void> runWorkflow(WorkflowDefinition workflow, Map<String, dynamic> eventData) async {
    final String organizationId = eventData['organization_id'];
    
    // Create workflow execution record
    final executionResponse = await supabase
      .from('workflow_executions')
      .insert({
        'organization_id': organizationId,
        'workflow_id': workflow.workflowId,
        'workflow_version': 1, // Get actual version
        'trigger_event_id': eventData['id'],
        'status': 'running',
        'current_step_id': workflow.steps[0].stepId,
        'created_by': eventData['created_by'] ?? eventData['updated_by']
      })
      .select()
      .single();
      
    if (executionResponse.error != null) {
      throw DatabaseError(
        'WORKFLOW_EXECUTION_ERROR',
        'Failed to create workflow execution: ${executionResponse.error!.message}',
        { 'workflow_id': workflow.workflowId }
      );
    }
    
    final executionId = executionResponse.data['id'];
    String currentStepId = workflow.steps[0].stepId;
    
    try {
      // Get step types from type system
      final stepTypes = await _getStepTypes(workflow);
      
      // Execute workflow steps
      while (currentStepId.isNotEmpty) {
        final step = workflow.steps.firstWhere(
          (s) => s.stepId == currentStepId,
          orElse: () => throw ValidationError(
            'INVALID_STEP_ID',
            'Step not found in workflow',
            { 'step_id': currentStepId }
          )
        );
        
        // Update execution record
        await supabase
          .from('workflow_executions')
          .update({
            'current_step_id': step.stepId,
            'updated_at': DateTime.now().toIso8601String()
          })
          .eq('id', executionId);
        
        // Validate step input against type schema
        final stepType = stepTypes[step.type];
        if (stepType != null) {
          final isValid = await supabase.rpc('validate_field_type', params: {
            'value': step.config ?? {},
            'type_name': step.type
          });
          
          if (isValid == false) {
            throw ValidationError(
              'WORKFLOW_STEP_INVALID',
              'Invalid configuration for step',
              { 'step_id': step.stepId, 'step_type': step.type }
            );
          }
        }
        
        // Execute the step
        final handler = stepHandlers[step.handler];
        if (handler == null) {
          throw ValidationError(
            'INVALID_STEP_HANDLER',
            'Handler not registered for step',
            { 'handler': step.handler }
          );
        }
        
        final result = await handler(step, eventData);
        
        // Log to audit trail
        await supabase.rpc('log_workflow_step', params: {
          'workflow_id': workflow.workflowId,
          'step_id': step.stepId,
          'execution_id': executionId,
          'result': result['success'] ? 'success' : 'failure',
          'organization_id': organizationId
        });
        
        // Determine next step
        currentStepId = result['success'] && step.onSuccess != null 
          ? step.onSuccess!
          : !result['success'] && step.onFailure != null
            ? step.onFailure!
            : '';
      }
      
      // Mark workflow as completed
      await supabase
        .from('workflow_executions')
        .update({
          'status': 'completed',
          'end_time': DateTime.now().toIso8601String(),
          'execution_result': { 'success': true },
          'updated_at': DateTime.now().toIso8601String()
        })
        .eq('id', executionId);
        
    } catch (error) {
      // Handle error according to workflow error handling configuration
      final retryCount = executionResponse.data['retry_count'] ?? 0;
      final maxRetries = workflow.errorHandling['max_retries'] ?? 3;
      
      if (retryCount < maxRetries) {
        // Schedule retry
        await supabase
          .from('workflow_executions')
          .update({
            'status': 'retrying',
            'retry_count': retryCount + 1,
            'error_details': {
              'message': error.toString(),
              'step_id': currentStepId,
              'retry_attempt': retryCount + 1
            },
            'updated_at': DateTime.now().toIso8601String()
          })
          .eq('id', executionId);
          
        // TODO: Implement retry logic based on workflow.errorHandling['retry_delay']
      } else {
        // Mark as failed
        await supabase
          .from('workflow_executions')
          .update({
            'status': 'failed',
            'end_time': DateTime.now().toIso8601String(),
            'error_details': {
              'message': error.toString(),
              'step_id': currentStepId
            },
            'updated_at': DateTime.now().toIso8601String()
          })
          .eq('id', executionId);
          
        // Send to dead letter queue if configured
        if (workflow.errorHandling['dead_letter_queue'] != null) {
          await _sendToDeadLetterQueue(
            workflow.errorHandling['dead_letter_queue'],
            executionId,
            error.toString()
          );
        }
      }
      
      rethrow;
    }
  }
  
  // Helper method to get step type definitions
  Future<Map<String, Map<String, dynamic>>> _getStepTypes(WorkflowDefinition workflow) async {
    final stepTypes = <String>{};
    for (final step in workflow.steps) {
      stepTypes.add(step.type);
    }
    
    final response = await supabase
      .from('system_metadata.type_definitions')
      .select('type_name, validation_schema')
      .in('type_name', stepTypes.toList());
      
    if (response.error != null) {
      throw DatabaseError(
        'TYPE_DEFINITION_ERROR',
        'Failed to fetch step type definitions',
        { 'error': response.error!.message }
      );
    }
    
    final result = <String, Map<String, dynamic>>{};
    for (final type in response.data) {
      result[type['type_name']] = type;
    }
    
    return result;
  }
  
  // Example step handlers
  Future<Map<String, dynamic>> _validateSubscription(
    WorkflowStep step, 
    Map<String, dynamic> eventData
  ) async {
    // Implementation of subscription validation
    return { 'success': true };
  }
  
  Future<Map<String, dynamic>> _sendEmail(
    WorkflowStep step, 
    Map<String, dynamic> eventData
  ) async {
    // Implementation of email sending
    return { 'success': true };
  }
  
  Future<void> _sendToDeadLetterQueue(
    String queueName, 
    String executionId, 
    String errorMessage
  ) async {
    // Implementation of dead letter queue
  }
}
```

### 5. Supabase Edge Function Implementation

#### Event Router Function
```dart
// Pseudo-code for Supabase Edge Function (Dart equivalent)
// arioncomply-v1/supabase/functions/event-router/index.dart

import 'dart:convert';
import 'package:supabase/supabase.dart';
import '../../lib/error/error_classes.dart';
import '../../lib/utils/field_mapper.dart';

class EventMessage {
  final String eventType;
  final String organizationId;
  final Map<String, dynamic> eventData;
  final Map<String, dynamic>? metadata;
  
  EventMessage({
    required this.eventType,
    required this.organizationId,
    required this.eventData,
    this.metadata,
  });
  
  factory EventMessage.fromJson(Map<String, dynamic> json) {
    return EventMessage(
      eventType: json['event_type'],
      organizationId: json['organization_id'],
      eventData: json['event_data'],
      metadata: json['metadata'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'event_type': eventType,
      'organization_id': organizationId,
      'event_data': eventData,
      'metadata': metadata,
    };
  }
}

Future<Response> handleRequest(Request request) async {
  try {
    if (request.method != 'POST') {
      throw ValidationError(
        'METHOD_NOT_ALLOWED',
        'Only POST method is allowed',
        { 'method': request.method }
      );
    }

    final jsonBody = await request.json();
    final event = EventMessage.fromJson(jsonBody);
    
    // Set organization context for RLS
    await supabase.rpc('set_config', params: {
      'setting_name': 'app.current_organization_id',
      'setting_value': event.organizationId,
      'is_local': true
    });
    
    // Get type definition for validation
    final typeResponse = await supabase
      .from('system_metadata.type_definitions')
      .select('validation_schema')
      .eq('type_name', 'event_payload')
      .single();
      
    if (typeResponse.error != null) {
      throw DatabaseError(
        'TYPE_DEFINITION_ERROR',
        'Failed to fetch event type definition',
        { 'error': typeResponse.error!.message }
      );
    }
    
    // Validate using established validation function
    final validationResult = await supabase.rpc('validate_field_type', params: {
      'value': jsonEncode(event.toJson()),
      'type_name': 'event_payload'
    });

    if (validationResult == false) {
      throw ValidationError(
        'EVENT_SCHEMA_INVALID',
        'Invalid event schema format',
        { 'event_type': event.eventType }
      );
    }

    // Route to appropriate handlers
    final handlersResponse = await getEventHandlers(event.eventType);
    final handlers = handlersResponse['handlers'] as List<String>;
    
    final results = await Future.wait(
      handlers.map((handler) => processEventHandler(handler, event))
    );

    // Log results and handle failures
    final failures = results.where((r) => r['success'] == false).toList();
    if (failures.isNotEmpty) {
      print('Event processing failures: $failures');
    }

    return Response.json({
      'processed': results.length,
      'failures': failures.length
    });
  } catch (error) {
    if (error is ApplicationError) {
      return Response.json(
        error.toResponse(),
        statusCode: error.statusCode
      );
    }
    
    // Unexpected errors
    return Response.json({
      'error': {
        'code': 'INTERNAL_SERVER_ERROR',
        'message': 'An unexpected error occurred processing the event',
        'details': { 'error': error.toString() }
      },
      'meta': {
        'timestamp': DateTime.now().toIso8601String(),
        'requestId': request.headers['x-request-id']
      }
    }, statusCode: 500);
  }
}

Future<Map<String, dynamic>> getEventHandlers(String eventType) async {
  final response = await supabase
    .from('system_metadata.event_registry')
    .select('event_definition')
    .eq('event_type', eventType)
    .eq('is_active', true)
    .single();
    
  if (response.error != null) {
    throw ResourceNotFoundError(
      'EVENT_TYPE_NOT_FOUND',
      'Event type not registered',
      { 'event_type': eventType }
    );
  }
    
  return response.data['event_definition'];
}

Future<Map<String, dynamic>> processEventHandler(
  String handler, 
  EventMessage event
) async {
  try {
    // Convert event to database format using field mapper
    final fieldMappingsResponse = await supabase
      .from('field_mappings')
      .select('api_to_db_mapping')
      .eq('table_name', 'system_metadata.event_registry')
      .single();
      
    if (fieldMappingsResponse.error != null) {
      throw DatabaseError(
        'FIELD_MAPPING_ERROR',
        'Failed to fetch field mappings',
        { 'error': fieldMappingsResponse.error!.message }
      );
    }
    
    final fieldMappings = fieldMappingsResponse.data['api_to_db_mapping'];
    final dbEvent = FieldMapper.mapToDatabaseFormat(event.toJson(), fieldMappings);
    
    // Process event with appropriate handler
    switch (handler) {
      case 'workflow_engine':
        await _processWithWorkflowEngine(dbEvent);
        break;
      case 'notification':
        await _processWithNotificationService(dbEvent);
        break;
      default:
        throw ValidationError(
          'UNKNOWN_HANDLER',
          'Event handler not recognized',
          { 'handler': handler }
        );
    }
    
    // Log successful processing
    await supabase.rpc('log_event_processing', params: {
      'event_type': dbEvent['event_type'],
      'handler': handler,
      'success': true,
      'organization_id': dbEvent['organization_id']
    });
    
    return { 'success': true, 'handler': handler };
  } catch (error) {
    // Log processing failure
    await supabase.rpc('log_event_processing', params: {
      'event_type': event.eventType,
      'handler': handler,
      'success': false,
      'error_message': error.toString(),
      'organization_id': event.organizationId
    });
    
    return { 
      'success': false, 
      'handler': handler,
      'error': error.toString()
    };
  }
}

Future<void> _processWithWorkflowEngine(Map<String, dynamic> event) async {
  // Invoke workflow engine with event
}

Future<void> _processWithNotificationService(Map<String, dynamic> event) async {
  // Process event with notification service
}
```

### 6. Event-Workflow Bridge Configuration

#### Event Subscription Management
```json
{
  "event_subscriptions": {
    "subscription_management": {
      "events": [
        "subscription.created",
        "subscription.updated", 
        "subscription.cancelled"
      ],
      "handler": "subscription_workflow_engine",
      "filter_conditions": {
        "organization_id": ["uuid"],
        "plan_tier": ["premium", "enterprise"]
      }
    }
  }
}
```

#### Database Event Bridge
```sql
-- Event bridge function for real-time processing
CREATE OR REPLACE FUNCTION process_event_bridge()
RETURNS VOID AS $$
DECLARE
    event_record RECORD;
    notification_payload TEXT;
BEGIN
    FOR event_record IN 
        SELECT * FROM pg_listening_channels() 
        WHERE channel = 'system_events'
    LOOP
        -- Process each notification
        SELECT payload INTO notification_payload 
        FROM pg_notify_queue 
        WHERE channel = 'system_events';
        
        -- Set organization context for RLS
        PERFORM set_config(
            'app.current_organization_id', 
            (notification_payload::jsonb->>'organization_id')::text,
            false
        );
        
        -- Forward to edge function
        PERFORM net.http_post(
            url := current_setting('app.edge_function_url') || '/event-router',
            headers := jsonb_build_object(
                'Content-Type', 'application/json',
                'Authorization', 'Bearer ' || current_setting('app.service_role_key')
            ),
            body := notification_payload::jsonb
        );
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function documentation
COMMENT ON FUNCTION process_event_bridge() IS
'Processes database notifications from the system_events channel and forwards them to the event router edge function.
Sets organization context for proper RLS enforcement during event processing.
Used by: event system, workflow engine.
Related components: event_registry, workflow_definitions.';
```

### 7. Flutter Integration

#### Workflow Monitoring Widget
```dart
// arioncomply-v1/frontend-flutter/lib/widgets/workflow/workflow_monitor.dart

import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/workflow_execution.dart';
import '../../services/workflow_service.dart';

class WorkflowMonitorWidget extends StatefulWidget {
  final String workflowId;
  final String organizationId;

  const WorkflowMonitorWidget({
    Key? key,
    required this.workflowId,
    required this.organizationId
  }) : super(key: key);

  @override
  _WorkflowMonitorWidgetState createState() => _WorkflowMonitorWidgetState();
}

class _WorkflowMonitorWidgetState extends State<WorkflowMonitorWidget> {
  late Stream<List<WorkflowExecution>> _executionsStream;
  final WorkflowService _workflowService = WorkflowService();

  @override
  void initState() {
    super.initState();
    _executionsStream = _subscribeToExecutions();
  }

  Stream<List<WorkflowExecution>> _subscribeToExecutions() {
    // Subscribe to workflow executions using Supabase realtime
    return Supabase.instance.client
      .from('workflow_executions')
      .stream(['id'])
      .eq('workflow_id', widget.workflowId)
      .eq('organization_id', widget.organizationId)
      .order('created_at', ascending: false)
      .map((List<Map<String, dynamic>> data) {
        return data.map((json) => WorkflowExecution.fromJson(json)).toList();
      });
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<WorkflowExecution>>(
      stream: _executionsStream,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }

        final executions = snapshot.data ?? [];
        
        if (executions.isEmpty) {
          return const Center(child: Text('No workflow executions found'));
        }
        
        return ListView.builder(
          itemCount: executions.length,
          itemBuilder: (context, index) {
            final execution = executions[index];
            return WorkflowExecutionCard(execution: execution);
          }
        );
      }
    );
  }
}

class WorkflowExecutionCard extends StatelessWidget {
  final WorkflowExecution execution;
  
  const WorkflowExecutionCard({
    Key? key,
    required this.execution
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Execution: ${execution.id.substring(0, 8)}',
                  style: Theme.of(context).textTheme.subtitle1
                ),
                _buildStatusBadge(execution.status)
              ]
            ),
            const SizedBox(height: 8.0),
            Text('Started: ${_formatDateTime(execution.startTime)}'),
            if (execution.endTime != null)
              Text('Completed: ${_formatDateTime(execution.endTime!)}'),
            const SizedBox(height: 8.0),
            Text('Current Step: ${execution.currentStepId ?? "Completed"}'),
            if (execution.status == 'failed')
              Container(
                margin: const EdgeInsets.only(top: 8.0),
                padding: const EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(4.0)
                ),
                child: Text(
                  'Error: ${execution.errorDetails?['message'] ?? "Unknown error"}',
                  style: TextStyle(color: Colors.red.shade700)
                )
              ),
            const SizedBox(height: 8.0),
            TextButton(
              onPressed: () {
                // Navigate to execution details
              },
              child: const Text('View Details')
            )
          ]
        )
      )
    );
  }
  
  Widget _buildStatusBadge(String status) {
    Color color;
    switch (status) {
      case 'completed':
        color = Colors.green;
        break;
      case 'running':
        color = Colors.blue;
        break;
      case 'retrying':
        color = Colors.orange;
        break;
      case 'failed':
        color = Colors.red;
        break;
      default:
        color = Colors.grey;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16.0),
        border: Border.all(color: color)
      ),
      child: Text(
        status.toUpperCase(),
        style: TextStyle(color: color)
      )
    );
  }
  
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
```

#### Event History Widget
```dart
// arioncomply-v1/frontend-flutter/lib/widgets/events/event_history.dart

import 'package:flutter/material.dart';
import '../../models/event.dart';
import '../../services/event_service.dart';

class EventHistoryWidget extends StatefulWidget {
  final String organizationId;
  final String? eventType;
  final int limit;
  
  const EventHistoryWidget({
    Key? key,
    required this.organizationId,
    this.eventType,
    this.limit = 10
  }) : super(key: key);
  
  @override
  _EventHistoryWidgetState createState() => _EventHistoryWidgetState();
}

class _EventHistoryWidgetState extends State<EventHistoryWidget> {
  late Future<List<Event>> _eventsFuture;
  final EventService _eventService = EventService();
  
  @override
  void initState() {
    super.initState();
    _loadEvents();
  }
  
  void _loadEvents() {
    _eventsFuture = _eventService.getEventHistory(
      organizationId: widget.organizationId,
      eventType: widget.eventType,
      limit: widget.limit
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<Event>>(
      future: _eventsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }
        
        final events = snapshot.data ?? [];
        
        if (events.isEmpty) {
          return const Center(child: Text('No events found'));
        }
        
        return ListView.builder(
          itemCount: events.length,
          itemBuilder: (context, index) {
            final event = events[index];
            return EventCard(event: event);
          }
        );
      }
    );
  }
}

class EventCard extends StatelessWidget {
  final Event event;
  
  const EventCard({
    Key? key,
    required this.event
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 16.0),
      child: ListTile(
        title: Text(event.eventType),
        subtitle: Text('${_formatDateTime(event.timestamp)} - ${event.tableName}'),
        trailing: Icon(
          Icons.circle,
          color: _getEventTypeColor(event.eventType),
          size: 12.0
        ),
        onTap: () {
          // Navigate to event details
        }
      )
    );
  }
  
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
  
  Color _getEventTypeColor(String eventType) {
    if (eventType.contains('created')) return Colors.green;
    if (eventType.contains('updated')) return Colors.blue;
    if (eventType.contains('deleted') || eventType.contains('cancelled')) return Colors.red;
    return Colors.grey;
  }
}
```

## Implementation Guidelines

### Database Layer
1. **Standardize Trigger Functions**: Replace ad-hoc triggers with metadata-driven generic functions
2. **Event Registry**: Centralize event definitions in metadata tables
3. **Payload Standardization**: Ensure consistent event payload structure

### API Layer  
1. **Event Endpoints**: Create RESTful endpoints for event management
2. **Webhook Support**: Implement webhook delivery for external integrations
3. **Event Replay**: Support for event replay and debugging

### UI Layer
1. **Event Dashboard**: Real-time event monitoring interface
2. **Workflow Builder**: Visual workflow design tools using Flutter
3. **Event History**: Audit trail and event search capabilities

## Testing Framework

### Event Testing Strategy
```dart
// arioncomply-v1/tests/event_testing.dart
import 'package:test/test.dart';
import '../lib/services/event_service.dart';
import '../lib/services/workflow_service.dart';

class EventTestFramework {
  final EventService eventService;
  final WorkflowService workflowService;
  
  EventTestFramework({
    required this.eventService,
    required this.workflowService
  });
  
  Future<Map<String, dynamic>> testEventFlow(
    String eventType, 
    Map<String, dynamic> testPayload
  ) async {
    // 1. Emit test event
    final eventId = await emitTestEvent(eventType, testPayload);
    
    // 2. Verify database triggers
    final triggerResults = await verifyTriggerExecution(eventId);
    
    // 3. Verify workflow execution
    final workflowResults = await verifyWorkflowExecution(eventId);
    
    // 4. Verify side effects
    final sideEffects = await verifySideEffects(eventId);
    
    return {
      'eventId': eventId,
      'triggerResults': triggerResults,
      'workflowResults': workflowResults,
      'sideEffects': sideEffects,
      'success': triggerResults['success'] && workflowResults['success']
    };
  }
  
  Future<String> emitTestEvent(
    String eventType, 
    Map<String, dynamic> testPayload
  ) async {
    // Implementation of test event emission
    final response = await eventService.emitEvent(eventType, testPayload);
    return response['id'];
  }
  
  Future<Map<String, dynamic>> verifyTriggerExecution(String eventId) async {
    // Implementation of trigger verification
    return {'success': true};
  }
  
  Future<Map<String, dynamic>> verifyWorkflowExecution(String eventId) async {
    // Implementation of workflow verification
    return {'success': true};
  }
  
  Future<Map<String, dynamic>> verifySideEffects(String eventId) async {
    // Implementation of side effect verification
    return {'success': true};
  }
}

void main() {
  late EventTestFramework testFramework;
  
  setUp(() {
    final eventService = EventService();
    final workflowService = WorkflowService();
    testFramework = EventTestFramework(
      eventService: eventService,
      workflowService: workflowService
    );
  });
  
  group('Subscription Events', () {
    test('subscription.created triggers welcome email workflow', () async {
      final testPayload = {
        'organization_id': 'test-org-id',
        'subscription_id': 'test-sub-id',
        'plan_id': 'premium-plan',
        'status': 'active'
      };
      
      final result = await testFramework.testEventFlow(
        'subscription.created', 
        testPayload
      );
      
      expect(result['success'], true);
      expect(
        result['workflowResults']['executed_workflows'],
        contains('subscription_lifecycle')
      );
      expect(
        result['sideEffects']['emails_sent'],
        contains('welcome_email')
      );
    });
    
    test('subscription.cancelled triggers cancellation workflow', () async {
      final testPayload = {
        'organization_id': 'test-org-id',
        'subscription_id': 'test-sub-id',
        'status': 'cancelled',
        'cancellation_reason': 'user_requested'
      };
      
      final result = await testFramework.testEventFlow(
        'subscription.cancelled', 
        testPayload
      );
      
      expect(result['success'], true);
      expect(
        result['workflowResults']['executed_workflows'],
        contains('subscription_cancellation')
      );
      expect(
        result['sideEffects']['user_access_updated'],
        true
      );
    });
  });
}
```

## Migration Strategy

### Phase 1: Infrastructure Setup
- [ ] Create event registry tables
- [ ] Deploy generic trigger functions
- [ ] Set up event router edge function

### Phase 2: Event Registration
- [ ] Register existing subscription events
- [ ] Migrate existing triggers to metadata-driven approach
- [ ] Update workflow definitions

### Phase 3: Integration Testing
- [ ] Test event-workflow integration
- [ ] Verify cross-system event propagation
- [ ] Performance testing and optimization

## Assumptions
- PostgreSQL NOTIFY/LISTEN for real-time events
- Supabase Edge Functions for event processing
- JSON schema validation for event payloads
- Retry mechanisms for failed event processing

## Review Flags
- [ ] Event schema validation performance impact
- [ ] Database trigger overhead on high-volume tables
- [ ] Edge function cold start latency for event processing
- [ ] Event ordering guarantees for related events

## Completeness Checklist
- [x] Event system architecture definition
- [x] Database trigger standardization
- [x] Workflow engine integration
- [x] Supabase edge function implementation
- [x] Testing framework specification
- [x] Migration strategy outline
- [x] Flutter integration
- [x] Type system integration
- [x] Field mapper integration
- [x] Error handling alignment