File: arioncomply-v1/ai-backend/vector-db/README.md
# Supabase Vector DB (MVP → Demo-Light)

Goals
- Org-scoped retrieval with provenance and predictable performance.
- Simple ops for MVP; swappable later (ChromeDB vs Supabase Vector).

Schema (default 768-dim)
- `documents` (per org): id uuid pk, org_id uuid not null, title, source, version, hash, metadata jsonb, created_at.
- `chunks` (per doc): id uuid pk, org_id uuid not null, doc_id uuid fk, seq int, text, tokens int, metadata jsonb, created_at.
- `embeddings`: id uuid pk, org_id uuid not null, chunk_id uuid fk, model text, dim int, embedding vector(768), created_at.

RLS & Policies
- ENABLE RLS on all tables.
- SELECT/INSERT/UPDATE/DELETE USING (org_id = app_current_org_id() OR app_has_role('admin')).

Functions
- `match_chunks(org_id uuid, query vector(768), limit int, min_score float)` → top-k chunk rows with score.
- Optional BM25 hybrid (later): fuse BM25 and vector with RRF.

Ingestion (MVP)
- Extract (Locling) → chunk (500–800 tokens, 15–20% overlap) → embed (bge-small/e5-small) → insert.
- Record provenance: version/hash at doc level; seq per-chunk; citations use `[doc_id:version#seq]`.
- Implementation stubs: see `python-backend/services/ingestion/*` and `ingest_pipeline.py`.

Retrieval (MVP)
- Vector top-k filtered by org; pack by token budget; return citations.
- Emit `retrieval_run` with backend/topK/latency/citations.
 - Implementation stub: see `python-backend/services/retrieval/supabase_vector.py`.

Tuning
- `ivfflat` index on embeddings; tune lists/probes; ANALYZE periodically.

Migration location
- See `ai-backend/supabase_migrations/vector/0001_vector_schema.sql` (apply to the vector Supabase project).
File: arioncomply-v1/ai-backend/vector-db/README.md
