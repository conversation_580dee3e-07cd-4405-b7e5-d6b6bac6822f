id: Q230
query: >-
  What if compliance requirements start conflicting with business innovation?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/4.2"
overlap_ids: []
capability_tags:
  - "Report"
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Understanding the Organization"
    id: "ISO27001:2022/4.2"
    locator: "Clause 4.2"
ui:
  cards_hint:
    - "Conflict mitigation plan"
  actions:
    - type: "start_workflow"
      target: "innovation_compliance"
      label: "Balance Innovation & Compliance"
output_mode: "both"
graph_required: false
notes: "Use risk-based approach to balance agility and control"
---
### 230) What if compliance requirements start conflicting with business innovation?

**Standard terms**  
- **Understanding the organization (Cl.4.2):** map internal/external needs.

**Plain-English answer**  
Log conflicts in a **Conflict Mitigation Plan**, assess risks vs benefits, and apply compensating controls or phased rollouts. Engage stakeholders to find acceptable trade-offs.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 4.2

**Why it matters**  
Maintains business agility without sacrificing security or compliance.

**Do next in our platform**  
- Run **Innovation Compliance** workflow.  
- Document decisions in the **Conflict Mitigation** register.

**How our platform will help**  
- **[Report]** Impact analysis of proposed innovations.  
- **[Workflow]** Approval gates and exception tracking.

**Likely follow-ups**  
- How to automate exception renewals?

**Sources**  
- ISO/IEC 27001:2022 Clause 4.2
