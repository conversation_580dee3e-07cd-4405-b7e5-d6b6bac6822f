id: Q186
query: >-
  How do we handle data residency requirements with global cloud providers?
packs:
  - "GDPR:2016"
  - "NIS2:2023"
primary_ids:
  - "GDPR:2016/Arts.44–50"
  - "NIS2:2023/Art.5"
overlap_ids: []
capability_tags:
  - "Register"
  - "Workflow"
flags:
  - "LOCAL LAW_CHECK"
sources:
  - title: "GDPR — International Transfers"
    id: "GDPR:2016/Arts.44–50"
    locator: "Articles 44–50"
  - title: "NIS2 — Territorial Scope"
    id: "NIS2:2023/Art.5"
    locator: "Article 5"
ui:
  cards_hint:
    - "Data residency register"
  actions:
    - type: "open_register"
      target: "residency_register"
      label: "View Residency Rules"
    - type: "start_workflow"
      target: "residency_compliance"
      label: "Ensure Residency"
output_mode: "both"
graph_required: false
notes: "Some countries mandate local storage or specific encryption measures"
---
### 186) How do we handle data residency requirements with global cloud providers?

**Standard terms)**  
- **International transfers (GDPR Arts.44–50):** adequacy, SCCs, BCRs.  
- **Territorial scope (NIS2 Art.5):** national implementation may add restrictions.

**Plain-English answer**  
Maintain a **Residency Register** mapping data types to allowed regions. Use provider APIs to restrict where data is stored and processed, encrypt data at rest, and apply SCCs where needed **[LOCAL LAW_CHECK]**.

**Applies to**  
- **Primary:** GDPR Articles 44–50; NIS2 Article 5

**Why it matters**  
Violating residency rules can lead to data seizure, fines, and service disruptions.

**Do next in our platform**  
- Populate **Residency Register** with all storage locations.  
- Run **Residency Compliance** workflow to enforce limits.

**How our platform will help**  
- **[Register]** Country-by-country residency rules.  
- **[Workflow]** Automated enforcement via policy-as-code.

**Likely follow-ups**  
- Which CSP regions support guaranteed EU-only storage?  
- How do we audit residency logs?

**Sources**  
- GDPR Articles 44–50; NIS2 Article 5

**Legal note:** Always verify local data-residency laws with counsel.  
