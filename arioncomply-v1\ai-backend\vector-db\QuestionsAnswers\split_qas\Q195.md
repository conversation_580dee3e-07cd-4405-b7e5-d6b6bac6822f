id: Q195
query: >-
  How do we train different levels of employees—executives, managers, and staff?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/7.3"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Draft Doc"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Awareness, Education, and Training"
    id: "ISO27001:2022/7.3"
    locator: "Clause 7.3"
ui:
  cards_hint:
    - "Training matrix"
  actions:
    - type: "start_workflow"
      target: "training_program_setup"
      label: "Design Training"
    - type: "open_register"
      target: "training_matrix"
      label: "View Training Levels"
output_mode: "both"
graph_required: false
notes: "Tailor content: executives need high-level risk overviews; staff need hands-on procedure training"
---
### 195) How do we train different levels of employees—executives, managers, and staff?

**Standard terms**  
- **Awareness & training (ISO27001 Cl.7.3):** competence matched to roles.

**Plain-English answer**  
Build a **training matrix**:  
- **Executives:** strategic risks, compliance oversight.  
- **Managers:** process integration, metrics review.  
- **Staff:** daily procedures, incident reporting.  
Use role-based modules and track completion.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 7.3

**Why it matters**  
Ensures everyone understands and fulfills their security roles.

**Do next in our platform**  
- Launch **Training Program Setup** workflow.  
- Define role-based modules in the **Training Matrix**.

**How our platform will help**  
- **[Draft Doc]** Auto-generate slide decks per audience.  
- **[Workflow]** Assign training and track completions.

**Likely follow-ups**  
- How often to refresh executive training?  
- Can we integrate with our LMS?

**Sources**  
- ISO/IEC 27001:2022 Clause 7.3  
