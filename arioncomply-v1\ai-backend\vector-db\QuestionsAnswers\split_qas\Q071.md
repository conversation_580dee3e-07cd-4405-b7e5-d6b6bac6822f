id: Q071
query: >-
  What’s a “gap analysis” and do we need to pay someone to do one?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/9.1"
  - "GDPR:2016/Art.24"
overlap_ids:
  - "ISO27701:2019/9"
capability_tags:
  - "Workflow"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Monitoring & Evaluation"
    id: "ISO27001:2022/9.1"
    locator: "Clause 9.1"
  - title: "GDPR — Accountability"
    id: "GDPR:2016/Art.24"
    locator: "Article 24"
  - title: "ISO/IEC 27701:2019 — PIMS Improvement"
    id: "ISO27701:2019/9"
    locator: "Clause 9"
ui:
  cards_hint:
    - "Gap analysis template"
  actions:
    - type: "start_workflow"
      target: "gap_analysis"
      label: "Run Gap Analysis"
output_mode: "both"
graph_required: false
notes: "You can self-serve using platform tools or engage an expert for deeper review"
---
### 71) What’s a “gap analysis” and do we need to pay someone to do one?

**Standard terms)**  
- **Monitoring & evaluation (ISO 27001 Cl. 9.1):** internal audit identifying gaps.  
- **Accountability (GDPR Art. 24):** evaluates existing measures vs requirements.  
- **Continual improvement (ISO 27701 Cl. 9):** updates PIMS based on gap findings.

**Plain-English answer**  
A gap analysis compares your current state against the standard’s requirements, identifying what’s missing. Our platform provides automated gap reports—no external fees needed unless you want third-party validation.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 9.1; GDPR Article 24  
- **Also relevant/Overlaps:** ISO/IEC 27701:2019 Clause 9

**Why it matters**  
Knowing gaps upfront focuses your efforts and budget.

**Do next in our platform**  
- Launch **Gap Analysis** workflow.  
- Review auto-generated gap report.

**How our platform will help**  
- **[Workflow]** Guided gap discovery and scoring.  
- **[Report]** Gap heatmap and remediation plan.

**Likely follow-ups**  
- “Can we export the gap report for stakeholders?” (Yes—PDF/Excel export)

**Sources**  
- ISO/IEC 27001:2022 Clause 9.1  
- GDPR Article 24  
- ISO/IEC 27701:2019 Clause 9