id: Q117
query: >-
  How do we handle compliance when we're changing or growing fast?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/4.3"
  - "ISO27001:2022/10.2"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Planner"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Scope Changes"
    id: "ISO27001:2022/4.3"
    locator: "Clause 4.3"
  - title: "ISO/IEC 27001:2022 — Corrective Action"
    id: "ISO27001:2022/10.2"
    locator: "Clause 10.2"
ui:
  cards_hint:
    - "Change management plan"
  actions:
    - type: "start_workflow"
      target: "growth_compliance"
      label: "Manage Growth Changes"
output_mode: "both"
graph_required: false
notes: "Use corrective/improvement process to adjust scope and controls dynamically"
---
### 117) How do we handle compliance when we're changing or growing fast?

**Standard terms**  
- **Scope changes (Cl. 4.3):** update ISMS boundaries.  
- **Corrective action (Cl. 10.2):** manage improvements.

**Plain-English answer**  
Trigger your change-management process: reassess scope, update risk register, reassign control owners, and launch corrective actions for new processes or assets.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clauses 4.3; 10.2

**Why it matters**  
Ensures your compliance program scales without gaps.

**Do next in our platform**  
- Launch **Growth Compliance** workflow.  
- Adjust your **Scope & Risk** registers.

**How our platform will help**  
- **[Workflow]** Automated scope-change notifications.  
- **[Planner]** Dynamic task reallocation planning.

**Likely follow-ups**  
- “How often to review scope during growth?” (After each major milestone)

**Sources**  
- ISO/IEC 27001:2022 Clauses 4.3; 10.2
