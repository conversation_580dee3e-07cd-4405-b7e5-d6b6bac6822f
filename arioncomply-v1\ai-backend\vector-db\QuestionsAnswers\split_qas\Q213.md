```yaml
id: Q213
question: What happens when regulations change — do we need to update everything?
packs: ["ISO27001:2022","GDPR:2016"]
primary_ids: ["ISO27001:2022/Cl.6.3","ISO27001:2022/Cl.10","GDPR:2016/Art.24"]
overlap_ids: ["ISO27701:2019/Cl.7.5","ISO27701:2019/Cl.9"]
capability_tags: ["Workflow","Versioning","Approval","Reminder","Report"]
ui:
  actions:
    - target: "workflow"
      action: "open"
      args: { key: "regulatory_change" }
    - target: "report"
      action: "open"
      args: { key: "change_impact_summary" }
cards_hint:
  - Assess impact; update only affected items.
  - Redline docs; capture approvals.
  - Retrain if roles/processes change.
graph_required: false
```

### 213) What happens when regulations change—do we need to update everything?

**Standard term(s)**

- **Change management (Cl. 6.3)**; **Accountability (GDPR Art. 24)**.

**Plain-English answer**\
Update the **impacted** notices, records, contracts, controls, and training—**not** everything.

**Applies to**

- **Primary:** ISO/IEC 27001:2022 **Cl. 6.3, 10**; GDPR **Art. 24**.
- **Also relevant/Overlaps:** ISO/IEC 27701.

**Why it matters**\
Timely updates prevent **drift**.

**Do next in our platform**

- Open a **regulatory-change** item; assess impact; redline docs; route approvals; schedule retraining.

**How our platform will help**

- **[Workflow] [Versioning] [Approval] [Reminder] [Report]** — Impact checklist, sign-offs, and an auditable trail.

**Likely follow-ups**

- “Who signs off?” → See RACI (Q103).

**Sources**

- ISO/IEC 27001:2022 **Cl. 6.3, 10**; GDPR **Art. 24**.