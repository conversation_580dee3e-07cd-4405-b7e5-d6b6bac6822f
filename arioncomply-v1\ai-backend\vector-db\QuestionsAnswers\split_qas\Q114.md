id: Q114
query: >-
  What's business continuity planning and how detailed does it need to be?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/A.17.1"
overlap_ids:
  - "ISO22301:2019/4"
capability_tags:
  - "Draft Doc"
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Annex A.17.1 Business Continuity"
    id: "ISO27001:2022/A.17.1"
    locator: "Annex A.17.1"
  - title: "ISO 22301:2019 — BCMS Requirements"
    id: "ISO22301:2019/4"
    locator: "Clause 4"
ui:
  cards_hint:
    - "BCP template library"
  actions:
    - type: "start_workflow"
      target: "bcp_setup"
      label: "Create BCP"
    - type: "open_register"
      target: "bcp_documents"
      label: "View BCP Docs"
output_mode: "both"
graph_required: false
notes: "High-level plans plus detailed procedures for critical processes"
---
### 114) What's business continuity planning and how detailed does it need to be?

**Standard terms**  
- **BCP controls (A.17.1):** protect information availability.  
- **BCMS requirements (ISO 22301 Cl. 4):** scope and framework.

**Plain-English answer**  
BCP outlines how to maintain or restore critical operations after disruption. It needs a high-level strategy for all key services, plus detailed recovery procedures for top-3 critical processes (RTOs/RPOs defined).

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.17.1  
- **Also relevant/Overlaps:** ISO 22301:2019 Clause 4

**Why it matters**  
Ensures you can continue essential functions under adverse conditions.

**Do next in our platform**  
- Launch **BCP Setup** workflow.  
- Generate critical process recovery plans.

**How our platform will help**  
- **[Draft Doc]** BCP and BC procedures templates.  
- **[Workflow]** BCP approval and test scheduling.

**Likely follow-ups**  
- “How often should we test the BCP?” (At least annually)

**Sources**  
- ISO/IEC 27001:2022 Annex A.17.1; ISO 22301:2019 Clause 4
