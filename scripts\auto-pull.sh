#!/bin/bash
#
# File: scripts/auto-pull.sh
# File Description: Automatic pull from GitHub when remote changes are available
# Purpose: Keep local repo synchronized with GitHub changes from other devices/users
#
# Usage: ./scripts/auto-pull.sh
#
# Setup Instructions:
#   1. Created by <PERSON> for bidirectional sync
#   2. Made executable with: chmod +x scripts/auto-pull.sh
#   3. Can be run standalone or integrated into auto-sync.sh
#   4. Add to crontab for regular pull checks if running separately
#
# How it works:
#   - Fetches latest remote references without merging
#   - Compares local HEAD with remote HEAD hashes
#   - Pulls only if remote has newer commits
#   - <PERSON>les merge conflicts gracefully
#   - Logs all pull activities for debugging
#
# Conflict handling:
#   - For auto-generated files, remote takes precedence
#   - For manual edits, requires manual resolution
#   - Failed pulls are logged for manual review
#
# Dependencies: Git, GitHub remote origin configured
# Security/RLS: Uses existing git credentials and SSH keys
# Notes: Complements push automation for full bidirectional sync
#

cd "$(dirname "$0")/.." || exit 1

LOG_FILE=".git/auto-pull.log"

log_msg() {
    echo "$(date '+%Y-%m-%d %H:%M:%S'): $*" | tee -a "$LOG_FILE"
}

BRANCH=$(git branch --show-current)
log_msg "Checking for remote changes on $BRANCH..."

# Fetch latest remote refs without merging
if ! git fetch origin "$BRANCH" 2>/dev/null; then
    log_msg "Failed to fetch from origin/$BRANCH"
    exit 1
fi

# Compare local and remote commit hashes
LOCAL_HASH=$(git rev-parse HEAD)
REMOTE_HASH=$(git rev-parse "origin/$BRANCH" 2>/dev/null || echo "$LOCAL_HASH")

if [[ "$LOCAL_HASH" != "$REMOTE_HASH" ]]; then
    log_msg "Remote changes detected (local: ${LOCAL_HASH:0:8}, remote: ${REMOTE_HASH:0:8})"
    
    # Check for local uncommitted changes
    if ! git diff-index --quiet HEAD --; then
        log_msg "Local changes detected, stashing before pull..."
        git stash push -m "Auto-stash before pull $(date '+%Y-%m-%d %H:%M:%S')"
        STASHED=1
    else
        STASHED=0
    fi
    
    # Attempt to pull remote changes
    if git pull origin "$BRANCH" 2>/dev/null; then
        log_msg "Successfully pulled changes from origin/$BRANCH"
        
        # Restore stashed changes if any
        if [[ $STASHED -eq 1 ]]; then
            log_msg "Restoring stashed local changes..."
            if git stash pop 2>/dev/null; then
                log_msg "Successfully restored local changes"
            else
                log_msg "Conflict restoring stash - manual resolution needed"
            fi
        fi
    else
        log_msg "Failed to pull from origin/$BRANCH - may need manual merge"
        
        # Restore stash on failed pull
        if [[ $STASHED -eq 1 ]]; then
            log_msg "Restoring stash after failed pull..."
            git stash pop 2>/dev/null || log_msg "Failed to restore stash"
        fi
    fi
else
    log_msg "Local branch up to date with remote"
fi