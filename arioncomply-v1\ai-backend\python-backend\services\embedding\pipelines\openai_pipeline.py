"""
# File: arioncomply-v1/ai-backend/python-backend/services/embedding/pipelines/openai_pipeline.py
# File Description: OpenAI embeddings pipeline (optional, security-sensitive)
# Purpose: Provide cloud-based high-quality embeddings when explicitly allowed

OpenAI Embeddings pipeline for cloud-based high-quality embeddings.

⚠️ SECURITY WARNING: This pipeline sends data to external servers!
- All text is transmitted to OpenAI's servers
- May violate data residency requirements
- Not suitable for sensitive or confidential data
- Requires explicit administrator approval
"""

import asyncio
import logging
import os
import time
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any

from ..pipeline_interface import (
    EmbeddingPipeline,
    PipelineMetadata,
    EmbeddingResult,
    HealthCheckResult,
    PipelineStatus,
    QualityTier
)

logger = logging.getLogger(__name__)

try:
    import openai
    from openai import OpenAI
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"OpenAI dependencies not available: {e}")
    DEPENDENCIES_AVAILABLE = False


class OpenAIPipeline(EmbeddingPipeline):
    """
    OpenAI Embeddings pipeline.
    
    ⚠️ SECURITY WARNING: Data is sent to external servers!
    
    Features:
    - High-quality cloud-based embeddings
    - text-embedding-3-small (1536-dim) or text-embedding-3-large (3072-dim)
    - Automatic retry logic with exponential backoff
    - Rate limiting and cost tracking
    - Explicit security warnings and opt-in requirement
    """
    
    SMALL_MODEL = "text-embedding-3-small"
    LARGE_MODEL = "text-embedding-3-large"
    SMALL_DIMENSION = 1536
    LARGE_DIMENSION = 3072
    CONTEXT_LENGTH = 8192
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize OpenAI embeddings pipeline from configuration."""
        super().__init__(config)
        self._name = "openai"
        
        # Configuration
        self.api_key = config.get("api_key") or os.getenv("OPENAI_API_KEY")
        self.model_name = config.get("model_name", self.SMALL_MODEL)
        self.max_retries = config.get("max_retries", 3)
        self.request_timeout = config.get("request_timeout", 30)
        
        # Security configuration
        self.security_approved = config.get("security_approved", False)
        self.explicit_approval_required = config.get("explicit_approval_required", True)
        
        # Rate limiting
        self.max_rpm = config.get("max_rpm", 3000)  # Default tier limit
        self.max_tpm = config.get("max_tpm", 1000000)  # Default tier limit
        
        # Model-specific settings
        if self.model_name == self.LARGE_MODEL:
            self.dimension = self.LARGE_DIMENSION
            self.cost_per_1m_tokens = 0.13
        else:
            self.dimension = self.SMALL_DIMENSION
            self.cost_per_1m_tokens = 0.02
        
        # OpenAI client
        self.client = None
        
        # Performance tracking
        self._avg_inference_time_ms = None
        self._total_tokens_used = 0
        self._total_cost = 0.0
        
        # Metadata
        self._metadata = PipelineMetadata(
            name=self._name,
            model_name=self.model_name,
            dimension=self.dimension,
            quality_tier=QualityTier.HIGH,
            is_local=False,  # ⚠️ Cloud service
            version="3.0.0",
            provider="OpenAI",
            creation_date=datetime(2024, 1, 25),  # text-embedding-3 release
            memory_mb=0,  # No local model
            inference_time_ms=300,  # Network latency estimate
            context_length=self.CONTEXT_LENGTH,
            config={
                "model": self.model_name,
                "dimension": self.dimension,
                "cost_per_1m_tokens": self.cost_per_1m_tokens,
                "security_warning": "⚠️ DATA SENT TO EXTERNAL SERVERS",
                "data_residency_risk": True,
                "requires_approval": self.explicit_approval_required
            }
        )
    
    @property
    def name(self) -> str:
        """Return pipeline name."""
        return self._name
    
    @property
    def metadata(self) -> PipelineMetadata:
        """Return pipeline metadata with dynamic metrics and usage totals."""
        # Update dynamic metrics
        if self._avg_inference_time_ms:
            self._metadata.inference_time_ms = int(self._avg_inference_time_ms)
        
        # Update usage statistics
        self._metadata.config.update({
            "total_tokens_used": self._total_tokens_used,
            "estimated_total_cost": self._total_cost,
            "avg_inference_time_ms": self._avg_inference_time_ms
        })
        
        return self._metadata
    
    async def load(self) -> None:
        """Initialize OpenAI client and validate configuration."""
        if not DEPENDENCIES_AVAILABLE:
            raise RuntimeError("OpenAI dependencies not available. Install: pip install openai")
        
        if self._is_loaded:
            return
        
        # Security check
        if self.explicit_approval_required and not self.security_approved:
            raise RuntimeError(
                "⚠️ SECURITY WARNING: OpenAI pipeline requires explicit approval. "
                "This pipeline sends all text data to external servers. "
                "Set 'security_approved': True in config to acknowledge data exposure risk."
            )
        
        # API key validation
        if not self.api_key:
            raise RuntimeError("OpenAI API key not found. Set OPENAI_API_KEY environment variable or provide in config.")
        
        logger.warning("⚠️ SECURITY WARNING: Initializing OpenAI pipeline - data will be sent to external servers!")
        
        try:
            # Initialize OpenAI client
            self.client = OpenAI(
                api_key=self.api_key,
                timeout=self.request_timeout,
                max_retries=self.max_retries
            )
            
            # Test API connection
            logger.info("Testing OpenAI API connection...")
            test_response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.embeddings.create(
                    input="test connection",
                    model=self.model_name
                )
            )
            
            if test_response and test_response.data:
                self._is_loaded = True
                logger.info("OpenAI pipeline loaded successfully")
            else:
                raise RuntimeError("OpenAI API test failed - invalid response")
                
        except Exception as e:
            logger.error(f"Failed to load OpenAI pipeline: {e}")
            raise
    
    async def embed_texts(self, texts: List[str], trace_id: Optional[str] = None) -> EmbeddingResult:
        """
        Generate embeddings using OpenAI API.
        
        ⚠️ WARNING: This sends data to external servers!
        """
        start_time = time.time()
        operation_id = str(uuid.uuid4())
        
        # Log security warning for every embedding operation
        logger.warning(f"⚠️ OpenAI embedding operation {operation_id}: Sending {len(texts)} texts to external servers")
        
        try:
            # Validate inputs
            self.validate_inputs(texts)
            
            if not self._is_loaded:
                await self.load()
            
            # Prepare texts (truncate if necessary)
            processed_texts = []
            total_chars = 0
            for text in texts:
                # Rough token estimation (1 token ≈ 4 characters)
                if len(text) > self.CONTEXT_LENGTH * 4:
                    text = text[:self.CONTEXT_LENGTH * 4]
                    logger.warning(f"Text truncated to fit context length: {len(text)} chars")
                
                processed_texts.append(text)
                total_chars += len(text)
            
            # Estimate token usage for cost tracking
            estimated_tokens = total_chars // 4
            
            # Generate embeddings with retry logic
            logger.debug(f"Sending {len(processed_texts)} texts to OpenAI API")
            response = await self._generate_embeddings_with_retry(processed_texts)
            
            # Extract embeddings
            embeddings_list = [data.embedding for data in response.data]
            
            # Update usage statistics
            actual_tokens = response.usage.total_tokens if hasattr(response, 'usage') else estimated_tokens
            self._total_tokens_used += actual_tokens
            cost = (actual_tokens / 1000000) * self.cost_per_1m_tokens
            self._total_cost += cost
            
            processing_time_ms = (time.time() - start_time) * 1000
            
            # Update average inference time
            if self._avg_inference_time_ms is None:
                self._avg_inference_time_ms = processing_time_ms
            else:
                self._avg_inference_time_ms = (self._avg_inference_time_ms * 0.9) + (processing_time_ms * 0.1)
            
            logger.debug(f"OpenAI embedding completed in {processing_time_ms:.1f}ms, cost: ${cost:.6f}")
            
            return self._create_embedding_result(
                operation_id=operation_id,
                input_texts=texts,
                embeddings=embeddings_list,
                processing_time_ms=processing_time_ms,
                success=True,
                trace_id=trace_id
            )
            
        except Exception as e:
            processing_time_ms = (time.time() - start_time) * 1000
            logger.error(f"OpenAI embedding failed: {e}")
            
            return self._create_embedding_result(
                operation_id=operation_id,
                input_texts=texts,
                embeddings=[],
                processing_time_ms=processing_time_ms,
                success=False,
                error_message=str(e),
                trace_id=trace_id
            )
    
    async def _generate_embeddings_with_retry(self, texts: List[str]):
        """Generate embeddings with exponential backoff retry."""
        max_retries = self.max_retries
        base_delay = 1.0
        
        for attempt in range(max_retries + 1):
            try:
                response = await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: self.client.embeddings.create(
                        input=texts,
                        model=self.model_name
                    )
                )
                return response
                
            except openai.RateLimitError as e:
                if attempt < max_retries:
                    delay = base_delay * (2 ** attempt)
                    logger.warning(f"Rate limit hit, retrying in {delay}s (attempt {attempt + 1}/{max_retries + 1})")
                    await asyncio.sleep(delay)
                    continue
                else:
                    raise e
                    
            except openai.APITimeoutError as e:
                if attempt < max_retries:
                    delay = base_delay * (2 ** attempt)
                    logger.warning(f"API timeout, retrying in {delay}s (attempt {attempt + 1}/{max_retries + 1})")
                    await asyncio.sleep(delay)
                    continue
                else:
                    raise e
                    
            except openai.APIConnectionError as e:
                if attempt < max_retries:
                    delay = base_delay * (2 ** attempt)
                    logger.warning(f"Connection error, retrying in {delay}s (attempt {attempt + 1}/{max_retries + 1})")
                    await asyncio.sleep(delay)
                    continue
                else:
                    raise e
                    
            except Exception as e:
                # Don't retry for other errors
                raise e
    
    async def health_check(self) -> HealthCheckResult:
        """Perform health check on OpenAI pipeline."""
        start_time = time.time()
        
        try:
            # Check dependencies
            if not DEPENDENCIES_AVAILABLE:
                return HealthCheckResult(
                    pipeline_name=self.name,
                    status=PipelineStatus.FAILED,
                    timestamp=datetime.utcnow(),
                    error_message="Dependencies not available: openai",
                    dependencies_available=False
                )
            
            # Check security approval
            if self.explicit_approval_required and not self.security_approved:
                return HealthCheckResult(
                    pipeline_name=self.name,
                    status=PipelineStatus.FAILED,
                    timestamp=datetime.utcnow(),
                    error_message="Security approval required - data exposure risk not acknowledged",
                    dependencies_available=True,
                    is_model_loaded=False
                )
            
            # Check API key
            if not self.api_key:
                return HealthCheckResult(
                    pipeline_name=self.name,
                    status=PipelineStatus.FAILED,
                    timestamp=datetime.utcnow(),
                    error_message="OpenAI API key not configured",
                    dependencies_available=True,
                    is_model_loaded=False
                )
            
            # Initialize if not loaded
            if not self._is_loaded:
                try:
                    await self.load()
                except Exception as load_error:
                    return HealthCheckResult(
                        pipeline_name=self.name,
                        status=PipelineStatus.FAILED,
                        timestamp=datetime.utcnow(),
                        error_message=f"Failed to initialize: {load_error}",
                        dependencies_available=True,
                        is_model_loaded=False
                    )
            
            # Test embedding generation
            test_text = "Health check test for OpenAI embedding API."
            test_result = await self.embed_texts([test_text])
            
            if not test_result.success:
                return HealthCheckResult(
                    pipeline_name=self.name,
                    status=PipelineStatus.FAILED,
                    timestamp=datetime.utcnow(),
                    error_message=f"Test embedding failed: {test_result.error_message}",
                    is_model_loaded=self._is_loaded,
                    dependencies_available=True
                )
            
            # Validate embedding dimensions
            if len(test_result.embeddings[0]) != self.dimension:
                return HealthCheckResult(
                    pipeline_name=self.name,
                    status=PipelineStatus.FAILED,
                    timestamp=datetime.utcnow(),
                    error_message=f"Invalid embedding dimension: got {len(test_result.embeddings[0])}, expected {self.dimension}",
                    is_model_loaded=self._is_loaded,
                    dependencies_available=True
                )
            
            return HealthCheckResult(
                pipeline_name=self.name,
                status=PipelineStatus.HEALTHY,
                timestamp=datetime.utcnow(),
                load_time_ms=100.0,  # Minimal initialization time
                test_inference_time_ms=test_result.processing_time_ms,
                memory_usage_mb=10.0,  # Minimal local memory
                is_model_loaded=self._is_loaded,
                dependencies_available=True
            )
            
        except Exception as e:
            return HealthCheckResult(
                pipeline_name=self.name,
                status=PipelineStatus.FAILED,
                timestamp=datetime.utcnow(),
                error_message=str(e),
                error_details={"exception_type": type(e).__name__},
                is_model_loaded=self._is_loaded,
                dependencies_available=DEPENDENCIES_AVAILABLE
            )
    
    async def unload(self) -> None:
        """Unload the pipeline (minimal cleanup for API client)."""
        logger.info("Unloading OpenAI pipeline")
        
        if self.client:
            # Close any persistent connections
            if hasattr(self.client, 'close'):
                self.client.close()
            self.client = None
        
        self._is_loaded = False
        logger.info("OpenAI pipeline unloaded")
