#!/bin/bash
#
# File: arioncomply-v1/ai-backend/python-backend/stop-backend.sh
# File Description: Stop the ArionComply Python AI Backend HTTP server
# Purpose: Gracefully terminate the FastAPI uvicorn server and cleanup resources
#
# Usage: bash stop-backend.sh [--force]
#
# Options:
#   --force    Force kill server processes (SIGKILL instead of SIGTERM)
#
# How it works:
#   1. Reads server PID from .backend.pid file if available
#   2. Attempts graceful shutdown with SIGTERM
#   3. Falls back to port-based cleanup for orphaned processes
#   4. Cleans up log files and PID files
#   5. Verifies server has stopped
#
# Dependencies: lsof (for port cleanup), kill command
# Security/RLS: Local development only
# Notes: Complements start-backend.sh for complete server lifecycle management
#

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd "$SCRIPT_DIR"

DEFAULT_PORT="9000"
FORCE_KILL=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --force|-f)
            FORCE_KILL=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [--force]"
            echo "Stop ArionComply AI Backend server"
            echo ""
            echo "Options:"
            echo "  --force    Force kill server processes"
            echo "  --help     Show this help message"
            exit 0
            ;;
        *)
            echo "❌ Unknown argument: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

echo "🛑 Stopping ArionComply AI Backend server..."

# Attempt to stop via PID file first
if [ -f ".backend.pid" ]; then
    BACKEND_PID="$(cat .backend.pid 2>/dev/null || echo "")"
    if [ -n "$BACKEND_PID" ] && kill -0 "$BACKEND_PID" 2>/dev/null; then
        echo "📋 Found server process ID: $BACKEND_PID"
        
        if [ "$FORCE_KILL" = true ]; then
            echo "💥 Force killing server process..."
            kill -9 "$BACKEND_PID" 2>/dev/null || true
        else
            echo "🏃 Gracefully shutting down server..."
            kill -TERM "$BACKEND_PID" 2>/dev/null || true
            
            # Wait for graceful shutdown
            sleep 2
            
            # Check if process is still running
            if kill -0 "$BACKEND_PID" 2>/dev/null; then
                echo "⚠️  Process still running, force killing..."
                kill -9 "$BACKEND_PID" 2>/dev/null || true
            fi
        fi
        
        echo "✅ Server process terminated"
    else
        echo "⚠️  PID file exists but process not running"
    fi
    
    # Clean up PID file
    rm -f .backend.pid
else
    echo "📋 No PID file found, checking port-based cleanup..."
fi

# Fallback: Clean up any processes on the default port
echo "🧹 Cleaning up any remaining processes on port $DEFAULT_PORT..."
PROCESSES_ON_PORT=$(lsof -ti:"$DEFAULT_PORT" 2>/dev/null || echo "")

if [ -n "$PROCESSES_ON_PORT" ]; then
    echo "🔍 Found processes on port $DEFAULT_PORT: $PROCESSES_ON_PORT"
    
    if [ "$FORCE_KILL" = true ]; then
        echo "💥 Force killing port-bound processes..."
        echo "$PROCESSES_ON_PORT" | xargs kill -9 2>/dev/null || true
    else
        echo "🏃 Gracefully terminating port-bound processes..."
        echo "$PROCESSES_ON_PORT" | xargs kill -TERM 2>/dev/null || true
        
        # Wait and force kill if needed
        sleep 2
        REMAINING_PROCESSES=$(lsof -ti:"$DEFAULT_PORT" 2>/dev/null || echo "")
        if [ -n "$REMAINING_PROCESSES" ]; then
            echo "💥 Force killing remaining processes..."
            echo "$REMAINING_PROCESSES" | xargs kill -9 2>/dev/null || true
        fi
    fi
    
    echo "✅ Port cleanup completed"
else
    echo "✅ No processes found on port $DEFAULT_PORT"
fi

# Clean up log files (optional)
if [ -f ".server.log" ]; then
    echo "🗑️  Archiving server logs..."
    mv .server.log ".server.log.$(date +%Y%m%d_%H%M%S)" 2>/dev/null || rm -f .server.log
    echo "✅ Log files cleaned up"
fi

# Verify server has stopped
sleep 1
if curl -s --connect-timeout 2 "http://127.0.0.1:$DEFAULT_PORT/docs" > /dev/null 2>&1; then
    echo "⚠️  Server may still be running on port $DEFAULT_PORT"
    echo "💡 Try running with --force flag"
    exit 1
else
    echo "✅ Server confirmed stopped"
fi

echo ""
echo "🎯 ArionComply AI Backend server stopped successfully"
echo "💡 To restart: bash ./start-backend.sh"
echo ""