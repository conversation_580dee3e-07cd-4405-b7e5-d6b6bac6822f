-- File: arioncomply-v1/db/migrations/0014_logging_add_session_trace.sql
-- Migration 0014: Add session_id and trace_id to logging tables
-- Purpose: Improve traceability and filtering by linking logs to sessions and traces

BEGIN;

-- Add columns (nullable to support requests without a session)
ALTER TABLE api_request_logs ADD COLUMN IF NOT EXISTS session_id uuid;
ALTER TABLE api_request_logs ADD COLUMN IF NOT EXISTS trace_id text;

ALTER TABLE api_event_logs ADD COLUMN IF NOT EXISTS session_id uuid;
ALTER TABLE api_event_logs ADD COLUMN IF NOT EXISTS trace_id text;

-- Helpful indexes for filtering and analytics
CREATE INDEX IF NOT EXISTS idx_api_request_logs_session ON api_request_logs (org_id, session_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_api_request_logs_trace ON api_request_logs (org_id, trace_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_api_event_logs_session ON api_event_logs (org_id, session_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_api_event_logs_trace ON api_event_logs (org_id, trace_id, created_at DESC);

COMMIT;

