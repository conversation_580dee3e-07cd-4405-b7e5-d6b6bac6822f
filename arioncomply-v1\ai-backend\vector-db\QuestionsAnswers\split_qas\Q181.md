id: Q181
query: >-
  Are we responsible for compliance when using cloud services or is the cloud provider?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/A.14.2.7"
  - "GDPR:2016/Art.28"
overlap_ids: []
capability_tags:
  - "Register"
  - "Report"
  - "Dashboard"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Outsourced Development & Cloud Services"
    id: "ISO27001:2022/A.14.2.7"
    locator: "Annex A.14.2.7"
  - title: "GDPR — Processor Obligations"
    id: "GDPR:2016/Art.28"
    locator: "Article 28"
ui:
  cards_hint:
    - "Shared-responsibility matrix"
  actions:
    - type: "open_register"
      target: "cloud_shared_responsibility"
      label: "View Responsibility Matrix"
    - type: "start_workflow"
      target: "cloud_compliance_setup"
      label: "Configure Cloud Controls"
output_mode: "both"
graph_required: false
notes: "Cloud providers secure infrastructure; you secure data and legal basis"
---
### 181) Are we responsible for compliance when using cloud services or is the cloud provider?

**Standard terms)**  
- **Outsourced development (ISO27001 A.14.2.7):** controls for third-party platforms.  
- **Processor (GDPR Art.28):** cloud provider as data processor under your control.

**Plain-English answer**  
Cloud vendors manage physical and network security, but **you** remain the data controller: configuring encryption, managing identities, and ensuring lawful data transfers (e.g., SCCs). Always treat it as a **shared-responsibility model**.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.14.2.7; GDPR Article 28

**Why it matters**  
Misunderstanding roles leads to audit failures and data breaches.

**Do next in our platform**  
1. Populate the **Cloud Shared-Responsibility** register.  
2. Configure encryption and identity controls via the **Cloud Compliance** workflow.

**How our platform will help**  
- **[Register]** Pre-built matrix for major CSPs.  
- **[Report]** Gap analysis of your vs provider’s duties.  
- **[Dashboard]** Real-time compliance status by region and service.

**Likely follow-ups**  
- Which CSP regions require extra data-residency measures?  
- Do we need to encrypt in transit or at rest?

**Sources**  
- ISO/IEC 27001:2022 Annex A.14.2.7  
- GDPR Article 28  
