# Claude Code Session Continuity Instructions

## CRITICAL: Todo List Continuity Protocol

**ALWAYS maintain todo list continuity across conversation restarts.**

### Required Actions on Every TodoWrite Usage:

1. **IMMEDIATELY** after using the TodoWrite tool, update the "CURRENT SESSION TODO LIST" section in:
   ```
   arioncomply-v1/ProjectManagement/ActionsToDo.md
   ```

2. **Update format**:
   ```markdown
   **📋 CURRENT SESSION TODO LIST (YYYY-MM-DD):**
   *Updated automatically from TodoWrite tool to maintain continuity across sessions*

   ✅ **COMPLETED:**
   - [x] Task name

   ⏳ **PENDING (Priority Order):**
   1. [ ] Task name - **PRIORITY LEVEL**
   2. [ ] Task name - **PRIORITY LEVEL**
   ```

3. **Keep ActionsToDo.md as the single source of truth** for all task tracking.

### Hook Configuration

The `.claude/settings.local.json` contains a PostToolUse hook that will remind you to update ActionsToDo.md whenever TodoWrite is used.

### Session Start Protocol

At the start of any conversation:
1. Check the "CURRENT SESSION TODO LIST" in ActionsToDo.md
2. Recreate the TodoWrite list from that section
3. Continue work from where you left off

This ensures **zero loss of progress** across conversation restarts and maintains full project continuity.