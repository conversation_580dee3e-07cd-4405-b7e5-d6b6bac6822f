# Evaluation and Test Harness (Placeholder)

Decisions
- Use `testing/llm-comparison` to exercise `conversation.*` endpoints and display `requestId`.
- Golden prompts and retrieval sanity checks for regression.

Design Points
- Scenarios: Q&A, summarization, redaction, proposal generation.
- Metrics: accuracy, groundedness, latency, cost.
- Data: synthetic samples and de-identified real examples.

Open Questions
- Acceptance thresholds per phase; review cadence.

Next Steps
- Wire test UI to show logs and trace links.
- Add fixtures and minimal assertions; record baseline metrics.

