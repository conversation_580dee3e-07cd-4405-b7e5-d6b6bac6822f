```yaml
id: Q211
question: Is compliance a one-time project or an ongoing obligation?
packs: ["ISO27001:2022","ISO27701:2019","GDPR:2016"]
primary_ids: ["ISO27001:2022/Cl.8","ISO27001:2022/Cl.9","ISO27001:2022/Cl.10","GDPR:2016/Art.24"]
overlap_ids: ["ISO27701:2019/Cl.9","ISO27701:2019/Cl.10"]
capability_tags: ["Planner","Reminder","Workflow","Tracker","Dashboard","Report"]
ui:
  actions:
    - target: "planner"
      action: "open"
      args: { template: "annual_compliance_cadence" }
    - target: "workflow"
      action: "open"
      args: { key: "internal_audit" }
    - target: "workflow"
      action: "open"
      args: { key: "management_review" }
cards_hint:
  - Set annual cadence: risk, internal audit, 9.3 review, training, policy refresh.
  - Assign owners & SLAs; attach evidence as you go.
  - Track KPIs monthly; fix drift with CAPs.
graph_required: false
```

### 211) Is compliance a one-time project or an ongoing obligation?

**Standard term(s)**

- **Accountability (GDPR Art. 24):** implement and *demonstrate* appropriate measures, continuously.
- **Continual improvement (ISO 27001 Cl. 10):** keep the ISMS effective over time.

**Plain-English answer**\
Compliance is **ongoing**. You operate and improve it like any core business process—plan, run, measure, and refine—rather than “finish” it once.

**Applies to**

- **Primary:** ISO/IEC 27001:2022 **Clauses 8–10**; GDPR **Art. 24**.
- **Also relevant/Overlaps:** ISO/IEC 27701 **Cl. 9–10**.

**Why it matters**\
Treating compliance as a project leads to **drift** and surprise findings.

**Do next in our platform**

- Create an **annual cadence** for risk, internal audits, management reviews, training, and policy refresh.
- Assign **owners & SLAs**; capture evidence continuously.

**How our platform will help**

- **[Planner] [Reminder] [Workflow] [Tracker] [Dashboard] [Report]** — Cadenced tasks, nudges, KPIs, and audit-ready evidence.

**Likely follow-ups**

- “How often should each activity run?” → See Q61–69.

**Sources**

- ISO/IEC 27001:2022 **Cl. 8–10**; GDPR **Art. 24**.
