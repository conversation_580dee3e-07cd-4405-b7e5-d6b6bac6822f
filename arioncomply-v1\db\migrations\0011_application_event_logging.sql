-- File: arioncomply-v1/db/migrations/0011_application_event_logging.sql
-- Migration 0011: Application Event Logging
-- Purpose: Persist all inbound UI requests, outbound AI/backend events, and internal actions

BEGIN;

-- Request logs: one row per HTTP request handled by Edge Functions
CREATE TABLE IF NOT EXISTS api_request_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  request_id uuid NOT NULL,
  org_id uuid,
  user_id uuid,
  route text NOT NULL,
  method text NOT NULL,
  ip_address text,
  user_agent text,
  correlation_id text,
  request_headers jsonb,
  request_body jsonb,
  response_status integer,
  response_body jsonb,
  duration_ms integer,
  created_at timestamptz NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS idx_api_request_logs_request ON api_request_logs (request_id);
CREATE INDEX IF NOT EXISTS idx_api_request_logs_org ON api_request_logs (org_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_api_request_logs_route ON api_request_logs (route, created_at DESC);

ALTER TABLE api_request_logs ENABLE ROW LEVEL SECURITY;

-- Allow org-scoped visibility; admins can view all
CREATE POLICY api_request_logs_select ON api_request_logs
  FOR SELECT USING (
    org_id IS NULL OR org_id = app_current_org_id() OR app_has_role('admin')
  );

-- Event logs: granular events tied to a request (inbound/outbound/internal)
CREATE TABLE IF NOT EXISTS api_event_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  request_id uuid NOT NULL,
  org_id uuid,
  user_id uuid,
  event_type text NOT NULL,           -- e.g., request_received, response_sent, ai_call, db_write, action_generated
  direction text NOT NULL,            -- inbound | outbound | internal
  target text,                        -- e.g., ai_backend, database, edge, ui
  status text,                        -- ok | error | pending
  details jsonb,                      -- event-specific payload
  created_at timestamptz NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS idx_api_event_logs_request ON api_event_logs (request_id, created_at);
CREATE INDEX IF NOT EXISTS idx_api_event_logs_org ON api_event_logs (org_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_api_event_logs_type ON api_event_logs (event_type, created_at DESC);

ALTER TABLE api_event_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY api_event_logs_select ON api_event_logs
  FOR SELECT USING (
    org_id IS NULL OR org_id = app_current_org_id() OR app_has_role('admin')
  );

COMMIT;
