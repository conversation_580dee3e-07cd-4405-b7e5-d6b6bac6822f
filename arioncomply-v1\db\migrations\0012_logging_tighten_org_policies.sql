-- File: arioncomply-v1/db/migrations/0012_logging_tighten_org_policies.sql
-- Migration 0012: Tighten logging policies and require org_id
-- Purpose: Deny NULL org_id in api_request_logs/api_event_logs and remove NULL visibility from RLS
-- Changes:
--   - ALTER COLUMN org_id SET NOT NULL (both tables)
--   - DROP and recreate SELECT policies without "org_id IS NULL" clause
-- Notes:
--   - Ensure Edge/Backend always set org_id before applying this migration
--   - <PERSON><PERSON> continue to have bypass via app_has_role('admin')

BEGIN;

-- Enforce org_id presence
ALTER TABLE api_request_logs ALTER COLUMN org_id SET NOT NULL;
ALTER TABLE api_event_logs ALTER COLUMN org_id SET NOT NULL;

-- Replace existing RLS policies to deny NULL org visibility
DROP POLICY IF EXISTS api_request_logs_select ON api_request_logs;
CREATE POLICY api_request_logs_select ON api_request_logs
  FOR SELECT USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

DROP POLICY IF EXISTS api_event_logs_select ON api_event_logs;
CREATE POLICY api_event_logs_select ON api_event_logs
  FOR SELECT USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

COMMIT;
-- File: arioncomply-v1/db/migrations/0012_logging_tighten_org_policies.sql
