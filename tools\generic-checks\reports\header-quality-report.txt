Header Quality Report
=====================
Total files scanned: 761
Files with header issues: 117
Files with function doc issues: 6

Header issues (first 100):
  - addpidignore.sh: missing File: header; missing File Description; missing Purpose; shell header lacks Usage/Env/Purpose
  - .github/workflows/plantuml-render.yml: yaml missing header or companion README
  - .github/workflows/quality.yml: yaml missing header or companion README
  - arioncomply-v1/tools/export/generate_reports.py: missing File: header; missing File Description; missing Purpose
  - arioncomply-v1/tools/config/config.toml: missing File: header; missing File Description; missing Purpose
  - arioncomply-v1/create_test_structure.sh: missing File: header; missing File Description; missing Purpose; shell header lacks Usage/Env/Purpose
  - arioncomply-v1/update_phases_correct.py: missing File: header; missing File Description; missing Purpose
  - arioncomply-v1/tools/analyze/build_vocab.py: missing File: header; missing File Description; missing Purpose
  - arioncomply-v1/manage_docker_colima.sh: missing File: header; missing File Description; missing Purpose; shell header lacks Usage/Env/Purpose
  - arioncomply-v1/tools/preprocess/convert_yaml_to_toml.py: missing File: header; missing File Description; missing Purpose
  - arioncomply-v1/tools/preprocess/validate_front_matter.py: missing File: header; missing File Description; missing Purpose
  - arioncomply-v1/tools/preprocess/normalize_markdown.py: missing File: header; missing File Description; missing Purpose
  - arioncomply-v1/deploy.sh: missing File: header; missing File Description; missing Purpose; shell header lacks Usage/Env/Purpose
  - arioncomply-v1/supabase/config.toml: missing File Description; missing Purpose
  - arioncomply-v1/testing/workflow-gui/start-server.sh: missing File Description
  - arioncomply-v1/testing/workflow-gui/stop-server.sh: missing File Description
  - arioncomply-v1/operational-management/ingestion-pipeline-gui/styles/main.css: missing File: header; missing File Description; missing Purpose
  - arioncomply-v1/testing/llm-comparison/test-extraction.sh: missing File Description; missing Purpose; shell header lacks Usage/Env/Purpose
  - arioncomply-v1/testing/llm-comparison/deploy.sh: missing File Description; missing Purpose; shell header lacks Usage/Env/Purpose
  - arioncomply-v1/testing/llm-comparison/style.css: missing File Description; missing Purpose
  - arioncomply-v1/operational-management/dual-vector-monitoring-gui/index.html: missing File: header; missing File Description; missing Purpose
  - arioncomply-v1/operational-management/ingestion-pipeline-gui/index.html: missing File: header; missing File Description; missing Purpose
  - arioncomply-v1/operational-management/ingestion-pipeline-gui/js/api.js: missing File: header; missing File Description; missing Purpose
  - arioncomply-v1/operational-management/ingestion-pipeline-gui/js/utils.js: missing File: header; missing File Description; missing Purpose
  - arioncomply-v1/testing/llm-comparison/scripts/deploy.sh: missing File Description; missing Purpose; shell header lacks Usage/Env/Purpose
  - arioncomply-v1/Mockup/treeView.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/workflowStepEditor.js: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/workflowStepEdit.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/voice-guidance.js: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/userManagement.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/chartView.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/gllm-return-forma.js: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/fileManager.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/timelineViewEnhacements.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/gllm-query-engine.js: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/query-response-test-module.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/sidebar-component.js: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/workflowModel.js: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/settingsPanel.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/storage-test-page.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/wizzard.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/workflowEditor.js: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/timelineView.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/relationshipMapper.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/kanbanBoard.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/wizard.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/scripts.js: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/reportBuilder.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/workflowStepEdit.js: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/formBuilder.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/userProfile.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/prototypeIndex.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/seedData.js: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/workflowList.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/dashboard.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/splash.html: missing File Description; missing Purpose
  - arioncomply-v1/testing/llm-comparison/supabase/config.toml: missing File Description; missing Purpose
  - arioncomply-v1/ai-backend/python-backend/services/logging/__init__.py: missing Purpose
  - arioncomply-v1/supabase/functions/_shared/diagnostics.ts: missing File Description
  - arioncomply-v1/testing/llm-comparison/supabase/functions/compliance-proxy/index.ts: missing File Description; missing Purpose
  - arioncomply-v1/testing/llm-comparison/supabase/functions/_shared/cors.ts: missing File Description; missing Purpose
  - arioncomply-v1/ai-backend/python-backend/services/preprocessing/__init__.py: missing Purpose
  - arioncomply-v1/docs/API/openapi-conversation.yaml: yaml missing header or companion README
  - arioncomply-v1/supabase/schema/20250822170900_application_schema_update.sql: missing File: header; missing File Description; missing Purpose; sql header lacks Migration/Purpose/Security/RLS notes
  - arioncomply-v1/supabase/seed.sql: missing File Description; missing Purpose; sql header lacks Migration/Purpose/Security/RLS notes
  - arioncomply-v1/testing/llm-comparison/web-interface/index.html: missing File Description; missing Purpose
  - arioncomply-v1/testing/llm-comparison/start-server.sh: missing File Description
  - arioncomply-v1/ai-backend/templates/standards/ai_act/risk_levels.yaml: yaml missing header or companion README
  - arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js: missing File: header; missing File Description; missing Purpose
  - arioncomply-v1/Mockup/questionLoader.js: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/calendarView.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/layout-manager.js: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/listView-logic.js: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/chatInterface.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/timelineEnhancedFeatures.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/gllm-return-format.js: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/listView-content-config.js: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/notificationCenter.html: missing File Description; missing Purpose
  - arioncomply-v1/Mockup/documentEditor.html: missing File Description; missing Purpose
  - arioncomply-v1/db/migrations/0003_conversation_sessions_messages.sql: missing File Description
  - arioncomply-v1/ai-backend/python-backend/services/__init__.py: missing Purpose
  - arioncomply-v1/testing/llm-comparison/index.html: missing Purpose
  - arioncomply-v1/testing/llm-comparison/stop-server.sh: missing File Description
  - arioncomply-v1/db/migrations/0005_visualization_mapping_min.sql: missing File Description
  - arioncomply-v1/db/migrations/0015_audit_read_view.sql: missing File Description
  - arioncomply-v1/db/migrations/0014_logging_add_session_trace.sql: missing File Description
  - arioncomply-v1/db/migrations/0015_conversation_message_feedback.sql: missing File Description
  - arioncomply-v1/db/migrations/0007_document_management_min.sql: missing File Description
  - arioncomply-v1/db/migrations/0013_assessment_system.sql: missing File Description
  - arioncomply-v1/db/migrations/0011_application_event_logging.sql: missing File Description
  - arioncomply-v1/db/migrations/0009_access_helpers.sql: missing File Description
  - arioncomply-v1/db/migrations/0016_ui_shortcuts.sql: missing File Description
  - arioncomply-v1/db/migrations/0001_base_extensions_and_context.sql: missing File Description
  - arioncomply-v1/db/migrations/0002_org_and_profiles.sql: missing File Description
  - arioncomply-v1/db/migrations/0012_logging_tighten_org_policies.sql: missing File Description
  - arioncomply-v1/db/migrations/0006_subscription_and_rbac_min.sql: missing File Description
  - arioncomply-v1/db/migrations/0008_subscription_rbac_seed.sql: missing File Description
  - arioncomply-v1/db/migrations/0004_questionnaire_min.sql: missing File Description
  - arioncomply-v1/ai-backend/supabase_migrations/schemas/multi_dimensional_embeddings_migration.sql: missing File Description
  - arioncomply-v1/Mockup/routing.html: missing File Description; missing Purpose

Function doc issues (first 100):
  - tools/validation/check-env.py:
      * missing docstring: def looks_like_placeholder()
      * missing docstring: def main()
  - tools/checks/check-header-paths.py:
      * missing docstring: def _in_scope()
  - arioncomply-v1/update_phases_correct.py:
      * missing docstring: def add_phase_to_requirement()
  - tools/generic-checks/check-header-quality.py:
      * missing docstring: def load_policy()
      * missing docstring: def repo_root()
      * missing docstring: def list_files()
      * missing docstring: def within_scope()
      * missing docstring: def read_head()
  - tools/generic-checks/check-header-paths.py:
      * missing docstring: def repo_root()
      * missing docstring: def list_files()
      * missing docstring: def within_scope()
      * missing docstring: def find_header_path()
      * missing docstring: def main()
  - arioncomply-v1/ai-backend/python-backend/services/retrieval/vector_profiles.py:
      * missing docstring: def __post_init__()
      * missing docstring: def replace_env_var()