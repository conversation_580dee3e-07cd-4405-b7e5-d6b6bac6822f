<!-- File: arioncomply-v1/Mockup/Agents.md -->
# AGENTS.md

## 🧭 Project Scope

- **Single repo**, front-end only (HTML, CSS, JavaScript)
- No backend scripts or serverless functions included

## 🛠️ Tech Stack

- HTML5, modern CSS (Flexbox/Grid), ES2021+ JavaScript
- No frameworks—vanilla JS

## 🎨 Coding Conventions

- Format JS with: `prettier --write .`
- Lint using: `eslint . --fix`
- Naming: `camelCase` for variables/functions, `PascalCase` for any classes
- Constants: `UPPER_SNAKE_CASE`
- Use CSS classes—no inline styles
- Document public functions with JSDoc

## ✅ Testing Protocol

```bash
npm test
```
<!-- File: arioncomply-v1/Mockup/Agents.md -->
