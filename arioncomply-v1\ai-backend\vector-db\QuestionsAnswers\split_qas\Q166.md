id: Q166
query: >-
  What safeguards must we apply before sending data abroad for business purposes?
packs:
  - "GDPR:2016"
primary_ids:
  - "GDPR:2016/Art.44"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Draft Doc"
flags: []
sources:
  - title: "GDPR — General Principle on Transfers"
    id: "GDPR:2016/Art.44"
    locator: "Article 44"
ui:
  cards_hint:
    - "Transfer safeguard checklist"
  actions:
    - type: "start_workflow"
      target: "transfer_assessment"
      label: "Assess Transfer"
    - type: "open_register"
      target: "transfer_register"
      label: "View Transfers"
output_mode: "both"
graph_required: false
notes: "Choose adequacy, SCCs, BCRs, or a specific derogation"
---
### 166) What safeguards must we apply before sending data abroad for business purposes?

**Standard terms)**  
- **Appropriate safeguards (GDPR Art. 44):** legal tools ensuring GDPR-level protection.

**Plain-English answer**  
Pick one safeguard **before** any transfer:  
1. **Adequacy decision** (best).  
2. **SCCs** (most common).  
3. **Binding Corporate Rules** (for group transfers).  
4. **Specific derogation** (rare, e.g., explicit consent).

**Applies to**  
- **Primary:** GDPR Article 44

**Why it matters**  
Transfers without safeguards are unlawful and trigger hefty fines.

**Do next in our platform**  
- Run **Transfer Assessment** workflow to select and document the safeguard.  
- Store contracts or decisions in the **Transfer Register**.

**How our platform will help**  
- **[Workflow]** Decision tree & template generator.  
- **[Draft Doc]** Produces SCCs or BCR policies automatically.

**Likely follow-ups**  
- When do we need a **Transfer Impact Assessment**?

**Sources**  
- GDPR Article 44
