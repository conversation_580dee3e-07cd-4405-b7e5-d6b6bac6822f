-- File: arioncomply-v1/db/migrations/0006_subscription_and_rbac_min.sql
-- Migration 0006: Subscription Management and RBAC (minimum viable)
-- Purpose: Foundation for entitlements, feature gating, and user permissions.
-- Aligns with:
--   - docs/ArionComplyDesign/SubscriptionManagement/unified_subscription_schema.md
--   - Design principles (multi-tenant RLS, audit, JSONB checks)
-- Tables (subset/min viable):
--   - sub_plans: plan catalog (demo/assessment/basic/pro/enterprise)
--   - org_subscriptions: the active plan for an org
--   - roles, permissions: RBAC catalogs
--   - role_permissions: many-to-many mapping
--   - user_roles: assign users to roles within an org
--   - plan_entitlements: map plans to permission codes (feature flags)
-- Usage:
--   - UI and Edge Functions check entitlements and roles to enable/disable features.

BEGIN;

-- Plan catalog
CREATE TABLE IF NOT EXISTS sub_plans (
  plan_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  plan_code text NOT NULL UNIQUE,
  plan_name text NOT NULL,
  plan_description text,
  plan_type text NOT NULL CHECK (plan_type IN ('demo','assessment','basic','professional','enterprise')),
  plan_is_active boolean DEFAULT true,
  plan_is_public boolean DEFAULT true,
  plan_duration_days integer,
  plan_billing_cycle text CHECK (plan_billing_cycle IN ('monthly','quarterly','annual','one_time')),
  plan_price_amount numeric(10,2),
  plan_price_currency char(3) DEFAULT 'USD',
  plan_user_limit integer,
  plan_storage_limit_gb integer,
  plan_configuration jsonb,
  organization_id uuid REFERENCES organizations(org_id) ON DELETE CASCADE,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT chk_sub_plans_config CHECK (plan_configuration IS NULL OR jsonb_typeof(plan_configuration) = 'object')
);

CREATE INDEX IF NOT EXISTS idx_sub_plans_type ON sub_plans (plan_type);
CREATE INDEX IF NOT EXISTS idx_sub_plans_org ON sub_plans (organization_id);
CREATE INDEX IF NOT EXISTS idx_sub_plans_active_public ON sub_plans (plan_is_active, plan_is_public);
CREATE TRIGGER sub_plans_set_updated_at BEFORE UPDATE ON sub_plans FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

ALTER TABLE sub_plans ENABLE ROW LEVEL SECURITY;
CREATE POLICY sub_plans_select ON sub_plans FOR SELECT USING (
  organization_id IS NULL OR organization_id = app_current_org_id() OR app_has_role('admin')
);
CREATE POLICY sub_plans_insert ON sub_plans FOR INSERT WITH CHECK (
  organization_id IS NULL OR organization_id = app_current_org_id() OR app_has_role('admin')
);
CREATE POLICY sub_plans_update ON sub_plans FOR UPDATE USING (
  organization_id IS NULL OR organization_id = app_current_org_id() OR app_has_role('admin')
) WITH CHECK (
  organization_id IS NULL OR organization_id = app_current_org_id() OR app_has_role('admin')
);
CREATE POLICY sub_plans_delete ON sub_plans FOR DELETE USING (
  organization_id IS NULL OR organization_id = app_current_org_id() OR app_has_role('admin')
);

-- Organization subscriptions
CREATE TABLE IF NOT EXISTS org_subscriptions (
  sub_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  sub_org_id uuid NOT NULL REFERENCES organizations(org_id) ON DELETE CASCADE,
  sub_plan_id uuid NOT NULL REFERENCES sub_plans(plan_id) ON DELETE RESTRICT,
  sub_status text NOT NULL CHECK (sub_status IN ('active','expired','canceled','pending','trial','replaced')),
  sub_start_date timestamptz NOT NULL,
  sub_end_date timestamptz,
  sub_auto_renew boolean DEFAULT false,
  sub_billing_reference text,
  sub_canceled_at timestamptz,
  sub_canceled_by uuid,
  sub_cancel_reason text,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid,
  updated_by uuid
);

CREATE UNIQUE INDEX IF NOT EXISTS uq_org_active_subscription ON org_subscriptions (sub_org_id)
  WHERE sub_status = 'active';
CREATE INDEX IF NOT EXISTS idx_org_subscriptions_status ON org_subscriptions (sub_org_id, sub_status);
CREATE TRIGGER org_subscriptions_set_updated_at BEFORE UPDATE ON org_subscriptions FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

ALTER TABLE org_subscriptions ENABLE ROW LEVEL SECURITY;
CREATE POLICY org_subscriptions_select ON org_subscriptions FOR SELECT USING (sub_org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY org_subscriptions_insert ON org_subscriptions FOR INSERT WITH CHECK (sub_org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY org_subscriptions_update ON org_subscriptions FOR UPDATE USING (sub_org_id = app_current_org_id() OR app_has_role('admin')) WITH CHECK (sub_org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY org_subscriptions_delete ON org_subscriptions FOR DELETE USING (sub_org_id = app_current_org_id() OR app_has_role('admin'));

-- RBAC catalogs
CREATE TABLE IF NOT EXISTS roles (
  role_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  role_name text NOT NULL,
  role_description text,
  role_is_system boolean DEFAULT false,
  organization_id uuid REFERENCES organizations(org_id) ON DELETE CASCADE,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid,
  updated_by uuid
);

CREATE UNIQUE INDEX IF NOT EXISTS uq_roles_name_org ON roles (role_name, COALESCE(organization_id, '00000000-0000-0000-0000-000000000000'));
CREATE INDEX IF NOT EXISTS idx_roles_org ON roles (organization_id);
CREATE INDEX IF NOT EXISTS idx_roles_system ON roles (role_is_system);
CREATE TRIGGER roles_set_updated_at BEFORE UPDATE ON roles FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
CREATE POLICY roles_select ON roles FOR SELECT USING (
  role_is_system OR organization_id = app_current_org_id() OR app_has_role('admin')
);
CREATE POLICY roles_cud ON roles FOR ALL USING (
  app_has_role('admin') OR organization_id = app_current_org_id()
) WITH CHECK (
  app_has_role('admin') OR organization_id = app_current_org_id()
);

CREATE TABLE IF NOT EXISTS permissions (
  permission_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  permission_code text NOT NULL UNIQUE,
  permission_description text,
  feature_area text NOT NULL,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid,
  updated_by uuid
);

CREATE INDEX IF NOT EXISTS idx_permissions_area ON permissions (feature_area);
CREATE TRIGGER permissions_set_updated_at BEFORE UPDATE ON permissions FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

-- permissions are system-wide; no RLS to avoid blocking discovery across orgs

CREATE TABLE IF NOT EXISTS role_permissions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  role_id uuid NOT NULL REFERENCES roles(role_id) ON DELETE CASCADE,
  permission_id uuid NOT NULL REFERENCES permissions(permission_id) ON DELETE CASCADE,
  UNIQUE (role_id, permission_id)
);

CREATE INDEX IF NOT EXISTS idx_role_permissions_role ON role_permissions (role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_perm ON role_permissions (permission_id);

-- No tenant data; RLS not required for mapping tables when guarded at API level

CREATE TABLE IF NOT EXISTS user_roles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  org_id uuid NOT NULL REFERENCES organizations(org_id) ON DELETE CASCADE,
  role_id uuid NOT NULL REFERENCES roles(role_id) ON DELETE CASCADE,
  UNIQUE (user_id, org_id, role_id)
);

CREATE INDEX IF NOT EXISTS idx_user_roles_user_org ON user_roles (user_id, org_id);

ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;
CREATE POLICY user_roles_select ON user_roles FOR SELECT USING (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY user_roles_cud ON user_roles FOR ALL USING (org_id = app_current_org_id() OR app_has_role('admin')) WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));

-- Plan entitlements: map plans to permission codes (feature flags)
CREATE TABLE IF NOT EXISTS plan_entitlements (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  plan_id uuid NOT NULL REFERENCES sub_plans(plan_id) ON DELETE CASCADE,
  permission_code text NOT NULL REFERENCES permissions(permission_code) ON DELETE CASCADE,
  UNIQUE (plan_id, permission_code)
);

CREATE INDEX IF NOT EXISTS idx_plan_entitlements_plan ON plan_entitlements (plan_id);

-- No tenant data; plans can be global. Entitlements enforced by API joining org_subscriptions.

COMMIT;
-- File: arioncomply-v1/db/migrations/0006_subscription_and_rbac_min.sql
