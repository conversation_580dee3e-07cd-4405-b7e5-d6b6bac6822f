id: Q224
query: >-
  What’s the process for updating policies and procedures without breaking compliance?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/7.5"
overlap_ids: []
capability_tags:
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Documented Information"
    id: "ISO27001:2022/7.5"
    locator: "Clause 7.5"
ui:
  cards_hint:
    - "Policy change workflow"
  actions:
    - type: "start_workflow"
      target: "policy_update"
      label: "Update Policy"
output_mode: "both"
graph_required: false
notes: "Control versioning, approvals, and effective-date management"
---
### 224) What’s the process for updating policies and procedures without breaking compliance?

**Standard terms)**  
- **Documented information (Cl.7.5):** mandates version control and approval.

**Plain-English answer**  
Use a **Policy Change Workflow**: draft revisions, route for role-based review, capture approvals, maintain version history, and publish with clear effective dates. Communicate changes via training or notices.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 7.5

**Why it matters**  
Ensures clarity on which version is in force and avoids gaps.

**Do next in our platform**  
- Start **Policy Update** workflow.  
- Assign reviewers and set go-live date.

**How our platform will help**  
- **[Workflow]** Enforces review gates and captures electronic signatures.  
- **[Dashboard]** Shows upcoming policy expirations.

**Likely follow-ups**  
- Can we automate review reminders?

**Sources**  
- ISO/IEC 27001:2022 Clause 7.5
