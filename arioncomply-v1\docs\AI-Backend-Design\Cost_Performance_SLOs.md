# Cost, Performance, and SLOs (Placeholder)

Decisions
- Track per-request latency, token usage, and cost.
- Set initial SLOs for chat latency (P50/P95) per phase.

Design Points
- Token/cost estimation per provider; cache hits and retrieval timing breakdown.
- Budgets and alerts; backpressure or fallbacks when breaching SLOs.

Open Questions
- Target SLO values per phase; scaling strategies for SLLM hardware.

Next Steps
- Add latency and usage fields to `ai_call` and request_end metrics.
- Build simple dashboard queries; define alert thresholds.

