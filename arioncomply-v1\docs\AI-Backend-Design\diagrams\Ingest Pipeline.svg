<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentScriptType="application/ecmascript" contentStyleType="text/css" height="517px" preserveAspectRatio="none" style="width:850px;height:517px;" version="1.1" viewBox="0 0 850 517" width="850px" zoomAndPan="magnify"><defs/><g><text fill="#000000" font-family="sans-serif" font-size="18" lengthAdjust="spacingAndGlyphs" textLength="198" x="327.25" y="26.708">Ingest / Index Pipeline</text><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="61" x2="61" y1="117.25" y2="427.5781"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="221.5" x2="221.5" y1="117.25" y2="427.5781"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="348.5" x2="348.5" y1="117.25" y2="427.5781"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="419.5" x2="419.5" y1="117.25" y2="427.5781"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="630.5" x2="630.5" y1="117.25" y2="427.5781"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="777.5" x2="777.5" y1="117.25" y2="427.5781"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="100" x="8" y="113.9482">Edge Function</text><ellipse cx="61" cy="47.9531" fill="#F8F8F8" rx="8" ry="8" style="stroke: #383838; stroke-width: 2.0;"/><path d="M61,55.9531 L61,82.9531 M48,63.9531 L74,63.9531 M61,82.9531 L48,97.9531 M61,82.9531 L74,97.9531 " fill="none" style="stroke: #383838; stroke-width: 2.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="100" x="8" y="439.5732">Edge Function</text><ellipse cx="61" cy="452.875" fill="#F8F8F8" rx="8" ry="8" style="stroke: #383838; stroke-width: 2.0;"/><path d="M61,460.875 L61,487.875 M48,468.875 L74,468.875 M61,487.875 L48,502.875 M61,487.875 L74,502.875 " fill="none" style="stroke: #383838; stroke-width: 2.0;"/><rect fill="#F8F8F8" height="46.5938" style="stroke: #383838; stroke-width: 1.5;" width="99" x="172.5" y="69.6563"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="85" x="179.5" y="89.6514">API (FastAPI)</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="48" x="198" y="105.9482">/ingest</text><rect fill="#F8F8F8" height="46.5938" style="stroke: #383838; stroke-width: 1.5;" width="99" x="172.5" y="426.5781"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="85" x="179.5" y="446.5732">API (FastAPI)</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="48" x="198" y="462.8701">/ingest</text><path d="M324.5,90.9531 L373.5,90.9531 C378.5,90.9531 378.5,104.1016 378.5,104.1016 C378.5,104.1016 378.5,117.25 373.5,117.25 L324.5,117.25 C319.5,117.25 319.5,104.1016 319.5,104.1016 C319.5,104.1016 319.5,90.9531 324.5,90.9531 " fill="#F8F8F8" style="stroke: #383838; stroke-width: 2.0;"/><path d="M373.5,90.9531 C368.5,90.9531 368.5,104.1016 368.5,104.1016 C368.5,117.25 373.5,117.25 373.5,117.25 " fill="none" style="stroke: #383838; stroke-width: 2.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="39" x="324.5" y="108.9482">Redis</text><path d="M324.5,426.5781 L373.5,426.5781 C378.5,426.5781 378.5,439.7266 378.5,439.7266 C378.5,439.7266 378.5,452.875 373.5,452.875 L324.5,452.875 C319.5,452.875 319.5,439.7266 319.5,439.7266 C319.5,439.7266 319.5,426.5781 324.5,426.5781 " fill="#F8F8F8" style="stroke: #383838; stroke-width: 2.0;"/><path d="M373.5,426.5781 C368.5,426.5781 368.5,439.7266 368.5,439.7266 C368.5,452.875 373.5,452.875 373.5,452.875 " fill="none" style="stroke: #383838; stroke-width: 2.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="39" x="324.5" y="444.5732">Redis</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="63" x="388.5" y="85.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="49" x="395.5" y="105.9482">Worker</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="63" x="388.5" y="426.5781"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="49" x="395.5" y="446.5732">Worker</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="135" x="560.5" y="97.6514">Supabase Postgres</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="71" x="592.5" y="113.9482">(pgvector)</text><path d="M613,48.6563 C613,38.6563 631,38.6563 631,38.6563 C631,38.6563 649,38.6563 649,48.6563 L649,74.6563 C649,84.6563 631,84.6563 631,84.6563 C631,84.6563 613,84.6563 613,74.6563 L613,48.6563 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M613,48.6563 C613,58.6563 631,58.6563 631,58.6563 C631,58.6563 649,58.6563 649,48.6563 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="135" x="560.5" y="439.5732">Supabase Postgres</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="71" x="592.5" y="455.8701">(pgvector)</text><path d="M613,469.1719 C613,459.1719 631,459.1719 631,459.1719 C631,459.1719 649,459.1719 649,469.1719 L649,495.1719 C649,505.1719 631,505.1719 631,505.1719 C631,505.1719 613,505.1719 613,495.1719 L613,469.1719 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M613,469.1719 C613,479.1719 631,479.1719 631,479.1719 C631,479.1719 649,479.1719 649,469.1719 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="127" x="711.5" y="113.9482">Supabase Storage</text><ellipse cx="778" cy="84.9531" fill="#F8F8F8" rx="12" ry="12" style="stroke: #383838; stroke-width: 2.0;"/><line style="stroke: #383838; stroke-width: 2.0;" x1="766" x2="790" y1="98.9531" y2="98.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="127" x="711.5" y="439.5732">Supabase Storage</text><ellipse cx="778" cy="458.875" fill="#F8F8F8" rx="12" ry="12" style="stroke: #383838; stroke-width: 2.0;"/><line style="stroke: #383838; stroke-width: 2.0;" x1="766" x2="790" y1="472.875" y2="472.875"/><polygon fill="#383838" points="210,144.3828,220,148.3828,210,152.3828,214,148.3828" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="61" x2="216" y1="148.3828" y2="148.3828"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="137" x="68" y="143.3169">POST ingest(payload)</text><polygon fill="#383838" points="337,173.5156,347,177.5156,337,181.5156,341,177.5156" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="222" x2="343" y1="177.5156" y2="177.5156"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="103" x="229" y="172.4497">enqueue(job_id)</text><polygon fill="#383838" points="408,202.6484,418,206.6484,408,210.6484,412,206.6484" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="349" x2="414" y1="206.6484" y2="206.6484"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="19" x="356" y="201.5825">job</text><polygon fill="#383838" points="766,231.7813,776,235.7813,766,239.7813,770,235.7813" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="420" x2="772" y1="235.7813" y2="235.7813"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="103" x="427" y="230.7153">download object</text><line style="stroke: #383838; stroke-width: 1.0;" x1="420" x2="462" y1="264.9141" y2="264.9141"/><line style="stroke: #383838; stroke-width: 1.0;" x1="462" x2="462" y1="264.9141" y2="277.9141"/><line style="stroke: #383838; stroke-width: 1.0;" x1="421" x2="462" y1="277.9141" y2="277.9141"/><polygon fill="#383838" points="431,273.9141,421,277.9141,431,281.9141,427,277.9141" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="197" x="427" y="259.8481">convert -&gt; normalize -&gt; chunk</text><polygon fill="#383838" points="233,318.1797,223,322.1797,233,326.1797,229,322.1797" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="227" x2="419" y1="322.1797" y2="322.1797"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="146" x="239" y="301.981">embed(chunks) [HTTP]</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="141" x="239" y="317.1138">...or local embeddings</text><polygon fill="#383838" points="619,347.3125,629,351.3125,619,355.3125,623,351.3125" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="420" x2="625" y1="351.3125" y2="351.3125"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="174" x="427" y="346.2466">upsert vectors + metadata</text><polygon fill="#383838" points="233,376.4453,223,380.4453,233,384.4453,229,380.4453" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="227" x2="419" y1="380.4453" y2="380.4453"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="89" x="239" y="375.3794">status update</text><polygon fill="#383838" points="72,405.5781,62,409.5781,72,413.5781,68,409.5781" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="66" x2="221" y1="409.5781" y2="409.5781"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="88" x="78" y="404.5122">202 Accepted</text><!--MD5=[3c08d7d5b9cad68c0c2ba36fc88c2710]
@startuml Ingest Pipeline
title Ingest / Index Pipeline
skinparam shadowing false
skinparam monochrome true

actor "Edge Function" as Edge
participant "API (FastAPI)\n/ingest" as API
queue Redis
participant "Worker" as W
database "Supabase Postgres\n(pgvector)" as PG
entity "Supabase Storage" as ST

Edge -> API : POST ingest(payload)
API -> Redis : enqueue(job_id)
Redis -> W : job
W -> ST : download object
W -> W : convert -> normalize -> chunk
W -> API : embed(chunks) [HTTP] \n...or local embeddings
W -> PG : upsert vectors + metadata
W -> API : status update
API -> Edge : 202 Accepted

@enduml

PlantUML version 1.2020.02(Sun Mar 01 10:22:07 UTC 2020)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Java Version: 17.0.16+8
Operating System: Linux
Default Encoding: UTF-8
Language: en
Country: null
--></g></svg>