@startuml RAG Generate (LLM2.0)
title RAG Generate Flow with LLM 2.0 Preprocessing
skinparam shadowing false
skinparam monochrome true

actor UI
participant Edge
participant "API /generate" as API
queue Redis
participant Worker as W
collections "Preproc (LLM 2.0)\nnormalize • intent • entities • rules • redact" as PRE
database "Chroma (optional)" as Chroma
database "Postgres/pgvector" as PG
participant "Prompt Composer" as PC
participant "Provider Router" as PR
participant "Local LLM (/v1)" as LLM
participant "Cloud LLM (fallback)" as GLLM
participant "Verifier / HITL" as HITL

UI -> Edge : POST conversation.send
Edge -> API : forward (auth, ids, headers)
API -> Redis : enqueue(job)
Redis -> W : job

W -> PRE : normalize, classify intent,\nextract entities, apply rules, redact PII
PRE --> W : {intent, entities, filters, redactions}

W -> W : decide path (rules-only vs RAG)

W -> Chroma : query (filters) [if present]
W -> PG : query (filters) [fallback]
W -> W : select top-k, build citations

W -> PC : compile prompt\n(instructions + context + controls)
PC --> W : prompt

W -> PR : select provider (local first; guard rails)
PR -> LLM : /v1/chat/completions
LLM --> PR : stream tokens
PR --> W : stream

note over PR
  on guard/fail → anonymized fallback
end note

PR -> GLLM : chat (anonymized)
GLLM --> PR : stream
PR --> W : stream

W -> HITL : optional review/checks
HITL --> W : approve / edits

W -> API : progress/logs + result\n(citations, model, usage)
API -> Edge : stream/proxy result
Edge -> UI : response

@enduml
