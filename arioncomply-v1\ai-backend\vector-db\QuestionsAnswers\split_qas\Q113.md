id: Q113
query: >-
  How do we handle vendor changes after we've already contracted?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/A.15.2.2"
  - "GDPR:2016/Art.28"
overlap_ids: []
capability_tags:
  - "Tracker"
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Monitoring Supplier Services"
    id: "ISO27001:2022/A.15.2.2"
    locator: "Annex A.15.2.2"
  - title: "GDPR — Processor Contracts"
    id: "GDPR:2016/Art.28"
    locator: "Article 28"
ui:
  cards_hint:
    - "Vendor change log"
  actions:
    - type: "start_workflow"
      target: "vendor_change_mgmt"
      label: "Manage Vendor Changes"
    - type: "open_register"
      target: "vendor_compliance"
      label: "View Vendor Register"
output_mode: "both"
graph_required: false
notes: "Update DPAs, reassess risk, and re-audit as needed"
---
### 113) How do we handle vendor changes after we've already contracted?

**Standard terms**  
- **Monitoring supplier services (A.15.2.2):** track ongoing vendor performance.  
- **Processor contracts (GDPR Art. 28):** manage contract amendments.

**Plain-English answer**  
Log all changes—ownership, subprocessors, scope—in a **change log**, update DPAs accordingly, reassess risk, and, if major, trigger re-audit or performance review workflows.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.15.2.2; GDPR Article 28

**Why it matters**  
Maintains continuous compliance and reduces supply-chain risk.

**Do next in our platform**  
- Start **Vendor Change Mgmt** workflow.  
- Monitor updates in **Vendor Compliance** register.

**How our platform will help**  
- **[Workflow]** Change-impact assessment and DPA revision.  
- **[Tracker]** Versioned vendor log.

**Likely follow-ups**  
- “What triggers a full reassessment?” (Material change in processing)

**Sources**  
- ISO/IEC 27001:2022 Annex A.15.2.2; GDPR Article 28
