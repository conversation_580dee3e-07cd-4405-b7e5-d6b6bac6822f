id: Q026
query: >-
  What if we have a data breach before we're fully compliant - are we automatically in violation?
packs:
  - "GDPR:2016"
  - "ISO27001:2022"
primary_ids:
  - "GDPR:2016/Art.33"
  - "GDPR:2016/Art.34"
  - "ISO27001:2022/A.5.24"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Tracker"
  - "Report"
flags:
  - "LOCAL LAW CHECK"
sources:
  - title: "GDPR — Breach Notification"
    id: "GDPR:2016/Art.33"
    locator: "Article 33"
  - title: "GDPR — Communication to Data Subjects"
    id: "GDPR:2016/Art.34"
    locator: "Article 34"
  - title: "ISO/IEC 27001:2022 — Incident Management"
    id: "ISO27001:2022/A.5.24"
    locator: "Annex A.5.24"
ui:
  cards_hint:
    - "Breach response runbook"
  actions:
    - type: "start_workflow"
      target: "breach_response"
      label: "Trigger Breach Runbook"
output_mode: "both"
graph_required: false
notes: "Compliance status doesn’t excuse breach duties"
---
### 26) What if we have a data breach before we're fully compliant – are we automatically in violation?

**Standard terms)**
- **Notification duty (GDPR Art. 33):** report to authority within 72 h.
- **Communication to data subjects (Art. 34):** notify impacted individuals when high risk.
- **Incident management (ISO 27001 A.5.24):** detect, report, and evaluate incidents.

**Plain-English answer**
Even if you haven’t completed your compliance program, breach notification duties kick in immediately. You must contain, assess risk, and notify authorities within 72 h, and inform data subjects if there’s high risk to their rights. Lack of full compliance doesn’t delay this **[LOCAL LAW CHECK]**.

**Applies to**
- **Primary:** GDPR Articles 33; 34; ISO/IEC 27001 Annex A.5.24

**Why it matters**
Breach duties are independent of certification timelines.

**Do next in our platform**
- Activate **breach runbook**.
- Log timeline and notifications.
- Assign remediation tasks.

**How our platform will help**
- **[Workflow]** Breach response.
- **[Tracker]** Incident logs.
- **[Report]** Notification status.

**Likely follow-ups**
- “Can we extend the 72 h window?” (Only in exceptional cases **[LOCAL LAW CHECK]**)

**Sources**
- GDPR Articles 33; 34; ISO/IEC 27001:2022 Annex A.5.24
