# Stack and Data Flow (Aligned)

Roles
- Edge (Supabase Deno Functions): gateway only. Auth, validation, CORS, ID propagation, request/event logging, forwarding to AI backend. No preprocessing, retrieval, or anonymization.
- AI Backend (Python, local server): input/router pattern; preprocessing (LLM 2.0), hybrid retrieval (BM25 + vector + graph), prompt compile, provider selection (SLLM primary), anonymization only on GLLM fallback, HITL proposal generation.
- Vector DB (separate project): org_id isolation via RLS; accessed by AI backend.

Serverless constraints (Edge)
- Keep runtime light: no heavy libs; no long CPU work; small memory footprint.
- Use short timeouts and fail fast when backend is unavailable (`AI_BACKEND_TIMEOUT_MS`).
- Avoid long-lived connections; stream via backend if needed (Edge can proxy-only).
- Write minimal logs; skip DB calls if env not set.

Sequence (Chat)
1) UI → Edge: POST conversation.send
2) Edge: derive orgId/userId from JWT, log request_start, forward payload + headers to AI backend, await response, log response_sent and request_end.
3) Backend: intent → retrieval (if enabled) → build prompt → provider= SLLM → if inadequate and allowed → anonymize → GLLM → post-process/HITL → respond with text + usage.
4) Edge: return envelope to UI.

Config
- Edge env: `FORWARD_ONLY=true`, `AI_BACKEND_URL=http://localhost:9000/ai/chat`, `SYSTEM_PROMPT` (optional display/testing only).
- Per-request headers (forwarded; acted on by backend): `x-pipeline-mode`, `x-retrieval-backend`, `x-provider-order`, `x-allow-gllm`.

Logging
- Edge: `api_request_logs` and `api_event_logs` lifecycle events. Logging is mandatory; set `LOGGING_REQUIRED=true`.
- Backend: emits `ai_call` (provider, model, token usage, cost, anonymized flag) and must persist (own DB or Edge gateway). Logging is mandatory.
Note: For deployment topology, pipeline sequencing, and up‑to‑date wiring between Edge → API → workers → vector stores → LLMs, see `AI-Backend-Design/AI_Backend_Deployment.md` (source of truth). That document embeds PlantUML diagrams for quick visualization in GitHub.
