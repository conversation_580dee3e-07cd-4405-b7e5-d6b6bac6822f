{"embedding_pipelines": {"version": "1.0.0", "config_description": "Multi-pipeline embedding configuration for ArionComply", "default_pipeline": "bge-large-onnx", "selection_strategy": "quality_first", "auto_fallback": true, "health_check_interval_seconds": 300, "max_consecutive_failures": 3, "fallback_order": ["bge-large-onnx", "all-mpnet-base-v2", "placeholder"], "pipelines": {"bge-large-onnx": {"class_path": "services.embedding.pipelines.bge_onnx_pipeline.BGEOnnxPipeline", "enabled": true, "priority": 100, "description": "Primary high-quality pipeline with CPU optimization", "config": {"cache_dir": "~/llms/models/bge", "quantization": "int8", "device": "cpu", "max_length": 512, "model_name": "BAAI/bge-large-en-v1.5"}, "metadata": {"quality_tier": "sota", "dimension": 1024, "is_local": true, "estimated_memory_mb": 350, "estimated_inference_ms": 30, "use_cases": ["production", "compliance", "high_quality"]}}, "all-mpnet-base-v2": {"class_path": "services.embedding.pipelines.mpnet_pipeline.MPNetPipeline", "enabled": true, "priority": 200, "description": "Reliable local embeddings for fallback", "config": {"cache_dir": "~/.cache/sentence-transformers", "device": "cpu", "batch_size": 32, "model_name": "sentence-transformers/all-mpnet-base-v2"}, "metadata": {"quality_tier": "good", "dimension": 768, "is_local": true, "estimated_memory_mb": 400, "estimated_inference_ms": 75, "use_cases": ["development", "fallback", "ci_cd"]}}, "openai": {"class_path": "services.embedding.pipelines.openai_pipeline.OpenAIPipeline", "enabled": false, "priority": 150, "description": "⚠️ Cloud-based high-quality embeddings (data exposure risk)", "config": {"model_name": "text-embedding-3-small", "security_approved": false, "explicit_approval_required": true, "max_retries": 3, "request_timeout": 30, "max_rpm": 3000, "max_tpm": 1000000}, "metadata": {"quality_tier": "high", "dimension": 1536, "is_local": false, "estimated_memory_mb": 0, "estimated_inference_ms": 300, "security_warning": "⚠️ Data sent to external servers", "use_cases": ["quality_benchmarking", "client_approved_only"], "cost_per_1m_tokens": 0.02}}, "openai-large": {"class_path": "services.embedding.pipelines.openai_pipeline.OpenAIPipeline", "enabled": false, "priority": 140, "description": "⚠️ Highest quality cloud embeddings (data exposure risk)", "config": {"model_name": "text-embedding-3-large", "security_approved": false, "explicit_approval_required": true, "max_retries": 3, "request_timeout": 30, "max_rpm": 3000, "max_tpm": 1000000}, "metadata": {"quality_tier": "high", "dimension": 3072, "is_local": false, "estimated_memory_mb": 0, "estimated_inference_ms": 350, "security_warning": "⚠️ Data sent to external servers", "use_cases": ["maximum_quality_required", "research"], "cost_per_1m_tokens": 0.13}}, "placeholder": {"class_path": "services.embedding.pipelines.placeholder_pipeline.PlaceholderPipeline", "enabled": true, "priority": 300, "description": "⚠️ Testing and fallback only - NO SEMANTIC UNDERSTANDING", "config": {}, "metadata": {"quality_tier": "testing_only", "dimension": 768, "is_local": true, "estimated_memory_mb": 0, "estimated_inference_ms": 2, "warning": "NO SEMANTIC UNDERSTANDING - Testing only!", "use_cases": ["testing", "development", "system_fallback"]}}}, "selection_strategies": {"quality_first": {"description": "Prioritize embedding quality for accurate results", "priority_order": ["bge-large-onnx", "all-mpnet-base-v2", "placeholder"], "exclude_pipelines": ["openai", "openai-large"]}, "security_first": {"description": "Only use local pipelines for maximum security", "priority_order": ["bge-large-onnx", "all-mpnet-base-v2", "placeholder"], "exclude_pipelines": ["openai", "openai-large"]}, "performance_first": {"description": "Prioritize speed over quality", "priority_order": ["placeholder", "bge-large-onnx", "all-mpnet-base-v2", "openai"]}, "cloud_enabled": {"description": "Include cloud services when security permits", "priority_order": ["bge-large-onnx", "openai", "all-mpnet-base-v2", "placeholder"]}}, "deployment_profiles": {"development": {"description": "Fast iteration for development", "default_pipeline": "all-mpnet-base-v2", "selection_strategy": "performance_first", "enabled_pipelines": ["all-mpnet-base-v2", "placeholder"], "health_check_interval_seconds": 600}, "production": {"description": "High quality for production deployment", "default_pipeline": "bge-large-onnx", "selection_strategy": "quality_first", "enabled_pipelines": ["bge-large-onnx", "all-mpnet-base-v2", "placeholder"], "health_check_interval_seconds": 300, "auto_fallback": true}, "security_strict": {"description": "Local-only for maximum security", "default_pipeline": "bge-large-onnx", "selection_strategy": "security_first", "enabled_pipelines": ["bge-large-onnx", "all-mpnet-base-v2", "placeholder"], "cloud_pipelines_allowed": false}, "cloud_enabled": {"description": "Include cloud services for maximum quality", "default_pipeline": "bge-large-onnx", "selection_strategy": "cloud_enabled", "enabled_pipelines": ["bge-large-onnx", "openai", "all-mpnet-base-v2", "placeholder"], "cloud_pipelines_allowed": true, "security_warnings_enabled": true}}, "monitoring": {"performance_tracking": {"track_latency": true, "track_success_rate": true, "track_error_rate": true, "track_memory_usage": true, "track_costs": true}, "alerting": {"pipeline_failure_threshold": 3, "latency_threshold_ms": 1000, "success_rate_threshold": 0.95, "memory_usage_threshold_mb": 1000}, "logging": {"log_embedding_operations": true, "log_pipeline_switches": true, "log_health_checks": true, "log_performance_metrics": true, "include_trace_ids": true}}}}