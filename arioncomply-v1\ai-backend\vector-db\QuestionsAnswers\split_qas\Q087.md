id: Q087
query: >-
  How do we handle compliance across multiple locations or remote work?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/4.3"
  - "ISO27001:2022/5.3"
overlap_ids:
  - "ISO27701:2019/4.3"
capability_tags:
  - "Workflow"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — <PERSON><PERSON> (Clause 4.3)"
    id: "ISO27001:2022/4.3"
    locator: "Clause 4.3"
  - title: "ISO/IEC 27001:2022 — Organizational Roles"
    id: "ISO27001:2022/5.3"
    locator: "Clause 5.3"
  - title: "ISO/IEC 27701:2019 — PIMS Context"
    id: "ISO27701:2019/4.3"
    locator: "Clause 4.3"
ui:
  cards_hint:
    - "Multi-site compliance planner"
  actions:
    - type: "start_workflow"
      target: "multi_location_setup"
      label: "Configure Sites"
    - type: "open_register"
      target: "site_matrix"
      label: "View Site Matrix"
output_mode: "both"
graph_required: false
notes: "Use centralized policies with local registers and controls as needed"
---
### 87) How do we handle compliance across multiple locations or remote work?

**Standard terms**  
- **<PERSON><PERSON> (Cl. 4.3):** define ISMS boundaries including locations.  
- **Organizational roles (Cl. 5.3):** clarify responsibilities per site.  
- **PIMS context (ISO 27701 Cl. 4.3):** extends scope to privacy.

**Plain-English answer**  
Define a **central ISMS policy** and tailor site-specific registers. Use a **site matrix** to track local risks, controls, and contacts. Remote workers follow the same controls via virtual checklists and approvals.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clauses 4.3; 5.3  
- **Also relevant/Overlaps:** ISO/IEC 27701:2019 Clause 4.3

**Why it matters**  
Consistent yet flexible approach ensures coverage without duplication.

**Do next in our platform**  
- Run **Multi-Location Setup** workflow.  
- Populate **Site Matrix** with key details.

**How our platform will help**  
- **[Workflow]** Site configuration wizard.  
- **[Register]** Centralized site & risk matrix.

**Likely follow-ups**  
- “How to audit remote employees?” (Use virtual evidence collection)

**Sources**  
- ISO/IEC 27001:2022 Clause 4.3  
- ISO/IEC 27001:2022 Clause 5.3  
- ISO/IEC 27701:2019 Clause 4.3