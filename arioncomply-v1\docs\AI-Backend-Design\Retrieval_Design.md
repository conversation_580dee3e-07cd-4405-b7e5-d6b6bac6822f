# Retrieval Design: Hybrid BM25 + Vector + Graph (Placeholder)

Decisions
- Hybrid retrieval for accuracy and robustness: BM25 (text) + vector (semantic) with re-ranking.
- Graph expansion: one or more adjacency “hops” to related entities (requirements, controls, evidence) using relational joins.
- Isolation: All retrieval queries scoped by `org_id` (RLS) in both main and vector projects.

Pipeline
1) Candidate generation
   - BM25: text search in main DB (e.g., `tsvector`/extensions) over normalized text.
   - Vector: top-k from vector project (`pgvector`) with `org_id` filter.
2) Merge + re-rank
   - Score normalization and reciprocal rank fusion; deduplicate by doc/chunk.
3) Graph hops
   - Expand via join tables, e.g., `requirement_documents`, `control_evidence`.
4) Context packing
   - Token-budget aware packing; include citations and provenance.

ChromeDB vs Supabase VectorDB
- Criteria: latency, throughput, cost, RLS support, maintenance burden, ops maturity.
- Interim: proceed with Supabase Vector (separate project); keep adapter abstraction to allow ChromeDB later.

Open Questions
- Re-ranking algorithm choice and weights; learning-to-rank in future?
- Exact adjacency rules for graph expansion per metadata ontology.
- Target latency budgets (P50/P95) per phase.

Next Steps
- Implement a vector client in Edge with `org_id` filters.
- Define minimal BM25 schema/indexes in main DB; add a merge-and-rerank helper.
- Draft adjacency rules from metadata to drive first-hop graph expansion.

