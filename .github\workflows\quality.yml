name: Quality Checks

on:
  pull_request:
    paths:
      - '**/*.py'
      - '**/*.sh'
      - 'tools/checks/**'
      - '**/*.toml'
      - '.github/workflows/quality.yml'
  push:
    branches: [ main, master ]
    paths:
      - '**/*.py'
      - '**/*.sh'
      - 'tools/checks/**'
      - '**/*.toml'
      - '.github/workflows/quality.yml'

jobs:
  checks:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install ripgrep
        run: sudo apt-get update && sudo apt-get install -y ripgrep

      - name: Conflict markers
        run: bash tools/checks/check-conflicts.sh

      - name: Header enforcement
        run: bash tools/checks/check-headers.sh

      - name: Validate TOML
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
      - name: Run TOML check
        run: python3 tools/checks/check-toml.py
# File: .github/workflows/quality.yml
# File Description: CI quality checks for headers and formatting
# Purpose: Run header path + quality checkers and basic lint/build on PRs
