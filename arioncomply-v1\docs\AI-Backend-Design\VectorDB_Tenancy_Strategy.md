# Vector DB Tenancy Strategy (Placeholder)

Decisions
- Start simple: shared vector project with `org_id` and strict RLS.
- Option: dedicated vector project per large customer for isolation/perf/compliance.

Shared Instance Pros/Cons
- Pros: simpler ops, lower cost, shared index infra.
- Cons: noisy neighbor risk, RLS complexity, migration overhead if moving to dedicated.

Dedicated Instance Pros/Cons
- Pros: hard isolation, custom scaling/tuning, data residency options.
- Cons: higher cost, deployment automation needed.

Open Questions
- Project-per-tenant triggers; automated provisioning.
- Encryption-at-rest and key management specifics.

Next Steps
- Implement RLS policies over `org_id` in vector schema.
- Abstract vector client so target can be swapped (Supabase/ChromeDB).
- Define migration path from shared to dedicated.

