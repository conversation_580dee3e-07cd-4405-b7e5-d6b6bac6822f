# File: arioncomply-v1/ai-backend/python-backend/requirements.txt  
# File Description: Python dependencies for ArionComply AI Backend
# Purpose: Define all required packages for FastAPI server, Supabase integration, and local LLM support
# Usage: pip install -r requirements.txt
# Dependencies: Python 3.8+
# Security/RLS: N/A (dependency specification only)
# Notes: Pinned versions for reproducible builds; update as needed for security patches

# FastAPI web framework and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# HTTP client for API calls (LLM endpoints, external services)
httpx==0.25.2
requests==2.31.0

# Supabase client for database operations and logging
supabase==2.3.0

# JSON and data processing
pydantic==2.5.0
python-json-logger==2.0.7

# Environment and configuration management
python-dotenv==1.0.0
pyyaml==6.0.1

# Async support and utilities
asyncio-compat==0.1.2
aiofiles==23.2.1

# Text processing for document ingestion
nltk==3.8.1
tiktoken==0.5.2

# Vector operations for embeddings (if using local embeddings)
numpy==1.24.4
scikit-learn==1.3.2

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1

# Optional: If we add OpenAI compatibility for local LLMs
openai==1.3.9

# CORS support for web requests
python-multipart==0.0.6