# Prompt Management (Placeholder)

Decisions
- Versioned templates with IDs; provider-specific adapters for SLLM vs GLLM.
- Parameterized sections (system, retrieval context, task), with safe variable injection.
- Store prompt_version and template_id in logs for reproducibility.

Structure
- Template registry: JSON or DB table (id, version, purpose, body, provider_overrides).
- Runtime: compile with context blocks, citations, and guardrails.
- Safety: enforce max-context size; scrub PII before GLLM calls.

Open Questions
- Storage location (DB vs file) per phase; editorial workflow for updates.
- A/B testing hooks and evaluation metrics.

Next Steps
- Create registry schema and minimal loader utility in Edge.
- Draft baseline templates for Q&A, summarization, redaction, and suggestion generation.
- Add `prompt_used` detail to `ai_call` events.

