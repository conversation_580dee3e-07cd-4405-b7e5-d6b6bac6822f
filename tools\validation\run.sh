#!/usr/bin/env bash
# File: tools/validation/run.sh
# File Description: Validation pipeline runner for this stack (autodetect + non-destructive)
# Purpose: Run headers, syntax, lint, type, and formatting checks when tools are available

set -euo pipefail

# Args: --full to force full scan; --since <git-ref> override
FULL=0
SINCE_OVERRIDE=""
while [[ $# -gt 0 ]]; do
  case "$1" in
    --full) FULL=1; shift ;;
    --since) SINCE_OVERRIDE="$2"; shift 2 ;;
    *) echo "Unknown arg: $1"; exit 2 ;;
  esac
done

ROOT_DIR="$(git rev-parse --show-toplevel 2>/dev/null || pwd)"
cd "$ROOT_DIR"

green() { printf "\033[32m%s\033[0m" "$1"; }
red()   { printf "\033[31m%s\033[0m" "$1"; }
yellow(){ printf "\033[33m%s\033[0m" "$1"; }

have() { command -v "$1" >/dev/null 2>&1; }
npm_bin() { [ -x "node_modules/.bin/$1" ]; }

STATE_DIR="tools/validation"
STATE_FILE="$STATE_DIR/.state.json"
CHANGED_LIST_FILE="$STATE_DIR/changed-files.txt"

RESULTS=()
record() {
  RESULTS+=("$1|$2")
}

# Track missing tools for a friendly summary
MISSING=""
note_missing() {
  local tool="$1"
  [ -z "$tool" ] && return
  if ! printf '%s\n' "$MISSING" | grep -qx "$tool"; then
    MISSING="${MISSING}${MISSING:+$'\n'}$tool"
  fi
}

run_step() {
  local label="$1"; shift
  echo "\n== $label =="
  if "$@"; then
    echo "$(green OK) $label"
    record "$label" OK
    return 0
  else
    echo "$(red FAIL) $label"
    record "$label" FAIL
    return 1
  fi
}

skip_step() {
  local label="$1"; shift || true
  local reason="$*"
  echo "$(yellow SKIP) $label ($reason)"
  # Heuristics to capture missing tools from reason text
  if [[ "$reason" =~ ([A-Za-z0-9._/-]+)\ not\ installed ]]; then
    note_missing "${BASH_REMATCH[1]}"
  elif [[ "$reason" =~ node_modules/.bin/([A-Za-z0-9_-]+)\ not\ found ]]; then
    note_missing "${BASH_REMATCH[1]}"
  elif [[ "$reason" =~ ([A-Za-z0-9_-]+)\ not\ found ]]; then
    note_missing "${BASH_REMATCH[1]}"
  fi
  record "$label" SKIP
}

any_py_changed() {
  rg -n --hidden -g '!**/node_modules/**' '^' arioncomply-v1/ai-backend/python-backend >/dev/null 2>&1
}

any_ts_files() { rg --files -g 'arioncomply-v1/supabase/functions/**' -g '!**/_shared/**' -g '!**/node_modules/**' | rg -q '\.(ts|tsx)$'; }
any_js_files() { rg --files -g '!**/node_modules/**' | rg -q '\.(js|jsx)$'; }
any_html_css() { rg --files -g '!**/node_modules/**' | rg -q '\.(html|css)$'; }
any_sql() { rg --files arioncomply-v1/db/migrations arioncomply-v1/ai-backend/supabase_migrations 2>/dev/null | rg -q '\.sql$'; }
any_shell() { rg --files -g '!**/node_modules/**' | rg -q '\.sh$'; }
any_yaml() { rg --files -g '!**/node_modules/**' | rg -q '\.(ya?ml)$'; }
any_md() { rg --files -g '!**/node_modules/**' | rg -q '\.(md|mdx)$'; }
any_flutter() { [ -d "arioncomply-v1/frontend-flutter" ]; }
env_files_present() { [ -f ".env" ] || [ -f ".env.example" ]; }

FAIL=0

# Determine changed files since last run
mkdir -p "$STATE_DIR"
HEAD_SHA="$(git rev-parse HEAD 2>/dev/null || echo)"
PREV_SHA=""
if [[ -f "$STATE_FILE" ]]; then
  PREV_SHA="$(sed -n 's/.*"last_git_head"\s*:\s*"\([^"]*\)".*/\1/p' "$STATE_FILE" | head -n1)"
fi

CHANGED_FILES=()
if [[ $FULL -eq 1 ]]; then
  echo "Forcing full validation (--full)"
else
  if [[ -n "$SINCE_OVERRIDE" ]]; then
    mapfile -t CHANGED_FILES < <(git diff --name-only "$SINCE_OVERRIDE"..HEAD || true)
  elif [[ -n "$PREV_SHA" ]]; then
    mapfile -t CHANGED_FILES < <(git diff --name-only "$PREV_SHA"..HEAD || true)
  else
    # First run: no prior state → treat as full
    FULL=1
  fi
fi

if [[ ${#CHANGED_FILES[@]} -gt 0 ]]; then
  printf "%s\n" "${CHANGED_FILES[@]}" > "$CHANGED_LIST_FILE"
  echo "Changed files since last run: ${#CHANGED_FILES[@]} (recorded in $CHANGED_LIST_FILE)"
else
  : > "$CHANGED_LIST_FILE"
fi

# Helpers to select changed files by ext
changed_with_ext() {
  local ext="$1"; awk -v e=".$ext" 'index($0, e)==length($0)-length(e)+1' "$CHANGED_LIST_FILE" 2>/dev/null || true
}
changed_match() {
  local pattern="$1"; rg -n --no-line-number --no-heading "$pattern" "$CHANGED_LIST_FILE" 2>/dev/null | cut -d: -f1 || true
}

ONLY_CHANGED=$(( FULL == 0 && ${#CHANGED_FILES[@]} > 0 ))

# 0) Repo policy: headers (repo-specific and generic)
if [ -f tools/checks/check-header-paths.py ] && [ -f tools/checks/check-header-quality.py ]; then
  if [[ $ONLY_CHANGED -eq 1 ]]; then
    # Fast path: generic checks scoped to changed files
    if [ -f tools/generic-checks/check-header-paths.py ]; then
      # Build includes from changed files
      INCLUDES=()
      while IFS= read -r f; do INCLUDES+=(--include "$f"); done < "$CHANGED_LIST_FILE"
      run_step "Headers (generic paths, changed)" python3 tools/generic-checks/check-header-paths.py "${INCLUDES[@]}" --write-report tools/generic-checks/reports/latest-header-report.txt || true
      run_step "Headers (generic quality, changed)" python3 tools/generic-checks/check-header-quality.py --policy tools/generic-checks/policy.example.yml "${INCLUDES[@]}" --write-report tools/generic-checks/reports/header-quality-report.txt || true
    else
      skip_step "Headers (generic changed)" "generic checkers not present"
    fi
  else
    # Full path: run repo-specific + generic
    run_step "Headers (repo path strict)" python3 tools/checks/check-header-paths.py > tools/checks/latest-header-report.txt || FAIL=1
    run_step "Headers (repo quality)" python3 tools/checks/check-header-quality.py > tools/checks/header-quality-report.txt || FAIL=1
    if [ -f tools/generic-checks/check-header-paths.py ]; then
      run_step "Headers (generic paths)" python3 tools/generic-checks/check-header-paths.py --write-report tools/generic-checks/reports/latest-header-report.txt || true
      run_step "Headers (generic quality)" python3 tools/generic-checks/check-header-quality.py --policy tools/generic-checks/policy.example.yml --write-report tools/generic-checks/reports/header-quality-report.txt || true
    fi
  fi
else
  skip_step "Headers (repo)" "missing repo-specific checkers"
fi

# 1) Python: syntax, lint, types, security
if any_py_changed; then
  if [[ $ONLY_CHANGED -eq 1 ]]; then
    PY_CHANGED=( $(changed_with_ext py) )
    if (( ${#PY_CHANGED[@]} > 0 )); then
      run_step "Python syntax (py_compile changed)" python3 - <<'PY'
import sys, py_compile
files = [l.strip() for l in sys.stdin if l.strip()]
for f in files:
    py_compile.compile(f, doraise=True)
print(f"compiled {len(files)} files")
PY
    else
      skip_step "Python syntax" "no changed .py files"
    fi < "$CHANGED_LIST_FILE" || FAIL=1
    if have ruff && (( ${#PY_CHANGED[@]} > 0 )); then
      run_step "Ruff lint (changed)" ruff check "${PY_CHANGED[@]}" || FAIL=1
    else
      skip_step "Ruff lint (changed)" "ruff not installed or no changed .py"
    fi
    if have mypy && (( ${#PY_CHANGED[@]} > 0 )); then
      run_step "Mypy (changed)" mypy --ignore-missing-imports "${PY_CHANGED[@]}" || FAIL=1
    else
      skip_step "Mypy (changed)" "mypy not installed or no changed .py"
    fi
    if have bandit && (( ${#PY_CHANGED[@]} > 0 )); then
      run_step "Bandit (changed)" bandit -q -lll "${PY_CHANGED[@]}" || true
    else
      skip_step "Bandit (changed)" "bandit not installed or no changed .py"
    fi
  else
    run_step "Python syntax (compileall)" python3 -m compileall -q arioncomply-v1/ai-backend/python-backend || FAIL=1
    if have ruff; then run_step "Ruff lint" ruff check arioncomply-v1/ai-backend/python-backend || FAIL=1; else skip_step "Ruff lint" "ruff not installed"; fi
    if have mypy; then run_step "Mypy type-check" mypy arioncomply-v1/ai-backend/python-backend --ignore-missing-imports || FAIL=1; else skip_step "Mypy" "mypy not installed"; fi
    if have bandit; then run_step "Bandit security scan" bandit -q -r arioncomply-v1/ai-backend/python-backend -lll || true; else skip_step "Bandit" "bandit not installed"; fi
  fi
else
  skip_step "Python checks" "no python backend files detected"
fi

# 2) Deno/TypeScript/JS (Supabase Edge)
if any_ts_files || any_js_files; then
  if have deno; then
    if [[ $ONLY_CHANGED -eq 1 ]]; then
      TS_CHANGED=( $(changed_match '\.(ts|tsx)$') )
      JS_CHANGED=( $(changed_match '\.(js|jsx)$') )
      if (( ${#TS_CHANGED[@]} + ${#JS_CHANGED[@]} > 0 )); then
        run_step "Deno fmt (changed)" deno fmt --check "${TS_CHANGED[@]}" "${JS_CHANGED[@]}" || FAIL=1
        run_step "Deno lint (changed)" deno lint "${TS_CHANGED[@]}" "${JS_CHANGED[@]}" || true
      else
        skip_step "Deno fmt/lint" "no changed ts/js"
      fi
    else
      run_step "Deno fmt (check)" deno fmt --check arioncomply-v1/supabase/functions || FAIL=1
      run_step "Deno lint" deno lint arioncomply-v1/supabase/functions || true
    fi
  else
    skip_step "Deno checks" "deno not installed"
  fi
  if npm_bin tsc; then
    if [[ $ONLY_CHANGED -eq 1 ]]; then
      # project-based; if any TS changed, run project check
      if changed_match '\.(ts|tsx)$' >/dev/null; then
        run_step "tsc --noEmit (changed)" node_modules/.bin/tsc --noEmit || FAIL=1
      else
        skip_step "tsc" "no changed ts/tsx"
      fi
    else
      run_step "tsc --noEmit" node_modules/.bin/tsc --noEmit || FAIL=1
    fi
  else
    skip_step "tsc" "node_modules/.bin/tsc not found"
  fi
  if npm_bin eslint; then
    if [[ $ONLY_CHANGED -eq 1 ]]; then
      ESL_CHANGED=( $(changed_match '\.(ts|tsx|js|jsx)$') )
      if (( ${#ESL_CHANGED[@]} > 0 )); then
        run_step "ESLint (changed)" node_modules/.bin/eslint "${ESL_CHANGED[@]}" || true
      else
        skip_step "ESLint" "no changed ts/js"
      fi
    else
      run_step "ESLint" node_modules/.bin/eslint "arioncomply-v1/supabase/functions/**" || true
    fi
  else
    skip_step "ESLint" "node_modules/.bin/eslint not found"
  fi
  if npm_bin prettier; then
    if [[ $ONLY_CHANGED -eq 1 ]]; then
      PRETTIER_CHANGED=( $(changed_match '\.(ts|tsx|js|jsx|html|css|md|yml|yaml|json)$') )
      if (( ${#PRETTIER_CHANGED[@]} > 0 )); then
        run_step "Prettier (changed)" node_modules/.bin/prettier --check "${PRETTIER_CHANGED[@]}" || true
      else
        skip_step "Prettier" "no changed assets"
      fi
    else
      run_step "Prettier check" node_modules/.bin/prettier --check "**/*.{ts,tsx,js,jsx,html,css,md,yml,yaml,json}" || true
    fi
  else
    skip_step "Prettier" "node_modules/.bin/prettier not found"
  fi
else
  skip_step "TS/JS checks" "no ts/js files detected"
fi

# 3) SQL (migrations)
if any_sql; then
  if have sqlfluff; then
    if [[ $ONLY_CHANGED -eq 1 ]]; then
      SQL_CHANGED=( $(changed_with_ext sql) )
      if (( ${#SQL_CHANGED[@]} > 0 )); then
        run_step "SQLFluff (changed)" sqlfluff lint --dialect postgres "${SQL_CHANGED[@]}" || true
      else
        skip_step "SQLFluff" "no changed .sql"
      fi
    else
      run_step "SQLFluff lint" sqlfluff lint arioncomply-v1/db/migrations arioncomply-v1/ai-backend/supabase_migrations --dialect postgres || true
    fi
  else
    skip_step "SQLFluff" "sqlfluff not installed"
  fi
else
  skip_step "SQL checks" "no sql files detected"
fi

# 4) Shell
if any_shell; then
  if have shellcheck; then
    if [[ $ONLY_CHANGED -eq 1 ]]; then
      SH_CHANGED=( $(changed_with_ext sh) )
      if (( ${#SH_CHANGED[@]} > 0 )); then
        run_step "shellcheck (changed)" shellcheck "${SH_CHANGED[@]}" || true
      else
        skip_step "shellcheck" "no changed .sh"
      fi
    else
      run_step "shellcheck" sh -c 'shellcheck $(rg --files -g "**/*.sh")' || true
    fi
  else
    skip_step "shellcheck" "shellcheck not installed"
  fi
  if have shfmt; then
    if [[ $ONLY_CHANGED -eq 1 ]]; then
      if (( ${#SH_CHANGED[@]} > 0 )); then
        run_step "shfmt (changed)" shfmt -d "${SH_CHANGED[@]}" || true
      else
        skip_step "shfmt" "no changed .sh"
      fi
    else
      run_step "shfmt (diff)" shfmt -d . || true
    fi
  else
    skip_step "shfmt" "shfmt not installed"
  fi
else
  skip_step "Shell checks" "no .sh files detected"
fi

# 5) YAML/JSON/Markdown/HTML/CSS
if any_yaml; then
  if have yamllint; then
    if [[ $ONLY_CHANGED -eq 1 ]]; then
      Y_CHANGED=( $(changed_match '\.(ya?ml)$') )
      if (( ${#Y_CHANGED[@]} > 0 )); then
        run_step "yamllint (changed)" yamllint "${Y_CHANGED[@]}" || true
      else
        skip_step "yamllint" "no changed yaml"
      fi
    else
      run_step "yamllint" yamllint . || true
    fi
  else
    skip_step "yamllint" "yamllint not installed"
  fi
else
  skip_step "YAML checks" "no yaml files detected"
fi

if any_md; then
  if npm_bin markdownlint; then
    if [[ $ONLY_CHANGED -eq 1 ]]; then
      MD_CHANGED=( $(changed_match '\.(md|mdx)$') )
      if (( ${#MD_CHANGED[@]} > 0 )); then
        run_step "markdownlint (changed)" node_modules/.bin/markdownlint "${MD_CHANGED[@]}" || true
      else
        skip_step "markdownlint" "no changed md"
      fi
    else
      run_step "markdownlint" node_modules/.bin/markdownlint "**/*.md" || true
    fi
  else
    skip_step "markdownlint" "node_modules/.bin/markdownlint not found"
  fi
  # Local link checker (no network)
  if [[ $ONLY_CHANGED -eq 1 ]]; then
    if (( ${#MD_CHANGED[@]} > 0 )); then
      run_step "Markdown links (changed)" python3 tools/validation/check-markdown-links.py "${MD_CHANGED[@]}" || FAIL=1
    else
      skip_step "Markdown links" "no changed md"
    fi
  else
    run_step "Markdown links" python3 tools/validation/check-markdown-links.py || FAIL=1
  fi
else
  skip_step "Markdown checks" "no markdown files detected"
fi

if any_html_css; then
  if npm_bin stylelint; then
    if [[ $ONLY_CHANGED -eq 1 ]]; then
      CSS_CHANGED=( $(changed_with_ext css) )
      if (( ${#CSS_CHANGED[@]} > 0 )); then
        run_step "stylelint (changed)" node_modules/.bin/stylelint "${CSS_CHANGED[@]}" || true
      else
        skip_step "stylelint" "no changed css"
      fi
    else
      run_step "stylelint" node_modules/.bin/stylelint "**/*.css" || true
    fi
  else
    skip_step "stylelint" "node_modules/.bin/stylelint not found"
  fi
  # HTMLHint optional
  if npm_bin htmlhint; then
    if [[ $ONLY_CHANGED -eq 1 ]]; then
      HTML_CHANGED=( $(changed_with_ext html) )
      if (( ${#HTML_CHANGED[@]} > 0 )); then
        run_step "htmlhint (changed)" node_modules/.bin/htmlhint "${HTML_CHANGED[@]}" || true
      else
        skip_step "htmlhint" "no changed html"
      fi
    else
      run_step "htmlhint" node_modules/.bin/htmlhint "**/*.html" || true
    fi
  else
    skip_step "htmlhint" "node_modules/.bin/htmlhint not found"
  fi
else
  skip_step "HTML/CSS checks" "no html/css files detected"
fi

# 6) Flutter/Dart (opt-in via VALIDATE_DART=1)
if [[ "${VALIDATE_DART:-0}" == "1" ]]; then
  if any_flutter; then
    if have dart; then
      run_step "dart format (check)" dart format --set-exit-if-changed arioncomply-v1/frontend-flutter || true
      run_step "dart analyze" dart analyze arioncomply-v1/frontend-flutter || true
    else
      skip_step "Dart" "dart not installed"
    fi
  else
    skip_step "Flutter/Dart checks" "frontend-flutter not present"
  fi
else
  skip_step "Flutter/Dart checks" "set VALIDATE_DART=1 to enable"
fi

# 7) GitHub Actions (workflows)
if [ -d .github/workflows ]; then
  if have actionlint; then
    run_step "actionlint" actionlint || true
  else
    skip_step "actionlint" "actionlint not installed"
  fi
fi

# 8) Env files
if env_files_present; then
  if [[ $ONLY_CHANGED -eq 1 ]]; then
    if rg -N --no-line-number -q "^(.env|.env.example)$" "$CHANGED_LIST_FILE"; then
      run_step ".env / .env.example" python3 tools/validation/check-env.py || FAIL=1
    else
      skip_step ".env / .env.example" "unchanged"
    fi
  else
    run_step ".env / .env.example" python3 tools/validation/check-env.py || FAIL=1
  fi
else
  skip_step ".env / .env.example" "files not present"
fi

# 9) Secrets scan (optional)
if have gitleaks; then
  run_step "gitleaks (redacted)" gitleaks detect --redact --no-git --source . || true
else
  skip_step "gitleaks" "gitleaks not installed"
fi

echo "\nSummary"
echo "-------"
for r in "${RESULTS[@]}"; do
  IFS='|' read -r label status <<<"$r"
  printf "%-30s %s\n" "$label" "$status"
done

# Missing tools summary with install suggestions
if [ -n "$MISSING" ]; then
  echo ""
  echo "Missing tools detected (not installed):"
  echo "--------------------------------------"
  printf "%s\n" "$MISSING" | sort -u
  echo ""
  echo "Install suggestions (examples):"
  while IFS= read -r t; do
    case "$t" in
      ruff|mypy|bandit|sqlfluff|yamllint)
        echo "  $t: pipx install $t   # or: pip install $t";;
      deno)
        echo "  deno: brew install deno   # or see https://deno.land";;
      shellcheck|shfmt|actionlint|gitleaks)
        echo "  $t: brew install $t";;
      eslint|prettier|stylelint|markdownlint|htmlhint|tsc)
        echo "  $t: npm i -D $t   # ensure node_modules/.bin/$t is present";;
      *)
        echo "  $t: install with your package manager";;
    esac
  done <<< "$(printf '%s\n' "$MISSING" | sort -u)"
fi

# Persist state after run
ts=$(date +%s)
cat > "$STATE_FILE" <<JSON
{ "last_run_ts": $ts, "last_git_head": "${HEAD_SHA}" }
JSON

exit "$FAIL"
