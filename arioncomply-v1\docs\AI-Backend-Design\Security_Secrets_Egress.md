# Security, Secrets, and Egress (Placeholder)

Decisions
- Secrets managed via Supabase environment config; never logged.
- Egress allowlist to approved provider endpoints and vector project.

Design Points
- Least privilege keys: separate service-role vs runtime keys.
- Outbound request signing and per-tenant scoping where possible.

Open Questions
- Key rotation cadence and storage; break-glass procedures.

Next Steps
- Document required secrets and scopes per environment.
- Add egress restrictions and validation in Edge where feasible.

