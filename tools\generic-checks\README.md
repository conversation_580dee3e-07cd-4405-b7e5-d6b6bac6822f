File: tools/generic-checks/README.md
File Description: Generic, repo-agnostic header and docs checkers
Purpose: Provide configurable checks you can copy into any repository

Overview
- check-header-paths.py: Validates that files include a top-of-file "File: <path>" header matching the repo-relative path.
- check-header-quality.py: Validates presence of Description/Purpose headers and optional function docs (Python docstrings, JS/TS JSDoc), with JSON/YAML/MD policies.

Key Features
- Configurable via CLI flags, environment variables, or an optional policy YAML.
- No repo-specific paths by default; can scope with include/exclude globs.
- Falls back gracefully if optional dependencies (rg, PyYAML) are unavailable.

Quick Start
1) Run both checks and print to console:
   python3 tools/generic-checks/check-header-paths.py
   python3 tools/generic-checks/check-header-quality.py

2) With a policy file:
   python3 tools/generic-checks/check-header-quality.py --policy tools/generic-checks/policy.example.yml

3) Write reports:
   tools/generic-checks/run-all.sh

CLI Flags (paths checker)
- --prefix <str>: Expected path prefix in header. Defaults to "<repo-name>/". Use "none" to disable prefix requirement.
- --include <glob> (repeatable): Limit scanning to these globs.
- --exclude <glob> (repeatable): Exclude these globs.
- --ext <ext> (repeatable): Additional file extensions to include.
- --skip-ext <ext> (repeatable): Additional extensions to skip.
- --max-lines <int>: Lines to search for the header (default 10).
- --write-report <file>: Write report instead of stdout.
- --strict-prefix: Require headers to start with the prefix (default true).

CLI Flags (quality checker)
- --policy <file>: YAML policy file (see policy.example.yml). If missing or PyYAML unavailable, uses defaults.
- --include/--exclude/--ext/--skip-ext: Same semantics as above.
- --write-report <file>: Write report to file.

Environment Variables (optional)
- HEADERCHECK_PREFIX: Overrides --prefix for paths checker.
- HEADERCHECK_INCLUDE / HEADERCHECK_EXCLUDE: Space-separated globs.

Notes
- JSON policy defaults to requiring a companion README (configurable).
- Markdown path header is optional by default (configurable).
- TS/JS JSDoc detection scans 600 chars above the export.

