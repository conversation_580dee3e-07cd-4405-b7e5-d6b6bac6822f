File: tools/validation/README.md
File Description: Validation pipeline runner (autodetects stack and runs checks)
Purpose: Provide a single entrypoint to run headers, syntax, lint, types, and formatting checks

Usage
- Incremental (only changed files since last run):
  make validate
  # or bash tools/validation/run.sh

- Full sweep:
  make validate-full
  # or bash tools/validation/run.sh --full

- From a specific baseline (git ref):
  make validate-since SINCE=origin/main
  # or bash tools/validation/run.sh --since origin/main

Notes
- The runner autodetects tools and skips checks if a tool is not installed.
- It uses existing project configs when present and avoids mutating files.
- It integrates both repo-specific and generic header checkers.
- If tools are missing, it summarizes them at the end with example install commands.

Markdown links and file references
- The runner includes a local Markdown link checker:
  - Validates inline links and images (e.g., `[text](path)`, `![alt](path)`) and reference-style links `[text][ref]` with `[ref]: path` definitions.
  - Skips external URLs (http/https) — no network calls.
  - Fails on broken local paths to help keep docs accurate.

Environment checks (.env)
- The env checker enforces:
  - .env.example must exist and list required keys with placeholder values only (no real secrets).
  - .env must exist locally and set those keys with your real values (file is gitignored).
  - If .env.example contains non-placeholder values, validation fails.
- Where to store secrets:
  - Local development: .env at repo root (never commit).
  - Production/Deployment: your platform’s secret manager (e.g., Supabase project secrets, cloud provider env vars).

Flutter/Dart checks (opt-in)
- Disabled by default due to environment variability.
- Enable by setting an environment variable:
  VALIDATE_DART=1 make validate
  # or VALIDATE_DART=1 bash tools/validation/run.sh

Incremental mode internals
- State is recorded in tools/validation/.state.json (last git HEAD and timestamp).
- Changed files are recorded in tools/validation/changed-files.txt.
- If no prior state is present, the first run acts as a full sweep to establish a baseline.
