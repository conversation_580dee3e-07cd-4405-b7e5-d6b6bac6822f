id: Q242
query: >-
  What changes do we need to make to prevent similar incidents in the future?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/6.1"
overlap_ids:
  - "ISO27001:2022/10.1"
capability_tags:
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Risk Assessment & Treatment"
    id: "ISO27001:2022/6.1"
    locator: "Clause 6.1"
  - title: "ISO/IEC 27001:2022 — Improvement"
    id: "ISO27001:2022/10.1"
    locator: "Clause 10.1"
ui:
  cards_hint:
    - "Control enhancement plan"
  actions:
    - type: "start_workflow"
      target: "control_enhancement"
      label: "Enhance Controls"
output_mode: "both"
graph_required: false
notes: "Use root-cause analysis to guide control updates"
---
### 242) What changes do we need to make to prevent similar incidents in the future?

**Standard terms)**  
- **Risk treatment (ISO 27001 Cl.6.1):** selecting and implementing controls.  
- **Improvement (Cl.10.1):** updating processes post-incident.

**Plain-English answer**  
Conduct a **root-cause analysis**, update or add security controls (technical, procedural, or organisational), adjust risk assessments, and update your Statement of Applicability to reflect the new measures.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 6.1  
- **Also relevant:** Clause 10.1

**Why it matters**  
Proactive control enhancements reduce recurrence and build regulator confidence.

**Do next in our platform**  
- Start the **Control Enhancement** workflow.  
- Review current controls vs incident-related gaps.

**How our platform will help**  
- **[Workflow]** Guided root-cause templates and control-selection tools.  
- **[Report]** Impact analysis showing risk reduction per control.

**Likely follow-ups**  
- How do we verify the effectiveness of new controls?

**Sources**  
- ISO/IEC 27001:2022 Clauses 6.1 and 10.1  
