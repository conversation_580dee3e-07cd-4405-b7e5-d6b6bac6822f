<!-- File: arioncomply-v1/Mockup/index.html -->
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ArionComply - AI Accountability & Compliance Platform</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link href="mystyles.css" rel="stylesheet" />
    <style>
      /* 
        =================================================================
        SPLASH SCREEN COLOR CUSTOMIZATION
        =================================================================
        
        To change the splash screen colors, simply modify the CSS variables below.
        
        EXAMPLE COLOR SCHEMES:
        
        🌊 Ocean Blue Theme:
        --splash-gradient-1: #667eea;
        --splash-gradient-2: #764ba2; 
        --splash-gradient-3: #f093fb;
        --splash-gradient-4: #f5576c;
        --splash-gradient-5: #4facfe;
        
        🌅 Sunset Theme:
        --splash-gradient-1: #ff9a56;
        --splash-gradient-2: #ff6b6b;
        --splash-gradient-3: #feca57;
        --splash-gradient-4: #ff7675;
        --splash-gradient-5: #fd79a8;
        
        🌲 Forest Theme:
        --splash-gradient-1: #11998e;
        --splash-gradient-2: #38ef7d;
        --splash-gradient-3: #2ecc71;
        --splash-gradient-4: #27ae60;
        --splash-gradient-5: #16a085;
        
        🌌 Galaxy Theme:
        --splash-gradient-1: #8e2de2;
        --splash-gradient-2: #4a00e0;
        --splash-gradient-3: #667eea;
        --splash-gradient-4: #764ba2;
        --splash-gradient-5: #f093fb;

            --splash-gradient-1: #8892b0;
            --splash-gradient-2: #a8b2d1;
            --splash-gradient-3: #ccd6f6;
            --splash-gradient-4: #64748b;
            --splash-gradient-5: #94a3b8;
        
        */

      /* Splash Screen Color Variables - Customize these to change the splash screen theme */
      :root {
        /* Background Gradient Colors - Currently set to MUTED theme */
        --splash-gradient-1: #8892b0;
        --splash-gradient-2: #a8b2d1;
        --splash-gradient-3: #ccd6f6;
        --splash-gradient-4: #64748b;
        --splash-gradient-5: #94a3b8;

        /* Text and Logo Colors */
        --splash-text-primary: #ffffff;
        --splash-text-secondary: rgba(255, 255, 255, 0.95);
        --splash-text-loading: rgba(255, 255, 255, 0.9);

        /* Spinner and Loading Colors - Adjusted for muted theme */
        --splash-spinner-primary: #ffffff;
        --splash-spinner-secondary: #ccd6f6;
        --splash-spinner-accent: #8892b0;
        --splash-spinner-trail: rgba(255, 255, 255, 0.15);

        /* Progress Bar Colors - Adjusted for muted theme */
        --splash-progress-bg: rgba(255, 255, 255, 0.15);
        --splash-progress-fill-1: #ffffff;
        --splash-progress-fill-2: #ccd6f6;
        --splash-progress-fill-3: #8892b0;

        /* Glow and Shadow Effects - Adjusted for muted theme */
        --splash-glow-primary: rgba(255, 255, 255, 0.3);
        --splash-glow-secondary: rgba(204, 214, 246, 0.4);
        --splash-shadow-dark: rgba(0, 0, 0, 0.2);
        --splash-shadow-light: rgba(0, 0, 0, 0.1);

        /* Floating Elements - Adjusted for muted theme */
        --splash-float-opacity: 0.06;
        --splash-float-opacity-active: 0.2;
      }

      /* Splash Screen Styles */
      .splash-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: linear-gradient(
          45deg,
          var(--splash-gradient-1) 0%,
          var(--splash-gradient-2) 25%,
          var(--splash-gradient-3) 50%,
          var(--splash-gradient-4) 75%,
          var(--splash-gradient-5) 100%
        );
        background-size: 400% 400%;
        animation: gradientShift 4s ease infinite;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition:
          opacity 0.5s ease,
          visibility 0.5s ease;
        overflow: hidden;
      }

      .splash-screen.hidden {
        opacity: 0;
        visibility: hidden;
      }

      .splash-screen::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="0.5" fill="rgba(255,255,255,0.08)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.06)"/><circle cx="90" cy="90" r="0.8" fill="rgba(255,255,255,0.04)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        animation: floatParticles 6s ease-in-out infinite;
      }

      .splash-logo {
        font-size: 4rem;
        color: var(--splash-text-primary);
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 0 4px 8px var(--splash-shadow-dark);
        animation: logoFloat 3s ease-in-out infinite;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .splash-logo i {
        animation: iconPulse 2s ease-in-out infinite;
        filter: drop-shadow(0 0 20px var(--splash-glow-primary));
      }

      .splash-tagline {
        color: var(--splash-text-secondary);
        font-size: 1.25rem;
        margin-bottom: 3rem;
        text-align: center;
        max-width: 500px;
        animation: textFadeIn 2s ease-out 0.5s both;
        text-shadow: 0 2px 4px var(--splash-shadow-light);
      }

      .splash-loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2rem;
      }

      .splash-spinner-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .splash-spinner {
        width: 80px;
        height: 80px;
        border: 6px solid var(--splash-spinner-trail);
        border-top: 6px solid var(--splash-spinner-primary);
        border-right: 6px solid var(--splash-spinner-secondary);
        border-radius: 50%;
        animation: spinMultiColor 1.5s linear infinite;
        filter: drop-shadow(0 0 10px var(--splash-glow-primary));
      }

      .splash-spinner::before {
        content: "";
        position: absolute;
        top: 10px;
        left: 10px;
        right: 10px;
        bottom: 10px;
        border: 3px solid transparent;
        border-top: 3px solid var(--splash-spinner-primary);
        border-radius: 50%;
        animation: spinReverse 2s linear infinite;
        opacity: 0.6;
      }

      .loading-dots {
        display: flex;
        gap: 8px;
        margin-top: 1rem;
      }

      .loading-dot {
        width: 12px;
        height: 12px;
        background: var(--splash-spinner-primary);
        border-radius: 50%;
        animation: dotPulse 1.4s ease-in-out infinite both;
        box-shadow: 0 0 10px var(--splash-glow-primary);
      }

      .loading-dot:nth-child(1) {
        animation-delay: -0.32s;
      }
      .loading-dot:nth-child(2) {
        animation-delay: -0.16s;
      }
      .loading-dot:nth-child(3) {
        animation-delay: 0;
      }

      .splash-loading-text {
        color: var(--splash-text-loading);
        font-size: 1rem;
        animation: textGlow 2s ease-in-out infinite alternate;
        font-weight: 500;
        letter-spacing: 1px;
      }

      .progress-bar {
        width: 300px;
        height: 4px;
        background: var(--splash-progress-bg);
        border-radius: 2px;
        overflow: hidden;
        margin-top: 1rem;
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(
          90deg,
          var(--splash-progress-fill-1),
          var(--splash-progress-fill-2),
          var(--splash-progress-fill-3)
        );
        width: 0%;
        animation: progressFill 2.5s ease-out forwards;
        border-radius: 2px;
        box-shadow: 0 0 10px var(--splash-glow-primary);
      }

      /* Floating Elements */
      .floating-elements {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        overflow: hidden;
      }

      .floating-element {
        position: absolute;
        opacity: var(--splash-float-opacity);
        animation: float 8s ease-in-out infinite;
        color: var(--splash-text-primary);
      }

      .floating-element:nth-child(1) {
        top: 20%;
        left: 10%;
        animation-delay: 0s;
        font-size: 2rem;
      }

      .floating-element:nth-child(2) {
        top: 60%;
        right: 15%;
        animation-delay: 2s;
        font-size: 1.5rem;
      }

      .floating-element:nth-child(3) {
        bottom: 20%;
        left: 20%;
        animation-delay: 4s;
        font-size: 1.8rem;
      }

      .floating-element:nth-child(4) {
        top: 30%;
        right: 25%;
        animation-delay: 1s;
        font-size: 1.2rem;
      }

      .floating-element:nth-child(5) {
        bottom: 40%;
        right: 10%;
        animation-delay: 3s;
        font-size: 2.2rem;
      }

      /* Animations */
      @keyframes gradientShift {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      @keyframes floatParticles {
        0%,
        100% {
          transform: translateY(0px) rotate(0deg);
        }
        50% {
          transform: translateY(-10px) rotate(180deg);
        }
      }

      @keyframes logoFloat {
        0%,
        100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-10px);
        }
      }

      @keyframes iconPulse {
        0%,
        100% {
          transform: scale(1);
          filter: drop-shadow(0 0 20px var(--splash-glow-primary));
        }
        50% {
          transform: scale(1.1);
          filter: drop-shadow(0 0 30px var(--splash-glow-primary));
        }
      }

      @keyframes textFadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 0.95;
          transform: translateY(0);
        }
      }

      @keyframes spinMultiColor {
        0% {
          transform: rotate(0deg);
          border-top-color: var(--splash-spinner-primary);
          border-right-color: var(--splash-spinner-secondary);
        }
        25% {
          border-top-color: var(--splash-spinner-secondary);
          border-right-color: var(--splash-spinner-accent);
        }
        50% {
          border-top-color: var(--splash-spinner-accent);
          border-right-color: var(--splash-gradient-2);
        }
        75% {
          border-top-color: var(--splash-gradient-2);
          border-right-color: var(--splash-gradient-4);
        }
        100% {
          transform: rotate(360deg);
          border-top-color: var(--splash-spinner-primary);
          border-right-color: var(--splash-spinner-secondary);
        }
      }

      @keyframes spinReverse {
        from {
          transform: rotate(360deg);
        }
        to {
          transform: rotate(0deg);
        }
      }

      @keyframes dotPulse {
        0%,
        80%,
        100% {
          transform: scale(0.8);
          background: var(--splash-spinner-trail);
        }
        40% {
          transform: scale(1.2);
          background: var(--splash-spinner-primary);
          box-shadow: 0 0 15px var(--splash-glow-primary);
        }
      }

      @keyframes textGlow {
        from {
          text-shadow: 0 0 5px var(--splash-glow-primary);
        }
        to {
          text-shadow:
            0 0 20px var(--splash-glow-primary),
            0 0 30px var(--splash-glow-secondary);
        }
      }

      @keyframes progressFill {
        from {
          width: 0%;
        }
        to {
          width: 100%;
        }
      }

      @keyframes float {
        0%,
        100% {
          transform: translateY(0px) translateX(0px) rotate(0deg);
          opacity: var(--splash-float-opacity);
        }
        25% {
          transform: translateY(-20px) translateX(10px) rotate(90deg);
          opacity: var(--splash-float-opacity-active);
        }
        50% {
          transform: translateY(-40px) translateX(-10px) rotate(180deg);
          opacity: var(--splash-float-opacity);
        }
        75% {
          transform: translateY(-20px) translateX(-20px) rotate(270deg);
          opacity: var(--splash-float-opacity-active);
        }
      }

      /* Auth Container Styles */
      .auth-container {
        min-height: 100vh;
        background: linear-gradient(135deg, var(--bg-light) 0%, #e0e7ff 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        opacity: 0;
        transition: opacity 0.5s ease;
      }

      .auth-container.visible {
        opacity: 1;
      }

      .auth-card {
        background: var(--bg-white);
        border-radius: var(--border-radius);
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        padding: 3rem;
        width: 100%;
        max-width: 450px;
        position: relative;
        overflow: hidden;
      }

      .auth-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(
          90deg,
          var(--primary-blue),
          var(--ai-purple)
        );
      }

      .auth-header {
        text-align: center;
        margin-bottom: 2rem;
      }

      .auth-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
      }

      .auth-logo i {
        font-size: 2.5rem;
        color: var(--primary-blue);
      }

      .auth-logo-text {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-blue);
      }

      .auth-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-dark);
        margin-bottom: 0.5rem;
      }

      .auth-subtitle {
        color: var(--text-gray);
        font-size: 0.875rem;
      }

      .auth-form {
        margin-bottom: 2rem;
      }

      .auth-tabs {
        display: flex;
        margin-bottom: 2rem;
        border-radius: var(--border-radius-sm);
        background: var(--bg-light);
        padding: 0.25rem;
      }

      .auth-tab {
        flex: 1;
        padding: 0.75rem;
        background: none;
        border: none;
        border-radius: var(--border-radius-sm);
        cursor: pointer;
        font-weight: 500;
        transition: all 0.15s ease;
        color: var(--text-gray);
      }

      .auth-tab.active {
        background: var(--bg-white);
        color: var(--primary-blue);
        box-shadow: var(--shadow-subtle);
      }

      .auth-form-group {
        margin-bottom: 1.5rem;
      }

      .auth-label {
        display: block;
        font-weight: 500;
        color: var(--text-dark);
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
      }

      .auth-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 1px solid var(--border-light);
        border-radius: var(--border-radius-sm);
        outline: none;
        transition: border-color 0.15s ease;
        font-size: 1rem;
      }

      .auth-input:focus {
        border-color: var(--primary-blue);
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
      }

      .auth-input-icon {
        position: relative;
      }

      .auth-input-icon i {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-gray);
      }

      .auth-input-icon .auth-input {
        padding-left: 2.75rem;
      }

      .auth-checkbox-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1.5rem;
      }

      .auth-checkbox {
        accent-color: var(--primary-blue);
      }

      .auth-checkbox-label {
        font-size: 0.875rem;
        color: var(--text-gray);
        cursor: pointer;
      }

      .auth-button {
        width: 100%;
        padding: 0.875rem;
        background: linear-gradient(
          135deg,
          var(--primary-blue),
          var(--ai-purple)
        );
        color: white;
        border: none;
        border-radius: var(--border-radius-sm);
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.15s ease;
        position: relative;
        overflow: hidden;
      }

      .auth-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
      }

      .auth-button:active {
        transform: translateY(0);
      }

      .auth-divider {
        text-align: center;
        margin: 2rem 0;
        position: relative;
        color: var(--text-gray);
        font-size: 0.875rem;
      }

      .auth-divider::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: var(--border-light);
      }

      .auth-divider span {
        background: var(--bg-white);
        padding: 0 1rem;
      }

      .auth-demo-section {
        text-align: center;
      }

      .demo-credentials {
        background: var(--bg-light);
        border-radius: var(--border-radius-sm);
        padding: 1rem;
        margin-bottom: 1rem;
        font-size: 0.875rem;
      }

      .demo-credentials strong {
        color: var(--primary-blue);
      }

      .demo-button {
        background: var(--success-green);
        color: white;
        border: none;
        border-radius: var(--border-radius-sm);
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.15s ease;
      }

      .demo-button:hover {
        background: #059669;
        transform: translateY(-1px);
      }

      .auth-footer {
        text-align: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid var(--border-light);
        color: var(--text-gray);
        font-size: 0.875rem;
      }

      .auth-footer a {
        color: var(--primary-blue);
        text-decoration: none;
      }

      .auth-footer a:hover {
        text-decoration: underline;
      }

      /* Loading State */
      .auth-button.loading {
        pointer-events: none;
      }

      .auth-button.loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 0.8s linear infinite;
      }

      .auth-button.loading span {
        opacity: 0;
      }

      /* Responsive */
      @media (max-width: 640px) {
        .auth-card {
          padding: 2rem;
          margin: 1rem;
        }

        .splash-logo {
          font-size: 2.5rem;
          flex-direction: column;
          gap: 0.5rem;
        }

        .splash-logo i {
          font-size: 2rem;
        }

        .splash-tagline {
          font-size: 1rem;
          padding: 0 1rem;
        }

        .splash-spinner {
          width: 60px;
          height: 60px;
          border-width: 4px;
        }

        .splash-spinner::before {
          top: 8px;
          left: 8px;
          right: 8px;
          bottom: 8px;
          border-width: 2px;
        }

        .progress-bar {
          width: 250px;
        }

        .floating-elements {
          display: none; /* Hide floating elements on mobile for performance */
        }

        .loading-dots {
          gap: 6px;
        }

        .loading-dot {
          width: 10px;
          height: 10px;
        }
      }

      /* Hide main content initially */
      .main-app {
        display: none;
      }
    </style>
  </head>
  <body>
    <!-- Splash Screen -->
    <div class="splash-screen" id="splashScreen">
      <!-- Floating Background Elements -->
      <div class="floating-elements">
        <i class="fas fa-shield-alt floating-element"></i>
        <i class="fas fa-robot floating-element"></i>
        <i class="fas fa-lock floating-element"></i>
        <i class="fas fa-chart-line floating-element"></i>
        <i class="fas fa-cog floating-element"></i>
      </div>

      <div class="splash-logo">
        <i class="fas fa-shield-alt"></i>ArionComply
      </div>
      <div class="splash-tagline">
        AI Accountability & Multi-Framework Compliance Platform
      </div>
      <div class="splash-loading-container">
        <div class="splash-spinner-container">
          <div class="splash-spinner"></div>
        </div>
        <div class="loading-dots">
          <div class="loading-dot"></div>
          <div class="loading-dot"></div>
          <div class="loading-dot"></div>
        </div>
        <div class="splash-loading-text" id="loadingText">
          Initializing secure environment...
        </div>
        <div class="progress-bar">
          <div class="progress-fill"></div>
        </div>
      </div>
    </div>

    <!-- Authentication Container -->
    <div class="auth-container" id="authContainer">
      <div class="auth-card">
        <div class="auth-header">
          <div class="auth-logo">
            <i class="fas fa-shield-alt"></i>
            <span class="auth-logo-text">ArionComply</span>
          </div>
          <h1 class="auth-title" id="authTitle">Welcome Back</h1>
          <p class="auth-subtitle" id="authSubtitle">
            Sign in to access your compliance dashboard
          </p>
        </div>

        <!-- Auth Tabs -->
        <div class="auth-tabs">
          <button class="auth-tab active" onclick="switchAuthMode('login')">
            Sign In
          </button>
          <button class="auth-tab" onclick="switchAuthMode('register')">
            Sign Up
          </button>
        </div>

        <!-- Login Form -->
        <form class="auth-form" id="loginForm">
          <div class="auth-form-group">
            <label class="auth-label" for="loginEmail">Email Address</label>
            <div class="auth-input-icon">
              <i class="fas fa-envelope"></i>
              <input
                type="email"
                id="loginEmail"
                class="auth-input"
                placeholder="Enter your email"
                value="<EMAIL>"
                required
              />
            </div>
          </div>

          <div class="auth-form-group">
            <label class="auth-label" for="loginPassword">Password</label>
            <div class="auth-input-icon">
              <i class="fas fa-lock"></i>
              <input
                type="password"
                id="loginPassword"
                class="auth-input"
                placeholder="Enter your password"
                value="demo123"
                required
              />
            </div>
          </div>

          <div class="auth-checkbox-group">
            <input
              type="checkbox"
              id="rememberMe"
              class="auth-checkbox"
              checked
            />
            <label for="rememberMe" class="auth-checkbox-label"
              >Remember me for 30 days</label
            >
          </div>

          <button type="submit" class="auth-button">
            <span>Sign In to Dashboard</span>
          </button>
        </form>

        <!-- Register Form -->
        <form class="auth-form" id="registerForm" style="display: none">
          <div class="auth-form-group">
            <label class="auth-label" for="registerName">Full Name</label>
            <div class="auth-input-icon">
              <i class="fas fa-user"></i>
              <input
                type="text"
                id="registerName"
                class="auth-input"
                placeholder="Enter your full name"
                value="Demo User"
                required
              />
            </div>
          </div>

          <div class="auth-form-group">
            <label class="auth-label" for="registerEmail">Email Address</label>
            <div class="auth-input-icon">
              <i class="fas fa-envelope"></i>
              <input
                type="email"
                id="registerEmail"
                class="auth-input"
                placeholder="Enter your email"
                value="<EMAIL>"
                required
              />
            </div>
          </div>

          <div class="auth-form-group">
            <label class="auth-label" for="registerCompany">Company Name</label>
            <div class="auth-input-icon">
              <i class="fas fa-building"></i>
              <input
                type="text"
                id="registerCompany"
                class="auth-input"
                placeholder="Enter your company name"
                value="Demo Corp"
                required
              />
            </div>
          </div>

          <div class="auth-form-group">
            <label class="auth-label" for="registerPassword">Password</label>
            <div class="auth-input-icon">
              <i class="fas fa-lock"></i>
              <input
                type="password"
                id="registerPassword"
                class="auth-input"
                placeholder="Create a password"
                value="demo123"
                required
              />
            </div>
          </div>

          <div class="auth-checkbox-group">
            <input
              type="checkbox"
              id="agreeTerms"
              class="auth-checkbox"
              checked
              required
            />
            <label for="agreeTerms" class="auth-checkbox-label"
              >I agree to the Terms of Service and Privacy Policy</label
            >
          </div>

          <button type="submit" class="auth-button">
            <span>Create Account</span>
          </button>
        </form>

        <div class="auth-divider">
          <span>or</span>
        </div>

        <!-- Demo Section -->
        <div class="auth-demo-section">
          <div class="demo-credentials">
            <strong>Demo Access</strong><br />
            Pre-configured with sample compliance data
          </div>
          <button class="demo-button" onclick="quickDemo()">
            <i class="fas fa-play"></i>
            Launch Demo Environment
          </button>
        </div>

        <div class="auth-footer">
          <p>&copy; 2024 ArionComply. Built for enterprise compliance teams.</p>
          <p>
            <a href="#privacy">Privacy Policy</a> •
            <a href="#terms">Terms of Service</a> •
            <a href="#support">Support</a>
          </p>
        </div>
      </div>
    </div>

    <script src="scripts.js"></script>
    <script>
      let authMode = "login";

      // Quick theme switching utility functions
      function setSplashTheme(themeName) {
        const themes = {
          ocean: {
            "--splash-gradient-1": "#667eea",
            "--splash-gradient-2": "#764ba2",
            "--splash-gradient-3": "#f093fb",
            "--splash-gradient-4": "#f5576c",
            "--splash-gradient-5": "#4facfe",
          },
          sunset: {
            "--splash-gradient-1": "#ff9a56",
            "--splash-gradient-2": "#ff6b6b",
            "--splash-gradient-3": "#feca57",
            "--splash-gradient-4": "#ff7675",
            "--splash-gradient-5": "#fd79a8",
          },
          forest: {
            "--splash-gradient-1": "#11998e",
            "--splash-gradient-2": "#38ef7d",
            "--splash-gradient-3": "#2ecc71",
            "--splash-gradient-4": "#27ae60",
            "--splash-gradient-5": "#16a085",
          },
          galaxy: {
            "--splash-gradient-1": "#8e2de2",
            "--splash-gradient-2": "#4a00e0",
            "--splash-gradient-3": "#667eea",
            "--splash-gradient-4": "#764ba2",
            "--splash-gradient-5": "#f093fb",
          },
          corporate: {
            "--splash-gradient-1": "#2563eb",
            "--splash-gradient-2": "#1e40af",
            "--splash-gradient-3": "#7c3aed",
            "--splash-gradient-4": "#6b21a8",
            "--splash-gradient-5": "#4338ca",
          },
          muted: {
            "--splash-gradient-1": "#8892b0",
            "--splash-gradient-2": "#a8b2d1",
            "--splash-gradient-3": "#ccd6f6",
            "--splash-gradient-4": "#64748b",
            "--splash-gradient-5": "#94a3b8",
            "--splash-spinner-secondary": "#ccd6f6",
            "--splash-spinner-accent": "#8892b0",
            "--splash-spinner-trail": "rgba(255,255,255,0.15)",
            "--splash-progress-bg": "rgba(255,255,255,0.15)",
            "--splash-progress-fill-2": "#ccd6f6",
            "--splash-progress-fill-3": "#8892b0",
            "--splash-glow-primary": "rgba(255,255,255,0.3)",
            "--splash-glow-secondary": "rgba(204,214,246,0.4)",
            "--splash-shadow-dark": "rgba(0,0,0,0.2)",
            "--splash-shadow-light": "rgba(0,0,0,0.1)",
            "--splash-float-opacity": "0.06",
            "--splash-float-opacity-active": "0.2",
          },
        };

        const theme = themes[themeName];
        if (theme) {
          Object.keys(theme).forEach((property) => {
            document.documentElement.style.setProperty(
              property,
              theme[property],
            );
          });
          console.log(`Splash theme changed to: ${themeName}`);
        }
      }

      // Example usage:
      // setSplashTheme('sunset'); - Call this function to switch themes
      // setSplashTheme('muted'); - For a subtle, understated look
      // Available themes: 'ocean', 'sunset', 'forest', 'galaxy', 'corporate', 'muted'

      // Quick comparison functions
      function showBrightTheme() {
        setSplashTheme("sunset");
      }
      //function showMutedTheme() { setSplashTheme('muted'); }

      // Splash screen timing
      document.addEventListener("DOMContentLoaded", function () {
        // Dynamic loading text
        const loadingTexts = [
          "Initializing secure environment...",
          "Loading compliance frameworks...",
          "Preparing AI governance tools...",
          "Configuring risk management...",
          "Finalizing dashboard setup...",
        ];

        let textIndex = 0;
        const loadingTextElement = document.getElementById("loadingText");

        const textInterval = setInterval(() => {
          textIndex = (textIndex + 1) % loadingTexts.length;
          loadingTextElement.style.opacity = "0.5";
          setTimeout(() => {
            loadingTextElement.textContent = loadingTexts[textIndex];
            loadingTextElement.style.opacity = "1";
          }, 200);
        }, 600);

        setTimeout(function () {
          clearInterval(textInterval);
          document.getElementById("splashScreen").classList.add("hidden");
          setTimeout(function () {
            document.getElementById("authContainer").classList.add("visible");
          }, 500);
        }, 3000); // Show splash for 3 seconds to see all the animations
      });

      // Auth mode switching
      function switchAuthMode(mode) {
        authMode = mode;

        // Update tabs
        document
          .querySelectorAll(".auth-tab")
          .forEach((tab) => tab.classList.remove("active"));
        event.target.classList.add("active");

        // Update forms
        if (mode === "login") {
          document.getElementById("loginForm").style.display = "block";
          document.getElementById("registerForm").style.display = "none";
          document.getElementById("authTitle").textContent = "Welcome Back";
          document.getElementById("authSubtitle").textContent =
            "Sign in to access your compliance dashboard";
        } else {
          document.getElementById("loginForm").style.display = "none";
          document.getElementById("registerForm").style.display = "block";
          document.getElementById("authTitle").textContent = "Get Started";
          document.getElementById("authSubtitle").textContent =
            "Create your account to begin compliance management";
        }
      }

      // Form submissions
      document
        .getElementById("loginForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();
          handleAuth("login");
        });

      document
        .getElementById("registerForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();
          handleAuth("register");
        });

      // --------------------------------------------------------------
      // handleAuth(type)
      // --------------------------------------------------------------
      // Handles both the login and registration flows. All data is stored in
      // localStorage for demonstration purposes. Any unexpected errors are
      // caught so the interface does not freeze if storage is unavailable.
      // --------------------------------------------------------------
      function handleAuth(type) {
        const button = document.querySelector(".auth-button");
        button.classList.add("loading");

        setTimeout(function () {
          try {
            const email =
              type === "login"
                ? document.getElementById("loginEmail").value.trim()
                : document.getElementById("registerEmail").value.trim();

            let user = getUserByEmail(email);

            if (type === "login") {
              if (!user) {
                alert("User not found. Please register.");
                return;
              }
            } else {
              if (user) {
                alert("User already exists. Please sign in.");
                return;
              }
              const name = document.getElementById("registerName").value.trim();
              const company = document
                .getElementById("registerCompany")
                .value.trim();
              const role =
                getStoredData("arioncomply_users").length === 0
                  ? "admin"
                  : "user";
              user = {
                id: generateId("U"),
                name,
                email,
                company,
                role,
                loginTime: new Date().toISOString(),
              };
              saveUser(user);
            }

            localStorage.setItem("arioncomply_user", JSON.stringify(user));
            localStorage.setItem("arioncomply_session", "active");
            window.location.href = "routing.html";
          } catch (err) {
            console.error("Authentication error", err);
            alert("An unexpected error occurred.");
          } finally {
            button.classList.remove("loading");
          }
        }, 500);
      }

      function quickDemo() {
        // Set demo user data
        const demoUser = {
          type: "demo",
          email: "<EMAIL>",
          name: "Demo User",
          company: "Demo Corporation",
          role: "Compliance Officer",
          loginTime: new Date().toISOString(),
        };

        localStorage.setItem("arioncomply_user", JSON.stringify(demoUser));
        localStorage.setItem("arioncomply_session", "active");

        // Add demo data flag
        localStorage.setItem("arioncomply_demo_mode", "true");

        // Redirect to routing page
        window.location.href = "routing.html";
      }

      // Check if user is already logged in
      if (localStorage.getItem("arioncomply_session") === "active") {
        // Skip splash and auth, go directly to routing
        document.getElementById("splashScreen").style.display = "none";
        document.getElementById("authContainer").style.display = "none";
        window.location.href = "routing.html";
      }

      // Auto-fill demo credentials on focus
      document.addEventListener("focusin", function (e) {
        if (e.target.classList.contains("auth-input")) {
          // Pre-fill demo data for easier testing
          setTimeout(() => {
            if (e.target.id === "loginEmail" && !e.target.value) {
              e.target.value = "<EMAIL>";
            } else if (e.target.id === "loginPassword" && !e.target.value) {
              e.target.value = "demo123";
            }
          }, 100);
        }
      });

      // Keyboard shortcuts
      document.addEventListener("keydown", function (e) {
        if (e.ctrlKey && e.key === "d") {
          e.preventDefault();
          quickDemo();
        }
      });
    </script>
  </body>
</html>
<!-- File: arioncomply-v1/Mockup/index.html -->
