id: Q137
query: >-
  What’s algorithmic bias and are we responsible for testing our AI for it?
packs:
  - "EUAI:2024"
primary_ids:
  - "EUAI:2024/Art.10"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Report"
flags: []
sources:
  - title: "EU AI Act — Bias & Discrimination Mitigation"
    id: "EUAI:2024/Art.10"
    locator: "Article 10"
ui:
  cards_hint:
    - "Bias testing toolkit"
  actions:
    - type: "start_workflow"
      target: "bias_testing"
      label: "Test for Bias"
output_mode: "both"
graph_required: false
notes: "High-risk AI must undergo bias testing and mitigation measures"
---
### 137) What’s algorithmic bias and are we responsible for testing our AI for it?

**Standard terms**  
- **Bias mitigation (EU AI Act Art. 10):** obligations for high-risk systems to prevent discriminatory outcomes.

**Plain-English answer**  
Algorithmic bias occurs when AI makes unfair decisions based on gender, race, etc. Under the EU AI Act, you must test high-risk AI for bias, document mitigation steps, and periodically reassess performance.

**Applies to**  
- **Primary:** EU AI Act Article 10

**Why it matters**  
Undetected bias can lead to legal liability and reputational harm.

**Do next in our platform**  
- Start **Bias Testing** workflow.  
- Review auto-generated bias reports.

**How our platform will help**  
- **[Report]** Bias detection dashboards.  
- **[Workflow]** Guided mitigation planning.

**Likely follow-ups**  
- “Which metrics detect bias?” (Difference in outcome rates across groups)

**Sources**  
- EU AI Act Article 10
