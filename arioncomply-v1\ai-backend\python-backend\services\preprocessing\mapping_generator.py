"""
File: arioncomply-v1/ai-backend/python-backend/services/preprocessing/mapping_generator.py
File Description: Generate synonym/paraphrase mappings from v2 JSON content
Purpose: Extract deterministic query mappings from ingested content for preprocessing pipeline
Inputs: V2 JSON content files with metadata and body text
Outputs: synonym_index, paraphrase_index, canonical_id_mappings for query resolution
Dependencies: content files, UI catalog, metadata schemas
Security/RLS: Org-scoped mapping generation with proper tenant isolation  
Notes: Builds indexes used by QueryPreprocessor for deterministic resolution before RAG/LLM
"""

from dataclasses import dataclass
from typing import Dict, List, Set, Optional, Tuple, Any, Union
import json
import os
import re
import time
from pathlib import Path
from collections import defaultdict, Counter
import hashlib

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from logging.events import log_event


@dataclass
class SynonymMapping:
    """
    Maps alternative terms to canonical terms for deterministic query resolution.
    
    Args:
        canonical_term: The standard/preferred term (e.g., "risk assessment")
        synonyms: List of alternative terms that map to canonical (e.g., ["risk evaluation", "risk analysis"])
        source_file: Which content file this mapping came from
        confidence: Confidence score for the mapping (0.0-1.0)
        context: Optional context where this synonym relationship was found
    """
    canonical_term: str
    synonyms: List[str]
    source_file: str
    confidence: float
    context: Optional[str] = None


@dataclass
class ParaphraseMapping:
    """
    Maps paraphrased questions/phrases to canonical content IDs.
    
    Args:
        canonical_id: The canonical content ID (e.g., "ISO27001:2022/A.5.1")
        paraphrases: List of different ways the same question/concept can be asked
        original_question: The original question from the content
        confidence: Confidence score for the paraphrase mapping (0.0-1.0)
        metadata: Additional metadata from the content file
    """
    canonical_id: str
    paraphrases: List[str]
    original_question: str
    confidence: float
    metadata: Dict[str, Any]


@dataclass
class CanonicalIdMapping:
    """
    Maps various ID formats to the standard canonical ID format.
    
    Args:
        canonical_id: The standard ID format (e.g., "ISO27001:2022/A.5.1")
        alternative_ids: List of alternative ID formats that map to canonical
        content_type: Type of content (qa, clause, control, etc.)
        title: Human-readable title for the content
        file_path: Path to the source content file
    """
    canonical_id: str
    alternative_ids: List[str]
    content_type: str
    title: str
    file_path: str


@dataclass
class MappingGenerationResult:
    """
    Complete result from mapping generation process.
    
    Args:
        synonym_mappings: List of synonym mappings extracted
        paraphrase_mappings: List of paraphrase mappings extracted  
        canonical_id_mappings: List of canonical ID mappings extracted
        processing_stats: Statistics about the generation process
        errors: Any errors encountered during processing
    """
    synonym_mappings: List[SynonymMapping]
    paraphrase_mappings: List[ParaphraseMapping]
    canonical_id_mappings: List[CanonicalIdMapping]
    processing_stats: Dict[str, Any]
    errors: List[str]


class MappingGenerator:
    """
    Generates synonym, paraphrase, and canonical ID mappings from v2 JSON content.
    
    This class processes ingested content files to build the deterministic indexes
    needed by the QueryPreprocessor. It extracts:
    1. Synonym relationships from content text and metadata
    2. Paraphrase variations of questions and concepts  
    3. Canonical ID mappings for various ID formats
    
    The generated mappings are used to resolve queries deterministically before
    expensive RAG/LLM operations.
    """
    
    def __init__(self, org_id: str, content_base_path: str):
        """
        Initialize mapping generator for specific org and content location.
        
        Args:
            org_id: Organization ID for RLS/tenant isolation
            content_base_path: Base path where v2 JSON content files are located
        """
        self.org_id = org_id
        self.content_base_path = Path(content_base_path)
        self.ui_catalog = None  # Will be loaded from config/ui_catalog.json
        
        # Regex patterns for extracting terms and IDs
        self.canonical_id_pattern = re.compile(r'^[A-Z0-9]+:\d{4}/[A-Z0-9\.]+$')
        self.iso_clause_pattern = re.compile(r'[A-Z0-9]+[.\s]*(\d+\.)*\d+')
        self.question_patterns = [
            re.compile(r'^(What|How|When|Where|Why|Which)\s+.+\?$', re.IGNORECASE),
            re.compile(r'^(Is|Are|Does|Do|Can|Should|Must|Will)\s+.+\?$', re.IGNORECASE),
            re.compile(r'.+\s+(required|necessary|mandatory|recommended)\?$', re.IGNORECASE)
        ]
        
        # Common synonym patterns in compliance content
        self.synonym_extraction_patterns = [
            (r'(also known as|aka|also called)\s+([^,.]+)', 'synonym_phrase'),
            (r'(or|and/or)\s+([A-Za-z\s]+)', 'alternative_term'),
            (r'\(([^)]+)\)', 'parenthetical_synonym'),
            (r'(i\.e\.|that is|namely)\s+([^,.]+)', 'clarification_synonym')
        ]


    def load_ui_catalog(self) -> Optional[Dict[str, Any]]:
        """
        Load UI catalog for action mappings and validation.
        
        Returns:
            Parsed UI catalog dict or None if not found
        """
        catalog_path = self.content_base_path.parent / "config" / "ui_catalog.json"
        try:
            if catalog_path.exists():
                with open(catalog_path, 'r') as f:
                    self.ui_catalog = json.load(f)
                    return self.ui_catalog
        except Exception as e:
            pass  # Log error in production
        return None


    def generate_mappings(self, request_id: Optional[str] = None) -> MappingGenerationResult:
        """
        Generate all mappings from v2 JSON content files.
        
        Args:
            request_id: Optional request ID for logging/tracing
            
        Returns:
            Complete mapping generation result with statistics and errors
        """
        start_time = time.time()
        errors = []
        synonym_mappings = []
        paraphrase_mappings = []
        canonical_id_mappings = []
        
        # Load UI catalog for validation
        self.load_ui_catalog()
        
        # Process all content directories (ISO27001, ISO27701, etc.)
        content_dirs = [d for d in self.content_base_path.iterdir() if d.is_dir()]
        
        for content_dir in content_dirs:
            try:
                # Process each content type (qa, clauses, controls, etc.)
                type_dirs = [d for d in content_dir.iterdir() if d.is_dir()]
                
                for type_dir in type_dirs:
                    content_type = type_dir.name
                    
                    # Process individual content items
                    item_dirs = [d for d in type_dir.iterdir() if d.is_dir()]
                    
                    for item_dir in item_dirs:
                        metadata_file = item_dir / "metadata.json"
                        body_file = item_dir / "body.md"
                        
                        if metadata_file.exists() and body_file.exists():
                            try:
                                # Load metadata and body content
                                with open(metadata_file, 'r') as f:
                                    metadata = json.load(f)
                                
                                with open(body_file, 'r') as f:
                                    body_content = f.read()
                                
                                # Extract mappings from this content item
                                item_synonyms, item_paraphrases, item_canonical = self._extract_mappings_from_item(
                                    metadata=metadata,
                                    body_content=body_content,
                                    content_type=content_type,
                                    file_path=str(item_dir),
                                    source_dir=content_dir.name
                                )
                                
                                synonym_mappings.extend(item_synonyms)
                                paraphrase_mappings.extend(item_paraphrases)
                                canonical_id_mappings.extend(item_canonical)
                                
                            except Exception as e:
                                errors.append(f"Error processing {item_dir}: {str(e)}")
                        
            except Exception as e:
                errors.append(f"Error processing content directory {content_dir}: {str(e)}")
        
        # Build processing statistics
        processing_time = time.time() - start_time
        stats = {
            "processing_time_seconds": processing_time,
            "content_directories_processed": len(content_dirs),
            "synonym_mappings_generated": len(synonym_mappings),
            "paraphrase_mappings_generated": len(paraphrase_mappings),
            "canonical_id_mappings_generated": len(canonical_id_mappings),
            "errors_encountered": len(errors),
            "org_id": self.org_id,
            "timestamp": time.time()
        }
        
        # Log mapping generation event
        if request_id:
            log_event(
                request_id=request_id,
                org_id=self.org_id,
                user_id=None,
                event_type="mapping_generation_complete",
                direction="internal",
                details=stats
            )
        
        return MappingGenerationResult(
            synonym_mappings=synonym_mappings,
            paraphrase_mappings=paraphrase_mappings,
            canonical_id_mappings=canonical_id_mappings,
            processing_stats=stats,
            errors=errors
        )


    def _extract_mappings_from_item(
        self,
        metadata: Dict[str, Any],
        body_content: str,
        content_type: str,
        file_path: str,
        source_dir: str
    ) -> Tuple[List[SynonymMapping], List[ParaphraseMapping], List[CanonicalIdMapping]]:
        """
        Extract all mapping types from a single content item.
        
        Args:
            metadata: Parsed metadata.json content
            body_content: Raw body.md content
            content_type: Type of content (qa, clauses, etc.)
            file_path: Path to the content item directory
            source_dir: Source directory name (ISO27001, etc.)
            
        Returns:
            Tuple of (synonym_mappings, paraphrase_mappings, canonical_id_mappings)
        """
        synonyms = []
        paraphrases = []
        canonical_ids = []
        
        # Extract canonical ID from metadata
        item_id = metadata.get('id', '')
        canonical_id = self._build_canonical_id(item_id, source_dir, content_type)
        
        # 1. Extract synonym mappings
        synonyms.extend(self._extract_synonyms_from_metadata(metadata, file_path))
        synonyms.extend(self._extract_synonyms_from_body(body_content, file_path))
        
        # 2. Extract paraphrase mappings (for Q&A content)
        if content_type == 'qa':
            paraphrases.extend(self._extract_paraphrases_from_qa(
                canonical_id, metadata, body_content
            ))
        
        # 3. Extract canonical ID mappings
        canonical_ids.extend(self._extract_canonical_id_mappings(
            canonical_id, metadata, content_type, file_path
        ))
        
        return synonyms, paraphrases, canonical_ids


    def _build_canonical_id(self, item_id: str, source_dir: str, content_type: str) -> str:
        """
        Build canonical ID in standard format from item components.
        
        Args:
            item_id: Item identifier from metadata
            source_dir: Source directory (ISO27001, ISO27701, etc.)
            content_type: Content type (qa, clauses, etc.)
            
        Returns:
            Canonical ID in format like "ISO27001:2022/A.5.1" or "ISO27001:2022/Q001"
        """
        # Default to current year if not specified in source_dir
        year = "2022"  # Could be extracted from source_dir or metadata
        
        if self.canonical_id_pattern.match(item_id):
            return item_id  # Already in canonical format
        
        # Build canonical format
        if content_type == 'qa':
            return f"{source_dir}:{year}/{item_id}"
        else:
            # For clauses, controls, etc., use the item_id as section identifier
            return f"{source_dir}:{year}/{item_id}"


    def _extract_synonyms_from_metadata(self, metadata: Dict[str, Any], file_path: str) -> List[SynonymMapping]:
        """
        Extract synonyms from metadata fields like title, keywords, tags.
        
        Args:
            metadata: Parsed metadata content
            file_path: Source file path for tracking
            
        Returns:
            List of synonym mappings found in metadata
        """
        synonyms = []
        
        # Extract from artifact title and alternative titles
        artifact = metadata.get('artifact', {})
        title = artifact.get('title', '')
        
        # Extract from tags and keywords if present
        tags = metadata.get('tags', [])
        keywords = metadata.get('keywords', [])
        
        # Extract from UI action labels (alternative ways to reference same concept)
        ui_actions = metadata.get('ui', {}).get('actions', [])
        action_labels = [action.get('label', '') for action in ui_actions]
        
        # Look for synonym patterns in title
        if title:
            for pattern, pattern_type in self.synonym_extraction_patterns:
                matches = re.finditer(pattern, title, re.IGNORECASE)
                for match in matches:
                    if len(match.groups()) >= 2:
                        canonical = match.group(1).strip()
                        synonym = match.group(2).strip()
                        
                        if canonical and synonym and len(synonym) > 2:
                            synonyms.append(SynonymMapping(
                                canonical_term=canonical,
                                synonyms=[synonym],
                                source_file=file_path,
                                confidence=0.7,  # Medium confidence from metadata
                                context=f"title_pattern_{pattern_type}"
                            ))
        
        # Extract synonyms from tags/keywords relationships
        if len(tags) > 1:
            # Treat tags as potential synonyms for the main concept
            main_concept = title.split()[0] if title else tags[0]
            synonym_tags = [tag for tag in tags if tag != main_concept]
            
            if synonym_tags:
                synonyms.append(SynonymMapping(
                    canonical_term=main_concept,
                    synonyms=synonym_tags,
                    source_file=file_path,
                    confidence=0.6,  # Lower confidence from tags
                    context="metadata_tags"
                ))
        
        return synonyms


    def _extract_synonyms_from_body(self, body_content: str, file_path: str) -> List[SynonymMapping]:
        """
        Extract synonyms from body text using pattern matching.
        
        Args:
            body_content: Raw markdown body content
            file_path: Source file path for tracking
            
        Returns:
            List of synonym mappings found in body text
        """
        synonyms = []
        
        # Split into sentences for better pattern matching
        sentences = re.split(r'[.!?]+', body_content)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) < 10:  # Skip very short sentences
                continue
                
            # Apply synonym extraction patterns
            for pattern, pattern_type in self.synonym_extraction_patterns:
                matches = re.finditer(pattern, sentence, re.IGNORECASE)
                for match in matches:
                    if len(match.groups()) >= 2:
                        canonical = match.group(1).strip()
                        synonym = match.group(2).strip()
                        
                        # Clean and validate extracted terms
                        canonical = self._clean_extracted_term(canonical)
                        synonym = self._clean_extracted_term(synonym)
                        
                        if (canonical and synonym and 
                            len(canonical) > 2 and len(synonym) > 2 and
                            canonical.lower() != synonym.lower()):
                            
                            synonyms.append(SynonymMapping(
                                canonical_term=canonical,
                                synonyms=[synonym],
                                source_file=file_path,
                                confidence=0.8,  # Higher confidence from body text context
                                context=f"body_pattern_{pattern_type}"
                            ))
        
        return synonyms


    def _extract_paraphrases_from_qa(
        self,
        canonical_id: str,
        metadata: Dict[str, Any],
        body_content: str
    ) -> List[ParaphraseMapping]:
        """
        Extract paraphrase variations for Q&A content.
        
        Args:
            canonical_id: Canonical ID for the Q&A item
            metadata: Metadata containing question and context
            body_content: Body content with answer
            
        Returns:
            List of paraphrase mappings for the Q&A content
        """
        paraphrases = []
        
        # Get original question from metadata
        artifact = metadata.get('artifact', {})
        original_question = artifact.get('title', '')
        
        if not original_question:
            return paraphrases
        
        # Generate paraphrase variations based on question patterns
        paraphrase_variations = []
        
        # Extract alternative question formulations from the question itself
        paraphrase_variations.extend(self._generate_question_variations(original_question))
        
        # Extract implied questions from the answer content
        implied_questions = self._extract_implied_questions_from_answer(body_content)
        paraphrase_variations.extend(implied_questions)
        
        # Look for explicit alternative phrasings in the content
        alt_phrasings = self._find_alternative_phrasings_in_content(body_content, original_question)
        paraphrase_variations.extend(alt_phrasings)
        
        if paraphrase_variations:
            paraphrases.append(ParaphraseMapping(
                canonical_id=canonical_id,
                paraphrases=paraphrase_variations,
                original_question=original_question,
                confidence=0.8,
                metadata=metadata
            ))
        
        return paraphrases


    def _generate_question_variations(self, original_question: str) -> List[str]:
        """
        Generate variations of a question using different sentence structures.
        
        Args:
            original_question: The original question to vary
            
        Returns:
            List of question variations
        """
        variations = []
        
        # Basic transformations for common question patterns
        question_transforms = [
            # "What is X?" -> "Define X", "Explain X"
            (r'^What is (.+)\?$', ['Define {0}', 'Explain {0}', 'Describe {0}']),
            # "How do I X?" -> "How can I X?", "What steps to X?"
            (r'^How do I (.+)\?$', ['How can I {0}?', 'What steps to {0}?', 'Process for {0}?']),
            # "When should X?" -> "At what point X?", "Under what conditions X?"
            (r'^When should (.+)\?$', ['At what point should {0}?', 'Under what conditions should {0}?']),
        ]
        
        for pattern, templates in question_transforms:
            match = re.match(pattern, original_question, re.IGNORECASE)
            if match:
                extracted = match.group(1)
                for template in templates:
                    variation = template.format(extracted)
                    if variation != original_question:
                        variations.append(variation)
                break  # Only apply first matching pattern
        
        return variations


    def _extract_implied_questions_from_answer(self, body_content: str) -> List[str]:
        """
        Extract implied questions from answer content.
        
        Args:
            body_content: The answer content to analyze
            
        Returns:
            List of implied questions found in the content
        """
        implied_questions = []
        
        # Look for phrases that suggest implicit questions
        implicit_patterns = [
            r'This means (.+)',  # "This means X" -> "What does this mean?"
            r'The purpose is (.+)',  # "The purpose is X" -> "What is the purpose?"
            r'You must (.+)',  # "You must X" -> "What must I do?"
            r'It is required to (.+)',  # "It is required to X" -> "What is required?"
        ]
        
        for pattern in implicit_patterns:
            matches = re.finditer(pattern, body_content, re.IGNORECASE)
            for match in matches:
                # Generate corresponding question based on pattern
                if 'means' in pattern:
                    implied_questions.append("What does this mean?")
                elif 'purpose' in pattern:
                    implied_questions.append("What is the purpose?")
                elif 'must' in pattern:
                    implied_questions.append("What must I do?")
                elif 'required' in pattern:
                    implied_questions.append("What is required?")
        
        return list(set(implied_questions))  # Remove duplicates


    def _find_alternative_phrasings_in_content(self, body_content: str, original_question: str) -> List[str]:
        """
        Find explicit alternative phrasings mentioned in the content.
        
        Args:
            body_content: Content to search for alternative phrasings
            original_question: Original question for comparison
            
        Returns:
            List of alternative phrasings found
        """
        alternatives = []
        
        # Look for explicit alternative phrasing indicators
        alt_patterns = [
            r'also asked as[:\s]*"([^"]+)"',
            r'alternatively[:\s]*"([^"]+)"',
            r'or in other words[:\s]*"([^"]+)"',
            r'sometimes phrased as[:\s]*"([^"]+)"'
        ]
        
        for pattern in alt_patterns:
            matches = re.finditer(pattern, body_content, re.IGNORECASE)
            for match in matches:
                alt_phrasing = match.group(1).strip()
                if alt_phrasing and alt_phrasing != original_question:
                    alternatives.append(alt_phrasing)
        
        return alternatives


    def _extract_canonical_id_mappings(
        self,
        canonical_id: str,
        metadata: Dict[str, Any],
        content_type: str,
        file_path: str
    ) -> List[CanonicalIdMapping]:
        """
        Extract canonical ID mappings for various ID formats.
        
        Args:
            canonical_id: The canonical ID format
            metadata: Item metadata
            content_type: Type of content
            file_path: Source file path
            
        Returns:
            List of canonical ID mappings
        """
        mappings = []
        
        # Get alternative IDs from metadata
        item_id = metadata.get('id', '')
        artifact = metadata.get('artifact', {})
        title = artifact.get('title', '')
        
        alternative_ids = []
        
        # Add the raw item ID if different from canonical
        if item_id != canonical_id:
            alternative_ids.append(item_id)
        
        # Extract ISO clause references from title
        iso_references = self.iso_clause_pattern.findall(title)
        alternative_ids.extend(iso_references)
        
        # Add any explicit references in metadata
        refs = metadata.get('references', [])
        if isinstance(refs, list):
            alternative_ids.extend(refs)
        
        # Add standard-specific formatting variations
        standard_id = canonical_id.split(':')[0]  # e.g., "ISO27001"
        if standard_id in canonical_id:
            # Add variations like "27001", "ISO 27001", etc.
            number_only = re.search(r'\d+', standard_id)
            if number_only:
                alternative_ids.append(number_only.group())
                alternative_ids.append(f"ISO {number_only.group()}")
        
        # Remove duplicates and empty strings
        alternative_ids = list(set(alt for alt in alternative_ids if alt and alt != canonical_id))
        
        if alternative_ids:
            mappings.append(CanonicalIdMapping(
                canonical_id=canonical_id,
                alternative_ids=alternative_ids,
                content_type=content_type,
                title=title,
                file_path=file_path
            ))
        
        return mappings


    def _clean_extracted_term(self, term: str) -> str:
        """
        Clean and normalize extracted terms.
        
        Args:
            term: Raw extracted term
            
        Returns:
            Cleaned and normalized term
        """
        if not term:
            return ""
        
        # Remove extra whitespace and punctuation
        term = re.sub(r'\s+', ' ', term.strip())
        term = re.sub(r'^[^\w]+|[^\w]+$', '', term)
        
        # Convert to title case for consistency
        term = term.title()
        
        return term


    def export_mappings(self, result: MappingGenerationResult, output_dir: str) -> Dict[str, str]:
        """
        Export generated mappings to JSON files for use by preprocessing pipeline.
        
        Args:
            result: Complete mapping generation result
            output_dir: Directory to write mapping files
            
        Returns:
            Dict mapping export type to file path
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        export_files = {}
        
        # Export synonym mappings
        synonyms_data = {
            "version": time.strftime("%Y-%m-%dT%H:%M:%S"),
            "org_id": self.org_id,
            "mappings": [
                {
                    "canonical_term": s.canonical_term,
                    "synonyms": s.synonyms,
                    "confidence": s.confidence,
                    "source_file": s.source_file,
                    "context": s.context
                }
                for s in result.synonym_mappings
            ]
        }
        
        synonyms_file = output_path / "synonym_mappings.json"
        with open(synonyms_file, 'w') as f:
            json.dump(synonyms_data, f, indent=2)
        export_files["synonyms"] = str(synonyms_file)
        
        # Export paraphrase mappings  
        paraphrases_data = {
            "version": time.strftime("%Y-%m-%dT%H:%M:%S"),
            "org_id": self.org_id,
            "mappings": [
                {
                    "canonical_id": p.canonical_id,
                    "original_question": p.original_question,
                    "paraphrases": p.paraphrases,
                    "confidence": p.confidence
                }
                for p in result.paraphrase_mappings
            ]
        }
        
        paraphrases_file = output_path / "paraphrase_mappings.json"
        with open(paraphrases_file, 'w') as f:
            json.dump(paraphrases_data, f, indent=2)
        export_files["paraphrases"] = str(paraphrases_file)
        
        # Export canonical ID mappings
        canonical_ids_data = {
            "version": time.strftime("%Y-%m-%dT%H:%M:%S"),
            "org_id": self.org_id,
            "mappings": [
                {
                    "canonical_id": c.canonical_id,
                    "alternative_ids": c.alternative_ids,
                    "content_type": c.content_type,
                    "title": c.title,
                    "file_path": c.file_path
                }
                for c in result.canonical_id_mappings
            ]
        }
        
        canonical_file = output_path / "canonical_id_mappings.json"
        with open(canonical_file, 'w') as f:
            json.dump(canonical_ids_data, f, indent=2)
        export_files["canonical_ids"] = str(canonical_file)
        
        # Export processing statistics
        stats_file = output_path / "generation_stats.json"
        with open(stats_file, 'w') as f:
            json.dump(result.processing_stats, f, indent=2)
        export_files["stats"] = str(stats_file)
        
        return export_files


def create_mapping_generator(org_id: str, content_base_path: str) -> MappingGenerator:
    """
    Factory function to create a mapping generator instance.
    
    Args:
        org_id: Organization ID for RLS/tenant isolation
        content_base_path: Base path where v2 JSON content files are located
        
    Returns:
        Configured MappingGenerator instance
    """
    return MappingGenerator(org_id=org_id, content_base_path=content_base_path)


if __name__ == "__main__":
    # CLI interface for testing/development
    import sys
    
    if len(sys.argv) < 3:
        print("Usage: python mapping_generator.py <org_id> <content_path> [output_dir]")
        sys.exit(1)
    
    org_id = sys.argv[1]
    content_path = sys.argv[2]
    output_dir = sys.argv[3] if len(sys.argv) > 3 else "./mappings_output"
    
    generator = create_mapping_generator(org_id, content_path)
    result = generator.generate_mappings()
    
    print(f"Generated {len(result.synonym_mappings)} synonym mappings")
    print(f"Generated {len(result.paraphrase_mappings)} paraphrase mappings")
    print(f"Generated {len(result.canonical_id_mappings)} canonical ID mappings")
    print(f"Processing time: {result.processing_stats['processing_time_seconds']:.2f} seconds")
    
    if result.errors:
        print(f"Encountered {len(result.errors)} errors:")
        for error in result.errors:
            print(f"  - {error}")
    
    export_files = generator.export_mappings(result, output_dir)
    print(f"Exported mappings to {output_dir}:")
    for export_type, file_path in export_files.items():
        print(f"  - {export_type}: {file_path}")