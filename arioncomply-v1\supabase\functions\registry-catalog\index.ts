// File: arioncomply-v1/supabase/functions/registry-catalog/index.ts
// File Description: Registry catalog Edge function
// Purpose: Serve config/ui_catalog.json as API endpoint for UI components
// Input: GET request
// Output: JSON catalog data (registers, workflows, templates, trackers)
// Notes: Returns ui_catalog.json with proper CORS, request tracking, and camelCase conversion

import { serve } from "https://deno.land/std@0.224.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { apiOk, apiError } from "../_shared/errors.ts";
import { getClientMeta, logRequestStart, logRequestEnd, logEvent } from "../_shared/logger.ts";
import { convertKeys } from "../_shared/utils.ts";

// Import the registry catalog - this is the source of truth
import uiCatalog from "../../../config/ui_catalog.json" assert { type: "json" };

serve(async (req) => {
  const requestId = crypto.randomUUID();
  const meta = getClientMeta(req, requestId);
  await logRequestStart(meta, req.headers);

  // CORS preflight
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  if (req.method !== "GET") {
    const res = apiError("METHOD_NOT_ALLOWED", "Only GET method is allowed", 405, undefined, requestId);
    await logRequestEnd(meta, 405);
    return res;
  }

  try {
    // Log registry access for analytics
    await logEvent(meta, {
      eventType: "registry_catalog_accessed",
      direction: "internal",
      status: "ok",
      details: {
        catalogVersion: uiCatalog.version,
        registersCount: uiCatalog.registers?.length || 0,
        workflowsCount: uiCatalog.workflows?.length || 0,
        templatesCount: uiCatalog.templates?.length || 0,
        trackersCount: uiCatalog.trackers?.length || 0,
      }
    });

    // Convert to camelCase for API consistency
    const camelCaseData = convertKeys(uiCatalog, "toCamel");
    
    const res = apiOk(camelCaseData, requestId, {
      headers: {
        ...corsHeaders,
        "Cache-Control": "public, max-age=300", // 5-minute cache
        "Content-Type": "application/json"
      }
    });
    
    await logRequestEnd(meta, 200);
    return res;
    
  } catch (error) {
    console.error("Registry catalog error:", error);
    
    const res = apiError(
      "REGISTRY_CATALOG_ERROR", 
      "Failed to load registry catalog", 
      500, 
      { error: String(error) }, 
      requestId
    );
    
    await logRequestEnd(meta, 500);
    return res;
  }
});