"""
File: arioncomply-v1/ai-backend/python-backend/services/ingestion/chunker.py
File Description: Text chunker
Purpose: Split extracted text into overlapping windows with metadata carry-over
Inputs: blocks: [{ text, section?, heading?, page? }], window_chars, overlap_chars
Outputs: chunks: [{ text, seq, section?, heading?, page? }]
"""

from typing import List, Dict


def chunk_blocks(blocks: List[Dict], window_chars: int = 2500, overlap_chars: int = 300) -> List[Dict]:
    """Split text blocks into overlapping fixed-size chunks.

    Args:
        blocks: List of extracted blocks with optional metadata.
        window_chars: Max characters per chunk window.
        overlap_chars: Overlap between consecutive chunks.

    Returns:
        A list of chunk dicts preserving basic source metadata.
    """
    chunks: List[Dict] = []
    seq = 1
    for b in blocks:
        text = b.get("text") or ""
        start = 0
        while start < len(text):
            end = min(len(text), start + window_chars)
            piece = text[start:end]
            chunks.append({
                "seq": seq,
                "text": piece,
                "section": b.get("section"),
                "heading": b.get("heading"),
                "page": b.get("page"),
            })
            seq += 1
            if end == len(text):
                break
            start = max(0, end - overlap_chars)
    return chunks
"""
File: arioncomply-v1/ai-backend/python-backend/services/ingestion/chunker.py
File Description: Split documents into manageable chunks for vectorization
"""
