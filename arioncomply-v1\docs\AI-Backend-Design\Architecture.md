# AI Backend Architecture (Placeholder)

Decisions
- Primary models: SLLMs on-prem with INT8 quantization.
- Backups: <PERSON>AI and Claude (GLLMs). All data sent to GLLMs must be anonymized.
- Vector DB: separate Supabase project; pgvector; org-level isolation using `org_id` with RLS. ChromeDB vs Supabase VectorDB under evaluation.
- API surface: Edge functions `conversation.start`, `conversation.send`, `conversation.stream` are the public interface.
- Orchestration: Lives in the Python AI backend (not at the Edge). Backend performs moderation → retrieval → prompt compile → provider selection → post-process.
- Logging: `api_request_logs` and `api_event_logs` record request lifecycle and granular events (model calls, db reads/writes, responses).
- Graph: start with relational graph in Postgres (requirements, controls, evidence) and perform adjacency “hops” in retrieval expansion. Dedicated graph store is future option.

Flow
- UI → Edge (`conversation.*`) with JWT → Edge forwards → Python AI backend router
- Backend: moderation/safety (as needed), retrieval (hybrid), prompt management, provider selection
- Model: SLLM first; fallback to GLLM with anonymized inputs
- Post-processing: classification, suggestion generation; HITL gates for state-changing actions
- Logging: start/end + `ai_call`, `db_read`, `db_write`, `response_sent`, `stream_finished`

Open Questions
- Which SLLM family/hardware and quantization toolchain for INT8?
- ChromeDB vs Supabase VectorDB final decision and performance targets.
- Moderation policies/thresholds and redaction taxonomy.

Next Steps
- Implement `assistant_router` inside `conversation/send`.
- Add unified vector client targeting the separate Supabase with org-scoped queries.
- Extend logger to capture token usage and cost per `ai_call`.
Reference
- Deployment source of truth: `AI-Backend-Design/AI_Backend_Deployment.md`
- Diagrams: `AI-Backend-Design/diagrams/*.puml` (PlantUML; renders in GitHub)
