
```yaml
id: Q220
question: How do we balance compliance maintenance with other business priorities?
packs: ["ISO27001:2022","GDPR:2016","ISO27701:2019"]
primary_ids: ["ISO27001:2022/Cl.6.1","ISO27001:2022/Cl.9.1","ISO27001:2022/Cl.9.3","GDPR:2016/Art.24"]
overlap_ids: ["ISO27701:2019/Cl.9"]
capability_tags: ["Dashboard","Planner","Reminder","Workflow","Report"]
ui:
  actions:
    - target: "dashboard"
      action: "open"
      args: { key: "risk_weighted_work_queue" }
    - target: "planner"
      action: "open"
      args: { template: "release_calendar_alignment" }
cards_hint:
  - Prioritize high-risk controls.
  - Automate routine tasks & reminders.
  - Review KPIs in management meetings.
graph_required: false
```

### 220) How do we balance compliance maintenance with other business priorities?

**Standard term(s)**

- **Risk-based approach (Cl. 6.1); Performance evaluation (Cl. 9.1–9.3).**

**Plain-English answer**\
Prioritize **high-risk** controls, automate routine tasks, and align the cadence with your **product/ops** calendars.

**Applies to**

- **Primary:** ISO/IEC 27001:2022 **Cl. 6, 8–10**; GDPR **Art. 24**.
- **Also relevant/Overlaps:** ISO/IEC 27701.

**Why it matters**\
Focus protects the business **without** overspending or slowing delivery.

**Do next in our platform**

- Tie tasks to **risk scores** and release calendars; automate reminders; review KPIs in management meetings.

**How our platform will help**

- **[Dashboard] [Planner] [Reminder] [Workflow] [Report]** — Risk-weighted queues, calendar sync, and exec-ready reporting.

**Likely follow-ups**

- “What’s a minimum viable cadence for us?” → See Q55–56.

**Sources**

- ISO/IEC 27001:2022 **Cl. 6, 8–10**; GDPR **Art. 24**.