# Subscription Management Edge Functions

## Overview

This document outlines how to implement the edge function components for ArionComply's subscription management system. The edge functions serve as the API layer between the client applications and the database, handling authentication, permissions, and business logic.

## Prerequisites

Before implementing these edge functions, ensure the following:

1. The database schema from `arioncomply-v1/docs/ApplicationDatabase/Unified-Subscription-Management-Schema-Design.md` has been applied
2. The database functions and triggers from `arioncomply-v1/docs/ApplicationDatabase/Subscription-Management-Functions-Triggers.md` have been implemented
3. The metadata registry entries from `arioncomply-v1/docs/ApplicationDatabase/Unified-Subscription-Management-Metadata-Implementation.md` have been created
4. The Supabase Edge Functions environment has been set up

## Architecture

The subscription management API follows ArionComply's edge function architecture as outlined in `arioncomply-v1/docs/ApplicationDatabase/DataDriveUISchemaAndFunctions/arioncomply-edge-function-router.md`. The key components are:

1. **Edge Function Router**: The main entry point that routes requests to the appropriate handler
2. **Permission Middleware**: Validates user permissions before processing requests
3. **Request Handlers**: Process specific types of requests (GET, POST, etc.)
4. **Response Formatter**: Formats responses according to metadata specifications

### Diagram

```
Client Request
      ↓
Edge Function Router
      ↓
Authentication Middleware
      ↓
Permission Middleware
      ↓
Request Handler (based on path and method)
      ↓
Database Operation
      ↓
Response Formatter
      ↓
Client Response
```

## Edge Function Router

The main edge function router for subscription management integrates with the central `assistant_router` function. Here's how to implement it:

### File: `assistant_router.js`

```javascript
import { createClient } from '@supabase/supabase-js'
import { handleSubscriptionRequest } from './handlers/subscription_handler.js'

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

// Main router function
export default async function(req, res) {
  // Get the request path
  const path = new URL(req.url).pathname
  
  // Extract JWT token from Authorization header
  const authHeader = req.headers.get('Authorization') || ''
  const token = authHeader.replace('Bearer ', '')
  
  // Get user information from token
  const { data: { user }, error } = await supabase.auth.getUser(token)
  
  // Create context object
  const context = {
    user,
    supabase,
    metadata: {}
  }
  
  // Route request to appropriate handler based on path
  if (path.startsWith('/subscription') || path.startsWith('/org_subscriptions') || path.startsWith('/sub_plans')) {
    return await handleSubscriptionRequest(req, res, context)
  }
  
  // Handle other paths...
  
  // If no handler matched, return 404
  return res.json({ error: 'not_found', message: 'Endpoint not found' }, { status: 404 })
}
```

## Subscription Handler

The subscription handler processes all subscription-related requests. Here's the implementation:

### File: `handlers/subscription_handler.js`

```javascript
import { setRequestContext } from '../middleware/context_middleware.js'
import { checkPermission } from '../middleware/permission_middleware.js'
import { getMetadataForEntity } from '../utils/metadata_utils.js'
import { 
  handleMetadataGet, 
  handleMetadataPost, 
  handleMetadataPut, 
  handleMetadataDelete 
} from '../utils/metadata_handlers.js'

// Main handler function
export async function handleSubscriptionRequest(req, res, context) {
  try {
    // Set request context for RLS policies
    await setRequestContext(context)
    
    // Extract path and method
    const url = new URL(req.url)
    const path = url.pathname
    const method = req.method
    
    // Process special endpoints
    if (path === '/subscription/active' && method === 'GET') {
      return await getActiveSubscription(req, res, context)
    } else if (path === '/subscription/plans' && method === 'GET') {
      return await getAvailablePlans(req, res, context)
    } else if (path === '/subscription/change-plan' && method === 'POST') {
      return await changePlan(req, res, context)
    } else if (path === '/subscription/cancel' && method === 'POST') {
      return await cancelSubscription(req, res, context)
    } else if (path === '/subscription/usage' && method === 'GET') {
      return await getUsageData(req, res, context)
    }
    
    // Handle metadata-driven CRUD operations
    const entityType = getEntityTypeFromPath(path)
    
    if (!entityType) {
      return res.json({ 
        error: 'invalid_endpoint', 
        message: 'Invalid API endpoint' 
      }, { status: 404 })
    }
    
    // Get metadata for entity
    const metadata = await getMetadataForEntity(context.supabase, entityType)
    
    if (!metadata) {
      return res.json({ 
        error: 'metadata_not_found', 
        message: 'Entity metadata not found' 
      }, { status: 404 })
    }
    
    // Add metadata to context
    context.metadata = metadata
    
    // Process request based on method
    switch (method) {
      case 'GET':
        return await handleMetadataGet(req, res, context)
      case 'POST':
        return await handleMetadataPost(req, res, context)
      case 'PUT':
      case 'PATCH':
        return await handleMetadataPut(req, res, context)
      case 'DELETE':
        return await handleMetadataDelete(req, res, context)
      default:
        return res.json({ 
          error: 'method_not_allowed', 
          message: `Method ${method} not allowed` 
        }, { status: 405 })
    }
  } catch (error) {
    console.error('Error in subscription handler:', error)
    
    return res.json({ 
      error: 'server_error', 
      message: 'An unexpected error occurred',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 })
  }
}

// Helper function to extract entity type from path
function getEntityTypeFromPath(path) {
  const pathParts = path.split('/').filter(Boolean)
  
  // Map path to entity type
  const entityMap = {
    'sub_plans': 'sub_plans',
    'plans': 'sub_plans',
    'org_subscriptions': 'org_subscriptions',
    'subscriptions': 'org_subscriptions',
    'feature_usage': 'feature_usage',
    'usage': 'feature_usage',
    'sub_transitions': 'sub_transitions',
    'transitions': 'sub_transitions'
  }
  
  return entityMap[pathParts[0]] || null
}
```

## Request Handler Implementations

Let's implement the specialized request handlers for subscription management:

### Get Active Subscription

```javascript
async function getActiveSubscription(req, res, context) {
  const { user, supabase } = context
  
  // Check permission
  const permissionResult = await checkPermission(
    supabase, 
    user.id, 
    user.organization_id,
    'view:subscription'
  )
  
  if (!permissionResult.allowed) {
    return res.json({
      error: 'permission_denied',
      message: permissionResult.message
    }, { status: 403 })
  }
  
  // Get active subscription with plan details
  const { data: subscription, error } = await supabase
    .from('org_subscriptions')
    .select(`
      sub_id,
      sub_plan_id,
      sub_status,
      sub_start_date,
      sub_end_date,
      sub_auto_renew,
      plan:sub_plans (
        plan_id,
        plan_name,
        plan_type,
        plan_description,
        plan_configuration
      )
    `)
    .eq('sub_org_id', user.organization_id)
    .eq('sub_status', 'active')
    .single()
  
  if (error) {
    if (error.code === 'PGRST116') {
      return res.json({
        error: 'not_found',
        message: 'No active subscription found'
      }, { status: 404 })
    }
    
    return res.json({
      error: 'database_error',
      message: 'Error retrieving subscription',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 })
  }
  
  // Calculate time remaining for time-limited subscriptions
  let timeRemaining = null
  if (subscription.sub_end_date) {
    const endDate = new Date(subscription.sub_end_date)
    const now = new Date()
    const diffMs = endDate - now
    
    if (diffMs > 0) {
      const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24))
      timeRemaining = {
        days: diffDays,
        formatted: `${diffDays} day${diffDays !== 1 ? 's' : ''}`
      }
    } else {
      timeRemaining = {
        days: 0,
        formatted: 'Expired'
      }
    }
  }
  
  // Return subscription data with additional context
  return res.json({
    data: {
      ...subscription,
      time_remaining: timeRemaining,
      is_demo: subscription.plan?.plan_type === 'demo',
      is_time_limited: !!subscription.sub_end_date
    }
  })
}
```

### Get Available Plans

```javascript
async function getAvailablePlans(req, res, context) {
  const { user, supabase } = context
  
  // Parse query parameters
  const url = new URL(req.url)
  const showAll = url.searchParams.get('all') === 'true'
  const planType = url.searchParams.get('type')
  
  // Check permission - anyone can view public plans
  let query = supabase
    .from('sub_plans')
    .select(`
      plan_id,
      plan_code,
      plan_name,
      plan_description,
      plan_type,
      plan_is_active,
      plan_is_public,
      plan_duration_days,
      plan_billing_cycle,
      plan_price_amount,
      plan_price_currency,
      plan_user_limit,
      plan_storage_limit_gb,
      plan_configuration
    `)
    .eq('plan_is_active', true)
  
  // Filter by plan type if specified
  if (planType) {
    query = query.eq('plan_type', planType)
  }
  
  // For regular users, only show public plans unless they request all and have permission
  if (!showAll) {
    query = query.eq('plan_is_public', true)
  } else {
    const permissionResult = await checkPermission(
      supabase, 
      user.id, 
      user.organization_id,
      'view:all_plans'
    )
    
    if (!permissionResult.allowed) {
      query = query.eq('plan_is_public', true)
    }
  }
  
  // Sort by price
  query = query.order('plan_price_amount', { ascending: true })
  
  // Execute query
  const { data: plans, error } = await query
  
  if (error) {
    return res.json({
      error: 'database_error',
      message: 'Error retrieving plans',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 })
  }
  
  // If user has an active subscription, mark the current plan
  const { data: activeSubscription } = await supabase
    .from('org_subscriptions')
    .select('sub_plan_id')
    .eq('sub_org_id', user.organization_id)
    .eq('sub_status', 'active')
    .single()
  
  const enhancedPlans = plans.map(plan => ({
    ...plan,
    is_current: activeSubscription ? plan.plan_id === activeSubscription.sub_plan_id : false
  }))
  
  return res.json({
    data: enhancedPlans
  })
}
```

### Change Plan

```javascript
async function changePlan(req, res, context) {
  const { user, supabase } = context
  
  // Check permission
  const permissionResult = await checkPermission(
    supabase, 
    user.id, 
    user.organization_id,
    'update:subscription'
  )
  
  if (!permissionResult.allowed) {
    return res.json({
      error: 'permission_denied',
      message: permissionResult.message
    }, { status: 403 })
  }
  
  // Parse request body
  const body = await req.json()
  const { plan_id, reason } = body
  
  if (!plan_id) {
    return res.json({
      error: 'invalid_request',
      message: 'plan_id is required'
    }, { status: 400 })
  }
  
  // Check if plan exists and is active
  const { data: plan, error: planError } = await supabase
    .from('sub_plans')
    .select('plan_id, plan_name, plan_is_active')
    .eq('plan_id', plan_id)
    .single()
  
  if (planError || !plan) {
    return res.json({
      error: 'invalid_plan',
      message: 'The specified plan does not exist'
    }, { status: 400 })
  }
  
  if (!plan.plan_is_active) {
    return res.json({
      error: 'inactive_plan',
      message: 'The specified plan is not active'
    }, { status: 400 })
  }
  
  // Execute the plan change workflow
  const { data: workflowResult, error: workflowError } = await supabase.rpc(
    'execute_workflow',
    {
      p_workflow_type: 'subscription_change_plan',
      p_parameters: {
        organization_id: user.organization_id,
        new_plan_id: plan_id,
        reason: reason || 'User requested plan change',
        preserve_data: ['policies', 'controls', 'risks', 'documents']
      }
    }
  )
  
  if (workflowError) {
    return res.json({
      error: 'workflow_error',
      message: 'Failed to change subscription plan',
      details: process.env.NODE_ENV === 'development' ? workflowError.message : undefined
    }, { status: 500 })
  }
  
  // Return success response
  return res.json({
    success: true,
    message: `Subscription changed to ${plan.plan_name}`,
    data: workflowResult
  })
}
```

### Cancel Subscription

```javascript
async function cancelSubscription(req, res, context) {
  const { user, supabase } = context
  
  // Check permission
  const permissionResult = await checkPermission(
    supabase, 
    user.id, 
    user.organization_id,
    'cancel:subscription'
  )
  
  if (!permissionResult.allowed) {
    return res.json({
      error: 'permission_denied',
      message: permissionResult.message
    }, { status: 403 })
  }
  
  // Parse request body
  const body = await req.json()
  const { reason } = body
  
  // Get active subscription
  const { data: subscription, error: subError } = await supabase
    .from('org_subscriptions')
    .select('sub_id, sub_status, sub_plan_id')
    .eq('sub_org_id', user.organization_id)
    .eq('sub_status', 'active')
    .single()
  
  if (subError) {
    return res.json({
      error: 'not_found',
      message: 'No active subscription found'
    }, { status: 404 })
  }
  
  // Update subscription status
  const { error: updateError } = await supabase
    .from('org_subscriptions')
    .update({
      sub_status: 'canceled',
      sub_canceled_at: new Date().toISOString(),
      sub_canceled_by: user.id,
      sub_cancel_reason: reason,
      updated_at: new Date().toISOString(),
      updated_by: user.id
    })
    .eq('sub_id', subscription.sub_id)
  
  if (updateError) {
    return res.json({
      error: 'update_error',
      message: 'Failed to cancel subscription',
      details: process.env.NODE_ENV === 'development' ? updateError.message : undefined
    }, { status: 500 })
  }
  
  // Create transition record
  await supabase
    .from('sub_transitions')
    .insert({
      transition_org_id: user.organization_id,
      transition_user_id: user.id,
      transition_from_sub_id: subscription.sub_id,
      transition_from_plan_id: subscription.sub_plan_id,
      transition_reason: reason || 'User canceled subscription',
      transition_source: 'user_initiated',
      created_by: user.id
    })
  
  // Return success response
  return res.json({
    success: true,
    message: 'Subscription has been canceled'
  })
}
```

### Get Usage Data

```javascript
async function getUsageData(req, res, context) {
  const { user, supabase } = context
  
  // Check permission
  const permissionResult = await checkPermission(
    supabase, 
    user.id, 
    user.organization_id,
    'view:usage'
  )
  
  if (!permissionResult.allowed) {
    return res.json({
      error: 'permission_denied',
      message: permissionResult.message
    }, { status: 403 })
  }
  
  // Get active subscription
  const { data: subscription, error: subError } = await supabase
    .from('org_subscriptions')
    .select(`
      sub_id,
      sub_plan_id,
      plan:sub_plans (
        plan_configuration
      )
    `)
    .eq('sub_org_id', user.organization_id)
    .eq('sub_status', 'active')
    .single()
  
  if (subError) {
    return res.json({
      error: 'not_found',
      message: 'No active subscription found'
    }, { status: 404 })
  }
  
  // Extract limits from plan configuration
  const limits = subscription.plan?.plan_configuration?.feature_limits || {}
  
  // Get usage counts for each feature
  const features = Object.keys(limits)
  const usage = {}
  
  // For each feature, get the count of records
  for (const feature of features) {
    // Skip if not a countable feature
    if (!['policies', 'controls', 'risks', 'documents'].includes(feature)) {
      continue
    }
    
    // Get count
    const { count, error: countError } = await supabase
      .from(feature)
      .select('id', { count: 'exact', head: true })
      .eq('organization_id', user.organization_id)
    
    if (!countError) {
      usage[feature] = count || 0
    }
  }
  
  // Get feature usage data
  const { data: featureUsage, error: usageError } = await supabase
    .from('feature_usage')
    .select('usage_feature_code, usage_count, usage_first, usage_last')
    .eq('usage_org_id', user.organization_id)
    .eq('usage_sub_id', subscription.sub_id)
  
  if (usageError) {
    console.error('Error fetching feature usage:', usageError)
  }
  
  // Process feature usage into a more usable format
  const featureUsageMap = {}
  if (featureUsage) {
    featureUsage.forEach(item => {
      featureUsageMap[item.usage_feature_code] = {
        count: item.usage_count,
        first_used: item.usage_first,
        last_used: item.usage_last
      }
    })
  }
  
  // Return combined usage and limits data
  return res.json({
    data: {
      subscription_id: subscription.sub_id,
      limits,
      usage,
      feature_usage: featureUsageMap
    }
  })
}
```

## Middleware Components

### Context Middleware

The context middleware sets up the request context for database operations.

```javascript
// middleware/context_middleware.js

export async function setRequestContext(context) {
  const { user, supabase } = context
  
  if (!user || !user.id || !user.organization_id) {
    throw new Error('User context not available')
  }
  
  // Set context for RLS policies
  await supabase.rpc('set_config', {
    setting_name: 'app.current_user_id',
    setting_value: user.id,
    is_local: true
  })
  
  await supabase.rpc('set_config', {
    setting_name: 'app.current_organization_id',
    setting_value: user.organization_id,
    is_local: true
  })
  
  return true
}
```

### Permission Middleware

The permission middleware checks if a user has the required permissions.

```javascript
// middleware/permission_middleware.js

export async function checkPermission(supabase, userId, organizationId, permissionCode) {
  if (!userId || !organizationId || !permissionCode) {
    return {
      allowed: false,
      reason: 'missing_parameters',
      message: 'User ID, organization ID, and permission code are required'
    }
  }
  
  try {
    // Call the unified permission check function
    const { data, error } = await supabase.rpc('unified_permission_check', {
      p_organization_id: organizationId,
      p_user_id: userId,
      p_permission_code: permissionCode
    })
    
    if (error) {
      return {
        allowed: false,
        reason: 'function_error',
        message: error.message
      }
    }
    
    return data
  } catch (error) {
    console.error('Permission check error:', error)
    
    return {
      allowed: false,
      reason: 'exception',
      message: 'An error occurred while checking permissions'
    }
  }
}
```

## Metadata Utility Functions

These utility functions help with metadata-driven API operations.

```javascript
// utils/metadata_utils.js

export async function getMetadataForEntity(supabase, entityType) {
  if (!entityType) {
    return null
  }
  
  try {
    const { data, error } = await supabase
      .from('metadata_registry')
      .select('*')
      .eq('table_name', entityType)
      .eq('is_active', true)
      .single()
    
    if (error || !data) {
      console.error('Error fetching metadata:', error)
      return null
    }
    
    // Parse JSON fields
    const metadata = {
      ...data,
      schema_definition: typeof data.schema_definition === 'string' 
        ? JSON.parse(data.schema_definition) 
        : data.schema_definition,
      api_config: typeof data.api_config === 'string' 
        ? JSON.parse(data.api_config) 
        : data.api_config,
      ui_config: typeof data.ui_config === 'string' 
        ? JSON.parse(data.ui_config) 
        : data.ui_config,
      permission_rules: typeof data.permission_rules === 'string' 
        ? JSON.parse(data.permission_rules) 
        : data.permission_rules,
      relationship_map: typeof data.relationship_map === 'string' 
        ? JSON.parse(data.relationship_map) 
        : data.relationship_map,
      workflow_triggers: typeof data.workflow_triggers === 'string' 
        ? JSON.parse(data.workflow_triggers) 
        : data.workflow_triggers
    }
    
    return metadata
  } catch (error) {
    console.error('Error processing metadata:', error)
    return null
  }
}
```

## Metadata Request Handlers

These handlers process metadata-driven CRUD operations.

```javascript
// utils/metadata_handlers.js

export async function handleMetadataGet(req, res, context) {
  const { user, supabase, metadata } = context
  const url = new URL(req.url)
  const path = url.pathname
  const pathParts = path.split('/').filter(Boolean)
  
  // Check if this is a single item request or a list request
  const isSingleItem = pathParts.length > 1 && pathParts[1]
  const itemId = isSingleItem ? pathParts[1] : null
  
  // Determine permission to check
  const permissionCode = isSingleItem ? `view:${metadata.table_name}` : `list:${metadata.table_name}`
  
  // Check permission
  const permissionResult = await checkPermission(
    supabase, 
    user.id, 
    user.organization_id,
    permissionCode
  )
  
  if (!permissionResult.allowed) {
    return res.json({
      error: 'permission_denied',
      message: permissionResult.message
    }, { status: 403 })
  }
  
  // Parse query parameters
  const limit = parseInt(url.searchParams.get('limit')) || metadata.api_config.pagination.default_limit
  const page = parseInt(url.searchParams.get('page')) || 1
  const sortField = url.searchParams.get('sort') || getDefaultSortField(metadata)
  const sortDirection = url.searchParams.get('direction') || getDefaultSortDirection(metadata, sortField)
  
  // Build query
  let query = supabase.from(metadata.table_name).select('*')
  
  // For single item request
  if (isSingleItem) {
    // Get the primary key field
    const primaryKeyField = getPrimaryKeyField(metadata)
    
    // Add where clause
    query = query.eq(primaryKeyField, itemId).single()
    
    // Execute query
    const { data, error } = await query
    
    if (error) {
      if (error.code === 'PGRST116') {
        return res.json({
          error: 'not_found',
          message: 'Item not found'
        }, { status: 404 })
      }
      
      return res.json({
        error: 'database_error',
        message: 'Error retrieving item',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      }, { status: 500 })
    }
    
    // Return data
    return res.json({
      data
    })
  }
  
  // For list request
  
  // Apply filters
  query = applyFiltersFromQueryParams(query, url.searchParams, metadata)
  
  // Apply sorting
  query = query.order(sortField, { ascending: sortDirection === 'asc' })
  
  // Apply pagination
  query = query.range((page - 1) * limit, page * limit - 1)
  
  // Execute query
  const { data, error, count } = await query
  
  if (error) {
    return res.json({
      error: 'database_error',
      message: 'Error retrieving items',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 })
  }
  
  // Get total count
  const { count: totalCount } = await supabase
    .from(metadata.table_name)
    .select('*', { count: 'exact', head: true })
  
  // Return data with pagination info
  return res.json({
    data,
    pagination: {
      page,
      limit,
      total: totalCount,
      pages: Math.ceil(totalCount / limit)
    },
    sorting: {
      field: sortField,
      direction: sortDirection
    }
  })
}

export async function handleMetadataPost(req, res, context) {
  const { user, supabase, metadata } = context
  
  // Check permission
  const permissionResult = await checkPermission(
    supabase, 
    user.id, 
    user.organization_id,
    `create:${metadata.table_name}`
  )
  
  if (!permissionResult.allowed) {
    return res.json({
      error: 'permission_denied',
      message: permissionResult.message
    }, { status: 403 })
  }
  
  // Parse request body
  const body = await req.json()
  
  // Validate required fields
  const validationErrors = validateRequiredFields(body, metadata)
  
  if (validationErrors.length > 0) {
    return res.json({
      error: 'validation_error',
      message: 'Validation failed',
      errors: validationErrors
    }, { status: 400 })
  }
  
  // Add audit fields
  const dataToInsert = {
    ...body,
    created_by: user.id,
    updated_by: user.id
  }
  
  // Execute insert
  const { data, error } = await supabase
    .from(metadata.table_name)
    .insert(dataToInsert)
    .select()
  
  if (error) {
    return res.json({
      error: 'database_error',
      message: 'Error creating item',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 })
  }
  
  // Return created item
  return res.json({
    success: true,
    message: 'Item created successfully',
    data
  })
}

export async function handleMetadataPut(req, res, context) {
  const { user, supabase, metadata } = context
  const url = new URL(req.url)
  const path = url.pathname
  const pathParts = path.split('/').filter(Boolean)
  
  // Check if ID is provided
  if (pathParts.length <= 1 || !pathParts[1]) {
    return res.json({
      error: 'invalid_request',
      message: 'Item ID is required'
    }, { status: 400 })
  }
  
  const itemId = pathParts[1]
  
  // Check permission
  const permissionResult = await checkPermission(
    supabase, 
    user.id, 
    user.organization_id,
    `update:${metadata.table_name}`
  )
  
  if (!permissionResult.allowed) {
    return res.json({
      error: 'permission_denied',
      message: permissionResult.message
    }, { status: 403 })
  }
  
  // Parse request body
  const body = await req.json()
  
  // Get the primary key field
  const primaryKeyField = getPrimaryKeyField(metadata)
  
  // Add audit fields
  const dataToUpdate = {
    ...body,
    updated_by: user.id,
    updated_at: new Date().toISOString()
  }
  
  // Remove primary key from update data
  delete dataToUpdate[primaryKeyField]
  
  // Execute update
  const { data, error } = await supabase
    .from(metadata.table_name)
    .update(dataToUpdate)
    .eq(primaryKeyField, itemId)
    .select()
  
  if (error) {
    return res.json({
      error: 'database_error',
      message: 'Error updating item',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 })
  }
  
  if (!data || data.length === 0) {
    return res.json({
      error: 'not_found',
      message: 'Item not found'
    }, { status: 404 })
  }
  
  // Return updated item
  return res.json({
    success: true,
    message: 'Item updated successfully',
    data: data[0]
  })
}

export async function handleMetadataDelete(req, res, context) {
  const { user, supabase, metadata } = context
  const url = new URL(req.url)
  const path = url.pathname
  const pathParts = path.split('/').filter(Boolean)
  
  // Check if ID is provided
  if (pathParts.length <= 1 || !pathParts[1]) {
    return res.json({
      error: 'invalid_request',
      message: 'Item ID is required'
    }, { status: 400 })
  }
  
  const itemId = pathParts[1]
  
  // Check permission
  const permissionResult = await checkPermission(
    supabase, 
    user.id, 
    user.organization_id,
    `delete:${metadata.table_name}`
  )
  
  if (!permissionResult.allowed) {
    return res.json({
      error: 'permission_denied',
      message: permissionResult.message
    }, { status: 403 })
  }
  
  // Get the primary key field
  const primaryKeyField = getPrimaryKeyField(metadata)
  
  // Get item before deletion
  const { data: item, error: getError } = await supabase
    .from(metadata.table_name)
    .select('*')
    .eq(primaryKeyField, itemId)
    .single()
  
  if (getError) {
    if (getError.code === 'PGRST116') {
      return res.json({
        error: 'not_found',
        message: 'Item not found'
      }, { status: 404 })
    }
    
    return res.json({
      error: 'database_error',
      message: 'Error retrieving item',
      details: process.env.NODE_ENV === 'development' ? getError.message : undefined
    }, { status: 500 })
  }
  
  // Execute delete
  const { error: deleteError } = await supabase
    .from(metadata.table_name)
    .delete()
    .eq(primaryKeyField, itemId)
  
  if (deleteError) {
    return res.json({
      error: 'database_error',
      message: 'Error deleting item',
      details: process.env.NODE_ENV === 'development' ? deleteError.message : undefined
    }, { status: 500 })
  }
  
  // Return success
  return res.json({
    success: true,
    message: 'Item deleted successfully',
    data: item
  })
}

// Helper functions

function getPrimaryKeyField(metadata) {
  const primaryKeyField = metadata.schema_definition.fields.find(field => field.primary_key)
  return primaryKeyField ? primaryKeyField.name : 'id'
}

function getDefaultSortField(metadata) {
  const defaultSort = metadata.api_config.sorting.find(sort => sort.default)
  return defaultSort ? defaultSort.field : getPrimaryKeyField(metadata)
}

function getDefaultSortDirection(metadata, field) {
  const sortConfig = metadata.api_config.sorting.find(sort => sort.field === field)
  return sortConfig && sortConfig.direction ? sortConfig.direction : 'asc'
}

function validateRequiredFields(data, metadata) {
  const errors = []
  
  metadata.schema_definition.fields.forEach(field => {
    if (field.required && !field.default && data[field.name] === undefined) {
      errors.push({
        field: field.name,
        message: `${field.name} is required`
      })
    }
  })
  
  return errors
}

function applyFiltersFromQueryParams(query, params, metadata) {
  let filteredQuery = query
  
  // Get available filters from metadata
  const availableFilters = metadata.api_config.filters || []
  
  // Process each filter parameter
  for (const [key, value] of params.entries()) {
    // Skip non-filter parameters
    if (['limit', 'page', 'sort', 'direction'].includes(key)) {
      continue
    }
    
    // Parse filter key to get field and operator
    const filterParts = key.split('.')
    const fieldName = filterParts[0]
    const operator = filterParts.length > 1 ? filterParts[1] : 'eq'
    
    // Check if this is a valid filter
    const filterDef = availableFilters.find(f => f.field === fieldName)
    
    if (!filterDef || !filterDef.operators.includes(operator)) {
      continue
    }
    
    // Apply filter based on operator
    switch (operator) {
      case 'eq':
        filteredQuery = filteredQuery.eq(fieldName, value)
        break
      case 'neq':
        filteredQuery = filteredQuery.neq(fieldName, value)
        break
      case 'gt':
        filteredQuery = filteredQuery.gt(fieldName, value)
        break
      case 'gte':
        filteredQuery = filteredQuery.gte(fieldName, value)
        break
      case 'lt':
        filteredQuery = filteredQuery.lt(fieldName, value)
        break
      case 'lte':
        filteredQuery = filteredQuery.lte(fieldName, value)
        break
      case 'like':
        filteredQuery = filteredQuery.like(fieldName, `%${value}%`)
        break
      case 'ilike':
        filteredQuery = filteredQuery.ilike(fieldName, `%${value}%`)
        break
      case 'in':
        const values = value.split(',')
        filteredQuery = filteredQuery.in(fieldName, values)
        break
      case 'is':
        if (value === 'null') {
          filteredQuery = filteredQuery.is(fieldName, null)
        } else if (value === 'true') {
          filteredQuery = filteredQuery.is(fieldName, true)
        } else if (value === 'false') {
          filteredQuery = filteredQuery.is(fieldName, false)
        }
        break
      case 'contains':
        if (value.startsWith('{') && value.endsWith('}')) {
          try {
            const jsonValue = JSON.parse(value)
            filteredQuery = filteredQuery.contains(fieldName, jsonValue)
          } catch (e) {
            // Invalid JSON, skip filter
            console.warn(`Invalid JSON in contains filter: ${value}`)
          }
        } else {
          filteredQuery = filteredQuery.contains(fieldName, [value])
        }
        break
    }
  }
  
  return filteredQuery
}
```

## Deployment

To deploy the subscription management edge functions, follow these steps:

1. Create the necessary files in your Supabase Edge Functions project:
   - `assistant_router.js` - Main router
   - `handlers/subscription_handler.js` - Subscription handler
   - `middleware/context_middleware.js` - Context middleware
   - `middleware/permission_middleware.js` - Permission middleware
   - `utils/metadata_utils.js` - Metadata utilities
   - `utils/metadata_handlers.js` - Metadata handlers

2. Install required dependencies:
   ```bash
   npm install @supabase/supabase-js
   ```

3. Deploy the edge function:
   ```bash
   supabase functions deploy assistant_router
   ```

4. Set up environment variables:
   ```bash
   supabase secrets set SUPABASE_URL=your-supabase-url
   supabase secrets set SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   ```

## Testing

To test the subscription management edge functions, you can use the following examples:

### Example 1: Get Active Subscription

```javascript
// Client-side code
const { data, error } = await supabase.functions.invoke('assistant_router', {
  url: '/subscription/active',
  method: 'GET'
})

if (error) {
  console.error('Error:', error)
} else {
  console.log('Active subscription:', data)
}
```

### Example 2: Change Subscription Plan

```javascript
// Client-side code
const { data, error } = await supabase.functions.invoke('assistant_router', {
  url: '/subscription/change-plan',
  method: 'POST',
  body: {
    plan_id: 'target-plan-id',
    reason: 'Upgrading to access more features'
  }
})

if (error) {
  console.error('Error:', error)
} else {
  console.log('Plan change result:', data)
}
```

### Example 3: Get Available Plans

```javascript
// Client-side code
const { data, error } = await supabase.functions.invoke('assistant_router', {
  url: '/subscription/plans',
  method: 'GET'
})

if (error) {
  console.error('Error:', error)
} else {
  console.log('Available plans:', data)
}
```

## Conclusion

This document has outlined how to implement the edge function components for ArionComply's subscription management system. By following the metadata-driven approach, the edge functions can handle all subscription-related API requests while maintaining proper authentication, permission checking, and business logic.

Key benefits of this approach:

1. **Centralized Logic**: All subscription management logic is in one place
2. **Metadata-Driven**: Changes to behavior can be made by updating metadata rather than code
3. **Secure**: Proper authentication and permission checking is enforced
4. **Extensible**: New endpoints can be added easily

## Next Steps

After implementing these edge functions, the next step is to develop the React UI components as described in `arioncomply-v1/docs/Frontend/Subscription-Management-React-Components.md`. These components will interact with the edge functions to provide a complete subscription management user interface.
