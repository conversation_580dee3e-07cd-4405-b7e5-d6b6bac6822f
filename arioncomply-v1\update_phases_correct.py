#!/usr/bin/env python3

# Corrected phase mapping based on actual phase scope
# MVP-Assessment-App: Only chat/avatar for assessments, onboarding questions, platform info, and compliance posture reports

phase_mapping = {
    # MVP-Assessment-App: Only basic conversational assessment (8 requirements)
    3: "MVP-Assessment-App",   # Conversational AI Assessment Interface (core feature)
    4: "MVP-Assessment-App",   # Multi-Modal Input Processing (basic voice/text for chat)
    5: "MVP-Assessment-App",   # Deterministic Retrieval and Explainable AI (for answering questions)
    10: "MVP-Assessment-App",  # Phased Deployment Strategy (assessment app phase)
    11: "MVP-Assessment-App",  # Advanced Analytics and Reporting (basic compliance posture reports)
    23: "MVP-Assessment-App",  # Planning, Scheduling, and Guidance (basic guidance through chat)
    50: "MVP-Assessment-App",  # Compliance Knowledge Base and Learning (knowledge for chat responses)
    47: "MVP-Assessment-App",  # Multi-Language and Localization Support (basic i18n for chat)
    
    # MVP-Demo-Light-App: Demo capabilities with preset scenarios (15 requirements)
    1: "MVP-Demo-Light-App",   # Metadata-Driven Architecture (needed for demo scenarios)
    2: "MVP-Demo-Light-App",   # Multi-Framework Compliance Support (demo multiple frameworks)
    6: "MVP-Demo-Light-App",   # Comprehensive Document Generation (demo document creation)
    8: "MVP-Demo-Light-App",   # Enterprise Security and Multi-Tenancy (demo security)
    9: "MVP-Demo-Light-App",   # Task and Workflow Management (demo workflows)
    13: "MVP-Demo-Light-App",  # Edge Function Orchestration (technical foundation for demos)
    14: "MVP-Demo-Light-App",  # UI/UX Standards and Responsiveness (enhanced UI for demos)
    15: "MVP-Demo-Light-App",  # AI Backend Operations and Model Lifecycle (AI infrastructure)
    21: "MVP-Demo-Light-App",  # Compliance Registers and Trackers (demo data management)
    22: "MVP-Demo-Light-App",  # Automated Document Retrieval and Creation (demo automation)
    25: "MVP-Demo-Light-App",  # Asset Management (demo asset tracking)
    26: "MVP-Demo-Light-App",  # Risk Management (demo risk assessment)
    44: "MVP-Demo-Light-App",  # Mobile and Offline Capabilities (demo mobile features)
    45: "MVP-Demo-Light-App",  # API Management and Integration Hub (demo integrations)
    48: "MVP-Demo-Light-App",  # Backup, Recovery, and Data Portability (demo data management)
    
    # MVP-Pilot: Real customer deployment (30 requirements)
    7: "MVP-Pilot",   # Real-Time Compliance Monitoring
    12: "MVP-Pilot",  # Integration and Extensibility
    16: "MVP-Pilot",  # Workflow Lifecycle and Governance
    17: "MVP-Pilot",  # Workflow Engine Reliability
    18: "MVP-Pilot",  # Human-in-the-Loop and Approvals
    19: "MVP-Pilot",  # Workflow Templates, Import/Export, and Reuse
    20: "MVP-Pilot",  # Certification Preparation Flows
    24: "MVP-Pilot",  # Incident and Breach Management
    27: "MVP-Pilot",  # Vendor and Third-Party Management
    28: "MVP-Pilot",  # Data Subject Rights and Consent Management
    29: "MVP-Pilot",  # DPIAs and Records of Processing Activities (RoPA)
    30: "MVP-Pilot",  # AI Act Risk Classification and Monitoring
    31: "MVP-Pilot",  # Vulnerability and Patch Management
    32: "MVP-Pilot",  # Business Continuity and Disaster Recovery (BCP/DRP)
    33: "MVP-Pilot",  # Training and Awareness Management
    34: "MVP-Pilot",  # Policy Lifecycle Management
    35: "MVP-Pilot",  # Regulatory Reporting and Notifications
    36: "MVP-Pilot",  # ISMS/PIMS Process Management
    37: "MVP-Pilot",  # Policy and Record Control
    38: "MVP-Pilot",  # GDPR/27701 Processor-Controller Governance
    39: "MVP-Pilot",  # Vendor and Supply-Chain Security
    40: "MVP-Pilot",  # International Data Transfers
    42: "MVP-Pilot",  # Audit Management and Evidence Collection
    43: "MVP-Pilot",  # Compliance Dashboard and Executive Reporting
    46: "MVP-Pilot",  # Compliance Automation Engine
    49: "MVP-Pilot",  # Performance Monitoring and Optimization
    51: "MVP-Pilot",  # Third-Party Risk Assessment Automation
    52: "MVP-Pilot",  # Compliance Cost Management and ROI Tracking
    53: "MVP-Pilot",  # Incident Response Coordination
    58: "MVP-Pilot",  # Cross-Border Data Governance
    
    # Production: Full enterprise deployment (32 requirements)
    41: "Production", # Vulnerability, Patch, and Logging Management (enhanced)
    54: "Production", # Supplier Diversity and ESG Compliance
    55: "Production", # Blockchain and Immutable Audit Trails
    56: "Production", # AI-Powered Compliance Insights
    57: "Production", # Regulatory Change Management
    59: "Production", # Compliance Maturity Assessment
    60: "Production", # Compliance Simulation and Testing
    61: "Production", # Stakeholder Communication and Collaboration
    62: "Production", # Compliance Innovation and Emerging Technologies
    63: "Production", # End-to-End Auditability (enhanced)
    64: "Production", # System-Wide Transparency (enhanced)
    65: "Production", # Top-to-Bottom Traceability (enhanced)
    66: "Production", # Cross-System Data Lineage and Provenance (enhanced)
    67: "Production", # Regulatory Compliance Traceability (enhanced)
    68: "Production", # Advanced Compliance Analytics Platform
    69: "Production", # Enterprise Integration and Ecosystem Management
    70: "Production", # Compliance Intelligence and Predictive Analytics
    71: "Production", # Advanced Workflow Orchestration and Automation
    72: "Production", # Enterprise Security and Compliance Operations Center
    73: "Production", # Multi-Tenant Enterprise Management
    74: "Production", # Advanced Reporting and Business Intelligence
    75: "Production", # Compliance Data Warehouse and Analytics
    76: "Production", # Advanced AI and Machine Learning Platform
    77: "Production", # Enterprise API Gateway and Microservices
    78: "Production", # Performance Optimization and Scalability
    79: "Production", # Advanced Security and Threat Management
    80: "Production", # Compliance Ecosystem and Marketplace
    81: "Production", # Platform Customization and Extensibility
    82: "Production", # Testing and Quality Assurance Platform
    83: "Production", # Platform Analytics and Business Intelligence
    84: "Production", # Platform Security and Compliance Operations
    85: "Production", # Platform Maintenance and Operations
}

# Read the file
with open('.kiro/specs/standards-compliance-platform/requirements-fixed.md', 'r') as f:
    content = f.read()

# Remove existing phase lines and add correct ones
import re

# First, remove any existing **Phase:** lines
content = re.sub(r'\*\*Phase:\*\* [^\n]+\n', '', content)

def add_phase_to_requirement(match):
    req_num = int(match.group(1))
    req_title = match.group(2)
    phase = phase_mapping.get(req_num, "Production")  # Default to Production if not specified
    return f'### Requirement {req_num}: {req_title}\n**Phase:** {phase}'

# Pattern to match requirement headers
pattern = r'### Requirement (\d+): ([^\n]+)'
updated_content = re.sub(pattern, add_phase_to_requirement, content)

# Write back to file
with open('.kiro/specs/standards-compliance-platform/requirements-fixed.md', 'w') as f:
    f.write(updated_content)

print("Corrected phase mappings added to all requirements!")
print(f"MVP-Assessment-App: {sum(1 for p in phase_mapping.values() if p == 'MVP-Assessment-App')} requirements")
print(f"MVP-Demo-Light-App: {sum(1 for p in phase_mapping.values() if p == 'MVP-Demo-Light-App')} requirements") 
print(f"MVP-Pilot: {sum(1 for p in phase_mapping.values() if p == 'MVP-Pilot')} requirements")
print(f"Production: {sum(1 for p in phase_mapping.values() if p == 'Production')} requirements")
print(f"Total mapped: {len(phase_mapping)} requirements")