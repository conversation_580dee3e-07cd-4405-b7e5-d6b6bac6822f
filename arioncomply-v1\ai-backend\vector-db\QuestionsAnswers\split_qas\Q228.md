id: Q228
query: >-
  What’s the difference between meeting minimum requirements vs. best practices?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/A.18.2"
overlap_ids: []
capability_tags:
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Compliance with Legal Requirements"
    id: "ISO27001:2022/A.18.2"
    locator: "Annex A.18.2"
ui:
  cards_hint:
    - "Maturity model"
  actions:
    - type: "open_register"
      target: "maturity_models"
      label: "View Maturity Levels"
output_mode: "both"
graph_required: false
notes: "Minimum = must-do; best = aspirational for resilience and efficiency"
---
### 228) What’s the difference between meeting minimum requirements vs. best practices?

**Standard terms**  
- **Legal/compliance (A.18.2):** must satisfy mandatory obligations.

**Plain-English answer**  
**Minimum requirements** are the non-negotiable legal or standard controls. **Best practices** go beyond—implement advanced monitoring, automation, and continuous testing to optimize security and reduce operational overhead.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.18.2

**Why it matters**  
Best practices drive competitive advantage and deeper risk reduction.

**Do next in our platform**  
- Compare your control set against **Maturity Models** register.  
- Identify gaps at “best practice” level for roadmap inclusion.

**How our platform will help**  
- **[Report]** Maturity-heatmap by control family.  
- **[Dashboard]** Tracks progress toward best-practice targets.

**Likely follow-ups**  
- What’s the ROI of moving from “compliant” to “optimized”?

**Sources**  
- ISO/IEC 27001:2022 Annex A.18.2
