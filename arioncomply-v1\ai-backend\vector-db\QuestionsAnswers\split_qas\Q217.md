```yaml
id: Q217
question: What’s the process for renewing certifications and how often does it happen?
packs: ["ISO17021-1:2015","ISO27001:2022"]
primary_ids: ["ISO17021-1:2015/Certification.Cycle","ISO27001:2022/Cl.9","ISO27001:2022/Cl.10"]
overlap_ids: ["IAF-MD5/Guidance"]
capability_tags: ["Planner","Reminder","Workflow","Report","Evidence-Guided"]
ui:
  actions:
    - target: "planner"
      action: "open"
      args: { template: "iso_cycle_three_years" }
    - target: "workflow"
      action: "open"
      args: { key: "surveillance_readiness" }
cards_hint:
  - Annual surveillance; recert before 3-year expiry.
  - Keep evidence fresh to avoid overrun.
  - Back-plan internal audit & 9.3.
graph_required: false
```

### 217) What’s the process for renewing certifications and how often does it happen?

**Standard term(s)**

- **Certification cycle:** typically **3 years** with **annual surveillance**; **recertification** at cycle end. **[CB POLICY VARIES]**

**Plain-English answer**\
Expect **annual surveillance audits** and a **recertification** audit before year three to keep the certificate active.

**Applies to**

- **Primary:** ISO/IEC **17021-1**; ISO/IEC 27001 **Cl. 9–10**.
- **Also relevant/Overlaps:** IAF MD 5 **[CB POLICY VARIES]**.

**Why it matters**\
Missing windows risks **lapse**.

**Do next in our platform**

- Build a **3-year calendar**: evidence refresh → internal audit → 9.3 → surveillance/recert readiness.

**How our platform will help**

- **[Planner] [Reminder] [Workflow] [Report] [Evidence-Guided]** — Timelines, checklists, and packaged evidence for your CB.

**Likely follow-ups**

- “How many audit days?” → Depends on scope/size (IAF MD 5) **[CB POLICY VARIES]**.

**Sources**

- ISO/IEC **17021-1**; ISO/IEC 27001 **Cl. 9–10**; IAF MD 5 **[CB POLICY VARIES]**.
