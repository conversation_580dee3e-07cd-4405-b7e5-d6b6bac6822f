-- File: arioncomply-v1/db/migrations/0016_ui_shortcuts.sql
-- Migration 0016: UI slash shortcuts (org-scoped)
-- Purpose: Store command shortcuts like /goto with action/target/url
-- Creates: ui_shortcuts; unique index on (org_id, shortcut)
-- Policies: ENABLE RLS; org-scoped SELECT/INSERT/UPDATE; admin bypass via app_has_role('admin')
-- Verify: SELECT * FROM ui_shortcuts LIMIT 5;

BEGIN;

CREATE TABLE IF NOT EXISTS ui_shortcuts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL,
  shortcut text NOT NULL,
  description text,
  action_type text NOT NULL,
  target text,
  url text,
  enabled boolean NOT NULL DEFAULT true,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

CREATE UNIQUE INDEX IF NOT EXISTS idx_ui_shortcuts_org_shortcut
  ON ui_shortcuts (org_id, shortcut);
CREATE INDEX IF NOT EXISTS idx_ui_shortcuts_org_enabled
  ON ui_shortcuts (org_id, enabled);

ALTER TABLE ui_shortcuts ENABLE ROW LEVEL SECURITY;

CREATE POLICY ui_shortcuts_select ON ui_shortcuts
  FOR SELECT USING (org_id = app_current_org_id() OR app_has_role('admin'));

CREATE POLICY ui_shortcuts_insert ON ui_shortcuts
  FOR INSERT WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));

CREATE POLICY ui_shortcuts_update ON ui_shortcuts
  FOR UPDATE USING (org_id = app_current_org_id() OR app_has_role('admin'))
  WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));

COMMIT;

