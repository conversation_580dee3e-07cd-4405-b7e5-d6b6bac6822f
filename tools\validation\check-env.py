#!/usr/bin/env python3
# File: tools/validation/check-env.py
# File Description: Validate .env and .env.example consistency and formatting
# Purpose: Ensure .env.example documents required keys; detect format errors and duplicates

import re
import sys
from pathlib import Path
from typing import Dict, List, Tuple

ROOT = Path(__file__).resolve().parents[2]
ENV_PATH = ROOT / ".env"
EXAMPLE_PATH = ROOT / ".env.example"

LINE_RE = re.compile(r"^(?:export\s+)?([A-Za-z_][A-Za-z0-9_]*)\s*=\s*(.*)$")


def parse_env_file(path: Path) -> Tuple[Dict[str, str], List[str], List[str]]:
    """Return (vars, invalid_lines, duplicate_keys) for the given .env-like file."""
    env: Dict[str, str] = {}
    invalid: List[str] = []
    dupes: List[str] = []
    if not path.exists():
        return env, invalid, dupes
    try:
        lines = path.read_text(encoding="utf-8", errors="ignore").splitlines()
    except Exception:
        return env, ["<unable to read file>"], dupes
    for i, raw in enumerate(lines, start=1):
        line = raw.strip()
        if not line or line.startswith('#'):
            continue
        m = LINE_RE.match(line)
        if not m:
            invalid.append(f"{path.name}:{i}: {raw}")
            continue
        key, val = m.group(1), m.group(2)
        # strip surrounding quotes if present
        if (val.startswith('"') and val.endswith('"')) or (val.startswith("'") and val.endswith("'")):
            val = val[1:-1]
        if key in env:
            dupes.append(f"{path.name}:{i}: duplicate key {key}")
        env[key] = val
    return env, invalid, dupes


def looks_like_placeholder(v: str) -> bool:
    s = v.strip().lower()
    if s == "" or s in {"changeme", "change_me", "todo", "example", "<value>", "<secret>", "<token>", "your_value"}:
        return True
    if s.startswith("<") and s.endswith(">"):
        return True
    if s.startswith("your_") or s.startswith("example_") or s.startswith("placeholder"):
        return True
    return False


def main() -> int:
    env, env_invalid, env_dupes = parse_env_file(ENV_PATH)
    ex, ex_invalid, ex_dupes = parse_env_file(EXAMPLE_PATH)

    hard_fail = 0
    if not EXAMPLE_PATH.exists():
        print(".env.example missing: create it to document required variables")
        hard_fail += 1
    if not ENV_PATH.exists():
        print(".env missing: create local environment (.env is required locally)")
        hard_fail += 1

    issues = 0
    if env_invalid or ex_invalid:
        print("Invalid lines:")
        for l in ex_invalid + env_invalid:
            print(f"  {l}")
        issues += len(ex_invalid) + len(env_invalid)
    if env_dupes or ex_dupes:
        print("Duplicate keys:")
        for l in ex_dupes + env_dupes:
            print(f"  {l}")
        issues += len(ex_dupes) + len(env_dupes)

    # Compare key sets only if example exists
    missing_in_env = []
    extra_in_env = []
    suspicious_values = []
    if ex:
        ex_keys = set(ex.keys())
        env_keys = set(env.keys())
        missing_in_env = sorted(ex_keys - env_keys)
        extra_in_env = sorted(env_keys - ex_keys)
        for k, v in ex.items():
            if v and not looks_like_placeholder(v):
                suspicious_values.append(k)

    if missing_in_env:
        print("Keys required by .env.example but missing in .env:")
        for k in missing_in_env:
            print(f"  {k}")
        issues += len(missing_in_env)
    if extra_in_env:
        print("Keys present in .env but not documented in .env.example:")
        for k in extra_in_env[:20]:
            print(f"  {k}")
        # Not failing on extras; informational
    if suspicious_values:
        print(".env.example contains non-placeholder values (secrets must NOT be in .env.example):")
        for k in suspicious_values[:50]:
            print(f"  {k}")
        issues += len(suspicious_values)

    issues += hard_fail

    if issues:
        print("")
        print("How to fix / where to store secrets:")
        print("- .env.example: Document required keys with placeholder values only (e.g., YOUR_API_KEY). Never real secrets.")
        print("- .env: Local-only secrets file (gitignored). Create it at repo root with real values.")
        print("- Production: Store secrets in your platform’s secret manager (e.g., Supabase project secrets, cloud env vars).")
        print("- Do not commit .env or any real secrets to the repository.")
        print(f"Env check failed with {issues} issue(s)")
        return 1
    print("Env files look OK")
    return 0


if __name__ == "__main__":
    sys.exit(main())
