# Unified Subscription Management: Metadata-Driven Implementation

## Overview

This document provides a complete implementation of ArionComply's subscription management system using the metadata-driven architecture. The design treats all subscription types (including demo) as regular subscription plans with different configurations, integrating seamlessly with role-based access control (RBAC).

## Prerequisites

Before implementing this metadata-driven approach, ensure the following:

1. The database schema from `arioncomply-v1/docs/ApplicationDatabase/Unified-Subscription-Management-Schema-Design.md` has been applied
2. The database functions and triggers from `arioncomply-v1/docs/ApplicationDatabase/Subscription-Management-Functions-Triggers.md` have been implemented
3. The metadata registry schema from `arioncomply-v1/docs/ApplicationDatabase/DataDriveUISchemaAndFunctions/arioncomply-metadata-registry-schema.md` has been created

## Metadata Registry Entries

The metadata registry is the central repository that defines how the subscription management system behaves. Each table in the system has a corresponding entry in the metadata_registry table.

### Sub Plans Metadata Registry Entry

```sql
INSERT INTO metadata_registry (
  table_name,
  display_name,
  description,
  schema_definition,
  api_config,
  ui_config,
  permission_rules,
  relationship_map,
  workflow_triggers,
  is_active,
  is_system_table,
  created_at
) VALUES (
  'sub_plans',
  'Subscription Plans',
  'Defines available subscription plans including demo and paid tiers',
  '{
    "fields": [
      {
        "name": "plan_id",
        "type": "uuid",
        "primary_key": true,
        "default": "uuid_generate_v4()",
        "description": "Unique identifier"
      },
      {
        "name": "plan_code",
        "type": "text",
        "required": true,
        "unique": true,
        "min_length": 3,
        "max_length": 50,
        "description": "Machine-readable identifier (e.g., demo-basic, enterprise-standard)"
      },
      {
        "name": "plan_name",
        "type": "text",
        "required": true,
        "min_length": 3,
        "max_length": 100,
        "description": "Human-readable name shown in UI"
      },
      {
        "name": "plan_description",
        "type": "text",
        "max_length": 1000,
        "description": "Detailed description for marketing/sales"
      },
      {
        "name": "plan_type",
        "type": "text",
        "required": true,
        "enum_values": ["demo", "assessment", "basic", "professional", "enterprise"],
        "description": "Type of subscription plan"
      },
      {
        "name": "plan_is_active",
        "type": "boolean",
        "default": true,
        "description": "Whether this plan can be subscribed to"
      },
      {
        "name": "plan_is_public",
        "type": "boolean",
        "default": true,
        "description": "Whether plan appears in public pricing"
      },
      {
        "name": "plan_duration_days",
        "type": "integer",
        "min": 1,
        "description": "Time-limited duration for any plan type (not just demos)"
      },
      {
        "name": "plan_billing_cycle",
        "type": "text",
        "enum_values": ["monthly", "quarterly", "annual", "one_time"],
        "description": "Billing frequency"
      },
      {
        "name": "plan_price_amount",
        "type": "decimal",
        "precision": 10,
        "scale": 2,
        "min": 0,
        "description": "Base price before taxes/discounts"
      },
      {
        "name": "plan_price_currency",
        "type": "text",
        "length": 3,
        "default": "USD",
        "description": "Currency code"
      },
      {
        "name": "plan_user_limit",
        "type": "integer",
        "min": 0,
        "description": "Maximum allowed users"
      },
      {
        "name": "plan_storage_limit_gb",
        "type": "integer",
        "min": 0,
        "description": "Storage quota in gigabytes"
      },
      {
        "name": "plan_configuration",
        "type": "jsonb",
        "description": "Flexible JSON for all plan-specific settings, incl. feature limits",
        "json_schema": {
          "type": "object",
          "properties": {
            "feature_limits": {
              "type": "object",
              "properties": {
                "policies": { "type": "integer", "minimum": 0 },
                "controls": { "type": "integer", "minimum": 0 },
                "risks": { "type": "integer", "minimum": 0 },
                "documents": { "type": "integer", "minimum": 0 }
              }
            },
            "feature_access": {
              "type": "object",
              "properties": {
                "advanced_reporting": { "type": "boolean" },
                "risk_management": { "type": "boolean" },
                "audit_trails": { "type": "boolean" },
                "custom_templates": { "type": "boolean" }
              }
            },
            "ui_customization": {
              "type": "object",
              "properties": {
                "custom_branding": { "type": "boolean" },
                "dashboard_customization": { "type": "boolean" }
              }
            }
          }
        }
      },
      {
        "name": "organization_id",
        "type": "uuid",
        "references": {
          "table": "organizations",
          "field": "org_id",
          "on_delete": "CASCADE"
        },
        "description": "Organization that owns this plan (null for system plans)"
      },
      {
        "name": "created_at",
        "type": "timestamptz",
        "default": "NOW()",
        "description": "When this record was created"
      },
      {
        "name": "updated_at",
        "type": "timestamptz",
        "default": "NOW()",
        "description": "When this record was last updated"
      },
      {
        "name": "created_by",
        "type": "uuid",
        "references": {
          "table": "users",
          "field": "user_id",
          "on_delete": "SET NULL"
        },
        "description": "User who created this record"
      },
      {
        "name": "updated_by",
        "type": "uuid",
        "references": {
          "table": "users",
          "field": "user_id",
          "on_delete": "SET NULL"
        },
        "description": "User who last updated this record"
      }
    ],
    "indexes": [
      {
        "name": "idx_sub_plans_code",
        "fields": ["plan_code"],
        "unique": false
      },
      {
        "name": "idx_sub_plans_type",
        "fields": ["plan_type"],
        "unique": false
      },
      {
        "name": "idx_sub_plans_active_public",
        "fields": ["plan_is_active", "plan_is_public"],
        "unique": false
      },
      {
        "name": "idx_sub_plans_organization",
        "fields": ["organization_id"],
        "unique": false
      }
    ],
    "constraints": [
      {
        "name": "valid_plan_configuration",
        "check": "plan_configuration IS NULL OR jsonb_typeof(plan_configuration) = ''object''",
        "description": "Ensures plan_configuration is a valid JSON object"
      }
    ]
  }',  -- End of schema_definition
  '{
    "resource_name": "subscription_plans",
    "enable_crud": true,
    "batch_operations": true,
    "max_batch_size": 10,
    "pagination": {
      "default_limit": 20,
      "max_limit": 50,
      "cursor_based": true
    },
    "endpoints": [
      {
        "path": "/subscription_plans",
        "methods": ["GET", "POST"],
        "description": "List and create subscription plans"
      },
      {
        "path": "/subscription_plans/{id}",
        "methods": ["GET", "PUT", "PATCH", "DELETE"],
        "description": "Get, update, or delete a specific subscription plan"
      },
      {
        "path": "/subscription_plans/public",
        "methods": ["GET"],
        "description": "Get all public subscription plans"
      },
      {
        "path": "/subscription_plans/by_type/{type}",
        "methods": ["GET"],
        "description": "Get plans by type (e.g., demo, professional)"
      }
    ],
    "filters": [
      {
        "field": "plan_name",
        "operators": ["eq", "contains", "startsWith"]
      },
      {
        "field": "plan_code",
        "operators": ["eq", "in"]
      },
      {
        "field": "plan_type",
        "operators": ["eq", "in"]
      },
      {
        "field": "plan_is_active",
        "operators": ["eq"]
      },
      {
        "field": "plan_is_public",
        "operators": ["eq"]
      },
      {
        "field": "plan_price_amount",
        "operators": ["eq", "gt", "lt", "between"]
      },
      {
        "field": "plan_configuration",
        "operators": ["jsonContains"]
      }
    ],
    "sorting": [
      {"field": "plan_name", "default": false},
      {"field": "plan_price_amount", "default": true, "direction": "asc"},
      {"field": "created_at", "default": false}
    ],
    "response_fields": {
      "default": [
        "plan_id", "plan_code", "plan_name", "plan_type", "plan_is_active", 
        "plan_is_public", "plan_price_amount", "plan_price_currency"
      ],
      "detailed": [
        "plan_id", "plan_code", "plan_name", "plan_description", "plan_type",
        "plan_is_active", "plan_is_public", "plan_duration_days", "plan_billing_cycle",
        "plan_price_amount", "plan_price_currency", "plan_user_limit",
        "plan_storage_limit_gb", "plan_configuration", "created_at", "updated_at"
      ],
      "minimal": ["plan_id", "plan_name", "plan_type"]
    },
    "hooks": {
      "before_create": ["validate_plan_data", "set_plan_defaults"],
      "after_create": ["log_plan_creation"],
      "before_update": ["validate_plan_data", "check_plan_in_use"],
      "after_update": ["log_plan_update", "update_dependent_subscriptions"],
      "before_delete": ["check_plan_in_use"],
      "after_delete": ["log_plan_deletion"]
    }
  }',  -- End of api_config
  '{
    "list_view": {
      "default_columns": [
        {"field": "plan_name", "width": "25%"},
        {"field": "plan_type", "width": "15%", "formatter": "planTypeBadge"},
        {"field": "plan_price_amount", "width": "15%", "formatter": "currency"},
        {"field": "plan_billing_cycle", "width": "15%"},
        {"field": "plan_is_active", "width": "10%", "formatter": "boolean"},
        {"field": "plan_is_public", "width": "10%", "formatter": "boolean"},
        {"field": "created_at", "width": "10%", "formatter": "relativeDate"}
      ],
      "actions": [
        {"name": "view", "icon": "eye", "permission": "subscription.view"},
        {"name": "edit", "icon": "edit", "permission": "subscription.edit"},
        {"name": "delete", "icon": "trash", "permission": "subscription.delete"}
      ],
      "filters": [
        {"field": "plan_type", "type": "select", "options": [
          {"value": "demo", "label": "Demo"},
          {"value": "assessment", "label": "Assessment"},
          {"value": "basic", "label": "Basic"},
          {"value": "professional", "label": "Professional"},
          {"value": "enterprise", "label": "Enterprise"}
        ]},
        {"field": "plan_is_active", "type": "boolean"},
        {"field": "plan_is_public", "type": "boolean"},
        {"field": "plan_price_amount", "type": "range"}
      ],
      "default_sort": {"field": "plan_price_amount", "direction": "asc"}
    },
    "detail_view": {
      "layout": "tabs",
      "sections": [
        {
          "id": "basic",
          "label": "Basic Information",
          "fields": [
            {"field": "plan_name", "size": "large"},
            {"field": "plan_code", "size": "medium"},
            {"field": "plan_description", "size": "large", "control": "textarea"},
            {"field": "plan_type", "size": "medium", "control": "select", "options": [
              {"value": "demo", "label": "Demo"},
              {"value": "assessment", "label": "Assessment"},
              {"value": "basic", "label": "Basic"},
              {"value": "professional", "label": "Professional"},
              {"value": "enterprise", "label": "Enterprise"}
            ]},
            {"field": "plan_is_active", "size": "small", "control": "switch"},
            {"field": "plan_is_public", "size": "small", "control": "switch"}
          ]
        },
        {
          "id": "billing",
          "label": "Billing Details",
          "fields": [
            {"field": "plan_price_amount", "size": "medium", "control": "currency"},
            {"field": "plan_price_currency", "size": "small", "control": "select", "options": [
              {"value": "USD", "label": "USD"},
              {"value": "EUR", "label": "EUR"},
              {"value": "GBP", "label": "GBP"}
            ]},
            {"field": "plan_billing_cycle", "size": "medium", "control": "select", "options": [
              {"value": "monthly", "label": "Monthly"},
              {"value": "quarterly", "label": "Quarterly"},
              {"value": "annual", "label": "Annual"},
              {"value": "one_time", "label": "One-time"}
            ]},
            {"field": "plan_duration_days", "size": "medium", "control": "number", "min": 1, "description": "For time-limited plans"}
          ]
        },
        {
          "id": "limits",
          "label": "Resource Limits",
          "fields": [
            {"field": "plan_user_limit", "size": "medium", "control": "number", "min": 1},
            {"field": "plan_storage_limit_gb", "size": "medium", "control": "number", "min": 0},
            {"field": "plan_configuration", "size": "full", "control": "jsonEditor"}
          ]
        },
        {
          "id": "roles",
          "label": "Available Roles",
          "component": "PlanRolesManager",
          "props": {
            "planIdField": "plan_id"
          }
        },
        {
          "id": "metadata",
          "label": "Metadata",
          "fields": [
            {"field": "created_by", "size": "medium", "control": "userReference", "readOnly": true},
            {"field": "created_at", "size": "medium", "control": "dateTime", "readOnly": true},
            {"field": "updated_by", "size": "medium", "control": "userReference", "readOnly": true},
            {"field": "updated_at", "size": "medium", "control": "dateTime", "readOnly": true}
          ]
        }
      ],
      "related_entities": [
        {
          "entity": "org_subscriptions",
          "label": "Subscriptions",
          "relationship": "plan_subscriptions",
          "filter": {"field": "sub_plan_id", "value": "{plan_id}"}
        }
      ],
      "actions": [
        {"name": "edit", "label": "Edit Plan", "icon": "edit", "permission": "subscription.edit"},
        {"name": "duplicate", "label": "Duplicate", "icon": "copy", "permission": "subscription.create"},
        {"name": "delete", "label": "Delete", "icon": "trash", "permission": "subscription.delete", "confirmation": {
          "title": "Delete Subscription Plan",
          "message": "Are you sure you want to delete this subscription plan? This cannot be undone."
        }}
      ]
    },
    "create_view": {
      "layout": "tabs",
      "inherit_from": "detail_view",
      "actions": [
        {"name": "save", "label": "Create Plan", "icon": "save", "primary": true},
        {"name": "save_and_new", "label": "Save and New", "icon": "plus"},
        {"name": "cancel", "label": "Cancel", "icon": "x"}
      ]
    },
    "edit_view": {
      "layout": "tabs",
      "inherit_from": "detail_view",
      "actions": [
        {"name": "save", "label": "Save Changes", "icon": "save", "primary": true},
        {"name": "cancel", "label": "Cancel", "icon": "x"}
      ]
    },
    "form_validation": {
      "plan_name": [
        {"rule": "required", "message": "Plan name is required"},
        {"rule": "min", "value": 3, "message": "Plan name must be at least 3 characters"},
        {"rule": "max", "value": 100, "message": "Plan name must be less than 100 characters"}
      ],
      "plan_code": [
        {"rule": "required", "message": "Plan code is required"},
        {"rule": "min", "value": 3, "message": "Plan code must be at least 3 characters"},
        {"rule": "max", "value": 50, "message": "Plan code must be less than 50 characters"},
        {"rule": "pattern", "value": "^[a-z0-9-]+$", "message": "Plan code must contain only lowercase letters, numbers, and hyphens"}
      ],
      "plan_type": [
        {"rule": "required", "message": "Plan type is required"}
      ],
      "plan_price_amount": [
        {"rule": "min", "value": 0, "message": "Price cannot be negative"}
      ],
      "plan_configuration": [
        {"rule": "json", "message": "Invalid JSON format"}
      ]
    }
  }',  -- End of ui_config
  '{
    "roles": {
      "admin": {
        "create": true,
        "read": true,
        "update": true,
        "delete": true,
        "field_restrictions": {}
      },
      "subscription_manager": {
        "create": true,
        "read": true,
        "update": true,
        "delete": false,
        "field_restrictions": {}
      },
      "organization_manager": {
        "create": false,
        "read": true,
        "update": false,
        "delete": false,
        "field_restrictions": {}
      },
      "standard_user": {
        "create": false,
        "read": true,
        "update": false,
        "delete": false,
        "field_restrictions": {
          "restricted_fields": ["plan_configuration"]
        }
      }
    },
    "conditions": {
      "is_public_plan": {
        "field": "plan_is_public",
        "operator": "eq",
        "value": true
      },
      "is_organization_plan": {
        "field": "organization_id",
        "operator": "eq",
        "value": "{current_organization_id}"
      }
    },
    "rule_sets": [
      {
        "name": "public_plans_visible",
        "description": "Public plans are visible to all users",
        "conditions": ["is_public_plan"],
        "permissions": {
          "read": true
        }
      },
      {
        "name": "organization_plans_management",
        "description": "Organization managers can manage their organization''s custom plans",
        "conditions": ["is_organization_plan"],
        "permissions": {
          "create": true,
          "read": true,
          "update": true,
          "delete": true
        }
      }
    ],
    "field_level_permissions": {
      "plan_configuration": {
        "roles": {
          "admin": {"read": true, "write": true},
          "subscription_manager": {"read": true, "write": true},
          "organization_manager": {"read": true, "write": false},
          "standard_user": {"read": false, "write": false}
        }
      }
    }
  }',  -- End of permission_rules
  '{
    "relationships": [
      {
        "name": "plan_subscriptions",
        "type": "one_to_many",
        "entity": "org_subscriptions",
        "foreign_key": "sub_plan_id",
        "display": "Subscriptions"
      },
      {
        "name": "plan_roles",
        "type": "many_to_many",
        "entity": "roles",
        "junction_table": "plan_roles",
        "local_key": "plan_id",
        "foreign_key": "role_id",
        "display": "Available Roles"
      },
      {
        "name": "plan_transitions_from",
        "type": "one_to_many",
        "entity": "sub_transitions",
        "foreign_key": "transition_from_plan_id",
        "display": "Transitions From"
      },
      {
        "name": "plan_transitions_to",
        "type": "one_to_many",
        "entity": "sub_transitions",
        "foreign_key": "transition_to_plan_id",
        "display": "Transitions To"
      }
    ]
  }',  -- End of relationship_map
  '{
    "workflow_triggers": [
      {
        "name": "plan_activation",
        "description": "Triggered when a plan is activated",
        "event": "update",
        "condition": {
          "field_changes": {
            "plan_is_active": {
              "from": false,
              "to": true
            }
          }
        },
        "actions": [
          {
            "type": "notification",
            "config": {
              "template": "plan_activated",
              "channels": ["email", "in_app"],
              "recipients": [
                {
                  "type": "role",
                  "role": "subscription_manager"
                }
              ]
            }
          }
        ]
      },
      {
        "name": "plan_deactivation",
        "description": "Triggered when a plan is deactivated",
        "event": "update",
        "condition": {
          "field_changes": {
            "plan_is_active": {
              "from": true,
              "to": false
            }
          }
        },
        "actions": [
          {
            "type": "notification",
            "config": {
              "template": "plan_deactivated",
              "channels": ["email", "in_app"],
              "recipients": [
                {
                  "type": "role",
                  "role": "subscription_manager"
                }
              ]
            }
          }
        ]
      }
    ]
  }',  -- End of workflow_triggers
  true,  -- is_active
  true,  -- is_system_table
  NOW()  -- created_at
);
```

### Organization Subscriptions Metadata Registry Entry

```sql
INSERT INTO metadata_registry (
  table_name,
  display_name,
  description,
  schema_definition,
  api_config,
  ui_config,
  permission_rules,
  relationship_map,
  workflow_triggers,
  is_active,
  is_system_table,
  created_at
) VALUES (
  'org_subscriptions',
  'Organization Subscriptions',
  'Links organizations to their active subscription plans',
  '{
    "fields": [
      {
        "name": "sub_id",
        "type": "uuid",
        "primary_key": true,
        "default": "uuid_generate_v4()",
        "description": "Unique identifier"
      },
      {
        "name": "sub_org_id",
        "type": "uuid",
        "required": true,
        "references": {
          "table": "organizations",
          "field": "org_id",
          "on_delete": "CASCADE"
        },
        "description": "Organization with this subscription"
      },
      {
        "name": "sub_plan_id",
        "type": "uuid",
        "required": true,
        "references": {
          "table": "sub_plans",
          "field": "plan_id",
          "on_delete": "RESTRICT"
        },
        "description": "Current plan"
      },
      {
        "name": "sub_status",
        "type": "text",
        "required": true,
        "enum_values": ["active", "expired", "canceled", "pending", "trial", "replaced"],
        "description": "Current status"
      },
      {
        "name": "sub_start_date",
        "type": "timestamptz",
        "required": true,
        "description": "When subscription became active"
      },
      {
        "name": "sub_end_date",
        "type": "timestamptz",
        "description": "When subscription expires (NULL for auto-renewing)"
      },
      {
        "name": "sub_auto_renew",
        "type": "boolean",
        "default": false,
        "description": "Whether subscription automatically renews"
      },
      {
        "name": "sub_billing_reference",
        "type": "text",
        "description": "External billing system reference (e.g., Stripe subscription ID)"
      },
      {
        "name": "sub_canceled_at",
        "type": "timestamptz",
        "description": "When subscription was canceled (if applicable)"
      },
      {
        "name": "sub_canceled_by",
        "type": "uuid",
        "references": {
          "table": "users",
          "field": "user_id",
          "on_delete": "SET NULL"
        },
        "description": "User who canceled the subscription"
      },
      {
        "name": "sub_cancel_reason",
        "type": "text",
        "description": "Reason provided for cancellation"
      },
      {
        "name": "created_at",
        "type": "timestamptz",
        "default": "NOW()",
        "description": "When this record was created"
      },
      {
        "name": "updated_at",
        "type": "timestamptz",
        "default": "NOW()",
        "description": "When this record was last updated"
      },
      {
        "name": "created_by",
        "type": "uuid",
        "references": {
          "table": "users",
          "field": "user_id",
          "on_delete": "SET NULL"
        },
        "description": "User who created this record"
      },
      {
        "name": "updated_by",
        "type": "uuid",
        "references": {
          "table": "users",
          "field": "user_id",
          "on_delete": "SET NULL"
        },
        "description": "User who last updated this record"
      }
    ],
    "indexes": [
      {
        "name": "idx_org_subscriptions_org",
        "fields": ["sub_org_id"],
        "unique": false
      },
      {
        "name": "idx_org_subscriptions_plan",
        "fields": ["sub_plan_id"],
        "unique": false
      },
      {
        "name": "idx_org_subscriptions_status",
        "fields": ["sub_status"],
        "unique": false
      },
      {
        "name": "idx_org_subscriptions_dates",
        "fields": ["sub_start_date", "sub_end_date"],
        "unique": false
      },
      {
        "name": "idx_org_active_subscriptions",
        "fields": ["sub_org_id", "sub_status"],
        "where": "sub_status = ''active''",
        "unique": true
      }
    ]
  }',  -- End of schema_definition
  '{
    "resource_name": "organization_subscriptions",
    "enable_crud": true,
    "batch_operations": false,
    "pagination": {
      "default_limit": 25,
      "max_limit": 100,
      "cursor_based": true
    },
    "endpoints": [
      {
        "path": "/org_subscriptions",
        "methods": ["GET", "POST"],
        "description": "List and create organization subscriptions"
      },
      {
        "path": "/org_subscriptions/{id}",
        "methods": ["GET", "PUT", "PATCH", "DELETE"],
        "description": "Get, update, or delete a specific subscription"
      },
      {
        "path": "/org_subscriptions/active",
        "methods": ["GET"],
        "description": "Get the active subscription for the current organization"
      },
      {
        "path": "/org_subscriptions/history",
        "methods": ["GET"],
        "description": "Get subscription history for the current organization"
      },
      {
        "path": "/org_subscriptions/change",
        "methods": ["POST"],
        "description": "Change subscription plan"
      },
      {
        "path": "/org_subscriptions/cancel",
        "methods": ["POST"],
        "description": "Cancel subscription"
      }
    ],
    "filters": [
      {
        "field": "sub_org_id",
        "operators": ["eq"]
      },
      {
        "field": "sub_plan_id",
        "operators": ["eq", "in"]
      },
      {
        "field": "sub_status",
        "operators": ["eq", "in"]
      },
      {
        "field": "sub_start_date",
        "operators": ["eq", "gt", "lt", "between"]
      },
      {
        "field": "sub_end_date",
        "operators": ["eq", "gt", "lt", "between", "is_null"]
      }
    ],
    "sorting": [
      {"field": "sub_start_date", "default": true, "direction": "desc"},
      {"field": "sub_end_date", "default": false},
      {"field": "sub_status", "default": false}
    ],
    "response_fields": {
      "default": [
        "sub_id", "sub_org_id", "sub_plan_id", "sub_status", 
        "sub_start_date", "sub_end_date", "sub_auto_renew"
      ],
      "detailed": [
        "sub_id", "sub_org_id", "sub_plan_id", "sub_status", 
        "sub_start_date", "sub_end_date", "sub_auto_renew",
        "sub_billing_reference", "sub_canceled_at", "sub_canceled_by", "sub_cancel_reason",
        "created_at", "updated_at", "created_by", "updated_by"
      ],
      "minimal": ["sub_id", "sub_status", "sub_plan_id"]
    },
    "hooks": {
      "before_create": [
        "validate_subscription_data", 
        "check_organization_existing_subscription",
        "calculate_end_date_from_plan"
      ],
      "after_create": [
        "log_subscription_creation", 
        "assign_default_roles",
        "initialize_usage_tracking"
      ],
      "before_update": [
        "validate_subscription_data", 
        "check_valid_status_transition"
      ],
      "after_update": [
        "log_subscription_update",
        "handle_status_change_effects"
      ],
      "before_delete": ["check_subscription_delete_permission"],
      "after_delete": ["log_subscription_deletion"]
    }
  }',  -- End of api_config
  '{
    "list_view": {
      "default_columns": [
        {"field": "sub_plan_id", "width": "20%", "formatter": "planReference"},
        {"field": "sub_status", "width": "15%", "formatter": "statusBadge"},
        {"field": "sub_start_date", "width": "15%", "formatter": "date"},
        {"field": "sub_end_date", "width": "15%", "formatter": "date", "nullText": "Auto-renew"},
        {"field": "sub_auto_renew", "width": "10%", "formatter": "boolean"},
        {"field": "sub_billing_reference", "width": "15%", "formatter": "externalLink", "linkPrefix": "https://dashboard.stripe.com/subscriptions/", "nullText": "Manual"},
        {"field": "created_at", "width": "15%", "formatter": "relativeDate"}
      ],
      "actions": [
        {"name": "view", "icon": "eye", "permission": "subscription.view"},
        {"name": "edit", "icon": "edit", "permission": "subscription.edit"},
        {"name": "cancel", "icon": "x", "permission": "subscription.cancel"}
      ],
      "filters": [
        {"field": "sub_status", "type": "select", "options": [
          {"value": "active", "label": "Active"},
          {"value": "expired", "label": "Expired"},
          {"value": "canceled", "label": "Canceled"},
          {"value": "pending", "label": "Pending"},
          {"value": "trial", "label": "Trial"},
          {"value": "replaced", "label": "Replaced"}
        ]},
        {"field": "sub_start_date", "type": "dateRange"},
        {"field": "sub_end_date", "type": "dateRange", "allowNull": true, "nullLabel": "Auto-renew"}
      ],
      "default_sort": {"field": "sub_start_date", "direction": "desc"}
    },
    "detail_view": {
      "layout": "tabs",
      "sections": [
        {
          "id": "basic",
          "label": "Subscription Details",
          "fields": [
            {"field": "sub_plan_id", "size": "large", "control": "planSelect", "label": "Plan"},
            {"field": "sub_status", "size": "medium", "control": "select", "options": [
              {"value": "active", "label": "Active"},
              {"value": "expired", "label": "Expired"},
              {"value": "canceled", "label": "Canceled"},
              {"value": "pending", "label": "Pending"},
              {"value": "trial", "label": "Trial"}
            ]},
            {"field": "sub_start_date", "size": "medium", "control": "datePicker"},
            {"field": "sub_end_date", "size": "medium", "control": "datePicker", "allowNull": true, "nullLabel": "Auto-renew"},
            {"field": "sub_auto_renew", "size": "small", "control": "switch"}
          ]
        },
        {
          "id": "billing",
          "label": "Billing Information",
          "fields": [
            {"field": "sub_billing_reference", "size": "medium", "control": "text", "externalLink": "https://dashboard.stripe.com/subscriptions/{value}"},
            {"field": "sub_canceled_at", "size": "medium", "control": "dateTime", "readOnly": true},
            {"field": "sub_canceled_by", "size": "medium", "control": "userReference", "readOnly": true},
            {"field": "sub_cancel_reason", "size": "large", "control": "textarea", "readOnly": true}
          ]
        },
        {
          "id": "limits",
          "label": "Usage & Limits",
          "component": "SubscriptionLimits",
          "props": {
            "subscriptionId": "sub_id"
          }
        },
        {
          "id": "history",
          "label": "Subscription History",
          "component": "SubscriptionHistory",
          "props": {
            "organizationId": "sub_org_id"
          }
        },
        {
          "id": "metadata",
          "label": "Metadata",
          "fields": [
            {"field": "created_by", "size": "medium", "control": "userReference", "readOnly": true},
            {"field": "created_at", "size": "medium", "control": "dateTime", "readOnly": true},
            {"field": "updated_by", "size": "medium", "control": "userReference", "readOnly": true},
            {"field": "updated_at", "size": "medium", "control": "dateTime", "readOnly": true}
          ]
        }
      ],
      "actions": [
        {"name": "edit", "label": "Edit Subscription", "icon": "edit", "permission": "subscription.edit"},
        {"name": "cancel", "label": "Cancel Subscription", "icon": "x", "permission": "subscription.cancel", "confirmation": {
          "title": "Cancel Subscription",
          "message": "Are you sure you want to cancel this subscription?",
          "fields": [
            {"name": "reason", "label": "Cancellation Reason", "type": "textarea", "required": true}
          ]
        }},
        {"name": "change_plan", "label": "Change Plan", "icon": "refresh", "permission": "subscription.edit", "modal": {
          "title": "Change Subscription Plan",
          "component": "ChangePlanForm",
          "props": {
            "currentPlanId": "sub_plan_id",
            "subscriptionId": "sub_id"
          }
        }}
      ]
    },
    "create_view": {
      "layout": "tabs",
      "sections": [
        {
          "id": "basic",
          "label": "Subscription Details",
          "fields": [
            {"field": "sub_org_id", "size": "large", "control": "organizationSelect", "label": "Organization"},
            {"field": "sub_plan_id", "size": "large", "control": "planSelect", "label": "Plan"},
            {"field": "sub_status", "size": "medium", "control": "select", "options": [
              {"value": "active", "label": "Active"},
              {"value": "pending", "label": "Pending"},
              {"value": "trial", "label": "Trial"}
            ], "default": "active"},
            {"field": "sub_start_date", "size": "medium", "control": "datePicker", "default": "NOW()"},
            {"field": "sub_auto_renew", "size": "small", "control": "switch", "default": false}
          ]
        },
        {
          "id": "billing",
          "label": "Billing Information",
          "fields": [
            {"field": "sub_billing_reference", "size": "medium", "control": "text"}
          ]
        }
      ],
      "actions": [
        {"name": "save", "label": "Create Subscription", "icon": "save", "primary": true},
        {"name": "cancel", "label": "Cancel", "icon": "x"}
      ]
    },
    "edit_view": {
      "layout": "tabs",
      "inherit_from": "detail_view",
      "actions": [
        {"name": "save", "label": "Save Changes", "icon": "save", "primary": true},
        {"name": "cancel", "label": "Cancel", "icon": "x"}
      ]
    },
    "form_validation": {
      "sub_org_id": [
        {"rule": "required", "message": "Organization is required"}
      ],
      "sub_plan_id": [
        {"rule": "required", "message": "Plan is required"}
      ],
      "sub_status": [
        {"rule": "required", "message": "Status is required"}
      ],
      "sub_start_date": [
        {"rule": "required", "message": "Start date is required"}
      ]
    }
  }',  -- End of ui_config
  '{
    "roles": {
      "admin": {
        "create": true,
        "read": true,
        "update": true,
        "delete": true,
        "field_restrictions": {}
      },
      "subscription_manager": {
        "create": true,
        "read": true,
        "update": true,
        "delete": false,
        "field_restrictions": {}
      },
      "organization_manager": {
        "create": false,
        "read": true,
        "update": true,
        "delete": false,
        "field_restrictions": {}
      },
      "standard_user": {
        "create": false,
        "read": true,
        "update": false,
        "delete": false,
        "field_restrictions": {
          "restricted_fields": ["sub_auto_renew", "sub_cancel_reason"]
        }
      }
    },
    "conditions": {
      "is_organization_subscription": {
        "field": "sub_org_id",
        "operator": "eq",
        "value": "{current_organization_id}"
      }
    },
    "rule_sets": [
      {
        "name": "organization_subscription_access",
        "description": "Users can only access subscriptions for their organization",
        "conditions": ["is_organization_subscription"],
        "permissions": {
          "read": true
        }
      }
    ],
    "field_level_permissions": {
      "sub_cancel_reason": {
        "roles": {
          "admin": {"read": true, "write": true},
          "subscription_manager": {"read": true, "write": true},
          "organization_manager": {"read": true, "write": false},
          "standard_user": {"read": false, "write": false}
        }
      }
    }
  }',  -- End of permission_rules
  '{
    "relationships": [
      {
        "name": "subscription_organization",
        "type": "many_to_one",
        "entity": "organizations",
        "foreign_key": "sub_org_id",
        "display": "Organization"
      },
      {
        "name": "subscription_plan",
        "type": "many_to_one",
        "entity": "sub_plans",
        "foreign_key": "sub_plan_id",
        "display": "Plan"
      },
      {
        "name": "subscription_transitions_from",
        "type": "one_to_many",
        "entity": "sub_transitions",
        "foreign_key": "transition_from_sub_id",
        "display": "Transitions From"
      },
      {
        "name": "subscription_transitions_to",
        "type": "one_to_many",
        "entity": "sub_transitions",
        "foreign_key": "transition_to_sub_id",
        "display": "Transitions To"
      },
      {
        "name": "subscription_usage",
        "type": "one_to_many",
        "entity": "feature_usage",
        "foreign_key": "usage_sub_id",
        "display": "Feature Usage"
      }
    ]
  }',  -- End of relationship_map
  '{
    "workflow_triggers": [
      {
        "name": "subscription_creation",
        "description": "Triggered when a subscription is created",
        "event": "create",
        "condition": null,
        "actions": [
          {
            "type": "function",
            "config": {
              "function": "assign_default_roles_from_plan",
              "parameters": {
                "organization_id": "$.sub_org_id",
                "plan_id": "$.sub_plan_id"
              }
            }
          },
          {
            "type": "notification",
            "config": {
              "template": "subscription_created",
              "channels": ["email", "in_app"],
              "recipients": [
                {
                  "type": "role",
                  "role": "organization_manager",
                  "organization_id": "$.sub_org_id"
                }
              ]
            }
          }
        ]
      },
      {
        "name": "subscription_expiration",
        "description": "Triggered when a subscription expires",
        "event": "update",
        "condition": {
          "field_changes": {
            "sub_status": {
              "from": "active",
              "to": "expired"
            }
          }
        },
        "actions": [
          {
            "type": "notification",
            "config": {
              "template": "subscription_expired",
              "channels": ["email", "in_app"],
              "recipients": [
                {
                  "type": "role",
                  "role": "organization_manager",
                  "organization_id": "$.sub_org_id"
                }
              ]
            }
          }
        ]
      },
      {
        "name": "subscription_cancellation",
        "description": "Triggered when a subscription is canceled",
        "event": "update",
        "condition": {
          "field_changes": {
            "sub_status": {
              "from": "active",
              "to": "canceled"
            }
          }
        },
        "actions": [
          {
            "type": "notification",
            "config": {
              "template": "subscription_canceled",
              "channels": ["email", "in_app"],
              "recipients": [
                {
                  "type": "role",
                  "role": "organization_manager",
                  "organization_id": "$.sub_org_id"
                }
              ]
            }
          }
        ]
      },
      {
        "name": "subscription_plan_change",
        "description": "Triggered when a subscription''s plan is changed",
        "event": "update",
        "condition": {
          "field_changes": {
            "sub_plan_id": {
              "changed": true
            }
          }
        },
        "actions": [
          {
            "type": "function",
            "config": {
              "function": "create_subscription_transition",
              "parameters": {
                "organization_id": "$.sub_org_id",
                "user_id": "$.current_user_id",
                "from_sub_id": "$.sub_id",
                "to_sub_id": "$.sub_id",
                "reason": "Plan changed",
                "source": "user_initiated",
                "notes": "Subscription plan was changed",
                "preserved_data": ["policies", "controls", "risks", "documents"]
              }
            }
          },
          {
            "type": "notification",
            "config": {
              "template": "subscription_plan_changed",
              "channels": ["email", "in_app"],
              "recipients": [
                {
                  "type": "role",
                  "role": "organization_manager",
                  "organization_id": "$.sub_org_id"
                }
              ]
            }
          }
        ]
      }
    ]
  }',  -- End of workflow_triggers
  true,  -- is_active
  true,  -- is_system_table
  NOW()  -- created_at
);
```

Similar metadata registry entries would be created for the following tables:
- roles
- permissions
- role_permissions
- plan_roles
- user_roles
- feature_usage
- sub_transitions

## Workflow Configurations

Workflows define the business logic for subscription-related operations. Here are some key workflows:

### Subscription Creation Workflow

```json
{
  "workflow_type": "subscription_creation",
  "triggers": [
    {
      "event": "create",
      "entity": "org_subscriptions",
      "conditions": null
    }
  ],
  "steps": [
    {
      "id": "validate_subscription",
      "type": "validation",
      "config": {
        "validations": [
          {
            "field": "sub_org_id",
            "rule": "required"
          },
          {
            "field": "sub_plan_id",
            "rule": "required"
          },
          {
            "field": "sub_status",
            "rule": "required"
          },
          {
            "field": "sub_start_date",
            "rule": "required"
          }
        ]
      }
    },
    {
      "id": "check_existing_subscription",
      "type": "database_query",
      "config": {
        "query": "SELECT sub_id FROM org_subscriptions WHERE sub_org_id = :sub_org_id AND sub_status = 'active'",
        "parameters": {
          "sub_org_id": "$.sub_org_id"
        },
        "error_condition": "result.length > 0",
        "error_message": "Organization already has an active subscription"
      }
    },
    {
      "id": "calculate_end_date",
      "type": "function",
      "config": {
        "function": "calculate_subscription_end_date",
        "parameters": {
          "plan_id": "$.sub_plan_id",
          "start_date": "$.sub_start_date"
        },
        "output_field": "sub_end_date"
      }
    },
    {
      "id": "create_subscription",
      "type": "database_insert",
      "config": {
        "table": "org_subscriptions",
        "data": {
          "sub_org_id": "$.sub_org_id",
          "sub_plan_id": "$.sub_plan_id",
          "sub_status": "$.sub_status",
          "sub_start_date": "$.sub_start_date",
          "sub_end_date": "$.sub_end_date",
          "sub_auto_renew": "$.sub_auto_renew",
          "created_by": "$.current_user_id"
        },
        "return_fields": ["sub_id"]
      }
    },
    {
      "id": "assign_default_roles",
      "type": "function",
      "config": {
        "function": "assign_default_roles_from_plan",
        "parameters": {
          "organization_id": "$.sub_org_id",
          "plan_id": "$.sub_plan_id"
        }
      }
    },
    {
      "id": "initialize_usage_tracking",
      "type": "function",
      "config": {
        "function": "initialize_feature_usage_tracking",
        "parameters": {
          "subscription_id": "$.steps.create_subscription.result.sub_id",
          "organization_id": "$.sub_org_id"
        }
      }
    },
    {
      "id": "notify_stakeholders",
      "type": "notification",
      "config": {
        "template": "subscription_created",
        "channels": ["email", "in_app"],
        "recipients": [
          {
            "type": "user",
            "id": "$.current_user_id"
          },
          {
            "type": "role",
            "role": "organization_manager",
            "organization_id": "$.sub_org_id"
          }
        ],
        "data": {
          "organization_name": "$.lookup.organization_name",
          "plan_name": "$.lookup.plan_name",
          "start_date": "$.sub_start_date",
          "end_date": "$.sub_end_date"
        }
      }
    }
  ]
}
```

### Subscription Change Plan Workflow

```json
{
  "workflow_type": "subscription_change_plan",
  "triggers": [
    {
      "event": "custom",
      "action": "change_plan",
      "entity": "org_subscriptions"
    }
  ],
  "parameters": [
    {
      "name": "organization_id",
      "type": "uuid",
      "required": true
    },
    {
      "name": "new_plan_id",
      "type": "uuid",
      "required": true
    },
    {
      "name": "reason",
      "type": "text",
      "required": false
    },
    {
      "name": "preserve_data",
      "type": "array",
      "items": {
        "type": "string"
      },
      "default": ["policies", "controls", "risks", "documents"]
    }
  ],
  "steps": [
    {
      "id": "get_current_subscription",
      "type": "database_query",
      "config": {
        "query": "SELECT sub_id, sub_plan_id FROM org_subscriptions WHERE sub_org_id = :organization_id AND sub_status = 'active'",
        "parameters": {
          "organization_id": "$.organization_id"
        },
        "error_condition": "result.length === 0",
        "error_message": "No active subscription found for organization",
        "return_fields": ["sub_id", "sub_plan_id"]
      }
    },
    {
      "id": "create_new_subscription",
      "type": "database_insert",
      "config": {
        "table": "org_subscriptions",
        "data": {
          "sub_org_id": "$.organization_id",
          "sub_plan_id": "$.new_plan_id",
          "sub_status": "active",
          "sub_start_date": "NOW()",
          "sub_end_date": "$.function.calculate_subscription_end_date($.new_plan_id, NOW())",
          "sub_auto_renew": "$.function.get_plan_billing_cycle($.new_plan_id) !== 'one_time'",
          "created_by": "$.current_user_id"
        },
        "return_fields": ["sub_id"]
      }
    },
    {
      "id": "update_old_subscription",
      "type": "database_update",
      "config": {
        "table": "org_subscriptions",
        "where": {
          "sub_id": "$.steps.get_current_subscription.result[0].sub_id"
        },
        "data": {
          "sub_status": "replaced",
          "updated_at": "NOW()"
        }
      }
    },
    {
      "id": "record_transition",
      "type": "database_insert",
      "config": {
        "table": "sub_transitions",
        "data": {
          "transition_org_id": "$.organization_id",
          "transition_user_id": "$.current_user_id",
          "transition_from_sub_id": "$.steps.get_current_subscription.result[0].sub_id",
          "transition_to_sub_id": "$.steps.create_new_subscription.result.sub_id",
          "transition_from_plan_id": "$.steps.get_current_subscription.result[0].sub_plan_id",
          "transition_to_plan_id": "$.new_plan_id",
          "transition_reason": "$.reason",
          "transition_source": "user_initiated",
          "transition_data": {
            "preserved_data": "$.preserve_data",
            "transition_metadata": {
              "initiated_by": "$.current_user_id",
              "timestamp": "NOW()"
            }
          },
          "created_by": "$.current_user_id"
        }
      }
    },
    {
      "id": "update_user_roles",
      "type": "function",
      "config": {
        "function": "update_roles_for_plan_change",
        "parameters": {
          "organization_id": "$.organization_id",
          "old_plan_id": "$.steps.get_current_subscription.result[0].sub_plan_id",
          "new_plan_id": "$.new_plan_id"
        }
      }
    },
    {
      "id": "notify_stakeholders",
      "type": "notification",
      "config": {
        "template": "subscription_changed",
        "channels": ["email", "in_app"],
        "recipients": [
          {
            "type": "user",
            "id": "$.current_user_id"
          },
          {
            "type": "role",
            "role": "organization_manager",
            "organization_id": "$.organization_id"
          }
        ],
        "data": {
          "old_plan_name": "$.lookup.old_plan_name",
          "new_plan_name": "$.lookup.new_plan_name",
          "organization_name": "$.lookup.organization_name"
        }
      }
    }
  ]
}
```

### Subscription Expiration Workflow

```json
{
  "workflow_type": "subscription_expiration",
  "triggers": [
    {
      "event": "scheduled",
      "schedule": "0 0 * * *",  // Run daily at midnight
      "description": "Check for expired subscriptions"
    }
  ],
  "parameters": [],
  "steps": [
    {
      "id": "find_expired_subscriptions",
      "type": "database_query",
      "config": {
        "query": "SELECT sub_id, sub_org_id, sub_plan_id FROM org_subscriptions WHERE sub_status = 'active' AND sub_end_date IS NOT NULL AND sub_end_date < NOW()",
        "return_fields": ["sub_id", "sub_org_id", "sub_plan_id"]
      }
    },
    {
      "id": "process_expired_subscriptions",
      "type": "iteration",
      "config": {
        "items": "$.steps.find_expired_subscriptions.result",
        "steps": [
          {
            "id": "update_subscription",
            "type": "database_update",
            "config": {
              "table": "org_subscriptions",
              "where": {
                "sub_id": "$.item.sub_id"
              },
              "data": {
                "sub_status": "expired",
                "updated_at": "NOW()"
              }
            }
          },
          {
            "id": "record_transition",
            "type": "database_insert",
            "config": {
              "table": "sub_transitions",
              "data": {
                "transition_org_id": "$.item.sub_org_id",
                "transition_from_sub_id": "$.item.sub_id",
                "transition_from_plan_id": "$.item.sub_plan_id",
                "transition_reason": "Subscription expired",
                "transition_source": "system",
                "transition_notes": "Automatic expiration due to end date reached",
                "created_by": null
              }
            }
          },
          {
            "id": "notify_organization",
            "type": "notification",
            "config": {
              "template": "subscription_expired",
              "channels": ["email", "in_app"],
              "recipients": [
                {
                  "type": "role",
                  "role": "organization_manager",
                  "organization_id": "$.item.sub_org_id"
                }
              ],
              "data": {
                "organization_name": "$.lookup.organization_name",
                "plan_name": "$.lookup.plan_name",
                "expiration_date": "NOW()"
              }
            }
          }
        ]
      }
    }
  ]
}
```

## Implementation Examples

### Edge Function Handler for Subscription Management

This is an example of how the subscription management API would be implemented in an edge function:

```javascript
// subscription_handler.js - Part of the assistant_router edge function

import { createClient } from '@supabase/supabase-js'

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

export async function handleSubscriptionRequest(req, res, context) {
  try {
    // Extract authentication and context
    const { user } = context
    
    if (!user || !user.organization_id) {
      return res.status(401).json({
        error: 'unauthorized',
        message: 'Valid authentication required'
      })
    }
    
    // Set context for RLS policies
    await supabase.rpc('set_config', {
      setting_name: 'app.current_organization_id',
      setting_value: user.organization_id,
      is_local: true
    })
    
    await supabase.rpc('set_config', {
      setting_name: 'app.current_user_id',
      setting_value: user.id,
      is_local: true
    })
    
    // Route to appropriate handler based on path and method
    const path = req.path
    const method = req.method
    
    if (path === '/org_subscriptions/active' && method === 'GET') {
      return await getActiveSubscription(req, res, user)
    } else if (path === '/org_subscriptions/change' && method === 'POST') {
      return await changePlan(req, res, user)
    } else if (path === '/org_subscriptions/cancel' && method === 'POST') {
      return await cancelSubscription(req, res, user)
    } else if (path === '/subscription_plans/public' && method === 'GET') {
      return await getPublicPlans(req, res, user)
    } else {
      // Handle standard CRUD operations using metadata-driven approach
      return await handleMetadataRequest(req, res, user)
    }
  } catch (error) {
    console.error('Error in subscription handler:', error)
    return res.status(500).json({
      error: 'internal_error',
      message: 'An internal error occurred'
    })
  }
}

async function getActiveSubscription(req, res, user) {
  // Check permission
  const { data: permissionCheck } = await supabase.rpc('unified_permission_check', {
    p_organization_id: user.organization_id,
    p_user_id: user.id,
    p_permission_code: 'view:subscription'
  })
  
  if (!permissionCheck.allowed) {
    return res.status(403).json({
      error: 'permission_denied',
      message: permissionCheck.message
    })
  }
  
  // Get active subscription
  const { data: subscription, error } = await supabase
    .from('org_subscriptions')
    .select(`
      sub_id,
      sub_plan_id,
      sub_status,
      sub_start_date,
      sub_end_date,
      sub_auto_renew,
      sub_plans:sub_plan_id (
        plan_name,
        plan_type,
        plan_configuration
      )
    `)
    .eq('sub_org_id', user.organization_id)
    .eq('sub_status', 'active')
    .single()
  
  if (error) {
    if (error.code === 'PGRST116') {
      return res.status(404).json({
        error: 'not_found',
        message: 'No active subscription found'
      })
    }
    
    return res.status(500).json({
      error: 'database_error',
      message: 'Error retrieving subscription'
    })
  }
  
  // Return subscription data
  return res.json({
    data: subscription
  })
}

async function changePlan(req, res, user) {
  // Check permission
  const { data: permissionCheck } = await supabase.rpc('unified_permission_check', {
    p_organization_id: user.organization_id,
    p_user_id: user.id,
    p_permission_code: 'update:subscription'
  })
  
  if (!permissionCheck.allowed) {
    return res.status(403).json({
      error: 'permission_denied',
      message: permissionCheck.message
    })
  }
  
  // Validate request body
  const { new_plan_id, reason } = req.body
  
  if (!new_plan_id) {
    return res.status(400).json({
      error: 'invalid_request',
      message: 'new_plan_id is required'
    })
  }
  
  // Execute workflow
  const { data: workflowResult, error: workflowError } = await supabase.rpc('execute_workflow', {
    p_workflow_type: 'subscription_change_plan',
    p_parameters: {
      organization_id: user.organization_id,
      new_plan_id,
      reason: reason || 'User requested plan change',
      preserve_data: ['policies', 'controls', 'risks', 'documents']
    }
  })
  
  if (workflowError) {
    return res.status(500).json({
      error: 'workflow_error',
      message: workflowError.message
    })
  }
  
  // Return success response
  return res.json({
    success: true,
    data: {
      message: 'Subscription plan changed successfully',
      new_subscription_id: workflowResult.new_subscription_id
    }
  })
}

// Implement other handler functions...

async function handleMetadataRequest(req, res, user) {
  // Get metadata for entity
  const entityType = getEntityTypeFromPath(req.path)
  
  if (!entityType) {
    return res.status(404).json({
      error: 'not_found',
      message: 'Invalid API endpoint'
    })
  }
  
  const { data: metadata, error: metadataError } = await supabase
    .from('metadata_registry')
    .select('*')
    .eq('table_name', entityType)
    .single()
  
  if (metadataError) {
    return res.status(500).json({
      error: 'metadata_error',
      message: 'Error retrieving entity metadata'
    })
  }
  
  // Process request based on metadata and method
  switch (req.method) {
    case 'GET':
      return await handleMetadataGet(req, res, user, metadata)
    case 'POST':
      return await handleMetadataPost(req, res, user, metadata)
    case 'PUT':
    case 'PATCH':
      return await handleMetadataPut(req, res, user, metadata)
    case 'DELETE':
      return await handleMetadataDelete(req, res, user, metadata)
    default:
      return res.status(405).json({
        error: 'method_not_allowed',
        message: `Method ${req.method} not allowed`
      })
  }
}

// Implement metadata-driven handler functions...
```

### React Component for Subscription Management

This is an example React component for displaying subscription limits:

```jsx
import React, { useEffect, useState } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { Card, Progress, Text, Heading, Flex, Box, Icon } from '@chakra-ui/react';
import { AlertCircle, Check } from 'lucide-react';

export function SubscriptionLimits({ subscriptionId }) {
  const supabase = useSupabaseClient();
  const [limits, setLimits] = useState({});
  const [usage, setUsage] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    async function fetchLimitsAndUsage() {
      setLoading(true);
      setError(null);
      
      try {
        // Get subscription details with plan
        const { data: subscription, error: subError } = await supabase
          .from('org_subscriptions')
          .select(`
            sub_id,
            sub_plan_id,
            sub_plans:sub_plan_id (
              plan_configuration
            )
          `)
          .eq('sub_id', subscriptionId)
          .single();
          
        if (subError) throw subError;
        
        // Extract limits from plan configuration
        const limits = subscription?.sub_plans?.plan_configuration?.feature_limits || {};
        
        // Get usage counts for each feature
        const features = ['policies', 'controls', 'risks', 'documents'];
        const usageCounts = {};
        
        // Fetch counts for each feature
        for (const feature of features) {
          if (limits[feature]) {
            const { count, error: countError } = await supabase
              .from(feature)
              .select('id', { count: 'exact', head: true });
              
            if (!countError) {
              usageCounts[feature] = count || 0;
            }
          }
        }
        
        setLimits(limits);
        setUsage(usageCounts);
      } catch (error) {
        console.error('Error fetching subscription limits:', error);
        setError('Failed to load subscription limits');
      } finally {
        setLoading(false);
      }
    }
    
    if (subscriptionId) {
      fetchLimitsAndUsage();
    }
  }, [supabase, subscriptionId]);
  
  if (loading) {
    return <Text>Loading subscription limits...</Text>;
  }
  
  if (error) {
    return <Text color="red.500">{error}</Text>;
  }
  
  // If no limits are defined, show a message
  if (Object.keys(limits).length === 0) {
    return (
      <Card p={4}>
        <Heading size="md" mb={4}>Usage & Limits</Heading>
        <Text>This subscription plan does not have any usage limits.</Text>
      </Card>
    );
  }
  
  return (
    <Card p={4}>
      <Heading size="md" mb={4}>Usage & Limits</Heading>
      
      {Object.entries(limits).map(([feature, limit]) => {
        const currentUsage = usage[feature] || 0;
        const percentage = Math.min(100, Math.round((currentUsage / limit) * 100));
        const isApproachingLimit = percentage >= 80;
        const isAtLimit = percentage >= 100;
        
        return (
          <Box key={feature} mb={4}>
            <Flex justify="space-between" mb={1}>
              <Text fontWeight="medium" textTransform="capitalize">{feature}</Text>
              <Flex align="center">
                <Text>{currentUsage} / {limit}</Text>
                {isAtLimit && (
                  <Icon as={AlertCircle} color="red.500" ml={2} />
                )}
              </Flex>
            </Flex>
            <Progress 
              value={percentage} 
              size="sm"
              colorScheme={isAtLimit ? "red" : isApproachingLimit ? "orange" : "green"}
              borderRadius="full"
            />
          </Box>
        );
      })}
    </Card>
  );
}
```

### React Component for Plan Selection

This is an example React component for selecting a subscription plan:

```jsx
import React, { useEffect, useState } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { 
  Box, SimpleGrid, Card, CardHeader, CardBody, CardFooter,
  Heading, Text, Button, List, ListItem, ListIcon, Badge, 
  useToast, Modal, ModalOverlay, ModalContent, ModalHeader, 
  ModalBody, ModalCloseButton, ModalFooter
} from '@chakra-ui/react';
import { CheckCircle, XCircle } from 'lucide-react';

export function PlanSelection() {
  const supabase = useSupabaseClient();
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPlan, setCurrentPlan] = useState(null);
  const [confirmPlan, setConfirmPlan] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const toast = useToast();
  
  useEffect(() => {
    async function fetchPlans() {
      setLoading(true);
      setError(null);
      
      try {
        // Get public plans
        const { data: plansData, error: plansError } = await supabase
          .from('sub_plans')
          .select('*')
          .eq('plan_is_active', true)
          .eq('plan_is_public', true)
          .order('plan_price_amount', { ascending: true });
          
        if (plansError) throw plansError;
        
        // Get current active subscription
        const { data: subscription, error: subError } = await supabase
          .from('org_subscriptions')
          .select('sub_plan_id')
          .eq('sub_status', 'active')
          .single();
          
        if (!subError) {
          setCurrentPlan(subscription.sub_plan_id);
        }
        
        setPlans(plansData || []);
      } catch (error) {
        console.error('Error fetching plans:', error);
        setError('Failed to load subscription plans');
      } finally {
        setLoading(false);
      }
    }
    
    fetchPlans();
  }, [supabase]);
  
  const handleSelectPlan = (plan) => {
    setConfirmPlan(plan);
    setIsModalOpen(true);
  };
  
  const handleConfirmPlan = async () => {
    try {
      const { error } = await supabase.functions.invoke('subscription-management', {
        body: {
          action: 'change_plan',
          plan_id: confirmPlan.plan_id
        }
      });
      
      if (error) throw error;
      
      toast({
        title: 'Plan changed successfully',
        description: `Your subscription has been updated to ${confirmPlan.plan_name}`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      
      setCurrentPlan(confirmPlan.plan_id);
    } catch (error) {
      console.error('Error changing plan:', error);
      toast({
        title: 'Failed to change plan',
        description: error.message || 'An unexpected error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsModalOpen(false);
    }
  };
  
  if (loading) {
    return <Text>Loading subscription plans...</Text>;
  }
  
  if (error) {
    return <Text color="red.500">{error}</Text>;
  }
  
  return (
    <Box>
      <Heading size="lg" mb={6}>Subscription Plans</Heading>
      
      <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={8}>
        {plans.map((plan) => {
          const isCurrentPlan = plan.plan_id === currentPlan;
          const featureAccess = plan.plan_configuration?.feature_access || {};
          const featureLimits = plan.plan_configuration?.feature_limits || {};
          
          return (
            <Card 
              key={plan.plan_id} 
              borderWidth={isCurrentPlan ? 2 : 1}
              borderColor={isCurrentPlan ? "blue.500" : "gray.200"}
              boxShadow={isCurrentPlan ? "lg" : "base"}
            >
              <CardHeader bg={isCurrentPlan ? "blue.50" : "gray.50"} p={4}>
                {isCurrentPlan && (
                  <Badge colorScheme="blue" mb={2}>Current Plan</Badge>
                )}
                <Heading size="md">{plan.plan_name}</Heading>
                <Text mt={1} color="gray.600">{plan.plan_type}</Text>
              </CardHeader>
              
              <CardBody p={4}>
                <Text fontSize="2xl" fontWeight="bold" mb={4}>
                  ${plan.plan_price_amount}
                  <Text as="span" fontSize="sm" fontWeight="normal" ml={1}>
                    /{plan.plan_billing_cycle}
                  </Text>
                </Text>
                
                <Text mb={4}>{plan.plan_description}</Text>
                
                <Heading size="sm" mb={2}>Features</Heading>
                <List spacing={2} mb={4}>
                  {Object.entries(featureAccess).map(([feature, isIncluded]) => (
                    <ListItem key={feature} display="flex" alignItems="center">
                      <ListIcon as={isIncluded ? CheckCircle : XCircle} color={isIncluded ? "green.500" : "red.500"} />
                      <Text>
                        {feature.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </Text>
                    </ListItem>
                  ))}
                </List>
                
                <Heading size="sm" mb={2}>Limits</Heading>
                <List spacing={2}>
                  {Object.entries(featureLimits).map(([feature, limit]) => (
                    <ListItem key={feature}>
                      <Text>
                        <Text as="span" fontWeight="medium" textTransform="capitalize">
                          {feature}:
                        </Text> {limit === null ? 'Unlimited' : limit}
                      </Text>
                    </ListItem>
                  ))}
                </List>
              </CardBody>
              
              <CardFooter p={4} borderTopWidth={1}>
                <Button 
                  colorScheme={isCurrentPlan ? "gray" : "blue"}
                  isDisabled={isCurrentPlan}
                  onClick={() => handleSelectPlan(plan)}
                  width="full"
                >
                  {isCurrentPlan ? 'Current Plan' : 'Select Plan'}
                </Button>
              </CardFooter>
            </Card>
          );
        })}
      </SimpleGrid>
      
      <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Confirm Plan Change</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {confirmPlan && (
              <>
                <Text mb={4}>
                  Are you sure you want to change your subscription to the{' '}
                  <Text as="span" fontWeight="bold">{confirmPlan.plan_name}</Text> plan?
                </Text>
                
                <Text mb={4}>
                  You will be billed ${confirmPlan.plan_price_amount} per {confirmPlan.plan_billing_cycle}.
                </Text>
                
                <Text>
                  Your existing data will be preserved, but some features may no longer be available
                  if they are not included in the new plan.
                </Text>
              </>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={() => setIsModalOpen(false)}>
              Cancel
            </Button>
            <Button colorScheme="blue" onClick={handleConfirmPlan}>
              Confirm Change
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
}
```

## Summary

This document provides a complete metadata-driven implementation of ArionComply's subscription management system. The key components include:

1. **Metadata Registry Entries**: Define how the subscription management system behaves, including schema, API, UI, and permission rules
2. **Workflow Configurations**: Define the business logic for subscription-related operations
3. **Implementation Examples**: Show how to implement the system in edge functions and React components

By using a metadata-driven approach, we achieve several benefits:

1. **Consistency**: All aspects of the system (database, API, UI) are defined in a single place
2. **Flexibility**: New subscription types and features can be added without code changes
3. **Maintainability**: Business logic is separated from implementation details
4. **Security**: Permission rules are centrally defined and consistently applied

## Next Steps

After implementing this metadata-driven approach, the next steps are:

1. Create edge function components as described in `arioncomply-v1/docs/API/Subscription-Management-Edge-Functions.md`
2. Develop React UI components as described in `arioncomply-v1/docs/Frontend/Subscription-Management-React-Components.md`
3. Define subscription workflows as described in `arioncomply-v1/docs/Workflows/Subscription-Management-Workflows.md`
