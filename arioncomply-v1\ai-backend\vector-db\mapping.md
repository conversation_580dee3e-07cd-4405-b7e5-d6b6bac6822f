File: arioncomply-v1/ai-backend/vector-db/mapping.md
# InputDocs → Vector Metadata Mapping (MVP)

Purpose
- Normalize authored fields (templates/Q&A/ingest guidelines) into vector `documents.metadata` and `chunks.metadata` for consistent retrieval, explainability, and actions.

documents.metadata (optional)
- source_system: string (e.g., dms|upload|repo|url)
- taxonomy: { framework_ids?: string[], control_ids?: string[] }
- tags: string[]

chunks.metadata (recommended)
- section: string | null
- heading: string | null
- page: number | null
- framework_ids: string[] | []            # e.g., ["ISO27001:A.12.1.2"]
- control_ids: string[] | []
- actions:                                  # lifted into suggestions
  - type: "start_workflow"|"open_register"|"draft_doc"
    slug: string                             # CustomerWorkflows slug
    label?: string                           # optional human label

Citations
- Format: `[doc_id:version#seq]` per chunk; version comes from `documents.version`.

Validation
- Required keys for chunks: text present; seq present; metadata object exists; fields may be null but keys are stable.
- See `python-backend/services/ingestion/metadata_validator.py`.

Notes
- For authored InputDocs JSON, map their keys to the above; add classifier to populate framework/control IDs when possible.
- Keep actions small and deterministic; avoid free-text.
File: arioncomply-v1/ai-backend/vector-db/mapping.md
