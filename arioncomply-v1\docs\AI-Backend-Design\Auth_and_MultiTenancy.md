# Auth and Multi‑Tenancy (Placeholder)

Decisions
- Auth via Supabase JWT on Edge; derive `orgId` and `userId` from claims.
- Enforce tenant isolation with RLS on all data stores; vector project uses `org_id` filters.
- Logs must always include org/user context.

Design Points
- JWT claims: expected names (e.g., `app_org_id`, `sub`), fallbacks, validation.
- Consistent propagation into `RequestMeta` and downstream calls (vector/LLM).
- Deny access when `orgId` missing; avoid `org_id IS NULL` visibility in policies.

Open Questions
- Final claim names and issuer config; cross-project key verification.
- Admin/Support roles with cross-tenant read needs.

Next Steps
- Implement JWT parsing helper and guard in Edge.
- Tighten RLS policies to disallow NULL org visibility where applicable.

