// File: arioncomply-v1/supabase/functions/ai-conversation-send/index.ts
// File Description: AI conversation endpoint with deterministic intent classification and backend routing
// Purpose: Accept user message, classify intent, route to backend, and return assistant reply with context
// Inputs: POST JSON { sessionId?, message, hintContext? } with camelCase/snake_case conversion support
// Outputs: ApiSuccess with userMessage, assistantMessage, intentClassification, and backend routing metadata
// Dependencies: Deno HTTP server, _shared utils/errors/logger/schemas/assistant_router modules
// Security/RLS: Client metadata extraction, request correlation, audit logging for all conversations
// Notes: Deterministic intent classifier at Edge with 7 compliance categories and clarification flow support

import { serve } from "https://deno.land/std@0.224.0/http/server.ts";
import { readJson, nowIso, convertKeys } from "../_shared/utils.ts";
import { apiOk, apiError } from "../_shared/errors.ts";
import { getClientMeta, logEvent, logRequestEnd, logRequestStart } from "../_shared/logger.ts";
import { ConversationSendSchema, validate } from "../_shared/schemas.ts";
import { assistantRouter } from "../_shared/assistant_router.ts";

// TypeScript interface for the request body from frontend
type SendBody = {
  session_id: string;                    // Unique identifier for the conversation session
  message: string;                       // User's natural language message/query
  hint_context?: Record<string, unknown>; // Optional context hints from the UI
};

// TypeScript interface for intent classification results
type IntentClassification = {
  category: string;                      // Primary intent category (policy, standards, etc.)
  confidence: number;                    // Classification confidence (0.0-1.0)
  needsClarification: boolean;           // Whether the query is too ambiguous
  clarificationOptions?: string[];       // Suggested clarification questions for user
};

// Deterministic intent classifier for routing compliance queries
// This runs at the Edge (Supabase Function) for fast classification before backend routing
function classifyIntent(message: string): IntentClassification {
  // Convert to lowercase and trim whitespace for consistent pattern matching
  const msg = message.toLowerCase().trim();

  // High-confidence deterministic patterns - look for specific keywords
  // These patterns have high confidence (0.9) and don't need clarification
  // Policy/procedure questions - usually need org-specific knowledge
  if (msg.includes("policy") || msg.includes("procedure")) {
    return {
      category: "policy_inquiry",           // Routes to favor org-specific vector store
      confidence: 0.9,                      // High confidence - clear keywords
      needsClarification: false             // Clear intent, no clarification needed
    };
  }

  if (msg.includes("assessment") || msg.includes("audit") || msg.includes("evaluate")) {
    return {
      category: "assessment_inquiry",
      confidence: 0.9,
      needsClarification: false
    };
  }

  if (msg.includes("compliance") || msg.includes("regulation") || msg.includes("requirement")) {
    return {
      category: "compliance_inquiry",
      confidence: 0.9,
      needsClarification: false
    };
  }

  if (msg.includes("incident") || msg.includes("security") || msg.includes("breach")) {
    return {
      category: "security_inquiry",
      confidence: 0.9,
      needsClarification: false
    };
  }

  if (msg.includes("iso") || msg.includes("gdpr") || msg.includes("standard") ||
      msg.includes("framework") || msg.includes("nist") || msg.includes("sox")) {
    return {
      category: "standards_inquiry",
      confidence: 0.9,
      needsClarification: false
    };
  }

  if ((msg.includes("how") && msg.includes("implement")) ||
      msg.includes("setup") || msg.includes("configure")) {
    return {
      category: "implementation_guidance",
      confidence: 0.85,
      needsClarification: false
    };
  }

  // Medium confidence patterns that might need clarification
  // Very short or unclear messages need user clarification to route properly
  if (msg.length < 10 || msg.split(" ").length < 3) {
    return {
      category: "general_inquiry",           // Fallback category for unclear queries
      confidence: 0.3,                      // Low confidence - message too short/vague
      needsClarification: true,             // Need user to be more specific
      clarificationOptions: [               // Suggested clarification questions
        "Are you asking about compliance policies?",
        "Do you need help with a security assessment?",
        "Are you looking for implementation guidance?",
        "Do you want information about compliance standards?"
      ]
    };
  }

  // Ambiguous patterns - generic question words that could mean anything
  if (msg.includes("help") || msg.includes("what") || msg.includes("how")) {
    return {
      category: "general_inquiry",           // Generic category for broad questions
      confidence: 0.6,                      // Medium confidence - could be anything
      needsClarification: msg.split(" ").length < 5, // Need clarification if very short
      clarificationOptions: [               // Guide user to be more specific
        "What specific area do you need help with?",
        "Are you asking about your organization or general information?",
        "Which compliance framework interests you most?"
      ]
    };
  }

  // Default case
  return {
    category: "general_inquiry",
    confidence: 0.5,
    needsClarification: false
  };
}

serve(async (req) => {
  const requestId = crypto.randomUUID();
  const meta = getClientMeta(req, requestId);
  await logRequestStart(meta, req.headers);
  if (req.method !== "POST") {
    const res = apiError("method_not_allowed", "Method not allowed", 405, undefined, requestId);
    await logRequestEnd(meta, 405);
    return res;
  }

  let body: SendBody;
  try {
    // Accept camelCase inbound and convert to snake for internal handling
    const raw = await readJson<unknown>(req);
    const snake = convertKeys<SendBody>(raw, "toSnake");
    body = snake;
  } catch (err) {
    const res = apiError("invalid_json", "Invalid JSON body", 400, undefined, requestId);
    await logRequestEnd(meta, 400);
    return res;
  }

  if (!body.session_id || !body.message?.trim()) {
    const res = apiError(
      "validation_error",
      "session_id and message are required",
      400,
      { missing: [!body.session_id ? "session_id" : null, !body.message?.trim() ? "message" : null].filter(Boolean) },
      requestId,
    );
    await logRequestEnd(meta, 400);
    return res;
  }

  // Prepare meta enriched with session id for logging
  const metaWithSession = { ...meta, sessionId: body.session_id } as typeof meta & { sessionId?: string | null };

  // Step 1: Deterministic Intent Classification
  const intentResult = classifyIntent(body.message.trim());

  // Log intent detection event
  await logEvent(metaWithSession, {
    eventType: "intent_detected",
    direction: "internal",
    status: "ok",
    details: {
      intent_category: intentResult.category,
      confidence_score: intentResult.confidence,
      requires_clarification: intentResult.needsClarification,
      classification_source: "deterministic",
      session_id: body.session_id,
      original_message_length: body.message.trim().length
    }
  });

  // Step 2: Check if clarification is needed
  if (intentResult.needsClarification && intentResult.clarificationOptions) {
    // Return clarification options instead of forwarding to backend
    await logEvent(metaWithSession, {
      eventType: "intent_routing_decision",
      direction: "internal",
      status: "ok",
      details: {
        routing_decision: "clarification_required",
        clarification_options_count: intentResult.clarificationOptions.length,
        session_id: body.session_id
      }
    });

    const payload = {
      user_message: {
        message_id: crypto.randomUUID(),
        session_id: body.session_id,
        sender_type: "user",
        content_type: "text",
        content_text: body.message.trim(),
        created_at: nowIso(),
      },
      assistant_message: {
        message_id: crypto.randomUUID(),
        session_id: body.session_id,
        sender_type: "assistant",
        content_type: "clarification",
        content_text: "I'd like to help you with that. Could you clarify what you're looking for?",
        created_at: nowIso(),
        suggestions: intentResult.clarificationOptions.map(option => ({
          text: option,
          action: { type: "clarify", intent: intentResult.category }
        })),
        intent_metadata: {
          requires_clarification: true,
          detected_intent: intentResult.category,
          confidence: intentResult.confidence
        }
      },
    };

    await logEvent(metaWithSession, { eventType: "response_sent", direction: "outbound", status: "ok", details: { sessionId: body.session_id, messageId: payload.assistant_message.message_id, responseType: "clarification" } });
    const res = apiOk(convertKeys(payload, "toCamel"), requestId, { headers: { "traceparent": meta.traceHeader ?? "" } });
    await logRequestEnd(metaWithSession, 200);
    return res;
  }

  // Step 3: Log routing decision to forward to backend
  await logEvent(metaWithSession, {
    eventType: "intent_routing_decision",
    direction: "internal",
    status: "ok",
    details: {
      routing_decision: "forward_to_backend",
      intent_category: intentResult.category,
      confidence_score: intentResult.confidence,
      session_id: body.session_id
    }
  });

  // Run assistant via router: SLLM preferred; fallback to GLLM with anonymization
  const assistant = await assistantRouter({
    messages: [
      { role: "user", content: body.message.trim() },
    ],
    sessionId: body.session_id,
    orgId: meta.orgId ?? undefined,
    userId: meta.userId ?? undefined,
    headers: req.headers,
    // Pass intent classification to backend for enhanced processing
    hints: {
      intent_category: intentResult.category,
      intent_confidence: intentResult.confidence,
      ...(body.hint_context || {})
    }
  });
  const assistantText = assistant.text || "";

  await logEvent(metaWithSession, { eventType: "user_message_received", direction: "inbound", status: "ok", details: { session_id: body.session_id } });
  const fallbackSuggestions = [
    { text: "Start an assessment", action: { type: "nav", target: "assessment" } },
    { text: "What is ISO 27001 and where do I start?", action: { type: "nav", target: "standards/iso27001" } },
    { text: "Show recommended next steps for my organization" },
    { text: "Explore compliance registers", action: { type: "nav", target: "registers" } },
  ];

  const payload = {
    user_message: {
      message_id: crypto.randomUUID(),
      session_id: body.session_id,
      sender_type: "user",
      content_type: "text",
      content_text: body.message.trim(),
      created_at: nowIso(),
    },
    assistant_message: {
      message_id: crypto.randomUUID(),
      session_id: body.session_id,
      sender_type: "assistant",
      content_type: "text",
      content_text: assistantText,
      created_at: nowIso(),
      suggestions: (assistant as any)?.suggestions ?? fallbackSuggestions,
    },
  };
  await logEvent(metaWithSession, { eventType: "assistant_message_generated", direction: "internal", status: "ok", details: { len: assistantText.length, provider: assistant.provider, model: assistant.model, session_id: body.session_id } });
  // Record response_sent prior to returning
  await logEvent(metaWithSession, { eventType: "response_sent", direction: "outbound", status: "ok", details: { sessionId: body.session_id, messageId: payload.assistant_message.message_id, suggestionsCount: Array.isArray((assistant as any)?.suggestions) ? (assistant as any).suggestions.length : fallbackSuggestions.length } });
  const res = apiOk(convertKeys(payload, "toCamel"), requestId, { headers: { "traceparent": meta.traceHeader ?? "" } });
  await logRequestEnd(metaWithSession, 200);
  return res;
});
  // Validate camelCase view for UI/API alignment
  const camelInput = convertKeys(body, "toCamel") as any;
  const check = validate(camelInput, ConversationSendSchema);
  if (!check.valid) {
    const res = apiError("validation_error", "Invalid request", 400, { errors: check.errors }, requestId);
    await logRequestEnd(meta, 400);
    return res;
  }
// File: arioncomply-v1/supabase/functions/ai-conversation-send/index.ts
