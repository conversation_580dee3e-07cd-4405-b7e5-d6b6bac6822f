##
# File: arioncomply-v1/Makefile
# File Description: Developer shortcuts for serving and deploying Edge functions.
# Purpose: Simplify local development and deployment of Supabase Edge functions.
# Inputs: Make targets edge-serve, edge-deploy, edge-deploy-all.
# Outputs: Runs Supabase CLI commands to serve or deploy functions.
# Dependencies: supabase CLI.
# Security/RLS: N/A
# Notes: Ensure Supabase CLI is authenticated before running.
##

.PHONY: edge-serve edge-deploy edge-deploy-all

EDGE_FUNCS=ai-conversation-start ai-conversation-send ai-conversation-stream

edge-serve:
	@echo "Serving: $(EDGE_FUNCS)";
	supabase functions serve $(EDGE_FUNCS)

edge-deploy:
	@echo "Deploying: $(EDGE_FUNCS)";
	supabase functions deploy $(EDGE_FUNCS)

# Deploy all edge functions in supabase/functions (including compliance-proxy)
edge-deploy-all:
	@echo "Deploying all functions in supabase/functions";
	@cd supabase/functions && ls -1 | grep -v '^_shared$$' | xargs -I {} supabase functions deploy {}
validate:
	@bash tools/validation/run.sh

.PHONY: validate validate-full validate-since

validate-full:
	@bash tools/validation/run.sh --full

# Usage: make validate-since SINCE=origin/main
validate-since:
	@if [ -z "$(SINCE)" ]; then echo "Usage: make validate-since SINCE=origin/main"; exit 2; fi
	@bash tools/validation/run.sh --since $(SINCE)
