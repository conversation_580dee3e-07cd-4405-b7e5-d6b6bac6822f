File: arioncomply-v1/.kiro/specs/standards-compliance-platform/multi-tier-retrieval-architecture.md
# Multi-Tier Dual-Vector Retrieval Architecture

**Design Document**: ArionComply Standards Compliance Platform  
**Section**: Complete Multi-Tier Dual-Vector Retrieval Flow Architecture  
**Version**: 3.0 - Public/Private Data Separation with Progressive Escalation  
**Date**: 2025-09-13

---

## Complete Multi-Tier Dual-Vector Retrieval Flow

This document outlines the comprehensive multi-tier retrieval system that uses dual vector stores (public standards knowledge and private organizational data) with progressive quality escalation based on confidence scores and user intent evaluation.

### Dual-Vector Architecture Overview

```mermaid
flowchart TD
    User[👤 User Query] --> EdgeApp[🌐 Edge Function<br/>Application DB Supabase]
    
    EdgeApp --> AIBackend[🤖 AI Backend<br/>Python FastAPI]
    
    AIBackend --> Preprocessing{🔍 Preprocessing<br/>Search, Crawl, Similarity}
    
    Preprocessing --> Intent{🎯 Intent Evaluation<br/>Good Enough?}
    
    %% Tier 0: Direct/Deterministic Response
    Intent -->|✅ High Confidence<br/>Deterministic Match| FormatDirect[📄 Format Direct Response]
    FormatDirect --> UserReturn1[👤 Return to User]
    
    %% Tier 1: Dual Vector Search (Parallel)
    Intent -->|❓ Needs Retrieval| DualSearch[🔀 Dual Vector Search<br/>Parallel Queries]
    
    DualSearch --> ChromaDB[📚 ChromaDB<br/>Shared Standards Knowledge<br/>ISO/GDPR/Regulations]
    DualSearch --> SupabaseVector[🏢 Supabase Vector DB<br/>Private Company Data<br/>Org-Scoped Documents]
    
    ChromaDB --> ChromaResults[📊 Standards Results<br/>+ Confidence Scores]
    SupabaseVector --> CompanyResults[📊 Company Results<br/>+ Confidence Scores]
    
    ChromaResults --> HybridFusion{🎯 Hybrid Result Fusion<br/>Combine + Score}
    CompanyResults --> HybridFusion
    
    HybridFusion --> ResultConf{📈 Combined Confidence<br/>Good Enough?}
    
    %% High Confidence Path
    ResultConf -->|✅ High Confidence| ResponseRoute{🔀 Response Type}
    ResponseRoute -->|📝 Prose Needed| SLLM1[🧠 SLLM Local<br/>Standards + Company Context]
    ResponseRoute -->|📋 Structured| FormatHybrid[📄 Format Hybrid Response<br/>Standards + Company]
    SLLM1 --> UserReturn2[👤 Return to User]
    FormatHybrid --> UserReturn2
    
    %% Low Confidence - Cloud Escalation
    ResultConf -->|⚠️ Low Confidence| DataClassify[🏷️ Data Classification<br/>Public vs Private]
    
    DataClassify --> Anonymize[🎭 Anonymization<br/>Private Data Protection]
    
    Anonymize --> GLLM[🌩️ GLLM Cloud<br/>OpenAI/Claude/etc.<br/>Enhanced Reasoning]
    
    GLLM --> GLLMConf{🎯 GLLM Response<br/>Quality Check}
    
    GLLMConf -->|✅ Good Quality| DeAnonymize[🔓 De-Anonymization<br/>Restore Context]
    DeAnonymize --> UserReturn3[👤 Return to User]
    
    %% Ultimate Fallback
    GLLMConf -->|❓ Still Unclear| ClarifyGen[❓ Generate<br/>Clarifying Questions<br/>Standards + Company Context]
    ClarifyGen --> UserReturn4[👤 Ask User<br/>Clarifying Questions]
    
    %% Tracking and Recording
    UserReturn1 --> Tracking[📊 Track & Record<br/>All Interactions<br/>Public/Private Separation]
    UserReturn2 --> Tracking
    UserReturn3 --> Tracking
    UserReturn4 --> Tracking
    
    Tracking --> Analytics[📈 Analytics<br/>Dual Vector Performance<br/>Public vs Private Usage]
    
    %% Data Source Legend
    ChromaDBLegend[📚 ChromaDB - Shared Public Data:<br/>• Standards (ISO 27001, GDPR, etc.)<br/>• Regulations & Laws<br/>• Best Practices<br/>• Templates & FAQs<br/>• Cross-Org Shared Knowledge]
    
    SupabaseLegend[🏢 Supabase Vector - Private Org Data:<br/>• Company Policies<br/>• Internal Procedures<br/>• Assessment Results<br/>• Proprietary Content<br/>• Org-Specific Context]
    
    %% Styling
    classDef userNode fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef processNode fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef decisionNode fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef publicDataNode fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef privateDataNode fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef hybridNode fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef llmNode fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef trackingNode fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    classDef legendNode fill:#fafafa,stroke:#616161,stroke-width:1px,stroke-dasharray: 5 5
    
    class User,UserReturn1,UserReturn2,UserReturn3,UserReturn4 userNode
    class EdgeApp,AIBackend,Preprocessing,FormatDirect,FormatHybrid,DataClassify,Anonymize,DeAnonymize,ClarifyGen processNode
    class Intent,ResultConf,GLLMConf,ResponseRoute decisionNode
    class ChromaDB,ChromaDBLegend publicDataNode
    class SupabaseVector,SupabaseLegend privateDataNode
    class DualSearch,HybridFusion,ChromaResults,CompanyResults hybridNode
    class SLLM1,GLLM llmNode
    class Tracking,Analytics trackingNode
```

---

## Dual-Vector Tier Flow Description

### **Tier 0: Deterministic/Direct Response**
- **Input**: User query → Edge Function → AI Backend
- **Process**: Preprocessing (search, crawl, similarity analysis)
- **Evaluation**: Intent analysis determines if query can be answered directly
- **Output**: High-confidence deterministic match formatted and returned
- **Use Cases**: FAQ-style questions, direct policy lookups, known procedures
- **Data Source**: Neither vector store needed

### **Tier 1: Dual Vector Parallel Search**
- **Trigger**: Intent evaluation indicates retrieval needed
- **Process**: Parallel queries to both vector stores
  
  **ChromaDB Search (Public Knowledge)**:
  - Standards (ISO 27001, GDPR, CCPA, etc.)
  - Regulations and legal frameworks
  - Best practices and templates
  - Public compliance guidance
  - **Assessment questions and frameworks**
  - **Follow-up suggestions and action items**
  - **Compliance workflow templates**
  
  **Supabase Vector Search (Private Knowledge)**:
  - Company-specific policies and procedures
  - Internal assessment results
  - Proprietary implementations
  - Organizational context and decisions

- **Hybrid Fusion**: Combine results with intelligent ranking
- **Confidence Scoring**: Evaluate combined retrieval quality
- **High Confidence Path**:
  - **Structured Response**: Format hybrid results (standards + company context)
  - **Prose Response**: Use SLLM with both public standards and private context
- **Use Cases**: Most compliance queries requiring both regulatory knowledge and company context

### **Tier 2: Cloud LLM Enhanced Reasoning**
- **Trigger**: Hybrid vector search confidence below threshold
- **Data Classification**: Separate public standards from private company data
- **Anonymization**: Protect sensitive company information before cloud transmission
- **Process**: GLLM (OpenAI/Claude/etc.) with enhanced reasoning capabilities
  - Public standards data: Sent as-is (no privacy concerns)
  - Private company data: Anonymized and generalized
- **De-anonymization**: Restore company-specific context to responses
- **Quality Check**: Evaluate cloud LLM response quality and relevance
- **Use Cases**: Complex compliance scenarios, novel interpretations, cross-domain analysis

### **Tier 3: Clarifying Questions**
- **Trigger**: All retrieval tiers fail to meet confidence threshold
- **Context**: Use both public standards knowledge and private company context
- **Process**: Generate targeted clarifying questions considering:
  - Available standards and regulations
  - Company's specific compliance posture
  - Missing context needed for accurate guidance
- **Goal**: Gather additional context to improve subsequent retrieval attempts
- **Use Cases**: Ambiguous queries, insufficient context, novel scenarios, conflicting requirements

---

## Confidence Scoring Framework

### **Scoring Criteria**
1. **Semantic Similarity**: Embedding similarity scores
2. **Context Relevance**: Query-document alignment  
3. **Completeness**: Information coverage assessment
4. **Authority**: Source credibility and recency
5. **User Intent Match**: Query satisfaction likelihood

### **Threshold Configuration**
```yaml
confidence_thresholds:
  tier_0_deterministic: 0.95    # Very high confidence required
  tier_1_chromadb: 0.80         # Good confidence for local RAG
  tier_2_supabase: 0.70         # Moderate confidence for cloud fallback  
  tier_3_gllm: 0.60             # Lower threshold for cloud LLM
  tier_4_clarify: 0.60          # Below this, ask clarifying questions

response_type_thresholds:
  structured_only: 0.90         # High confidence = direct structured response
  prose_generation: 0.75        # Medium confidence = SLLM prose
  cloud_llm_required: 0.60      # Low confidence = cloud LLM needed
```

---

## ChromaDB Assessment and Follow-up Systems

### **Assessment Question Generation**
ChromaDB contains curated assessment frameworks and question banks that power:

- **Standards-Based Assessments**: Pre-built question sets for ISO 27001, GDPR, CCPA, etc.
- **Risk Assessment Templates**: Industry-specific risk evaluation frameworks
- **Maturity Assessment Models**: Compliance maturity scoring systems
- **Gap Analysis Questionnaires**: Systematic compliance gap identification

**Vector Search for Assessments**:
```
User Context → ChromaDB Assessment Search → Relevant Questions
- Organization industry/size
- Applicable standards/regulations  
- Current compliance maturity
- Specific risk areas

Result: Contextually relevant assessment questions
```

### **Follow-up Suggestions System** 
ChromaDB stores follow-up action templates that generate contextual next steps:

- **Implementation Guidance**: Next steps after assessment completion
- **Resource Recommendations**: Relevant templates, tools, training
- **Timeline Suggestions**: Compliance project planning guidance
- **Expert Connections**: When to engage specialists or consultants

**Vector Search for Follow-ups**:
```
Assessment Results + User Query → ChromaDB Follow-up Search → Suggested Actions
- Assessment outcomes/gaps identified
- Organization capabilities/resources
- Regulatory deadlines/priorities
- Implementation complexity

Result: Personalized follow-up action recommendations
```

### **Workflow Template Integration**
ChromaDB contains compliance workflow templates:

- **Implementation Roadmaps**: Step-by-step compliance implementation guides
- **Document Templates**: Policy templates, procedure frameworks
- **Review Cycles**: Audit preparation and monitoring workflows
- **Incident Response**: Compliance incident handling procedures

**Hybrid Integration**: 
- **ChromaDB**: Provides workflow templates and best practices
- **Supabase Vector**: Stores company-specific customizations and progress

---

## System Components Integration

### **Embedding Pipeline Integration**
- **ChromaDB**: Prioritize local high-quality embeddings (BGE-ONNX, MPNet)
- **Supabase Vector**: Support multi-dimensional embeddings (including OpenAI)
- **Pipeline Selection**: Optimize for primary system (ChromaDB) performance

### **LLM Routing Logic**
- **SLLM (Local)**: SmolLM3, Mistral7B, Phi3 for prose generation
- **GLLM (Cloud)**: OpenAI/Claude for complex reasoning (anonymized)
- **Selection Criteria**: Response complexity, privacy requirements, performance needs

### **Data Protection & Anonymization**
- **PII Scrubbing**: Remove personal identifiers before cloud calls
- **Context Anonymization**: Replace specific org details with generic terms
- **Response De-anonymization**: Map generic responses back to org context

---

## Tracking and Analytics

### **Interaction Recording**
- **Query Classification**: Intent, complexity, domain
- **Tier Progression**: Which tiers were used, escalation paths
- **Confidence Scores**: At each tier, decision rationales
- **Response Quality**: User satisfaction, follow-up queries
- **Performance Metrics**: Latency, cost, accuracy per tier

### **System Optimization Data**
- **Threshold Tuning**: Confidence threshold effectiveness
- **Tier Usage Patterns**: Which tiers handle which query types
- **Embedding Performance**: ChromaDB vs Supabase quality comparison
- **LLM Efficiency**: SLLM vs GLLM usage and outcomes

### **Compliance & Audit Trail**
- **Data Flow Tracking**: Where data went, what transformations
- **Anonymization Records**: What was anonymized, how
- **Decision Audit**: Why each escalation happened
- **Quality Assurance**: Response accuracy and compliance

---

## Operational Considerations

### **Performance Optimization**
- **Caching Strategy**: Results caching at each tier
- **Parallel Processing**: ChromaDB + preprocessing in parallel where possible
- **Resource Management**: SLLM resource allocation and queuing
- **Failure Handling**: Graceful degradation between tiers

### **Cost Management**
- **Tier Preference**: Bias toward lower-cost tiers (ChromaDB, SLLM)
- **Cloud LLM Budgeting**: Rate limiting and cost controls
- **Usage Monitoring**: Cost per query type analysis
- **Optimization Feedback**: Cost-effectiveness tuning

### **Security & Privacy**
- **Data Residency**: ChromaDB and SLLM fully local
- **Cloud Data Handling**: Anonymization, retention policies
- **Access Controls**: Tier-based permissions and audit logging
- **Compliance**: GDPR, CCPA, SOC2 requirements at each tier

---

## Success Metrics

### **Quality Metrics**
- **Response Accuracy**: Correctness across all tiers
- **User Satisfaction**: Follow-up query rates, feedback scores
- **Confidence Calibration**: How well confidence predicts quality
- **Tier Efficiency**: Right-tier-first-time rate

### **Performance Metrics**  
- **Response Time**: End-to-end latency per tier
- **System Availability**: Uptime across all components
- **Throughput**: Concurrent query handling capacity
- **Resource Utilization**: CPU, memory, storage efficiency

### **Business Metrics**
- **Cost per Query**: Total cost including all tiers
- **Automation Rate**: Queries handled without human intervention
- **Escalation Patterns**: Tier usage distribution over time
- **ROI**: Value delivered vs system costs

---

This multi-tier architecture provides progressive quality escalation while optimizing for cost, performance, and data privacy. The system automatically finds the right balance between local control and cloud capabilities based on query complexity and confidence requirements.
File: arioncomply-v1/.kiro/specs/standards-compliance-platform/multi-tier-retrieval-architecture.md
