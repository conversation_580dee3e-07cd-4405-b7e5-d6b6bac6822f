#!/usr/bin/env python3
# File: tools/checks/check-toml.py
"""
File Description: Validates TOML files parse with Python tomllib
Purpose: Catch syntax issues in TOML configuration (e.g., merge markers)
Notes: Scans all *.toml; prints offending file and error on failure
"""

import sys
from pathlib import Path

try:
    import tomllib  # Python 3.11+
except Exception:  # pragma: no cover
    print("tomllib not available (Python >=3.11 required)", file=sys.stderr)
    sys.exit(1)

repo = Path(__file__).resolve().parents[2]  # repo root
failures = []

for p in repo.rglob("*.toml"):
    try:
        with p.open("rb") as f:
            tomllib.load(f)
    except Exception as e:
        failures.append((p.relative_to(repo), e))

if failures:
    print("Invalid TOML files:")
    for path, err in failures:
        print(f"  {path}: {err}")
    sys.exit(1)

print("All TOML files parsed successfully.")
