id: Q092
query: >-
  Do we need special encryption software or are basic tools enough?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/A.10.1"
  - "GDPR:2016/Art.32"
overlap_ids: []
capability_tags:
  - "Register"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Annex A.10.1 Cryptographic Controls"
    id: "ISO27001:2022/A.10.1"
    locator: "Annex A.10.1"
  - title: "GDPR — Security of Processing"
    id: "GDPR:2016/Art.32"
    locator: "Article 32"
ui:
  cards_hint:
    - "Encryption toolkit"
  actions:
    - type: "open_register"
      target: "encryption_tools"
      label: "View Approved Tools"
output_mode: "both"
graph_required: false
notes: "Basic disk and file encryption often suffice; specialist tools only for high-risk data"
---
### 92) Do we need special encryption software or are basic tools enough?

**Standard terms)**  
- **Cryptographic controls (A.10.1):** require encryption to protect confidentiality, integrity.  
- **Security of processing (GDPR Art. 32):** mandates appropriate technical measures.

**Plain-English answer**  
Built-in OS/file-system encryption (BitLocker, FileVault), TLS for transit, and database-level encryption often meet requirements. Specialized hardware modules or tokenization are optional for extremely sensitive data.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.10.1; GDPR Article 32

**Why it matters**  
Right-sizing tools avoids excessive cost and complexity.

**Do next in our platform**  
- Inventory current **encryption tools**.  
- Map to required cryptographic controls.

**How our platform will help**  
- **[Register]** Encryption tool catalog.  
- **[Report]** Compliance gap analysis for cryptography.

**Likely follow-ups**  
- “What algorithms are considered secure today?”  

**Sources**  
- ISO/IEC 27001:2022 Annex A.10.1  
- GDPR Art. 32
