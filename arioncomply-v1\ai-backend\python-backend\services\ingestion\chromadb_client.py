"""
File: arioncomply-v1/ai-backend/python-backend/services/ingestion/chromadb_client.py
File Description: ChromaDB client for local vector storage with org-scoped collections
Purpose: Manage ChromaDB collections with org isolation, metadata support, and graceful degradation
Inputs: org_id, collection_name, documents with metadata, search queries with embeddings
Outputs: ChromaDB operations (create, upsert, search, delete) with tenant scoping and similarity scores
Dependencies: chromadb (optional), typing, logging, os (conditional imports with graceful fallback)
Security/RLS: Org-scoped collection naming, input sanitization, safe conditional imports
Notes: Optional component - enabled via CHROMA_ENABLED env var, graceful degradation when ChromaDB unavailable
"""

import os
from typing import List, Dict, Any, Optional
import logging

# ChromaDB imports - conditional based on availability
# This allows the application to run even if ChromaDB is not installed
# Useful for development environments or deployments that only use Supabase Vector
try:
    import chromadb
    from chromadb.config import Settings
    CHROMADB_AVAILABLE = True  # ChromaDB package is installed and importable
except ImportError:
    # ChromaDB package not installed - set everything to None for safe fallback
    CHROMADB_AVAILABLE = False
    chromadb = None
    Settings = None

# ChromaDB is only enabled if both environment flag is set AND package is available
CHROMA_ENABLED = os.getenv("CHROMA_ENABLED", "false").lower() == "true" and CHROMADB_AVAILABLE
# Directory for persistent ChromaDB storage - defaults to user's home directory
CHROMA_DIR = os.getenv("CHROMA_DIR", os.path.expanduser("~/llms/chroma"))

logger = logging.getLogger(__name__)


class ChromaDBClient:
    """ChromaDB client with org-scoped collections and metadata support.

    This client provides:
    1. Multi-tenant organization isolation through scoped collection names
    2. Graceful degradation when ChromaDB is not available
    3. Safe operations that handle both success and failure cases
    4. Automatic collection creation with org-specific metadata
    """

    def __init__(self):
        """Create a ChromaDB client wrapper with automatic initialization.

        The client will only initialize if ChromaDB is enabled and available.
        If not available, operations will safely return None or empty results.
        """
        self.client = None  # Will be set to actual ChromaDB client if available
        if CHROMA_ENABLED:
            self._initialize_client()  # Set up persistent storage connection
    
    def _initialize_client(self):
        """Initialize ChromaDB client with persistent storage.

        Sets up a persistent ChromaDB client that stores data on disk.
        Includes safety checks to prevent storing data inside the git repository.
        """
        # Double-check that ChromaDB is actually enabled
        if not CHROMA_ENABLED:
            logger.info("ChromaDB disabled - set CHROMA_ENABLED=true and install chromadb to enable")
            return

        # Double-check that the ChromaDB package is available
        if not CHROMADB_AVAILABLE:
            logger.warning("ChromaDB not available - install chromadb package: pip install chromadb")
            return

        try:
            # Security check: prevent storing ChromaDB data inside the git repository
            # This prevents accidentally committing vector data to version control
            if os.path.abspath(CHROMA_DIR).startswith(os.path.abspath(".")):
                raise ValueError(f"ChromaDB directory cannot be inside the repo: {CHROMA_DIR}")

            # Create the storage directory if it doesn't exist
            os.makedirs(CHROMA_DIR, exist_ok=True)

            # Initialize ChromaDB with persistent storage (data survives restarts)
            self.client = chromadb.PersistentClient(path=CHROMA_DIR)
            logger.info(f"ChromaDB client initialized successfully at {CHROMA_DIR}")

        except Exception as e:
            # If initialization fails, log error but don't crash - just disable ChromaDB
            logger.error(f"Failed to initialize ChromaDB client: {e}")
            self.client = None
    
    def get_collection_name(self, org_id: str, base_collection: str) -> str:
        """Generate org-scoped collection name for multi-tenancy.

        This ensures each organization has isolated collections, preventing
        data leakage between different tenants. Replaces hyphens with underscores
        since ChromaDB collection names have character restrictions.

        Args:
            org_id: Organization identifier (e.g., "acme-corp-123")
            base_collection: Base collection name (e.g., "policies")

        Returns:
            Scoped collection name (e.g., "policies_org_acme_corp_123")
        """
        # Replace hyphens with underscores for ChromaDB compatibility
        safe_org_id = org_id.replace('-', '_')
        return f"{base_collection}_org_{safe_org_id}"
    
    def create_or_get_collection(self, org_id: str, collection_name: str) -> Optional[Any]:
        """Create or get ChromaDB collection with org scoping.

        This method ensures each organization has isolated collections.
        If the collection exists, it returns it. If not, it creates a new one.
        Returns None if ChromaDB is disabled or unavailable.

        Args:
            org_id: Organization identifier for scoping
            collection_name: Base name of the collection (e.g., "policies")

        Returns:
            ChromaDB collection object or None if unavailable
        """
        # Return None immediately if ChromaDB is disabled or client failed to initialize
        if not CHROMA_ENABLED or not self.client:
            return None

        try:
            # Generate org-scoped collection name for multi-tenancy
            scoped_name = self.get_collection_name(org_id, collection_name)

            # Get existing collection or create new one with org metadata
            collection = self.client.get_or_create_collection(
                name=scoped_name,
                metadata={"org_id": org_id, "base_name": collection_name}  # Track org ownership
            )

            logger.debug(f"ChromaDB collection ready: {scoped_name}")
            return collection

        except Exception as e:
            # Log error but don't crash - graceful degradation
            logger.error(f"Failed to create/get ChromaDB collection {collection_name}: {e}")
            return None
    
    def upsert_chunks(
        self,
        org_id: str,
        collection_name: str,
        chunk_ids: List[str],
        embeddings: List[List[float]],
        texts: List[str],
        metadatas: List[Dict[str, Any]]
    ) -> bool:
        """Upsert document chunks to ChromaDB collection.

        'Upsert' means insert if new, update if exists. This method stores
        text chunks with their embeddings for semantic search. All arrays
        must have the same length and correspond to the same chunks.

        Args:
            org_id: Organization ID for scoping
            collection_name: Base collection name
            chunk_ids: Unique identifiers for each chunk
            embeddings: Vector embeddings for each chunk (for similarity search)
            texts: The actual text content of each chunk
            metadatas: Additional metadata (title, source, etc.) for each chunk

        Returns:
            True if successful, False if failed or ChromaDB unavailable
        """
        # Return early if ChromaDB is disabled
        if not CHROMA_ENABLED:
            return False

        # Validate that we have data to upsert
        if not (chunk_ids and embeddings and texts and metadatas):
            logger.warning("ChromaDB upsert called with empty data")
            return False

        # Ensure all data arrays have the same length (data consistency check)
        if not (len(chunk_ids) == len(embeddings) == len(texts) == len(metadatas)):
            logger.error("ChromaDB upsert data length mismatch")
            return False

        # Get or create the org-scoped collection
        collection = self.create_or_get_collection(org_id, collection_name)
        if not collection:
            return False

        try:
            # Store chunks in ChromaDB - upsert means insert new or update existing
            collection.upsert(
                ids=chunk_ids,           # Unique identifiers for deduplication
                embeddings=embeddings,   # Vector embeddings for similarity search
                documents=texts,         # The actual searchable text content
                metadatas=metadatas      # Additional metadata for context and filtering
            )

            logger.info(f"ChromaDB upserted {len(chunk_ids)} chunks to {collection_name}")
            return True

        except Exception as e:
            # Log error but don't crash - graceful degradation
            logger.error(f"Failed to upsert chunks to ChromaDB: {e}")
            return False
    
    def search_chunks(
        self,
        org_id: str,
        collection_name: str,
        query_embedding: List[float],
        limit: int = 8,
        where: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search ChromaDB collection for similar chunks.

        Performs semantic similarity search using vector embeddings.
        Returns chunks ordered by similarity (most similar first).

        Args:
            org_id: Organization ID for scoping
            collection_name: Base collection name to search
            query_embedding: Vector embedding of the search query
            limit: Maximum number of results to return
            where: Optional metadata filters (e.g., {"source": "policies"})

        Returns:
            List of matching chunks with similarity scores and metadata
        """
        # Return empty list if ChromaDB is disabled
        if not CHROMA_ENABLED:
            return []

        # Validate that we have a query embedding
        if not query_embedding:
            logger.warning("ChromaDB search called with empty query embedding")
            return []

        # Get the org-scoped collection
        collection = self.create_or_get_collection(org_id, collection_name)
        if not collection:
            return []

        try:
            # Perform semantic similarity search
            results = collection.query(
                query_embeddings=[query_embedding],  # Search query as vector
                n_results=limit,                     # Max results to return
                where=where,                         # Optional metadata filters
                include=["documents", "metadatas", "distances"]  # What to include in response
            )

            # Convert ChromaDB's nested format to our standard format
            chunks = []
            if results["ids"] and results["ids"][0]:
                for i in range(len(results["ids"][0])):
                    metadata = results["metadatas"][0][i] if results.get("metadatas") and results["metadatas"][0] else {}
                    chunks.append({
                        "chunk_id": results["ids"][0][i],
                        "text": results["documents"][0][i] if results.get("documents") and results["documents"][0] else "",
                        "metadata": metadata,
                        "score": results["distances"][0][i] if results.get("distances") and results["distances"][0] else 1.0,  # ChromaDB returns distances (lower = better)
                        "doc_id": metadata.get("doc_id")
                    })

            logger.debug(f"ChromaDB search returned {len(chunks)} chunks from {collection_name}")
            return chunks

        except Exception as e:
            logger.error(f"Failed to search ChromaDB collection {collection_name}: {e}")
            return []
    
    def delete_collection(self, org_id: str, collection_name: str) -> bool:
        """Delete org-scoped ChromaDB collection."""
        if not CHROMA_ENABLED or not self.client:
            return False

        try:
            scoped_name = self.get_collection_name(org_id, collection_name)
            self.client.delete_collection(name=scoped_name)
            logger.info(f"ChromaDB collection deleted: {scoped_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to delete ChromaDB collection {collection_name}: {e}")
            return False


# Global instance
chroma_client = ChromaDBClient()
