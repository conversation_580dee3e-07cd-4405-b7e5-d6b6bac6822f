#!/bin/bash
#
# File: arioncomply-v1/ai-backend/python-backend/start-backend.sh
# File Description: Start the ArionComply Python AI Backend HTTP server
# Purpose: Launch FastAPI server with uvicorn for local development and testing
#
# Usage: bash start-backend.sh [--port PORT] [--host HOST] [--dev]
#
# Setup Instructions:
#   1. Install dependencies: pip install -r requirements.txt
#   2. Create .env file from .env.example with required variables
#   3. Run script from ai-backend/python-backend directory
#   4. Server will be available at http://localhost:9000/ai/chat
#
# Options:
#   --port PORT    Set server port (default: 9000)
#   --host HOST    Set server host (default: 127.0.0.1)
#   --dev          Enable development mode with auto-reload
#
# Endpoints:
#   POST /ai/chat  Main chat endpoint for LLM interactions
#   GET /docs      FastAPI Swagger documentation
#   GET /health    Server health check (if implemented)
#
# Dependencies: Python 3.8+, uvicorn, fastapi, requirements.txt
# Security/RLS: Local development only - no production secrets
# Notes: Coordinates with workflow GUI on localhost:10000 and local LLMs on ports 8081-8083
#

set -euo pipefail

# Always run from this script's directory for proper Python module resolution
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd "$SCRIPT_DIR"

# Default server configuration
DEFAULT_HOST="127.0.0.1"
DEFAULT_PORT="9000"
DEVELOPMENT_MODE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --port)
            if [[ $# -gt 1 && "$2" =~ ^[0-9]+$ ]]; then
                DEFAULT_PORT="$2"
                shift 2
            else
                echo "❌ --port requires a numeric value"
                exit 1
            fi
            ;;
        --host)
            if [[ $# -gt 1 ]]; then
                DEFAULT_HOST="$2"
                shift 2
            else
                echo "❌ --host requires a hostname or IP"
                exit 1
            fi
            ;;
        --dev)
            DEVELOPMENT_MODE=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [--port PORT] [--host HOST] [--dev]"
            echo "Start ArionComply AI Backend server"
            echo ""
            echo "Options:"
            echo "  --port PORT    Server port (default: 9000)"
            echo "  --host HOST    Server host (default: 127.0.0.1)"  
            echo "  --dev          Enable development mode with auto-reload"
            echo "  --help         Show this help message"
            exit 0
            ;;
        *)
            echo "❌ Unknown argument: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

echo "🚀 Starting ArionComply AI Backend server..."

# Verify Python dependencies are installed
if [ ! -f "requirements.txt" ]; then
    echo "❌ requirements.txt not found!"
    echo "💡 Run from ai-backend/python-backend directory"
    exit 1
fi

echo "📋 Checking Python dependencies..."
if ! python3 -c "import fastapi, uvicorn" 2>/dev/null; then
    echo "❌ Required dependencies not installed"
    echo "💡 Run: pip install -r requirements.txt"
    exit 1
fi

# Check for environment configuration
if [ ! -f ".env" ] && [ ! -f ".env.example" ]; then
    echo "⚠️  No .env file found (optional for local development)"
else
    echo "✅ Environment configuration available"
fi

# Verify FastAPI app exists
if [ ! -f "app/main.py" ]; then
    echo "❌ app/main.py not found!"
    echo "💡 Ensure you're in the python-backend directory"
    exit 1
fi

echo "✅ Backend application found"

# Kill any existing server on the target port
echo "🧹 Cleaning up any existing servers on port $DEFAULT_PORT..."
lsof -ti:"$DEFAULT_PORT" | xargs kill -9 2>/dev/null || true

# Configure uvicorn arguments based on mode
UVICORN_ARGS="app.main:app --host $DEFAULT_HOST --port $DEFAULT_PORT"

if [ "$DEVELOPMENT_MODE" = true ]; then
    echo "🔄 Starting in development mode (auto-reload enabled)..."
    UVICORN_ARGS="$UVICORN_ARGS --reload --log-level debug"
else
    echo "🏃 Starting in production mode..."
    UVICORN_ARGS="$UVICORN_ARGS --log-level info"
fi

# Start uvicorn server in background
echo "🌐 Launching FastAPI server on http://$DEFAULT_HOST:$DEFAULT_PORT..."
python3 -m uvicorn $UVICORN_ARGS > .server.log 2>&1 &
SERVER_PID=$!

# Wait for server to start
sleep 3

# Verify server is responding
HEALTH_URL="http://$DEFAULT_HOST:$DEFAULT_PORT/ai/chat"
if curl -s -X POST "$HEALTH_URL" \
    -H "Content-Type: application/json" \
    -d '{"messages":[{"role":"user","content":"test"}],"orgId":"test"}' \
    > /dev/null 2>&1; then
    echo "✅ Server health check passed"
else
    echo "⚠️  Server started but health check failed (may be normal for skeleton)"
fi

# Save process ID for cleanup
echo "$SERVER_PID" > .backend.pid

echo ""
echo "✅ ArionComply AI Backend running on http://$DEFAULT_HOST:$DEFAULT_PORT"
echo "📖 API Documentation: http://$DEFAULT_HOST:$DEFAULT_PORT/docs"
echo "🎯 Chat Endpoint: POST http://$DEFAULT_HOST:$DEFAULT_PORT/ai/chat"
echo ""
echo "🔗 Integration ready for:"
echo "   • Workflow GUI (localhost:10000)"
echo "   • Local LLMs (ports 8081-8083)"
echo "   • Supabase Edge Functions"
echo ""
echo "💡 Server logs: tail -f .server.log"
echo "💡 To stop server: bash ./stop-backend.sh"
echo "💡 Process ID: $SERVER_PID"
echo ""