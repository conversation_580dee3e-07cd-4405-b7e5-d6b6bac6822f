id: Q093
query: >-
  What's multi-factor authentication and how hard is it to implement?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/A.9.4.2"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Annex A.9.4.2 Use of Secret Authentication Information"
    id: "ISO27001:2022/A.9.4.2"
    locator: "Annex A.9.4.2"
ui:
  cards_hint:
    - "MFA rollout plan"
  actions:
    - type: "start_workflow"
      target: "mfa_implementation"
      label: "Implement MFA"
output_mode: "both"
graph_required: false
notes: "Most cloud IAM providers offer MFA out-of-the-box; on-prem may need additional software"
---
### 93) What's multi-factor authentication and how hard is it to implement?

**Standard terms)**  
- **Secret authentication (A.9.4.2):** multi-factor usage to strengthen access controls.

**Plain-English answer**  
MFA requires two or more of: something you know (password), have (token/app), or are (biometrics). Cloud identity services (Azure AD, Okta) enable MFA with minimal effort; on-prem systems often integrate via plugins or RADIUS.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.9.4.2

**Why it matters**  
MFA is a high-impact control that significantly reduces unauthorized access.

**Do next in our platform**  
- Launch **MFA Implementation** workflow.  
- Assign user groups and pilot rollout.

**How our platform will help**  
- **[Workflow]** Pre-configured MFA tasks.  
- **[Report]** Adoption tracking dashboard.

**Likely follow-ups**  
- “Which MFA methods are most user-friendly?”  

**Sources**  
- ISO/IEC 27001:2022 Annex A.9.4.2
