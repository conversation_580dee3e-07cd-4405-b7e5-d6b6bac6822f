id: Q091
query: >-
  What security controls do we actually need to implement for ISO 27001?
packs:
  - "ISO27001:2022"
  - "ISO27002:2022"
primary_ids:
  - "ISO27001:2022/A"
overlap_ids:
  - "ISO27002:2022/All"
capability_tags:
  - "Register"
  - "Draft Doc"
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Annex A Controls Overview"
    id: "ISO27001:2022/A"
    locator: "Annex A"
  - title: "ISO/IEC 27002:2022 — Control Set"
    id: "ISO27002:2022/All"
    locator: "Full Standard"
ui:
  cards_hint:
    - "Control library"
  actions:
    - type: "open_register"
      target: "control_library"
      label: "View All Controls"
    - type: "start_workflow"
      target: "control_selection"
      label: "Select Required Controls"
output_mode: "both"
graph_required: false
notes: "Identify only those controls relevant to your risk treatment scope"
---
### 91) What security controls do we actually need to implement for ISO 27001?

**Standard terms)**  
- **Annex A (ISO 27001 Annex A):** list of 93 specific controls.  
- **Control guidance (ISO 27002):** detailed implementation advice.

**Plain-English answer**  
You implement only the Annex A controls that address risks in your defined scope. Start with core areas—access management, cryptography, incident handling—then add others as your risk register indicates.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A  
- **Also relevant/Overlaps:** ISO/IEC 27002:2022 full control set

**Why it matters**  
Targeted control selection prevents over-engineering and wasted effort.

**Do next in our platform**  
- Open the **Control Library** register.  
- Run **Control Selection** workflow based on your risk profile.

**How our platform will help**  
- **[Register]** Pre-mapped risk-to-control links.  
- **[Draft Doc]** Auto-generate control implementation templates.  
- **[Workflow]** Guided task assignments for each control.

**Likely follow-ups**  
- “Which controls are most common for small businesses?”  

**Sources**  
- ISO/IEC 27001:2022 Annex A  
- ISO/IEC 27002:2022 (full)
