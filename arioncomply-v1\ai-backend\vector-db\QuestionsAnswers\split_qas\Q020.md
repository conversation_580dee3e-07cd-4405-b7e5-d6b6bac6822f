id: Q020
query: >-
  We’re a startup — when do we need to start worrying about this stuff?
packs:
  - "GDPR:2016"
  - "ISO27001:2022"
primary_ids:
  - "GDPR:2016/Art.24"
  - "ISO27001:2022/4.6"
overlap_ids:
  - "ISO27701:2019/4.1"
capability_tags:
  - "Draft Doc"
  - "Register"
  - "Workflow"
  - "Planner"
flags: []
sources:
  - title: "GDPR (Regulation 2016/679) — Accountability"
    id: "GDPR:2016/Art.24"
    locator: "Article 24"
  - title: "ISO/IEC 27001:2022 — Planning & Objectives"
    id: "ISO27001:2022/4.6"
    locator: "Clause 4.6"
  - title: "ISO/IEC 27701:2019 — PIMS Context"
    id: "ISO27701:2019/4.1"
    locator: "Clause 4.1"
ui:
  cards_hint:
    - "MVP compliance program"
    - "Startup-ready templates"
  actions:
    - type: "start_workflow"
      target: "mvp_program"
      label: "Launch MVP Compliance"
    - type: "open_register"
      target: "risk"
      label: "Initialize Risk Register"
output_mode: "both"
graph_required: false
notes: "Accountability starts at first processing event—scale as you grow"
---
### 20) We’re a startup — when do we need to start worrying about this stuff?

**Standard terms)**
- **Accountability (GDPR Article 24):** you must implement appropriate measures from first processing.
- **Context & planning (ISO/IEC 27001 Clause 4.6):** set scope, objectives, and risk criteria early.
- **PIMS Context (ISO/IEC 27701 Clause 4.1):** privacy extension kickoff.

**Plain-English answer**
From **day one**, when you handle any personal data, you’re accountable for security and privacy. Start small—inventory data, publish basic notices, enforce MFA, and set up backups and a simple risk process. Scale your ISMS as you grow.

**Applies to**
- **Primary:** GDPR Article 24; ISO/IEC 27001:2022 Clause 4.6
- **Also relevant/Overlaps:** ISO/IEC 27701:2019 Clause 4.1

**Why it matters**
Early habits are cheaper and more effective than late retrofitting.

**Do next in our platform**
- Use the **MVP Program** template to stand up core artifacts.
- Assign owners and set a quarterly review cadence.
- Link initial risk findings to simple controls.

**How our platform will help**
- **[Draft Doc]** MVP policy & process templates.
- **[Register]** Initial RoPA & risk registers.
- **[Workflow]** Guided setup steps.
- **[Planner]** Quarterly review scheduler.

**Likely follow-ups**
- “What’s the minimum viable program?” (See Q055–Q056)

**Sources**
- GDPR Article 24
- ISO/IEC 27001:2022 Clause 4.6
- ISO/IEC 27701:2019 Clause 4.1
