```yaml
id: Q214
question: How do we handle compliance when launching new products or services?
packs: ["GDPR:2016","ISO27001:2022","ISO27701:2019"]
primary_ids: ["GDPR:2016/Art.25","GDPR:2016/Art.35","ISO27001:2022/Cl.6.1","ISO27001:2022/Annex.A"]
overlap_ids: ["ISO27701:2019/Cl.7.2"]
capability_tags: ["Workflow","Register","Draft Doc","Approval","Versioning","Report"]
ui:
  actions:
    - target: "workflow"
      action: "open"
      args: { key: "product_change" }
    - target: "register"
      action: "open"
      args: { key: "ropa" }
cards_hint:
  - Map data & lawful basis; update notices.
  - Run security checklist; do DPIA if high risk.
  - Capture go/no-go approvals.
graph_required: false
```

### 214) How do we handle compliance when launching new products or services?

**Standard term(s)**

- **Privacy by design (Art. 25); DPIA (Art. 35).**

**Plain-English answer**\
Pre-launch, **map data**, set **lawful basis**, update **notices**, complete a **security review**, and run a **DPIA** if risk is high.

**Applies to**

- **Primary:** GDPR **Arts. 25, 35**; ISO/IEC 27001 **Cl. 6.1; Annex A**.
- **Also relevant/Overlaps:** ISO/IEC 27701.

**Why it matters**\
Catches issues **before** release.

**Do next in our platform**

- Use the **product change** workflow: inventory → DPIA screen → security checklist → notice updates → approvals.

**How our platform will help**

- **[Workflow] [Register] [Draft Doc] [Approval] [Versioning] [Report]** — Orchestrated steps and generated DPIA/notice artifacts.

**Likely follow-ups**

- “Is this feature high-risk?” → See Q105.

**Sources**

- GDPR **Arts. 25, 35**; ISO/IEC 27001 **Cl. 6.1; Annex A**.