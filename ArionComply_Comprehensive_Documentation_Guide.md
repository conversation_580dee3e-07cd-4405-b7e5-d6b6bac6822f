# ArionComply Comprehensive Documentation Guide

## Overview

ArionComply is a comprehensive AI-powered Standards Compliance Platform designed to democratize compliance through transparent, explainable AI automation. The platform serves as a "Personal AI Compliance Expert" available 24/7, providing enterprise-grade compliance expertise at a fraction of traditional consultant costs.

## Architecture Overview

### Core Architecture Components

- **Frontend**: Flutter Web/Native applications with conversational UI and multi-modal input
- **API Layer**: Supabase Edge Functions with metadata-driven routing and dynamic schema
- **Application Database**: PostgreSQL with comprehensive multi-tenant RLS and workflow management
- **Vector Database**: Separate Supabase project with pgvector for semantic search (v1.1+)
- **AI Backend**: Local LLM services with deterministic retrieval and explainable responses
- **Hybrid Content Strategy**: Database for transactional data, RAG documents for knowledge content

### Multi-Tenant Hybrid Cloud Architecture

The system implements a sophisticated multi-org hybrid cloud architecture with:

- **Org-Isolated Frontend Layer**: Flutter Web/Native with per-org configuration
- **Org-Isolated API Gateway**: Supabase Edge Functions with org routing and RLS enforcement
- **CPU-Optimized AI Backend**: Python AI Backend with SmolLM3/Mistral INT8 and cloud LLM fallback
- **Multi-Org Data Layer**: PostgreSQL with RLS + org isolation, pgvector with org partitioning
- **Hybrid Cloud Infrastructure**: Support for public, private, and hybrid cloud deployments

## Phase-Based Implementation Strategy

### Phase 1: MVP-Assessment-App (Foundation - 20 Requirements)

**Timeline**: 2-3 weeks | **Frontend**: Flutter Web | **Focus**: Conversational AI assessment

**Key Features**:

- Pure conversational AI interface (NO forms/wizards)
- Basic compliance dashboard
- Multi-standard knowledge base (ISO 27001, GDPR)
- Assessment through natural language chat
- Production-ready database (migrations 0001-0013)
- Comprehensive edge functions

### Phase 2: MVP-Demo-Light-App (Enhanced - 20 Requirements)

**Timeline**: +2-3 weeks | **Frontend**: Flutter Web Enhanced | **Focus**: Demo scenarios and document generation

**Key Features**:

- Adds form-based wizards (FIRST introduction of forms)
- Document generation engine
- Multi-framework support (ISO 27001 + GDPR + ISO 27701)
- Preset demo scenarios
- Enhanced assessment scoring

### Phase 3: MVP-Pilot (Comprehensive - 25 Requirements)

**Timeline**: **** weeks | **Frontend**: Flutter Native | **Focus**: Real customer deployment

**Key Features**:

- Complete Flutter Native app
- Advanced workflow management
- Asset, audit, and task management
- Mobile-optimized features
- 4 new database schemas (migrations 0014-0017)

### Phase 4: Production (Enterprise - 20 Requirements)

**Timeline**: **** weeks | **Frontend**: Flutter Native Enhanced | **Focus**: Enterprise deployment

**Key Features**:

- Complete enterprise platform
- Advanced analytics and reporting
- 15 additional database schemas (migrations 0018-0032)
- Enterprise security and monitoring
- Complete knowledge base for all frameworks

## AI Backend Architecture

### Multi-Tier Confidence-Based Retrieval System

The AI backend implements a sophisticated multi-tier architecture with progressive escalation:

1. **Tier 0: Deterministic Preprocessing** (≥85% threshold)

   - Canonical ID matching
   - Synonym expansion
   - Paraphrase matching
   - E-PMI associations
   - Graph crawling

2. **Tier 1a: ChromaDB Public Knowledge** (≥85% threshold)

   - Standards, regulations, assessments
   - Shared non-proprietary data

3. **Tier 1b: Supabase Vector Private Knowledge** (≥85% threshold)

   - Organization-specific data
   - Company policies and results

4. **Tier 2a: SLLM (SmolLM3/Mistral7B/Phi3)** (≥85% threshold)

   - Local CPU-optimized models
   - INT8 quantization

5. **Tier 2b/2c: GLLM (GPT-4/Claude)** (≥85% threshold)

   - Cloud LLM fallback
   - Anonymized data processing

6. **Tier 3: Human Escalation**
   - Expert ticket creation
   - Transparent communication to users

### Dual-Vector Multi-Pipeline Embedding Architecture

**Primary Pipelines**:

- **BGE-Large-EN-v1.5 + ONNX**: 1024-dim, SOTA quality, CPU-optimized
- **all-mpnet-base-v2**: 768-dim, good quality, reliable fallback

**Optional Pipelines**:

- **OpenAI Embeddings**: 1536/3072-dim, high quality (security considerations)
- **Placeholder**: 768-dim, testing only (no semantic understanding)

**Dual Vector Storage**:

- **ChromaDB**: Public shared knowledge (standards, regulations)
- **Supabase Vector**: Private organizational data (policies, results)

## Database Architecture

### Production-Ready Schema (Migrations 0001-0013)

**Core Tables**:

- **Multi-Tenant Foundation**: organizations, user_profiles, organization_settings
- **Conversation System**: conversation_sessions, conversation_messages
- **Assessment System**: assessment_conversations, assessment_insights, assessment_scores
- **Questionnaire Framework**: questionnaire_templates, sections, questions, instances, responses
- **Subscription & RBAC**: sub_plans, org_subscriptions, roles, permissions, user_roles
- **Document Management**: documents, document_versions, document_relationships, document_approvals
- **Comprehensive Logging**: api_request_logs, api_event_logs

**Enterprise Features**:

- Row-Level Security (RLS) policies
- Comprehensive audit logging
- Soft deletes and JSONB validation
- Multi-tenant isolation with org-scoped access

### Future Schema Expansions

**Phase 3 Additions (Migrations 0014-0017)**:

- Asset Management Schema
- Audit Management Schema
- Task Management Schema
- Enhanced Evidence Management

**Phase 4 Completion (Migrations 0018-0032)**:

- Standards management, regulatory reporting
- Processing activities, corrective actions
- Metrics and analytics, notification system
- Training management, vendor management
- And 7 additional enterprise schemas

## User Interface Evolution

### Interface Progression Strategy

**Phase 1**: **Conversational AI ONLY** - No forms, wizards, or traditional UI components
**Phase 2**: **Hybrid Interface** - Chat + Forms for demo scenarios and document generation
**Phase 3**: **Mobile Native** - Full native app with advanced workflows
**Phase 4**: **Enterprise Grade** - Complete admin and analytics interfaces

### Key UI Components

**Chat Interface** (`chatInterface.dart`):

- Primary AI-powered conversational assessment system
- Context-aware chat with voice input/output
- Customizable avatar settings
- Multi-modal interaction support

**Metadata-Driven UI Framework**:

- Dynamic form generation from metadata
- Consistent styling and responsive layouts
- Field mapping and data transformation
- Validation and business rules enforcement

## Compliance Framework Support

### Multi-Standard Regulatory Compliance

The platform supports unlimited regulatory standards through a metadata-driven approach:

**Supported Standards**:

- **ISO 27001**: Information Security Management
- **ISO 27701**: Privacy Information Management
- **GDPR**: General Data Protection Regulation
- **EU AI Act**: Artificial Intelligence Regulation
- **NIS2**: Network and Information Security
- **SOC 2**: Service Organization Control 2
- **HIPAA, PCI DSS, SOX**: Additional frameworks

**Key Features**:

- Automatic overlap detection across standards
- Cross-referenced documentation
- Unified control implementation
- Conflict resolution guidance
- AI-powered gap analysis

### Extensible Standards Framework

```sql
-- Extensible standards registry (supports any regulatory framework)
CREATE TABLE regulatory_standards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    standard_code TEXT NOT NULL UNIQUE, -- 'ISO27001', 'GDPR', 'SOX', 'HIPAA', etc.
    standard_name TEXT NOT NULL,
    standard_type TEXT NOT NULL, -- 'framework', 'law', 'regulation', 'guideline'
    jurisdiction TEXT[], -- ['EU', 'US', 'Global'] - supports multiple jurisdictions
    version TEXT NOT NULL,
    effective_date DATE,
    metadata JSONB NOT NULL, -- Flexible structure for any standard
    org_id UUID REFERENCES organizations(org_id),
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Workflow and Process Management

### Event-Driven Workflow System

**Unified Event System Architecture**:

- Event registry with metadata-driven triggers
- Database trigger integration
- Workflow engine integration
- Real-time event processing

**Workflow Lifecycle Management**:

- Draft, active, paused, blocked, completed, retired states
- Version control with running instances
- RACI mapping and permissions
- Immutable evidence artifacts

### Customer Workflow Examples

**Assessment Workflows**:

- Conversational compliance assessments
- Multi-framework gap analysis
- Risk assessment and treatment planning
- Evidence collection and validation

**Document Management Workflows**:

- Policy lifecycle management
- Version control and approvals
- Document relationships and dependencies
- Automated generation from templates

**Audit Management Workflows**:

- Audit planning and execution
- Finding management and corrective actions
- Evidence collection and review
- Compliance reporting and certification

## Security and Compliance Features

### Enterprise-Grade Security

**Multi-Tenant Isolation**:

- Org-scoped data segregation
- Row-Level Security (RLS) enforcement
- Tenant-specific encryption keys
- Data residency compliance

**AI Security**:

- Local-first LLM processing
- PII anonymization for cloud services
- Explainable AI with audit trails
- Confidence-based escalation

**Audit and Compliance**:

- Comprehensive activity logging
- Tamper-evident record storage
- Complete traceability chains
- Regulatory reporting automation

### Data Classification and Handling

**Public vs Private Data**:

- **Public Data**: Standards, regulations, best practices (ChromaDB)
- **Private Data**: Organizational policies, assessments, proprietary information (Supabase Vector)
- **Automatic Classification**: AI-powered data routing
- **Security Controls**: Encryption, access controls, audit logging

## Integration and Extensibility

### API Architecture

**Supabase Edge Functions**:

- `ai-conversation-start/send/stream`: Chat interface
- `compliance-proxy`: Unified AI backend proxy
- `ui-suggestions-defaults`: Contextual suggestions
- `app-audit-read`: Audit log access

**Metadata-Driven APIs**:

- Dynamic endpoint generation
- Consistent validation and error handling
- Automatic documentation generation
- Version control and backward compatibility

### External System Integration

**Integration Capabilities**:

- Standard APIs and authentication protocols
- Data import/export with validation
- Webhook and event-driven integrations
- Third-party tool connections

**Deployment Options**:

- **Public Cloud**: AWS, Azure, GCP support
- **Private Cloud**: Kubernetes, bare metal
- **Hybrid**: Split architecture with configurable components
- **CPU-Only**: Optimized for environments without GPU

## Development and Deployment

### Technology Stack

**Frontend**: Flutter Web/Native with Dart
**Backend**: Python FastAPI with Supabase Edge Functions
**Database**: PostgreSQL with pgvector extension
**AI/ML**: Local SLLMs (SmolLM3, Mistral) + Cloud LLMs (GPT-4, Claude)
**Vector Storage**: ChromaDB + Supabase Vector
**Infrastructure**: Kubernetes, Docker, multi-cloud support

### Development Workflow

1. **Local Development**: Use lightweight models for fast iteration
2. **Quality Testing**: Switch to production models for validation
3. **Security Testing**: Validate with local-only pipelines
4. **Performance Testing**: Benchmark across different deployment scenarios

### Monitoring and Observability

**System Monitoring**:

- Pipeline health and performance metrics
- Dual-vector search analytics
- Confidence scoring trends
- Resource utilization tracking

**Business Metrics**:

- Assessment completion rates
- Document generation success
- User engagement analytics
- Compliance posture dashboards

## Getting Started

### Prerequisites

- PostgreSQL 14+ with pgvector extension
- Python 3.9+ with FastAPI
- Flutter 3.0+ for frontend development
- Supabase account for edge functions
- Docker for containerized deployment

### Quick Start

1. **Database Setup**: Run migrations 0001-0013 for core functionality
2. **AI Backend**: Deploy Python backend with embedding pipelines
3. **Edge Functions**: Deploy Supabase functions for API layer
4. **Frontend**: Build Flutter Web app for Phase 1 features
5. **Knowledge Base**: Populate RAG store with compliance content

### Configuration

**Environment Variables**:

```bash
# Database
DATABASE_URL="postgresql://..."
SUPABASE_URL="https://..."
SUPABASE_ANON_KEY="..."

# AI Backend
AI_BACKEND_URL="http://localhost:8000"
EMBEDDING_PIPELINE_PRIMARY="bge-large-onnx"
EMBEDDING_PIPELINE_FALLBACK="all-mpnet-base-v2,placeholder"

# Security
LOGGING_REQUIRED="true"
SYSTEM_ORG_ID="..."
```

## Detailed Requirements Analysis

### Core Requirements Summary (101 Total Requirements)

The ArionComply platform addresses 101 comprehensive requirements across 4 development phases:

**Phase 1 Requirements (20 Requirements)**:

- Metadata-driven architecture with dynamic schema
- Multi-standard regulatory compliance support
- Conversational AI assessment interface
- Conversation session management (auto-start, resume, retrieve)
- Suggested follow-ups & deep links
- Thumbs feedback (rating + comment)
- Slash shortcuts (org-scoped)
- Basic platform information interface
- Deterministic query preprocessing pipeline
- Multi-hop graph traversal and path scoring
- Synonym and paraphrase mapping generation
- Cards and prose output mode support
- Multi-modal input processing
- Deterministic retrieval and explainable AI
- Intent classification and routing system
- Multi-tier confidence scoring framework
- Human escalation and transparency system
- Enterprise security and multi-tenancy
- Edge function orchestration
- UI/UX standards and responsiveness
- AI backend operations and model lifecycle

**Phase 2 Requirements (20 Requirements)**:

- Comprehensive document generation
- Advanced analytics and reporting
- Integration and extensibility
- Comprehensive standards, regulations, and laws knowledge
- Legal disclaimers and human-in-the-loop requirements
- Enhanced assessment capabilities
- Multi-framework overlap detection
- Document template management
- Preset scenario processing
- Form-based assessment wizards

**Phase 3 Requirements (25 Requirements)**:

- Real-time compliance monitoring
- Task and workflow management
- Phased deployment strategy
- Workflow lifecycle and governance
- Workflow engine reliability
- Human-in-the-loop and approvals
- Workflow templates, import/export, and reuse
- Certification preparation flows
- Compliance registers and trackers
- Automated document retrieval and creation
- Planning, scheduling, and guidance
- Incident and breach management
- Asset management
- Risk management
- Vendor and third-party management

**Phase 4 Requirements (20 Requirements)**:

- Data subject rights and consent management
- DPIAs and records of processing activities (RoPA)
- AI Act risk classification and monitoring
- Vulnerability and patch management
- Business continuity and disaster recovery (BCP/DRP)
- Training and awareness management
- Policy lifecycle management
- Regulatory reporting and notifications
- ISMS/PIMS process management
- Policy and record control
- GDPR/27701 processor-controller governance
- Advanced enterprise features

### Conversation Session Lifecycle

**Goals**:

- Provide seamless UX on the chat screen: no explicit "Start Session" required
- Ensure every message is associated with an org-scoped session for logging and traceability
- Allow users to resume prior sessions and view recent history

**Behaviors**:

- **Auto-start on first send**: If the chat screen has no active sessionId and the user submits a message, the Edge layer creates a new session (equivalent to ai-conversation-start) and then processes the message
- **Resume on mount**: After login, the chat screen attempts to resume the most recent active session
- **Retrieve previous sessions**: Provide a lightweight listing of recent sessions scoped to org/user with RLS

**API Additions**:

- `ai-conversation-send` (adjustment): Accepts sessionId optional. If absent, creates a new session internally
- `ai-conversation-start` (unchanged): Explicit creation path remains for callers that want a welcome message
- `ai-conversation-sessions` (new): GET endpoint to list recent sessions for current org/user

### Suggestion Model & Feedback System

**Suggestion Model**:

```json
{
  "text": "string",
  "action": {
    "type": "nav|start_workflow|open_register|open_template",
    "target": "string",
    "url": "string"
  }
}
```

**Feedback Model**:

- Table: `conversation_message_feedback(feedback_id, org_id, user_id, session_id, message_id, rating, reason_code?, comment?, created_at)`
- Edge Endpoint: `ai-message-feedback` (POST)
- UI Behavior: Show thumbs adjacent to assistant reply; modal for optional comment

**Slash Shortcuts**:

- Table: `ui_shortcuts(id, org_id, shortcut, description, action_type, target?, url?, enabled, created_at, updated_at)`
- Edge Endpoint: `ui-shortcuts` (GET)
- UI Behavior: Load on startup and cache in memory; interpret input starting with `/`

## Metadata-Driven Architecture Deep Dive

### Core Metadata Components

**1. Metadata Registry**: Central repository for all entity definitions, API configurations, UI layouts, and business rules
**2. Edge Function Router**: Dynamic API endpoint generation based on metadata definitions
**3. Query Builder**: Automatic SQL generation with RLS enforcement and optimization
**4. UI Component Factory**: Dynamic Flutter widget generation from metadata specifications
**5. Validation Engine**: Comprehensive data validation using JSON Schema definitions
**6. Permission Enforcer**: Role-based access control with metadata-driven rules
**7. Audit Logger**: Comprehensive activity tracking with metadata context

### Dynamic Schema Generation

The system automatically generates:

- **CRUD API endpoints** based on entity metadata
- **UI forms and lists** from field definitions
- **Validation rules** from schema constraints
- **Permission checks** from role definitions
- **Database queries** with proper RLS enforcement

### JSON Schema Architecture

**Schema Definition Structure**:

```json
{
  "entity_name": "policies",
  "api_config": {
    "endpoints": ["create", "read", "update", "delete", "list"],
    "rate_limits": { "create": 10, "read": 100 },
    "auth_required": true
  },
  "ui_config": {
    "list_view": {
      "columns": ["title", "status", "updated_at"],
      "filters": ["status", "category"],
      "actions": ["edit", "delete", "duplicate"]
    },
    "form_view": {
      "layout": "two_column",
      "sections": ["basic_info", "content", "approval"]
    }
  },
  "database_config": {
    "table_name": "policies",
    "primary_key": "policy_id",
    "rls_policy": "org_scoped",
    "indexes": ["org_id", "status", "updated_at"]
  },
  "fields": {
    "title": {
      "type": "string",
      "required": true,
      "max_length": 200,
      "ui_component": "text_input"
    },
    "content": {
      "type": "string",
      "required": true,
      "ui_component": "rich_text_editor"
    },
    "status": {
      "type": "enum",
      "values": ["draft", "review", "approved", "archived"],
      "default": "draft",
      "ui_component": "dropdown"
    }
  },
  "permissions": {
    "create": ["policy_author", "policy_admin"],
    "read": ["all_authenticated"],
    "update": ["policy_author", "policy_admin"],
    "delete": ["policy_admin"]
  },
  "business_rules": {
    "approval_required": {
      "condition": "status == 'approved'",
      "action": "require_approval_workflow"
    }
  }
}
```

## Customer Workflows and User Flows

### Primary User Flows

**1. Compliance Assessment Flow**:

- User initiates conversation with AI assistant
- System guides through framework selection (ISO 27001, GDPR, etc.)
- Natural language assessment questions with intelligent follow-ups
- Real-time gap analysis and scoring
- Automated report generation with recommendations

**2. Document Generation Flow**:

- Assessment data collection through conversational interface
- Template selection based on compliance framework
- AI-powered document generation (95% complete drafts)
- Human review and customization workflow
- Version control and approval process

**3. Chat Interface Workflow**:

- Seamless session management (auto-start, resume, retrieve)
- Context-aware conversations with memory
- Multi-modal input (text, voice, document upload)
- Suggested follow-ups and deep links
- Feedback collection (thumbs up/down with comments)

### Detailed Workflow Examples

**Assessment Conversation Flow**:

```
User: "I need help with ISO 27001 compliance"
AI: "I'll help you assess your ISO 27001 readiness. Let's start with your organization's current security posture. Do you have an Information Security Management System (ISMS) in place?"
User: "We have some security policies but nothing formal"
AI: "That's a good starting point. Let me ask about specific controls..."
[Continues with intelligent follow-up questions]
```

**Document Generation Example**:

```
Input: Assessment data + Framework selection (ISO 27001)
Process: AI analyzes gaps and requirements
Output:
- Statement of Applicability (SoA)
- ISMS Policy draft
- Risk Assessment template
- Implementation roadmap
```

### User Interface Components

**Chat Interface Features**:

- Real-time typing indicators
- Message history with search
- Voice input/output capabilities
- File upload and document scanning
- Contextual suggestions and shortcuts

**Dashboard Components**:

- Compliance posture overview
- Progress tracking and metrics
- Task management and deadlines
- Document library and templates
- Audit trail and activity logs

**Assessment Wizard** (Phase 2+):

- Step-by-step guided assessment
- Progress indicators and validation
- Evidence collection and upload
- Scoring and gap analysis
- Report generation and export

## Event-Driven Workflow System

### Unified Event Architecture

**Event Registry System**:

- Centralized event definitions with metadata
- Trigger conditions and business rules
- Action mappings and workflow routing
- Audit logging and traceability

**Database Trigger Integration**:

```sql
-- Example: Automatic workflow trigger on assessment completion
CREATE OR REPLACE FUNCTION trigger_assessment_completion()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.completion_percentage = 100 AND OLD.completion_percentage < 100 THEN
        INSERT INTO workflow_instances (
            workflow_template_id,
            trigger_event,
            entity_id,
            org_id,
            status
        ) VALUES (
            'assessment_completion_workflow',
            'assessment_completed',
            NEW.instance_id,
            NEW.org_id,
            'active'
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

**Workflow Engine Integration**:

- State machine management
- Task assignment and routing
- Deadline tracking and escalation
- Approval workflows and gates
- Notification and communication

### Flutter Integration Patterns

**MetadataWidget System**:

```dart
class MetadataWidget extends StatefulWidget {
  final Map<String, dynamic> fieldMetadata;
  final Function(String, dynamic) onValueChanged;

  @override
  Widget build(BuildContext context) {
    return _buildWidgetFromMetadata(fieldMetadata);
  }

  Widget _buildWidgetFromMetadata(Map<String, dynamic> metadata) {
    switch (metadata['ui_component']) {
      case 'text_input':
        return TextFormField(
          decoration: InputDecoration(
            labelText: metadata['label'],
            hintText: metadata['hint']
          ),
          validator: _buildValidator(metadata),
          onChanged: (value) => onValueChanged(metadata['field_name'], value)
        );
      case 'dropdown':
        return DropdownButtonFormField(
          items: _buildDropdownItems(metadata['options']),
          decoration: InputDecoration(labelText: metadata['label']),
          onChanged: (value) => onValueChanged(metadata['field_name'], value)
        );
      // Additional widget types...
    }
  }
}
```

**Event Handling in Flutter**:

```dart
class EventHandler {
  static void handleWorkflowEvent(WorkflowEvent event) {
    switch (event.type) {
      case 'task_assigned':
        _showTaskNotification(event);
        _updateTaskList(event);
        break;
      case 'approval_required':
        _navigateToApprovalScreen(event);
        break;
      case 'deadline_approaching':
        _showDeadlineAlert(event);
        break;
    }
  }
}
```

## Advanced AI Features and Capabilities

### Vincent Granville's LLM2.0 Implementation

**6-Stage Deterministic Preprocessing Pipeline**:

1. **Stage 1: Canonical ID Matching**

   - Exact pattern matching for compliance IDs (e.g., "ISO27001:2022/A.5.1")
   - Immediate response with confidence score 1.0
   - No LLM processing required

2. **Stage 2: Synonym Expansion**

   - Pre-built synonym mappings from content metadata
   - Term expansion with confidence scoring
   - Domain-specific compliance terminology

3. **Stage 3: Paraphrase Matching**

   - Alternative question phrasing identification
   - Q&A content paraphrase mappings
   - Confidence threshold validation

4. **Stage 4: E-PMI Associations**

   - Enhanced Pointwise Mutual Information scoring
   - Contextually related term discovery
   - PMI confidence score calculation

5. **Stage 5: Graph Crawling**

   - Multi-hop graph traversal with weighted edges
   - Hop decay factors (1.0 → 0.7 → 0.4)
   - Relationship type support (see_also, prerequisite, implements, conflicts_with, supersedes)

6. **Stage 6: RAG Preparation**
   - Query enhancement with preprocessing results
   - Vector retrieval optimization
   - Fallback to traditional RAG if no deterministic match

### Multi-Hop Graph Traversal

**Graph Relationship Types**:

- **see_also**: Related compliance requirements
- **prerequisite**: Dependencies and prerequisites
- **implements**: Implementation relationships
- **conflicts_with**: Conflicting requirements
- **supersedes**: Version and update relationships

**Path Scoring Algorithm**:

```python
def calculate_path_score(path, edge_weights, hop_decay_factors):
    """Calculate composite path score with hop decay"""
    total_score = 0.0

    for i, edge in enumerate(path):
        hop_factor = hop_decay_factors[min(i, len(hop_decay_factors) - 1)]
        edge_weight = edge_weights.get(edge.relationship_type, 0.5)
        semantic_relevance = edge.semantic_similarity

        edge_score = edge_weight * semantic_relevance * hop_factor
        total_score += edge_score

    return min(1.0, total_score / len(path))
```

### Confidence Scoring Framework

**8-Factor Confidence Calculation**:

1. **Semantic Similarity** (25%): Vector similarity scores
2. **Intent Alignment** (20%): Query intent matching
3. **Source Authority** (15%): Authoritative source weighting
4. **Completeness** (15%): Response completeness assessment
5. **Context Relevance** (10%): Organizational context matching
6. **Historical Success** (10%): Past query success rates
7. **Response Coherence** (5%): Logical consistency check

**Universal 85% Threshold**:

- All tiers must achieve ≥85% confidence before user response
- Automatic escalation to next tier if threshold not met
- Transparent communication when all tiers fail
- Human expert ticket creation for <85% confidence

### Output Mode Support

**Cards Format** (Structured Evidence):

```json
{
  "output_mode": "cards",
  "cards": [
    {
      "title": "ISO 27001 A.5.1.1 - Information Security Policies",
      "content": "Structured evidence content...",
      "confidence": 0.92,
      "source": "ISO27001:2022",
      "ui_actions": ["view_details", "add_to_plan", "generate_template"],
      "cards_hint": ["Policy Template", "Implementation Guide", "Gap Analysis"]
    }
  ]
}
```

**Prose Format** (Natural Language):

```json
{
  "output_mode": "prose",
  "content": "Based on your organization's current state, I recommend starting with...",
  "confidence": 0.89,
  "reasoning_chain": [
    "Assessment analysis",
    "Gap identification",
    "Recommendation generation"
  ],
  "typing_simulation": true
}
```

**Both Format** (Combined):

```json
{
  "output_mode": "both",
  "prose": {
    /* prose response */
  },
  "cards": [
    /* structured evidence */
  ],
  "metadata": {
    "primary_mode": "prose",
    "evidence_count": 3,
    "confidence": 0.91
  }
}
```

## Supabase Edge Functions Architecture

### Production-Ready Edge Functions

**Core Functions**:

- `ai-conversation-start`: Session initiation with org context
- `ai-conversation-send`: Message processing with assistant router
- `ai-conversation-stream`: Server-sent events for real-time responses
- `compliance-proxy`: Unified AI backend proxy with provider routing
- `ui-suggestions-defaults`: Contextual suggestion delivery
- `app-audit-read`: Audit log access with RLS enforcement

**Shared Utilities**:

- `assistant_router.ts`: AI backend forwarding and load balancing
- `logger.ts`: Comprehensive event logging with W3C trace support
- `jwt.ts`: Authentication parsing and org extraction
- `config.ts`: Provider configuration and feature flags
- `schemas.ts`: Request validation and type safety

### Edge Function Features

**W3C Trace Support**:

- Full traceability with traceparent headers
- Correlation ID generation and propagation
- Distributed tracing across service boundaries
- Performance monitoring and debugging

**Mandatory Logging**:

- All requests/responses logged with retry logic
- Org-scoped logging with RLS enforcement
- Event correlation and audit trails
- Error tracking and alerting

**JWT Integration**:

- Org/user extraction from Supabase Auth
- Role-based access control
- Session management and validation
- Multi-tenant context enforcement

### Test Harness Strategy

**Two Lightweight Web UIs**:

1. **llm-comparison**: Provider/model experiments (OpenAI/Anthropic)

   - Independent harness for research-oriented model testing
   - No persistence required
   - Direct provider API integration

2. **workflow-gui**: End-to-end flow testing
   - Mirrors production contracts and headers
   - Tests ai-conversation-start/send/stream + compliance-proxy
   - Full integration testing capabilities

**Unified Proxy Contract**:

```typescript
// Simple (legacy) contract
{ user_query: string } → { content, model?, usage? }

// Rich contract
{
  provider?: 'openai'|'claude'|'both',
  messages: [{role, content}],
  systemPrompt?: string,
  parameters?: object
}

// For provider: 'both'
{ openai: {...}, claude: {...} }
```

## Knowledge Base and Content Architecture

### RAG Document Structure

**Hybrid Database-RAG Strategy**:

- **Database**: Transactional data, user interactions, assessments
- **RAG Documents**: Knowledge content, standards, templates, guidance

**Knowledge Base Organization**:

```
knowledge_base/
├── compliance_frameworks/
│   ├── iso27001/
│   │   ├── requirements.md
│   │   ├── implementation_guide.md
│   │   ├── evidence_examples.md
│   │   └── control_mappings.md
│   ├── gdpr/
│   │   ├── articles.md
│   │   ├── implementation_guide.md
│   │   ├── dpia_templates.md
│   │   └── consent_management.md
│   ├── iso27701/
│   ├── eu_ai_act/
│   ├── nis2/
│   └── sox/
├── document_templates/
│   ├── policies/
│   │   ├── isms_policy_template.md
│   │   ├── data_protection_policy.md
│   │   ├── incident_response_policy.md
│   │   └── access_control_policy.md
│   ├── procedures/
│   │   ├── risk_assessment_procedure.md
│   │   ├── audit_procedure.md
│   │   └── change_management_procedure.md
│   └── statements/
│       ├── soa_template.md
│       ├── privacy_notice_template.md
│       └── data_processing_agreement.md
├── assessment_guidance/
│   ├── question_libraries/
│   │   ├── iso27001_questions.md
│   │   ├── gdpr_questions.md
│   │   └── cross_framework_questions.md
│   ├── scoring_methodologies/
│   │   ├── maturity_models.md
│   │   ├── gap_analysis_frameworks.md
│   │   └── risk_scoring_models.md
│   └── best_practices/
│       ├── implementation_roadmaps.md
│       ├── common_pitfalls.md
│       └── success_patterns.md
└── cross_references/
    ├── framework_mappings.md
    ├── overlap_analysis.md
    ├── conflict_resolution.md
    └── unified_controls.md
```

### Multi-Standard Overlap Detection

**Automatic Overlap Analysis**:

- AI-powered requirement mapping across frameworks
- Conflict detection and resolution guidance
- Unified control implementation strategies
- Cross-reference documentation generation

**Example Overlap Mapping**:

```json
{
  "requirement_id": "unified_access_control",
  "mapped_standards": {
    "ISO27001": ["A.9.1.1", "A.9.1.2", "A.9.2.1"],
    "GDPR": ["Article 25", "Article 32"],
    "SOX": ["Section 302", "Section 404"],
    "HIPAA": ["164.312(a)(1)", "164.312(a)(2)"]
  },
  "overlap_type": "complementary",
  "implementation_strategy": "unified_policy",
  "conflicts": [],
  "unified_control": {
    "title": "Access Control Management",
    "description": "Comprehensive access control satisfying multiple frameworks",
    "implementation_guidance": "...",
    "evidence_requirements": [
      "policy_document",
      "access_logs",
      "review_records"
    ]
  }
}
```

## Enterprise Features and Scalability

### Multi-Tenant Architecture

**Tenant Isolation Strategy**:

```sql
-- Enhanced multi-tenant foundation
CREATE TABLE tenant_configurations (
    tenant_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_name TEXT NOT NULL UNIQUE,
    tenant_domain TEXT NOT NULL UNIQUE,
    deployment_type TEXT NOT NULL, -- 'shared', 'dedicated', 'hybrid'
    cloud_environment TEXT NOT NULL, -- 'public', 'private', 'hybrid'
    data_residency TEXT[], -- ['EU', 'US', 'UK'] - jurisdiction requirements
    encryption_key_id UUID NOT NULL, -- Tenant-specific encryption
    resource_limits JSONB NOT NULL, -- CPU, memory, storage limits
    compliance_requirements TEXT[], -- Required standards per tenant
    subscription_tier TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tenant-aware RLS policy template
CREATE OR REPLACE FUNCTION app_current_tenant_id()
RETURNS uuid LANGUAGE sql STABLE AS $
  SELECT COALESCE(
    NULLIF(current_setting('app.current_tenant_id', true), '')::uuid,
    NULLIF((current_setting('request.jwt.claims', true)::jsonb ->> 'tenant_id'), '')::uuid,
    NULLIF((current_setting('request.jwt.claims', true)::jsonb ->> 'org_id'), '')::uuid
  );
$;
```

**Tenant-Specific Security**:

- Encryption keys per tenant
- Data residency compliance
- Resource allocation and limits
- Compliance requirement customization

### CPU-Optimized Deployment

**SLLM Architecture**:

```python
class CPUOptimizedSLLMManager:
    def __init__(self):
        self.model_cache = ModelCache()
        self.batch_processor = BatchProcessor()
        self.performance_monitor = PerformanceMonitor()

    async def initialize_cpu_models(self) -> Dict[str, CPUModel]:
        """Initialize CPU-optimized models with INT8 quantization"""

        models = {}

        # SmolLM3 for general compliance tasks
        models['smollm3'] = await self.load_cpu_model(
            model_name='SmolLM3-8B-Instruct',
            quantization='INT8',
            optimization='cpu',
            max_context=4096,
            use_cases=['assessment', 'basic_qa', 'document_analysis']
        )

        # Mistral for complex reasoning
        models['mistral'] = await self.load_cpu_model(
            model_name='Mistral-7B-Instruct',
            quantization='INT8',
            optimization='cpu',
            max_context=8192,
            use_cases=['complex_reasoning', 'multi_step_analysis', 'document_generation']
        )

        return models
```

**Performance Characteristics**:

- **BGE-ONNX**: 2-3s load, 350MB memory, 25-30ms inference, 95% quality
- **all-mpnet**: 1-2s load, 400MB memory, 50-100ms inference, 85% quality
- **OpenAI**: 0s load, 0MB memory, 200-500ms inference, 90% quality (if permitted)
- **Placeholder**: 0s load, 0MB memory, 1-2ms inference, 0% quality (testing only)

### Hybrid Cloud Infrastructure

**Multi-Cloud Support**:

```yaml
deployment_options:
  public_cloud:
    aws:
      regions: ["us-east-1", "eu-west-1", "ap-southeast-1"]
      services: ["EKS", "RDS", "ElastiCache", "S3"]
      cpu_instance_types: ["c5.large", "c5.xlarge", "c5.2xlarge"]

    azure:
      regions: ["East US", "West Europe", "Southeast Asia"]
      services: ["AKS", "PostgreSQL", "Redis", "Blob Storage"]
      cpu_instance_types:
        ["Standard_F2s_v2", "Standard_F4s_v2", "Standard_F8s_v2"]

    gcp:
      regions: ["us-central1", "europe-west1", "asia-southeast1"]
      services: ["GKE", "Cloud SQL", "Memorystore", "Cloud Storage"]
      cpu_instance_types: ["c2-standard-4", "c2-standard-8", "c2-standard-16"]

  private_cloud:
    kubernetes:
      distributions: ["OpenShift", "Rancher", "Vanilla K8s"]
      storage: ["Ceph", "GlusterFS", "Local Storage"]
      networking: ["Calico", "Flannel", "Weave"]

  hybrid:
    data_plane: "private" # Sensitive data stays on-premises
    control_plane: "public" # Management and orchestration in cloud
    ai_processing: "configurable" # Can be either based on requirements
```

## Implementation Roadmap and Next Steps

### Immediate Actions (Phase 1 - 2-3 weeks)

**Priority 1: AI Backend Integration**

- Wire HybridSearchOrchestrator into active router.py path
- Complete SLLM/GLLM service implementations (Tier 2a/2b/2c)
- Implement database persistence for confidence scoring
- Complete ChromaDB client integration

**Priority 2: Flutter Web Frontend**

- Translate chatInterface.html to ConversationalAssessmentScreen
- Implement BasicComplianceDashboard from dashboard.html
- Add user registration and authentication flows
- Focus on pure conversational AI interface (no forms)

**Priority 3: Knowledge Base Population**

- Populate RAG knowledge base with ISO 27001 basic guidance
- Add GDPR basic requirements and implementation guidance
- Create cross-standard overlap analysis content
- Build assessment question libraries for multi-standard awareness

### Phase 2 Enhancements (2-3 weeks)

**Document Generation Engine**

- Extend AI backend with template-based document generation
- Implement multi-framework support (ISO 27001 + GDPR + ISO 27701)
- Add preset scenario processing and demo organization seed data
- Create form-based assessment wizards (first introduction of forms)

**Enhanced UI Components**

- Implement wizard.html → Form-Based Assessment Wizard
- Add documentEditor.html → DocumentGenerationScreen
- Create listView.html → MetadataDrivenListView
- Build reportBuilder.html → ReportGenerationScreen

### Phase 3 Production Readiness (4-5 weeks)

**Database Schema Expansion**

- Implement 4 new database migrations (0014-0017)
- Add Asset Management, Audit Management, Task Management schemas
- Implement Enhanced Evidence Management capabilities

**Flutter Native Development**

- Complete Flutter Native app with all mockup screens
- Add offline capabilities and mobile-specific features
- Implement advanced workflow management interfaces
- Add mobile document scanning and photo capture

**Advanced AI Backend**

- Implement advanced workflow orchestration engine
- Add incident management and breach response capabilities
- Create risk assessment and treatment planning features
- Build audit planning and execution support

### Phase 4 Enterprise Completion (6-8 weeks)

**Complete Database Schema**

- Implement remaining 15 database schemas (migrations 0018-0032)
- Add standards management, regulatory reporting capabilities
- Implement processing activities, corrective actions, metrics and analytics

**Enterprise AI Features**

- Add advanced compliance intelligence and gap analysis
- Implement predictive risk scoring and ML-based recommendations
- Create multi-tenant performance optimization
- Build enterprise security and advanced audit capabilities

**Enterprise Infrastructure**

- Implement advanced monitoring and observability platform
- Add enterprise security and threat management
- Create advanced integration and API gateway
- Build performance optimization and auto-scaling capabilities

This comprehensive guide provides a complete overview of the ArionComply platform architecture, implementation strategy, and key features. The system is designed to scale from simple assessment applications to enterprise-grade compliance management platforms while maintaining security, quality, and operational excellence throughout the journey.
