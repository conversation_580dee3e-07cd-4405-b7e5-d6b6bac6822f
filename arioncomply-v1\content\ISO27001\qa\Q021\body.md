# What evidence is acceptable for ISO 27001 A.8.12 (malware protection)?

## Standard terms

- **Malware protection**: Controls to prevent, detect, and recover from malware on endpoints/servers and email/web gateways.

## Plain-English answer

Provide evidence that malware controls are **implemented, up to date, and monitored**. Typical artifacts include: configuration/export from your AV/EDR tool (policies, exclusions), **coverage report** (assets in scope vs protected), **update/signature logs** showing regular updates, **alert and incident tickets** with triage/resolution, and any **approved exceptions** with compensating controls. Link these to SoA control **A.8.12** and record ownership and review cadence. Avoid generic screenshots without dates or scope.

## Applies to

- **Primary:** ISO27001:2022 **A.8.12**.
- **Also relevant/Overlaps:** ISO27002:2022 **8.12** (implementation guidance).

## Why it matters

Demonstrates that malware risk is actively managed and that detections would surface with timely response (auditors sample for this).

## Do next in our platform

- Inventory assets/systems; mark which are covered by AV/EDR.
- Upload current AV/EDR **policy config** and **coverage/export**.
- Ingest last **90 days of update logs** and **alert tickets**.
- Record **exceptions** with justification and compensating controls; set review dates.
- Update **SoA** to reference tool names, owners, and review cadence.

## How our platform will help

- **[Register]** Track covered assets/systems and coverage deltas.
- **[Evidence-Guided]** Prompts for required artifacts (configs, logs, tickets).
- **[Workflow]** Exception approval flow with expiry reminders.
- **[SoA]** Map A.8.12 to implemented controls/tools and owners.
- **[Dashboard]** Show coverage %, update freshness, and open alerts.

## Likely follow-ups

- Do we need **EDR** rather than AV? [MARKET PRACTICE—VALIDATE]
- How long should we **retain logs**? [LOCAL LAW CHECK]
- How to handle **isolated servers** without internet updates? [RESEARCH NEEDED]

## Sources

- ISO/IEC 27001:2022 — Annex **A.8.12** (Protection against malware).
- ISO/IEC 27002:2022 — **8.12** Protection against malware (guidance).