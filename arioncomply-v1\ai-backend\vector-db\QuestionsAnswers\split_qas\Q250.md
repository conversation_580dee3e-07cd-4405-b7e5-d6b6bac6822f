id: Q250
query: >-
  What if we need to notify many people about an incident—how do we manage that process?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/A.16.1.4"
  - "GDPR:2016/Art.33"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Register"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Incident Management"
    id: "ISO27001:2022/A.16.1.4"
    locator: "Annex A.16.1.4"
  - title: "GDPR — Breach Notification"
    id: "GDPR:2016/Art.33"
    locator: "Article 33"
ui:
  cards_hint:
    - "Mass-notification planner"
  actions:
    - type: "start_workflow"
      target: "mass_notification"
      label: "Plan Notifications"
    - type: "open_register"
      target: "notification_list"
      label: "View Recipients"
output_mode: "both"
graph_required: false
notes: "Use segmented lists and templated messages to streamline communications"
---
### 250) What if we need to notify many people about an incident—how do we manage that process?

**Standard terms)**  
- **Incident communication (A.16.1.4):** covers audience-specific notifications.  
- **Breach notification (GDPR Art.33):** mandates what to include.

**Plain-English answer**  
Build a **Notification List** segmented by stakeholder type, use templated messages with merge fields, schedule send-outs in batches, and track acknowledgements. Automate reminders for those who haven’t opened or responded.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.16.1.4; GDPR Article 33

**Why it matters**  
Efficient mass notifications ensure timely compliance with legal deadlines and keep stakeholders informed.

**Do next in our platform**  
- Launch **Mass-Notification** workflow.  
- Review and update the **Notification List** register.

**How our platform will help**  
- **[Workflow]** Bulk send with templating and logging.  
- **[Register]** Tracks delivery, opens, and follow-up status.

**Likely follow-ups**  
- Can we integrate with our email/SMS provider for real-time stats?

**Sources**  
- ISO/IEC 27001:2022 Annex A.16.1.4; GDPR Article 33  
