#!/bin/bash
# File: arioncomply-v1/testing/llm-comparison/start-server.sh
# Purpose: Start the LLM comparison static server and open browser
# Usage: bash start-server.sh

set -euo pipefail

# Always run from this script's directory
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd "$SCRIPT_DIR"

echo "🌐 Starting Compliance Assistant web server..."

# Check if frontend-config.js exists
if [ ! -f "frontend-config.js" ]; then
    echo "❌ frontend-config.js not found!"
    echo "💡 Run ./deploy.sh first to set up the configuration"
    exit 1
fi

# Check if index.html exists
if [ ! -f "index.html" ]; then
    echo "❌ index.html not found!"
    exit 1
fi

echo "✅ Configuration files found"

PORT=8000

# Kill any existing Python servers on port 8000
echo "🧹 Cleaning up any existing servers on port $PORT..."
lsof -ti:"$PORT" | xargs kill -9 2>/dev/null || true

# Start HTTP server in background
echo "🚀 Starting web server on http://localhost:$PORT..."
python3 -m http.server "$PORT" > /dev/null 2>&1 &
SERVER_PID=$!

# Wait for server to start
sleep 2

# Check if server is running
if ! curl -s "http://localhost:$PORT" > /dev/null 2>&1; then
    echo "❌ Failed to start web server"
    exit 1
fi

# Open browser
URL="http://localhost:$PORT"
echo "🌐 Opening browser..."
if command -v open >/dev/null 2>&1; then
  open "$URL"
elif command -v xdg-open >/dev/null 2>&1; then
  xdg-open "$URL" >/dev/null 2>&1 || true
else
  echo "ℹ️ Please open your browser to: $URL"
fi

echo ""
echo "✅ Web server running on $URL"
echo "✅ Browser opened automatically"
echo ""
echo "🎯 Ready to test! Ask compliance questions and compare responses!"
echo ""
echo "💡 To stop server: kill $SERVER_PID"
echo "💡 Or run: ./stop-server.sh"
echo ""

# Save PID for easy stopping
echo $SERVER_PID > .server.pid
