"""
File: arioncomply-v1/ai-backend/python-backend/services/classification/data_classification_system.py
File Description: Automated data classification system for dual-vector routing
Purpose: Classify documents and queries as public/private for appropriate vector store routing
Inputs: document_content, document_metadata, query_text, organizational_context
Outputs: classification_result, routing_decision, confidence_score, audit_trail
Security: Ensures proper data separation and prevents cross-boundary leakage
Notes: Core component for maintaining strict public/private data boundaries in dual-vector architecture
"""

import logging
import re
import asyncio
from typing import Dict, Any, List, Optional, Set, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import json
from datetime import datetime

logger = logging.getLogger(__name__)


class DataClassification(str, Enum):
    """Data classification categories for routing decisions."""
    PUBLIC_STANDARDS = "public_standards"              # Standards, regulations, laws
    PUBLIC_ASSESSMENTS = "public_assessments"          # Assessment frameworks, questions
    PUBLIC_TEMPLATES = "public_templates"              # Templates, best practices
    PRIVATE_POLICIES = "private_policies"              # Company-specific policies  
    PRIVATE_ASSESSMENTS = "private_assessments"        # Assessment results
    PRIVATE_DOCUMENTS = "private_documents"            # Proprietary content
    PRIVATE_SENSITIVE = "private_sensitive"            # Highly sensitive internal data


class SensitivityLevel(str, Enum):
    """Sensitivity levels for access control."""
    PUBLIC = "public"                    # Public information
    INTERNAL = "internal"               # General internal use
    CONFIDENTIAL = "confidential"       # Limited access required
    RESTRICTED = "restricted"           # Highly restricted access


class ClassificationConfidence(str, Enum):
    """Classification confidence levels."""
    VERY_HIGH = "very_high"     # 0.95+ - Automated routing safe
    HIGH = "high"               # 0.85-0.94 - High confidence
    MEDIUM = "medium"           # 0.70-0.84 - Manual review recommended
    LOW = "low"                 # 0.50-0.69 - Manual review required
    VERY_LOW = "very_low"       # <0.50 - Classification uncertain


class VectorStoreTarget(str, Enum):
    """Target vector store for routing."""
    CHROMADB = "chromadb"           # Public shared knowledge
    SUPABASE_VECTOR = "supabase_vector"  # Private organizational data
    MANUAL_REVIEW = "manual_review"      # Requires human decision


@dataclass
class ClassificationResult:
    """Comprehensive data classification result."""
    classification: DataClassification
    sensitivity_level: SensitivityLevel
    target_vector_store: VectorStoreTarget
    confidence_score: float
    confidence_level: ClassificationConfidence
    classification_reasons: List[str]
    detected_entities: Dict[str, List[str]]
    compliance_frameworks: List[str]
    risk_indicators: List[str]
    routing_metadata: Dict[str, Any]
    audit_trail: Dict[str, Any]


@dataclass
class ClassificationRules:
    """Classification rule configuration."""
    public_indicators: Set[str]
    private_indicators: Set[str]
    sensitive_patterns: List[str]
    organization_identifiers: Set[str]
    compliance_frameworks: Set[str]
    exclusion_patterns: List[str]


class AutomatedDataClassifier:
    """Automated data classification system for dual-vector routing."""
    
    def __init__(self):
        """Initialize automated data classifier with rules and patterns."""
        # Initialize classification rules
        self.classification_rules = self._initialize_classification_rules()
        
        # PII and sensitive data patterns
        self.sensitive_patterns = [
            r'\b\d{3}-\d{2}-\d{4}\b',  # SSN
            r'\b\d{4}[- ]?\d{4}[- ]?\d{4}[- ]?\d{4}\b',  # Credit card
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',  # Email
            r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b',  # IP Address
            r'\b[A-Z]{2}\d{2}[A-Z0-9]{4}\d{7}([A-Z0-9]?){0,16}\b'  # IBAN
        ]
        
        # Compilation for performance
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.sensitive_patterns]
        
        logger.info("AutomatedDataClassifier initialized with classification rules")
    
    def _initialize_classification_rules(self) -> ClassificationRules:
        """Initialize classification rules for public/private data determination."""
        
        # Public knowledge indicators
        public_indicators = {
            # Standards and regulations
            "iso 27001", "iso27001", "iso 9001", "iso9001", "iso 14001", "iso14001",
            "gdpr", "general data protection regulation", "ccpa", "california consumer privacy act",
            "sox", "sarbanes-oxley", "sarbanes oxley", "hipaa", "health insurance portability",
            "pci dss", "payment card industry", "nist", "nist csf", "nist cybersecurity framework",
            "soc 2", "soc2", "service organization control",
            
            # Legal and regulatory terms
            "regulation", "regulatory", "compliance", "standard", "framework", "guideline",
            "law", "legal", "statute", "requirement", "mandate", "directive",
            "best practice", "industry practice", "benchmark",
            
            # Assessment and audit frameworks
            "assessment framework", "audit framework", "control framework",
            "maturity model", "risk assessment", "compliance assessment",
            "security assessment", "privacy assessment",
            
            # Public templates and guidance
            "template", "example", "sample", "guide", "handbook", "manual",
            "checklist", "worksheet", "form template", "policy template"
        }
        
        # Private/organizational indicators
        private_indicators = {
            # Company-specific terms
            "company", "organization", "corporate", "internal", "proprietary",
            "confidential", "restricted", "sensitive", "private",
            "our company", "our organization", "this company", "this organization",
            
            # Organizational content
            "company policy", "corporate policy", "internal policy", "organizational policy",
            "employee", "staff", "personnel", "workforce", "team member",
            "department", "division", "business unit", "subsidiary",
            "vendor", "supplier", "contractor", "third party", "business partner",
            
            # Assessment results and internal documents
            "assessment result", "audit result", "compliance result", "evaluation result",
            "internal assessment", "self-assessment", "gap analysis result",
            "incident report", "security incident", "breach report", "violation report",
            "investigation", "findings", "remediation", "corrective action",
            
            # Financial and business information
            "revenue", "profit", "loss", "budget", "forecast", "projection",
            "financial statement", "balance sheet", "income statement", "cash flow",
            "customer data", "client information", "patient data", "personal data"
        }
        
        # Sensitive data patterns for enhanced detection
        sensitive_patterns = [
            r'\b(confidential|restricted|internal only|proprietary|trade secret)\b',
            r'\b(salary|wage|compensation|benefits|bonus)\b',
            r'\b(layoff|termination|dismissal|firing)\b',
            r'\b(acquisition|merger|partnership|deal)\b',
            r'\b(patient|medical|health|diagnosis|treatment)\b',
            r'\b(customer list|client list|contact list)\b'
        ]
        
        # Organization identifier patterns
        organization_identifiers = {
            "company name", "organization name", "corporate identity",
            "company logo", "corporate logo", "brand", "trademark",
            "office address", "headquarters", "location", "facility"
        }
        
        # Compliance frameworks for routing decisions
        compliance_frameworks = {
            "iso_27001", "gdpr", "ccpa", "sox", "hipaa", "pci_dss", "nist_csf", "soc2",
            "cobit", "coso", "itil", "togaf", "fair", "octave", "nist_rmf"
        }
        
        # Content to exclude from classification (too generic)
        exclusion_patterns = [
            r'^\w{1,3}$',  # Very short words
            r'^\d+$',      # Pure numbers
            r'^[^\w\s]+$'  # Only punctuation
        ]
        
        return ClassificationRules(
            public_indicators=public_indicators,
            private_indicators=private_indicators,
            sensitive_patterns=sensitive_patterns,
            organization_identifiers=organization_identifiers,
            compliance_frameworks=compliance_frameworks,
            exclusion_patterns=exclusion_patterns
        )
    
    async def classify_document(
        self,
        document_content: str,
        document_metadata: Dict[str, Any],
        organizational_context: Optional[Dict[str, Any]] = None
    ) -> ClassificationResult:
        """
        Classify a document for dual-vector routing.
        
        Args:
            document_content: Full text content of the document
            document_metadata: Document metadata (title, source, etc.)
            organizational_context: Optional organizational context
            
        Returns:
            ClassificationResult with routing decision and confidence
        """
        classification_start = datetime.utcnow()
        
        audit_trail = {
            "classification_type": "document",
            "start_time": classification_start.isoformat() + "Z",
            "content_length": len(document_content),
            "metadata_keys": list(document_metadata.keys()),
            "steps": []
        }
        
        try:
            # Step 1: Preprocess content for analysis
            processed_content = self._preprocess_content(document_content)
            audit_trail["steps"].append("content_preprocessed")
            
            # Step 2: Extract and analyze entities
            detected_entities = self._extract_entities(processed_content, document_metadata)
            audit_trail["steps"].append("entities_extracted")
            
            # Step 3: Detect sensitive information
            risk_indicators = self._detect_sensitive_information(processed_content)
            audit_trail["steps"].append("sensitive_information_detected")
            
            # Step 4: Identify compliance frameworks
            compliance_frameworks = self._identify_compliance_frameworks(processed_content, document_metadata)
            audit_trail["steps"].append("compliance_frameworks_identified")
            
            # Step 5: Calculate classification scores
            classification_scores = self._calculate_classification_scores(
                processed_content, document_metadata, detected_entities, organizational_context
            )
            audit_trail["steps"].append("classification_scores_calculated")
            
            # Step 6: Determine final classification
            final_classification = self._determine_final_classification(classification_scores)
            audit_trail["steps"].append("final_classification_determined")
            
            # Step 7: Determine sensitivity level
            sensitivity_level = self._determine_sensitivity_level(
                risk_indicators, detected_entities, classification_scores
            )
            audit_trail["steps"].append("sensitivity_level_determined")
            
            # Step 8: Choose target vector store
            target_vector_store = self._choose_target_vector_store(
                final_classification, sensitivity_level, classification_scores
            )
            audit_trail["steps"].append("vector_store_target_chosen")
            
            # Step 9: Calculate confidence score
            confidence_score, confidence_level = self._calculate_confidence(classification_scores)
            audit_trail["steps"].append("confidence_calculated")
            
            # Step 10: Generate classification reasons
            classification_reasons = self._generate_classification_reasons(
                classification_scores, detected_entities, risk_indicators
            )
            
            # Step 11: Create routing metadata
            routing_metadata = self._create_routing_metadata(
                final_classification, sensitivity_level, compliance_frameworks, detected_entities
            )
            
            # Finalize audit trail
            audit_trail["end_time"] = datetime.utcnow().isoformat() + "Z"
            audit_trail["success"] = True
            audit_trail["final_classification"] = final_classification.value
            audit_trail["confidence_score"] = confidence_score
            
            result = ClassificationResult(
                classification=final_classification,
                sensitivity_level=sensitivity_level,
                target_vector_store=target_vector_store,
                confidence_score=confidence_score,
                confidence_level=confidence_level,
                classification_reasons=classification_reasons,
                detected_entities=detected_entities,
                compliance_frameworks=compliance_frameworks,
                risk_indicators=risk_indicators,
                routing_metadata=routing_metadata,
                audit_trail=audit_trail
            )
            
            logger.info(f"Document classified: {final_classification.value} -> {target_vector_store.value} (confidence: {confidence_score:.3f})")
            return result
            
        except Exception as e:
            error_msg = f"Document classification failed: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            audit_trail["end_time"] = datetime.utcnow().isoformat() + "Z"
            audit_trail["success"] = False
            audit_trail["error"] = error_msg
            
            # Return fallback classification
            return self._create_fallback_classification("document", error_msg, audit_trail)
    
    async def classify_query(
        self,
        query_text: str,
        query_context: Dict[str, Any],
        organizational_context: Optional[Dict[str, Any]] = None
    ) -> ClassificationResult:
        """
        Classify a query for dual-vector routing.
        
        Args:
            query_text: User query text
            query_context: Query context (user, org, session)
            organizational_context: Optional organizational context
            
        Returns:
            ClassificationResult with routing decision
        """
        classification_start = datetime.utcnow()
        
        audit_trail = {
            "classification_type": "query",
            "start_time": classification_start.isoformat() + "Z",
            "query_length": len(query_text),
            "context_keys": list(query_context.keys()),
            "steps": []
        }
        
        try:
            # Step 1: Preprocess query
            processed_query = self._preprocess_content(query_text)
            audit_trail["steps"].append("query_preprocessed")
            
            # Step 2: Analyze query intent and content needs
            intent_analysis = self._analyze_query_intent(processed_query, query_context)
            audit_trail["steps"].append("query_intent_analyzed")
            
            # Step 3: Detect organizational context in query
            org_indicators = self._detect_organizational_context(processed_query, organizational_context)
            audit_trail["steps"].append("organizational_context_detected")
            
            # Step 4: Identify required knowledge domains
            knowledge_domains = self._identify_knowledge_domains(processed_query)
            audit_trail["steps"].append("knowledge_domains_identified")
            
            # Step 5: Determine data requirements
            data_requirements = self._determine_query_data_requirements(
                intent_analysis, org_indicators, knowledge_domains
            )
            audit_trail["steps"].append("data_requirements_determined")
            
            # Step 6: Route query to appropriate vector stores
            routing_decision = self._make_query_routing_decision(data_requirements)
            audit_trail["steps"].append("routing_decision_made")
            
            # Finalize audit trail
            audit_trail["end_time"] = datetime.utcnow().isoformat() + "Z"
            audit_trail["success"] = True
            audit_trail["routing_decision"] = routing_decision
            
            # Create result for query classification
            result = ClassificationResult(
                classification=DataClassification.PUBLIC_STANDARDS,  # Queries don't have inherent classification
                sensitivity_level=SensitivityLevel.INTERNAL,
                target_vector_store=VectorStoreTarget(routing_decision["primary_target"]),
                confidence_score=routing_decision["confidence"],
                confidence_level=self._score_to_confidence_level(routing_decision["confidence"]),
                classification_reasons=routing_decision["reasons"],
                detected_entities=org_indicators,
                compliance_frameworks=knowledge_domains.get("compliance_frameworks", []),
                risk_indicators=[],
                routing_metadata=routing_decision,
                audit_trail=audit_trail
            )
            
            logger.info(f"Query classified: {routing_decision['primary_target']} (confidence: {routing_decision['confidence']:.3f})")
            return result
            
        except Exception as e:
            error_msg = f"Query classification failed: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            audit_trail["end_time"] = datetime.utcnow().isoformat() + "Z"
            audit_trail["success"] = False
            audit_trail["error"] = error_msg
            
            return self._create_fallback_classification("query", error_msg, audit_trail)
    
    def _preprocess_content(self, content: str) -> str:
        """Preprocess content for classification analysis."""
        
        # Basic cleaning and normalization
        processed = content.lower().strip()
        
        # Remove excessive whitespace
        processed = re.sub(r'\s+', ' ', processed)
        
        # Remove common document artifacts
        processed = re.sub(r'page \d+', '', processed)
        processed = re.sub(r'confidential.*?(?=\s|$)', '', processed)  # Remove confidentiality headers
        
        return processed
    
    def _extract_entities(self, content: str, metadata: Dict[str, Any]) -> Dict[str, List[str]]:
        """Extract relevant entities from content and metadata."""
        
        entities = {
            "standards": [],
            "regulations": [],
            "organizations": [],
            "frameworks": [],
            "sensitive_terms": [],
            "document_types": []
        }
        
        # Extract standards references
        iso_standards = re.findall(r'iso\s*\d{4,5}', content)
        entities["standards"].extend(iso_standards)
        
        # Extract regulation references
        regulations = ["gdpr", "ccpa", "sox", "hipaa", "pci", "dss"]
        for regulation in regulations:
            if regulation in content:
                entities["regulations"].append(regulation)
        
        # Extract organization indicators
        org_patterns = [
            r'\b(?:our|this|the)\s+(?:company|organization|corporation|business)\b',
            r'\b(?:internal|proprietary|confidential)\b'
        ]
        for pattern in org_patterns:
            matches = re.findall(pattern, content)
            entities["organizations"].extend(matches)
        
        # Extract from metadata
        if "title" in metadata:
            title_lower = metadata["title"].lower()
            if any(indicator in title_lower for indicator in self.classification_rules.private_indicators):
                entities["document_types"].append("private_document")
            elif any(indicator in title_lower for indicator in self.classification_rules.public_indicators):
                entities["document_types"].append("public_document")
        
        return entities
    
    def _detect_sensitive_information(self, content: str) -> List[str]:
        """Detect sensitive information patterns in content."""
        
        risk_indicators = []
        
        # Check compiled sensitive patterns
        for i, pattern in enumerate(self.compiled_patterns):
            if pattern.search(content):
                risk_indicators.append(f"sensitive_pattern_{i}")
        
        # Check for PII indicators
        pii_indicators = [
            "social security", "ssn", "credit card", "bank account",
            "routing number", "driver license", "passport", "date of birth"
        ]
        
        for indicator in pii_indicators:
            if indicator in content:
                risk_indicators.append(f"pii_{indicator.replace(' ', '_')}")
        
        # Check for financial information
        financial_indicators = [
            "salary", "wage", "compensation", "revenue", "profit", "loss",
            "financial statement", "budget", "forecast"
        ]
        
        for indicator in financial_indicators:
            if indicator in content:
                risk_indicators.append(f"financial_{indicator.replace(' ', '_')}")
        
        # Check for confidential markers
        confidential_markers = [
            "confidential", "restricted", "internal only", "proprietary",
            "trade secret", "classified", "sensitive"
        ]
        
        for marker in confidential_markers:
            if marker in content:
                risk_indicators.append(f"confidential_{marker.replace(' ', '_')}")
        
        return risk_indicators
    
    def _identify_compliance_frameworks(self, content: str, metadata: Dict[str, Any]) -> List[str]:
        """Identify compliance frameworks referenced in content."""
        
        frameworks = []
        
        framework_patterns = {
            "iso_27001": r'\biso\s*27001\b',
            "gdpr": r'\bgdpr\b|general data protection regulation',
            "ccpa": r'\bccpa\b|california consumer privacy act',
            "sox": r'\bsox\b|sarbanes.?oxley',
            "hipaa": r'\bhipaa\b|health insurance portability',
            "pci_dss": r'\bpci\s*dss\b|payment card industry',
            "nist_csf": r'\bnist\s*csf\b|nist cybersecurity framework',
            "soc2": r'\bsoc\s*2\b|service organization control'
        }
        
        for framework, pattern in framework_patterns.items():
            if re.search(pattern, content, re.IGNORECASE):
                frameworks.append(framework)
        
        # Check metadata for framework indicators
        metadata_text = " ".join(str(v) for v in metadata.values()).lower()
        for framework, pattern in framework_patterns.items():
            if re.search(pattern, metadata_text, re.IGNORECASE):
                frameworks.append(framework)
        
        return list(set(frameworks))  # Remove duplicates
    
    def _calculate_classification_scores(
        self,
        content: str,
        metadata: Dict[str, Any],
        entities: Dict[str, List[str]],
        org_context: Optional[Dict[str, Any]]
    ) -> Dict[str, float]:
        """Calculate classification scores for different categories."""
        
        scores = {
            "public_standards": 0.0,
            "public_assessments": 0.0,
            "public_templates": 0.0,
            "private_policies": 0.0,
            "private_assessments": 0.0,
            "private_documents": 0.0,
            "private_sensitive": 0.0
        }
        
        # Public knowledge scoring
        public_score = 0.0
        for indicator in self.classification_rules.public_indicators:
            if indicator in content:
                public_score += 1.0
        
        # Normalize public score
        public_score = min(1.0, public_score / 5.0)  # Max at 5 indicators
        
        # Distribute public score based on content type
        if any(term in content for term in ["standard", "regulation", "law", "framework"]):
            scores["public_standards"] = public_score * 0.8
            scores["public_templates"] = public_score * 0.2
        elif any(term in content for term in ["assessment", "audit", "evaluation"]):
            scores["public_assessments"] = public_score * 0.8
            scores["public_standards"] = public_score * 0.2
        elif any(term in content for term in ["template", "example", "guide", "checklist"]):
            scores["public_templates"] = public_score * 0.8
            scores["public_standards"] = public_score * 0.2
        else:
            scores["public_standards"] = public_score
        
        # Private/organizational scoring
        private_score = 0.0
        for indicator in self.classification_rules.private_indicators:
            if indicator in content:
                private_score += 1.0
        
        # Normalize private score
        private_score = min(1.0, private_score / 5.0)  # Max at 5 indicators
        
        # Distribute private score based on content type
        if any(term in content for term in ["policy", "procedure", "corporate"]):
            scores["private_policies"] = private_score * 0.8
            scores["private_documents"] = private_score * 0.2
        elif any(term in content for term in ["assessment result", "audit result", "gap analysis"]):
            scores["private_assessments"] = private_score * 0.8
            scores["private_documents"] = private_score * 0.2
        else:
            scores["private_documents"] = private_score
        
        # Sensitivity boost for private content
        if any(term in content for term in ["confidential", "restricted", "sensitive", "proprietary"]):
            scores["private_sensitive"] = max(scores["private_sensitive"], private_score * 0.6)
        
        # Organizational context boost
        if org_context:
            org_boost = 0.3
            scores["private_policies"] += org_boost
            scores["private_assessments"] += org_boost
            scores["private_documents"] += org_boost
        
        # Entity-based adjustments
        if entities.get("standards") or entities.get("regulations"):
            scores["public_standards"] += 0.2
        if entities.get("organizations"):
            scores["private_documents"] += 0.2
        
        # Metadata-based adjustments
        source = metadata.get("source", "").lower()
        if "internal" in source or "company" in source:
            for key in ["private_policies", "private_assessments", "private_documents"]:
                scores[key] += 0.1
        elif "public" in source or "standard" in source:
            for key in ["public_standards", "public_assessments", "public_templates"]:
                scores[key] += 0.1
        
        # Ensure scores don't exceed 1.0
        return {k: min(1.0, v) for k, v in scores.items()}
    
    def _determine_final_classification(self, scores: Dict[str, float]) -> DataClassification:
        """Determine final classification from scores."""
        
        # Find highest scoring classification
        max_score = max(scores.values())
        if max_score < 0.3:
            return DataClassification.PRIVATE_DOCUMENTS  # Default to private when uncertain
        
        # Get classification with highest score
        for classification_str, score in scores.items():
            if score == max_score:
                return DataClassification(classification_str)
        
        return DataClassification.PRIVATE_DOCUMENTS  # Fallback
    
    def _determine_sensitivity_level(
        self,
        risk_indicators: List[str],
        entities: Dict[str, List[str]],
        scores: Dict[str, float]
    ) -> SensitivityLevel:
        """Determine sensitivity level based on risk indicators and content."""
        
        # High sensitivity indicators
        high_sensitivity_count = len([
            indicator for indicator in risk_indicators
            if indicator.startswith(("pii_", "financial_", "confidential_"))
        ])
        
        # Restricted content indicators
        restricted_indicators = [
            "confidential_restricted", "confidential_classified", "confidential_trade_secret"
        ]
        has_restricted = any(indicator in risk_indicators for indicator in restricted_indicators)
        
        # Determine sensitivity level
        if has_restricted or high_sensitivity_count >= 3:
            return SensitivityLevel.RESTRICTED
        elif high_sensitivity_count >= 1 or scores.get("private_sensitive", 0) > 0.5:
            return SensitivityLevel.CONFIDENTIAL
        elif any(scores.get(key, 0) > 0.3 for key in ["private_policies", "private_assessments", "private_documents"]):
            return SensitivityLevel.INTERNAL
        else:
            return SensitivityLevel.PUBLIC
    
    def _choose_target_vector_store(
        self,
        classification: DataClassification,
        sensitivity: SensitivityLevel,
        scores: Dict[str, float]
    ) -> VectorStoreTarget:
        """Choose target vector store based on classification and sensitivity."""
        
        # Public classifications go to ChromaDB
        if classification in [DataClassification.PUBLIC_STANDARDS, 
                            DataClassification.PUBLIC_ASSESSMENTS,
                            DataClassification.PUBLIC_TEMPLATES]:
            return VectorStoreTarget.CHROMADB
        
        # Private classifications go to Supabase Vector
        elif classification in [DataClassification.PRIVATE_POLICIES,
                              DataClassification.PRIVATE_ASSESSMENTS, 
                              DataClassification.PRIVATE_DOCUMENTS,
                              DataClassification.PRIVATE_SENSITIVE]:
            return VectorStoreTarget.SUPABASE_VECTOR
        
        # Uncertain cases require manual review
        else:
            return VectorStoreTarget.MANUAL_REVIEW
    
    def _calculate_confidence(self, scores: Dict[str, float]) -> Tuple[float, ClassificationConfidence]:
        """Calculate classification confidence score."""
        
        # Get top two scores
        sorted_scores = sorted(scores.values(), reverse=True)
        top_score = sorted_scores[0] if sorted_scores else 0.0
        second_score = sorted_scores[1] if len(sorted_scores) > 1 else 0.0
        
        # Confidence based on score separation and absolute score
        separation = top_score - second_score
        confidence = (top_score * 0.7) + (separation * 0.3)
        
        # Determine confidence level
        confidence_level = self._score_to_confidence_level(confidence)
        
        return confidence, confidence_level
    
    def _score_to_confidence_level(self, score: float) -> ClassificationConfidence:
        """Convert confidence score to confidence level."""
        if score >= 0.95:
            return ClassificationConfidence.VERY_HIGH
        elif score >= 0.85:
            return ClassificationConfidence.HIGH
        elif score >= 0.70:
            return ClassificationConfidence.MEDIUM
        elif score >= 0.50:
            return ClassificationConfidence.LOW
        else:
            return ClassificationConfidence.VERY_LOW
    
    def _generate_classification_reasons(
        self,
        scores: Dict[str, float],
        entities: Dict[str, List[str]],
        risk_indicators: List[str]
    ) -> List[str]:
        """Generate human-readable classification reasons."""
        
        reasons = []
        
        # Top scoring category reason
        max_score = max(scores.values())
        for category, score in scores.items():
            if score == max_score:
                reasons.append(f"Highest score for {category.replace('_', ' ')} ({score:.2f})")
                break
        
        # Entity-based reasons
        if entities.get("standards"):
            reasons.append(f"Contains standards references: {', '.join(entities['standards'][:3])}")
        if entities.get("regulations"):
            reasons.append(f"Contains regulatory references: {', '.join(entities['regulations'][:3])}")
        if entities.get("organizations"):
            reasons.append("Contains organizational context indicators")
        
        # Risk-based reasons
        if risk_indicators:
            risk_categories = set(indicator.split('_')[0] for indicator in risk_indicators)
            reasons.append(f"Contains sensitive information: {', '.join(list(risk_categories)[:3])}")
        
        return reasons[:5]  # Limit to top 5 reasons
    
    def _create_routing_metadata(
        self,
        classification: DataClassification,
        sensitivity: SensitivityLevel,
        frameworks: List[str],
        entities: Dict[str, List[str]]
    ) -> Dict[str, Any]:
        """Create routing metadata for vector store ingestion."""
        
        return {
            "data_classification": classification.value,
            "sensitivity_level": sensitivity.value,
            "compliance_frameworks": frameworks,
            "detected_entities": entities,
            "routing_timestamp": datetime.utcnow().isoformat() + "Z",
            "classification_version": "v1.0"
        }
    
    def _analyze_query_intent(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze query intent for routing decisions."""
        
        intent_analysis = {
            "intent_category": "general",
            "requires_public_knowledge": False,
            "requires_private_knowledge": False,
            "confidence": 0.5
        }
        
        # Analyze intent based on query patterns
        if any(term in query for term in ["what is", "define", "explain", "standard", "regulation"]):
            intent_analysis["intent_category"] = "definition_inquiry"
            intent_analysis["requires_public_knowledge"] = True
            intent_analysis["confidence"] = 0.8
        
        elif any(term in query for term in ["our policy", "company policy", "internal", "our organization"]):
            intent_analysis["intent_category"] = "policy_inquiry"
            intent_analysis["requires_private_knowledge"] = True
            intent_analysis["confidence"] = 0.9
        
        elif any(term in query for term in ["assessment", "audit", "compliance check", "evaluate"]):
            intent_analysis["intent_category"] = "assessment_inquiry"
            intent_analysis["requires_public_knowledge"] = True
            intent_analysis["requires_private_knowledge"] = True
            intent_analysis["confidence"] = 0.7
        
        elif any(term in query for term in ["how to", "implement", "best practice", "guidance"]):
            intent_analysis["intent_category"] = "implementation_inquiry"
            intent_analysis["requires_public_knowledge"] = True
            intent_analysis["confidence"] = 0.7
        
        return intent_analysis
    
    def _detect_organizational_context(
        self,
        query: str,
        org_context: Optional[Dict[str, Any]]
    ) -> Dict[str, List[str]]:
        """Detect organizational context indicators in query."""
        
        org_indicators = {
            "pronouns": [],
            "company_references": [],
            "internal_references": []
        }
        
        # Detect organizational pronouns
        org_pronouns = ["our", "we", "us", "this company", "this organization"]
        for pronoun in org_pronouns:
            if pronoun in query:
                org_indicators["pronouns"].append(pronoun)
        
        # Detect company references
        company_refs = ["company", "organization", "corporation", "business", "firm"]
        for ref in company_refs:
            if ref in query:
                org_indicators["company_references"].append(ref)
        
        # Detect internal references
        internal_refs = ["internal", "internally", "within our", "our own"]
        for ref in internal_refs:
            if ref in query:
                org_indicators["internal_references"].append(ref)
        
        return org_indicators
    
    def _identify_knowledge_domains(self, query: str) -> Dict[str, List[str]]:
        """Identify knowledge domains required for query."""
        
        domains = {
            "compliance_frameworks": [],
            "standards": [],
            "domain_areas": []
        }
        
        # Framework identification
        framework_keywords = {
            "iso_27001": ["iso 27001", "iso27001"],
            "gdpr": ["gdpr", "data protection"],
            "sox": ["sox", "sarbanes-oxley"],
            "hipaa": ["hipaa", "health insurance"],
            "pci_dss": ["pci", "payment card"]
        }
        
        for framework, keywords in framework_keywords.items():
            if any(keyword in query for keyword in keywords):
                domains["compliance_frameworks"].append(framework)
        
        # Domain area identification
        domain_areas = [
            "security", "privacy", "audit", "risk management",
            "data governance", "access control", "incident response"
        ]
        
        for area in domain_areas:
            if area in query:
                domains["domain_areas"].append(area)
        
        return domains
    
    def _determine_query_data_requirements(
        self,
        intent_analysis: Dict[str, Any],
        org_indicators: Dict[str, List[str]],
        knowledge_domains: Dict[str, List[str]]
    ) -> Dict[str, Any]:
        """Determine what type of data the query needs."""
        
        requirements = {
            "needs_public_standards": intent_analysis.get("requires_public_knowledge", False),
            "needs_private_data": intent_analysis.get("requires_private_knowledge", False),
            "confidence": intent_analysis.get("confidence", 0.5),
            "reasons": []
        }
        
        # Boost for organizational context
        if any(org_indicators.values()):
            requirements["needs_private_data"] = True
            requirements["confidence"] = min(1.0, requirements["confidence"] + 0.2)
            requirements["reasons"].append("Contains organizational context")
        
        # Boost for compliance frameworks
        if knowledge_domains.get("compliance_frameworks"):
            requirements["needs_public_standards"] = True
            requirements["confidence"] = min(1.0, requirements["confidence"] + 0.1)
            requirements["reasons"].append("References compliance frameworks")
        
        # Default to both if uncertain
        if not requirements["needs_public_standards"] and not requirements["needs_private_data"]:
            requirements["needs_public_standards"] = True
            requirements["needs_private_data"] = True
            requirements["reasons"].append("Uncertain intent - checking both sources")
        
        return requirements
    
    def _make_query_routing_decision(self, data_requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Make final routing decision for query."""
        
        needs_public = data_requirements.get("needs_public_standards", False)
        needs_private = data_requirements.get("needs_private_data", False)
        
        routing_decision = {
            "primary_target": "chromadb",  # Default
            "secondary_targets": [],
            "confidence": data_requirements.get("confidence", 0.5),
            "reasons": data_requirements.get("reasons", [])
        }
        
        if needs_public and needs_private:
            routing_decision["primary_target"] = "hybrid"
            routing_decision["secondary_targets"] = ["chromadb", "supabase_vector"]
            routing_decision["reasons"].append("Requires both public and private knowledge")
        elif needs_private:
            routing_decision["primary_target"] = "supabase_vector"
            routing_decision["reasons"].append("Requires private organizational data")
        elif needs_public:
            routing_decision["primary_target"] = "chromadb"
            routing_decision["reasons"].append("Requires public standards knowledge")
        
        return routing_decision
    
    def _create_fallback_classification(
        self,
        classification_type: str,
        error_message: str,
        audit_trail: Dict[str, Any]
    ) -> ClassificationResult:
        """Create fallback classification for error cases."""
        
        return ClassificationResult(
            classification=DataClassification.PRIVATE_DOCUMENTS,  # Safe default
            sensitivity_level=SensitivityLevel.INTERNAL,
            target_vector_store=VectorStoreTarget.MANUAL_REVIEW,
            confidence_score=0.0,
            confidence_level=ClassificationConfidence.VERY_LOW,
            classification_reasons=[f"Classification failed: {error_message}"],
            detected_entities={},
            compliance_frameworks=[],
            risk_indicators=["classification_error"],
            routing_metadata={
                "error": True,
                "classification_type": classification_type,
                "error_message": error_message
            },
            audit_trail=audit_trail
        )
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get data classification system status."""
        
        return {
            "status": "operational",
            "classification_rules": {
                "public_indicators": len(self.classification_rules.public_indicators),
                "private_indicators": len(self.classification_rules.private_indicators),
                "sensitive_patterns": len(self.classification_rules.sensitive_patterns),
                "compliance_frameworks": len(self.classification_rules.compliance_frameworks)
            },
            "supported_classifications": [c.value for c in DataClassification],
            "supported_sensitivity_levels": [s.value for s in SensitivityLevel],
            "target_vector_stores": [t.value for t in VectorStoreTarget],
            "version": "v1.0"
        }


# Global instance
data_classifier = AutomatedDataClassifier()


# Convenience functions
async def classify_document_for_routing(
    document_content: str,
    document_metadata: Dict[str, Any],
    organizational_context: Optional[Dict[str, Any]] = None
) -> ClassificationResult:
    """Convenience function for document classification."""
    return await data_classifier.classify_document(
        document_content, document_metadata, organizational_context
    )


async def classify_query_for_routing(
    query_text: str,
    query_context: Dict[str, Any],
    organizational_context: Optional[Dict[str, Any]] = None
) -> ClassificationResult:
    """Convenience function for query classification."""
    return await data_classifier.classify_query(
        query_text, query_context, organizational_context
    )


def is_public_data(classification: DataClassification) -> bool:
    """Check if classification indicates public data."""
    return classification in [
        DataClassification.PUBLIC_STANDARDS,
        DataClassification.PUBLIC_ASSESSMENTS,
        DataClassification.PUBLIC_TEMPLATES
    ]


def is_private_data(classification: DataClassification) -> bool:
    """Check if classification indicates private data."""
    return classification in [
        DataClassification.PRIVATE_POLICIES,
        DataClassification.PRIVATE_ASSESSMENTS,
        DataClassification.PRIVATE_DOCUMENTS,
        DataClassification.PRIVATE_SENSITIVE
    ]
