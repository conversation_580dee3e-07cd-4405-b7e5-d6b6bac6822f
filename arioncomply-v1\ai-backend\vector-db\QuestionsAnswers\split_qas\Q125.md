id: Q125
query: >-
  What due diligence do we need to do before signing new vendor contracts?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/A.15.1.1"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Annex A.15.1.1 Supplier Relationship Requirements"
    id: "ISO27001:2022/A.15.1.1"
    locator: "Annex A.15.1.1"
ui:
  cards_hint:
    - "Vendor due diligence checklist"
  actions:
    - type: "start_workflow"
      target: "vendor_dd"
      label: "Run Vendor Due Diligence"
    - type: "open_register"
      target: "vendor_dd_log"
      label: "View DD Log"
output_mode: "both"
graph_required: false
notes: "Assess security posture, certifications, controls, and contractual terms"
---
### 125) What due diligence do we need to do before signing new vendor contracts?

**Standard terms**  
- **Supplier relationship (A.15.1.1):** evaluate vendor security requirements.

**Plain-English answer**  
Perform a **vendor due-diligence**: review their security policies, certifications (ISO 27001, SOC 2), audit reports, penetration-test summaries, and contractual terms for SLAs and audit rights.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.15.1.1

**Why it matters**  
Identifies risks early and ensures contractual coverage.

**Do next in our platform**  
- Launch **Vendor Due Diligence** workflow.  
- Log findings in **Vendor DD Log**.

**How our platform will help**  
- **[Workflow]** Guided due-diligence steps.  
- **[Report]** Summary of vendor risk posture.

**Likely follow-ups**  
- “How do we score vendor risk?” (Use built-in risk matrix)

**Sources**  
- ISO/IEC 27001:2022 Annex A.15.1.1
