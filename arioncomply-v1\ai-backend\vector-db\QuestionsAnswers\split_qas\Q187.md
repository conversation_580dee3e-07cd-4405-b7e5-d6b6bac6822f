id: Q187
query: >-
  What’s the difference between different types of cloud services for compliance purposes?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/A.14.2.1"
overlap_ids: []
capability_tags:
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — System Acquisition, Development & Maintenance"
    id: "ISO27001:2022/A.14.2.1"
    locator: "Annex A.14.2.1"
ui:
  cards_hint:
    - "Cloud service comparison"
  actions:
    - type: "start_workflow"
      target: "cloud_service_assessment"
      label: "Assess Service Type"
output_mode: "both"
graph_required: false
notes: "IaaS, PaaS, SaaS have different control scopes and customer duties"
---
### 187) What’s the difference between different types of cloud services for compliance purposes?

**Standard terms)**  
- **IaaS/PaaS/SaaS (ISO27001 A.14.2.1):** levels of abstraction in cloud offerings.

**Plain-English answer**  
- **IaaS:** you manage OS, middleware, apps—more controls on you.  
- **PaaS:** provider manages OS/runtime; you manage apps/data.  
- **SaaS:** provider manages apps; you configure settings and data.  

Each model shifts certain controls from you to the vendor.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.14.2.1

**Why it matters**  
Helps allocate controls and audit focus appropriately.

**Do next in our platform**  
- Launch **Cloud Service Assessment** workflow.  
- Classify each service in your **Cloud Register**.

**How our platform will help**  
- **[Report]** Control-scope heatmap by service model.  
- **[Workflow]** Guidance on customer vs provider tasks.

**Likely follow-ups**  
- Which model best fits regulated workloads?  
- How to document shared controls per model?

**Sources**  
- ISO/IEC 27001:2022 Annex A.14.2.1  
