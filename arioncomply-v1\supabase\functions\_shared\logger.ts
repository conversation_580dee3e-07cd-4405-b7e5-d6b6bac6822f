// File: arioncomply-v1/supabase/functions/_shared/logger.ts
// File Description: Request/event logging utilities
// Purpose: Persist request lifecycle and granular events to api_request_logs/api_event_logs.
// Exports: logRequestStart(), logRequestEnd(), logEvent(), getClientMeta()
// Notes: Uses service-role client; include requestId/orgId/userId for traceability.
import { getSupabaseAdmin } from "./supabase.ts";
const loggingRequired = (Deno.env.get("LOGGING_REQUIRED") ?? "true").toLowerCase() === "true";
import { parseTraceparent, generateTraceContext } from "./trace.ts";
import { extractOrgAndUser } from "./jwt.ts";
const SYSTEM_ORG_ID = Deno.env.get("SYSTEM_ORG_ID") || null;

export type RequestMeta = {
  requestId: string;
  orgId?: string | null;
  userId?: string | null;
  route: string;
  method: string;
  ip?: string | null;
  userAgent?: string | null;
  correlationId?: string | null;
  traceId?: string | null;
  spanId?: string | null;
  parentSpanId?: string | null;
  traceHeader?: string | null;
  sessionId?: string | null;
};

//
/**
 * Insert a request_start log row and print structured diagnostic.
 * @param meta Request metadata (orgId required when LOGGING_REQUIRED=true)
 * @param headers Incoming request headers (persisted for audit)
 * @param body Optional parsed request body
 */
export async function logRequestStart(meta: RequestMeta, headers: Headers, body?: unknown) {
  if (loggingRequired && !meta.orgId) {
    throw new Error("Logging requires orgId; missing orgId in RequestMeta");
  }
  // Always console.log a structured line for quick diagnostics
  console.log(JSON.stringify({
    kind: "request_start",
    at: new Date().toISOString(),
    ...meta,
  }));

  const db = getSupabaseAdmin();
  try {
    const attempt = async () => db.from("api_request_logs").insert({
      request_id: meta.requestId,
      org_id: meta.orgId ?? null,
      user_id: meta.userId ?? null,
      route: meta.route,
      method: meta.method,
      ip_address: meta.ip ?? null,
      user_agent: meta.userAgent ?? null,
      correlation_id: meta.correlationId ?? null,
      trace_id: meta.traceId ?? null,
      session_id: meta.sessionId ?? null,
      request_headers: Object.fromEntries(headers.entries()),
      request_body: body ?? null,
    });
    const { error } = await attempt();
    if (error) {
      // One retry after a short delay
      await new Promise((r) => setTimeout(r, 50));
      const { error: error2 } = await attempt();
      if (error2) throw error2;
    }
  } catch (e) {
    console.error("logRequestStart fatal", e);
    // Propagate error to enforce mandatory logging policy (Edge will still continue returning response)
  }
}

/**
 * Update the request log row with status, response body and duration.
 */
export async function logRequestEnd(meta: RequestMeta, status: number, responseBody?: unknown, durationMs?: number) {
  if (loggingRequired && !meta.orgId) {
    throw new Error("Logging requires orgId; missing orgId in RequestMeta");
  }
  console.log(JSON.stringify({
    kind: "request_end",
    at: new Date().toISOString(),
    ...meta,
    status,
    durationMs,
  }));
  const db = getSupabaseAdmin();
  try {
    const attempt = async () => db.from("api_request_logs").update({
      response_status: status,
      response_body: responseBody ?? null,
      duration_ms: durationMs ?? null,
      trace_id: meta.traceId ?? null,
      session_id: meta.sessionId ?? null,
    }).eq("request_id", meta.requestId);
    const { error } = await attempt();
    if (error) {
      await new Promise((r) => setTimeout(r, 50));
      const { error: error2 } = await attempt();
      if (error2) throw error2;
    }
  } catch (e) {
    console.error("logRequestEnd fatal", e);
  }
}

/**
 * Insert a granular event (ai_call, retrieval_run, etc.) with trace context.
 */
export async function logEvent(meta: RequestMeta, evt: {
  eventType: string;
  direction: "inbound" | "outbound" | "internal";
  target?: string | null;
  status?: string | null;
  details?: unknown;
}) {
  // Resolve org: prefer meta.orgId; for internal events only, fallback to SYSTEM_ORG_ID
  const resolvedOrgId = meta.orgId ?? (evt.direction === "internal" ? SYSTEM_ORG_ID : null);
  if (loggingRequired && !resolvedOrgId) {
    throw new Error("Logging requires orgId; missing orgId and no SYSTEM_ORG_ID for internal event");
  }
  const enrichedDetails = typeof evt.details === "object" && evt.details !== null
    ? { ...(evt.details as Record<string, unknown>), traceId: meta.traceId, spanId: meta.spanId, parentSpanId: meta.parentSpanId, correlationId: meta.correlationId }
    : { traceId: meta.traceId, spanId: meta.spanId, parentSpanId: meta.parentSpanId, correlationId: meta.correlationId };
  console.log(JSON.stringify({ kind: "event", at: new Date().toISOString(), ...meta, ...evt, details: enrichedDetails }));
  const db = getSupabaseAdmin();
  try {
    // Try to promote sessionId to top-level column when present in details
    let sessionId: string | null = meta.sessionId ?? null;
    try {
      if (!sessionId && evt.details && typeof evt.details === 'object') {
        const d = evt.details as Record<string, unknown>;
        sessionId = (d.sessionId as string) || (d.session_id as string) || null;
      }
    } catch {}

    const attempt = async () => db.from("api_event_logs").insert({
      request_id: meta.requestId,
      org_id: resolvedOrgId ?? null,
      user_id: meta.userId ?? null,
      event_type: evt.eventType,
      direction: evt.direction,
      target: evt.target ?? null,
      status: evt.status ?? null,
      details: enrichedDetails,
      trace_id: meta.traceId ?? null,
      session_id: sessionId ?? null,
    });
    const { error } = await attempt();
    if (error) {
      await new Promise((r) => setTimeout(r, 50));
      const { error: error2 } = await attempt();
      if (error2) throw error2;
    }
  } catch (e) {
    console.error("logEvent fatal", e);
  }
}

/**
 * Derive RequestMeta from an incoming Request and generate/propagate trace context.
 */
export function getClientMeta(req: Request, requestId: string): RequestMeta {
  const url = new URL(req.url);
  const ip = req.headers.get("x-forwarded-for") || req.headers.get("x-real-ip") || null;
  const ua = req.headers.get("user-agent") || null;
  const { orgId, userId } = extractOrgAndUser(req.headers);
  const correlationId = req.headers.get("x-correlation-id");
  const incoming = parseTraceparent(req.headers.get("traceparent"));
  const trace = incoming ?? generateTraceContext(null);
  return {
    requestId,
    route: url.pathname,
    method: req.method,
    ip,
    userAgent: ua,
    orgId: orgId || null,
    userId: userId || null,
    correlationId: correlationId || null,
    traceId: trace.traceId,
    spanId: trace.spanId,
    parentSpanId: trace.parentSpanId ?? null,
    traceHeader: trace.header,
  };
}
