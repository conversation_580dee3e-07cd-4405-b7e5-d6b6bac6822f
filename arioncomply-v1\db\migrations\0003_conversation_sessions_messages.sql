-- File: arioncomply-v1/db/migrations/0003_conversation_sessions_messages.sql
-- Migration 0003: Conversation sessions and messages (Chat + Assessment)
-- Purpose: Persist chat sessions and messages for the conversation-first UI.
-- Tables:
--   - conversation_sessions: one row per chat thread; status, title, metadata
--   - conversation_messages: ordered messages within a session; supports text and JSON payloads
-- Usage:
--   - Drives chat UI history, streaming transcripts, and assistant actions/cards.
-- RLS:
--   - org-scoped access; admin bypass. Extend with participant-level rules later if needed.
-- Indices:
--   - sessions by org_id; messages by session_id and time.
-- Aligns with: chat-interface-workflow, ActionsToDo, consolidated demo spec

BEGIN;

-- Conversation session: a logical chat transcript container.
CREATE TABLE IF NOT EXISTS conversation_sessions (
  session_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL REFERENCES organizations(org_id) ON DELETE CASCADE,
  created_by uuid,
  title text,
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active','closed')),
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now(),
  deleted_at timestamptz,
  deleted_by uuid,
  CONSTRAINT chk_conv_sessions_metadata CHECK (jsonb_typeof(metadata) = 'object')
);

CREATE INDEX IF NOT EXISTS idx_conv_sessions_org_active ON conversation_sessions (org_id, created_at DESC)
  WHERE deleted_at IS NULL;

ALTER TABLE conversation_sessions ENABLE ROW LEVEL SECURITY;

CREATE POLICY conv_sessions_select ON conversation_sessions
  FOR SELECT USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE POLICY conv_sessions_update ON conversation_sessions
  FOR UPDATE USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  ) WITH CHECK (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE POLICY conv_sessions_delete ON conversation_sessions
  FOR DELETE USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE POLICY conv_sessions_insert ON conversation_sessions
  FOR INSERT WITH CHECK (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE TRIGGER conv_sessions_set_updated_at
  BEFORE UPDATE ON conversation_sessions
  FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

-- Conversation message: a single message within a session.
-- content_type:
--   'text'   -> use content_text
--   'json'   -> use content_json for structured payloads
--   'card'   -> UI cards (rendered from content_json)
--   'tool'   -> tool-call metadata/results
CREATE TABLE IF NOT EXISTS conversation_messages (
  message_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL REFERENCES organizations(org_id) ON DELETE CASCADE,
  session_id uuid NOT NULL REFERENCES conversation_sessions(session_id) ON DELETE CASCADE,
  sender_type text NOT NULL CHECK (sender_type IN ('user','assistant','system')),
  sender_user_id uuid,
  content_type text NOT NULL CHECK (content_type IN ('text','json','card','tool')),
  content_text text,
  content_json jsonb,
  token_count integer,
  created_at timestamptz NOT NULL DEFAULT now(),
  deleted_at timestamptz,
  deleted_by uuid,
  CONSTRAINT chk_conv_messages_json CHECK (content_json IS NULL OR jsonb_typeof(content_json) IN ('object','array')),
  CONSTRAINT chk_conv_messages_text_json CHECK (
    (content_type = 'text' AND content_text IS NOT NULL) OR
    (content_type <> 'text')
  )
);

CREATE INDEX IF NOT EXISTS idx_conv_messages_session_time ON conversation_messages (session_id, created_at);
CREATE INDEX IF NOT EXISTS idx_conv_messages_org ON conversation_messages (org_id)
  WHERE deleted_at IS NULL;

ALTER TABLE conversation_messages ENABLE ROW LEVEL SECURITY;

CREATE POLICY conv_messages_select ON conversation_messages
  FOR SELECT USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE POLICY conv_messages_insert ON conversation_messages
  FOR INSERT WITH CHECK (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE POLICY conv_messages_delete ON conversation_messages
  FOR DELETE USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

COMMIT;
-- File: arioncomply-v1/db/migrations/0003_conversation_sessions_messages.sql
