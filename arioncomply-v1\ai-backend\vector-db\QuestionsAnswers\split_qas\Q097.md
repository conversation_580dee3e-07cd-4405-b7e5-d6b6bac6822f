id: Q097
query: >-
  What do we need to do differently for cloud services vs. on-premise systems?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/A.14.2.7"
  - "GDPR:2016/Art.32"
overlap_ids: []
capability_tags:
  - "Register"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Annex A.14.2.7 Outsourced Development"
    id: "ISO27001:2022/A.14.2.7"
    locator: "Annex A.14.2.7"
  - title: "GDPR — Security of Processing"
    id: "GDPR:2016/Art.32"
    locator: "Article 32"
ui:
  cards_hint:
    - "Cloud vs on-prem checklist"
  actions:
    - type: "open_register"
      target: "cloud_controls"
      label: "View Cloud Controls"
output_mode: "both"
graph_required: false
notes: "Cloud adds shared-responsibility; ensure provider SLAs and encryption keys management"
---
### 97) What do we need to do differently for cloud services vs. on-premise systems?

**Standard terms**  
- **Outsourced development (A.14.2.7):** controls for external platforms.  
- **Security of processing (GDPR Art. 32):** applies equally to data in the cloud.

**Plain-English answer**  
In the cloud, follow a **shared-responsibility model**: verify provider security certifications, manage encryption keys yourself, enforce IAM policies, and review provider SLAs. On-prem you control the full stack but must maintain physical and network controls.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.14.2.7; GDPR Article 32

**Why it matters**  
Clarifying responsibilities prevents gaps in protection.

**Do next in our platform**  
- Use the **Cloud Controls** register.  
- Map provider responsibilities vs. your tasks.

**How our platform will help**  
- **[Register]** Shared-responsibility matrix.  
- **[Report]** Cloud compliance gap analysis.

**Likely follow-ups**  
- “How do we manage cloud key rotation?”  

**Sources**  
- ISO/IEC 27001:2022 Annex A.14.2.7  
- GDPR Art. 32
