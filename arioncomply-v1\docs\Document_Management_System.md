<!--
File: arioncomply-v1/docs/Document_Management_System.md
File Description: Design specification for the Document Management System.
Purpose: Capture requirements, workflows, and architecture for document handling.
Inputs: N/A
Outputs: N/A
Dependencies: Supabase backend, Flutter frontend.
Security/RLS: N/A
Notes: Provides context for developers building the DMS.
-->

# Document Management System: Comprehensive Overview

## Overview

This document outlines requirements, workflows, and low‑level design for a document management system built with Flutter (desktop first) and a Supabase backend. The system uses Markdown as the source of truth, supports offline editing with conflict resolution, and provides efficient search and filtering.

---

## 1. Requirements

### 1.1 Functional Requirements

* **FR1: Document Creation**

  * **FR1.1**: Create a new document from scratch.
  * **FR1.2**: Create from predefined Markdown templates.
  * **FR1.3**: Import an existing DOCX or PDF as a reference and store it.
  * **FR1.3.1** *(stretch)*: Attempt text/basic formatting extraction from imported DOCX/PDF to create an editable Markdown draft.

* **FR2: Document Editing (Markdown‑based)**

  * **FR2.1**: Edit content using a rich text (WYSIWYG‑like) editor.
  * **FR2.2**: Support text formatting (bold, italic, underline, strikethrough), headings H1–H4, lists (bulleted, numbered), blockquotes, links, image insertion.
  * **FR2.3**: Abstract Markdown syntax; users need not learn Markdown.
  * **FR2.4**: Live real‑time preview.
  * **FR2.5**: Insert predefined Markdown content blocks (template snippets).
  * **FR2.6**: Edit metadata (title, category, tags, version, author, review date).
  * **FR2.7**: **Contextual Template Access**: When a document with a specific `category_id` is active, the "New Document" dialog prioritizes templates for that category.

* **FR3: Document Storage**

  * **FR3.1**: Store Markdown content in Supabase (DB).
  * **FR3.2**: Store generated PDF and DOCX in Supabase Storage.
  * **FR3.3**: Store imported original DOCX/PDF in Supabase Storage.

* **FR4: Document Generation (PDF/DOCX)**

  * **FR4.1**: Explicitly trigger PDF generation from current Markdown.
  * **FR4.2**: Explicitly trigger DOCX generation from current Markdown.
  * **FR4.3**: Generated files respect Markdown formatting.
  * **FR4.4**: DOCX files based on predefined DOCX templates for branding.

* **FR5: Document Management & Retrieval**

  * **FR5.1**: View list of accessible documents.
  * **FR5.2**: Search by title, tags, or content.
  * **FR5.3**: Filter by criteria (status, category, etc.).
  * **FR5.4**: Download generated PDF and DOCX.
  * **FR5.5**: Delete documents and associated files.
  * **FR5.6: Filtering**: Filter by

    * `category_id` (e.g., Architecture Documents, HR Policies)
    * `status` (e.g., Draft, Published, Archived)
    * `user_id` (e.g., My Documents, Documents I've Reviewed)
    * Custom tags (if implemented)
  * **FR5.7: Search**: Full‑text search across `title`, `markdown_content`, `author`, `version`; partial, case‑insensitive.

* **FR6: Offline Support & Conflict Resolution**

  * **FR6.1**: Auto‑save to a local temp folder at intervals during editing.
  * **FR6.2**: On startup, detect local unsaved changes.
  * **FR6.3**: If local unsaved version conflicts with remote, prompt for resolution.
  * **FR6.4**: Options: Keep Local Changes, Use Online Version, Review Changes (Merge) with diff.
  * **FR6.5**: After successful sync or choosing remote, delete the local temp file.

* **FR7: Template Management**

  * **FR7.1**: Admins create, edit, delete Markdown templates.
  * **FR7.2**: Admins create, edit, delete DOCX templates.
  * **FR7.3**: Templates stored securely and accessible for creation/generation.
  * **FR7.4: Template Search & Filtering**: Search/filter by

    * Template name
    * Keywords in `template_markdown_content`
    * `is_active` status

### 1.2 Non‑Functional Requirements

* **NFR1: Performance**: Responsive loading/saving (1–3 s). PDF in 5–10 s. DOCX in 5–15 s. Responsive UI during operations.
* **NFR1.5: Search Performance**: Results within 1–2 s for typical queries at scale.
* **NFR2: Scalability**: Thousands of documents, hundreds of concurrent users; efficient Storage usage.
* **NFR3: Security**: Access enforced via Supabase RLS; encrypted in transit.
* **NFR4: Usability (Desktop first)**: Intuitive UI; editing similar to desktop word processors; clear feedback.
* **NFR4.4: Navigation**: Discoverable filtering and search controls in lists and template selection.
* **NFR5: Maintainability**: Modular, documented code; mature Flutter packages and Supabase features.

---

## 2. Workflow (with Screen Linkages)

### Primary Screens

* **AuthScreen**: Login/Signup.
* **HomeScreen**: Desktop workspace with

  * Left: Document Navigator
  * Center: Document Editor/Preview
  * Right: Document Details (optional)
* **TemplateManagementScreen**: Admin template management.
* **ConflictResolutionDialog**: Modal.
* **FileUploadDialog**: Modal for file selection/upload.
* **ConfirmDiscardDialog**: Modal.

### Initial App Launch & Login Flow

* **App Launch**

  * Logic: Check existing Supabase session.
  * Nav: If session exists → HomeScreen; otherwise → AuthScreen.
* **User Authentication**

  * Screen: AuthScreen
  * User: Enter credentials, click Login/Sign Up.
  * App: Supabase auth call. On success → HomeScreen; on failure show error.

### Scenario 1: Create New Document from Template (Contextual)

1. **Start New Document**

* Screen: HomeScreen
* User: Clicks **New Document → From Template**.
* App: Opens template selection overlay.
* Enhancement: If a document of Category A is active, prioritize templates for Category A (`category_id` or tags). Provide search and filters (FR7.4).

2. **Select Template**

* Screen: HomeScreen with overlay
* User: Selects a template.
* App: Closes overlay, loads template Markdown into editor, assigns temporary local `document_id`, initializes auto‑save, shows "Untitled Document (Draft)" with unsaved indicator in Navigator.

3. **Edit and Initial Save**

* Screen: HomeScreen
* User: Edits content, enters title. Auto‑save fires.
* User: Clicks **Save**.
* App: Shows Saving..., INSERT into `documents`. On success: replace temp `document_id` with Supabase ID, delete local temp file, update Navigator with title and Draft status, show feedback. On failure: show error; keep local file.

### Scenario 2: Edit Existing Document (with Conflict Resolution)

1. **Open Document**

* Screen: HomeScreen
* User: Clicks a document in Navigator.
* App: Show Loading..., check `unsaved_documents/` for `document_id`, fetch `markdown_content` and `updated_at` from Supabase, compare timestamps.

  * If no local or local older/equal: load remote into editor, delete any old local temp, init auto‑save, update Details, hide loader.
  * If local newer: present **ConflictResolutionDialog**.

2. **Conflict Resolution**

* Screen: ConflictResolutionDialog
* Options:

  * **Keep My Local Changes**: Load local Markdown, UPDATE Supabase. On success, delete local temp, show feedback.
  * **Discard My Local Changes**: Show ConfirmDiscardDialog → Confirm → Load remote, delete local temp, show feedback.
  * **Review Changes (Merge)**: Open Merge View diff → Resolve → Apply → UPDATE Supabase → delete local temp → show feedback.

3. **Continue Editing**

* Screen: HomeScreen
* User: Edit, auto‑save; click Save to persist remote.

### Scenario 3: Generate and Download PDF/DOCX

1. **Trigger Generation**

* Screen: HomeScreen
* User: Click **Generate PDF** or **Generate DOCX**.
* App: If unsaved changes exist, prompt to save. Show Generating....
* Background:

  * PDF: Client‑side generation.
  * DOCX: Supabase Edge Function `generate-docx`.
  * Upload generated file to Storage; update storage path in DB.
* Feedback: Show completion; enable download buttons.

2. **Download File**

* Screen: HomeScreen
* User: Click **Download PDF/DOCX**.
* App: Retrieve `_storage_path`, download from Storage, open native file picker, save, show completion with Open/Show in Folder.

### Scenario 4: Upload Existing External Document (DOCX/PDF)

1. **Initiate Upload**

* Screen: HomeScreen
* User: Click **Upload Document**.
* App: Open native file picker and show FileUploadDialog.

2. **Upload and Process**

* User: Select file.
* App: Upload to `documents_raw_imports` bucket, insert new `documents` row (`status='imported'`, `raw_import_path` set), update Navigator.
* Optional FR1.3.1: Prompt to convert to editable format.

  * If Yes: Trigger `extract-markdown-from-docx-pdf` Edge Function, show Converting..., update `markdown_content`, set `status='draft'` on completion.

### Scenario 5: Delete Document

1. **Initiate Deletion**

* Screen: HomeScreen
* User: Right‑click document → Delete or use delete icon.
* App: Show ConfirmDiscardDialog.

2. **Confirm and Delete**

* User: Confirm.
* App: Show Deleting..., DELETE from `documents`; on success delete associated files from Storage (PDF, DOCX, raw import, images), remove from Navigator, clear editor if open, show "Document Deleted".

### Scenario 6: Search and Filter Documents

1. **Initiate**

* Screen: HomeScreen (Navigator)
* User: Type in Search; set filters for Category, Status, Author.
* App: Debounce, build Supabase query using `ILIKE` for text and `eq` for IDs/status; show Searching....

2. **Display Results**

* App: Update list with results; hide loader.

3. **Clear**

* User: Clear filters or search.
* App: Fetch full list.

### Scenario 7: Search and Filter Templates (Template Management)

1. **Access**

* Screen: HomeScreen → navigate to **TemplateManagementScreen** (admin only).

2. **Initiate**

* Screen: TemplateManagementScreen
* User: Type in Search or set Active/Inactive and Category filters.
* App: Build query; show Loading....

3. **Display**

* App: Show filtered list; hide loader.

---

## 3. Low‑Level Design

### 3.1 Supabase Database Schema

```sql
-- public.documents table (Refined)
CREATE TABLE public.documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    markdown_content TEXT,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'pending_review', 'published', 'archived', 'imported')),
    category_id UUID REFERENCES public.categories(id) ON DELETE SET NULL,
    tags TEXT[] DEFAULT ARRAY[]::TEXT[],
    version TEXT DEFAULT '1.0',
    author TEXT,
    review_date DATE,
    pdf_storage_path TEXT,
    docx_storage_path TEXT,
    raw_import_path TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexing
CREATE INDEX idx_documents_user_id ON public.documents (user_id);
CREATE INDEX idx_documents_status ON public.documents (status);
CREATE INDEX idx_documents_category_id ON public.documents (category_id);

-- Full‑text search (tsvector)
ALTER TABLE public.documents ADD COLUMN tsv_content tsvector;
CREATE INDEX idx_documents_tsv_content ON public.documents USING GIN (tsv_content);

CREATE FUNCTION update_documents_tsv_content() RETURNS TRIGGER AS $$
BEGIN
    NEW.tsv_content = to_tsvector('english', NEW.title || ' ' || COALESCE(NEW.markdown_content, ''));
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER tgr_update_documents_tsv_content
BEFORE INSERT OR UPDATE OF title, markdown_content ON public.documents
FOR EACH ROW EXECUTE FUNCTION update_documents_tsv_content();

-- Tags GIN index
CREATE INDEX idx_documents_tags ON public.documents USING GIN (tags);

-- public.categories
CREATE TABLE public.categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT UNIQUE NOT NULL,
    description TEXT
);

-- public.templates (Refined)
CREATE TABLE public.templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT UNIQUE NOT NULL,
    template_markdown_content TEXT NOT NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    category_id UUID REFERENCES public.categories(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Template indexes
CREATE INDEX idx_templates_name ON public.templates (name);
CREATE INDEX idx_templates_category_id ON public.templates (category_id);
```

### 3.2 Supabase Storage Buckets and RLS

* **documents\_pdf**: generated PDF files.

  * RLS: allow `auth.uid()` to upload/download at `auth.uid()/{document_id}.pdf`.
* **documents\_docx**: generated DOCX files.

  * RLS: allow `auth.uid()` to upload/download at `auth.uid()/{document_id}.docx`.
* **documents\_raw\_imports**: original uploaded DOCX/PDF.

  * RLS: allow `auth.uid()` to upload/download at `auth.uid()/{document_id}/original_filename.ext`.
* **document\_images**: images embedded in Markdown.

  * RLS: allow `auth.uid()` to upload/download at `auth.uid()/{document_id}/image_name.png`.
* **app\_templates\_docx**: .docx template files for server‑side generation.

  * RLS: read‑only to Edge Functions; admin‑managed.

### 3.3 Supabase Edge Functions

* **generate-docx**

  * **Input**: `{ "document_id": "...", "markdown_content": "...", "docx_template_id": "..." }`
  * **Logic**:

    * Validate `document_id` and `auth.uid()`.
    * Fetch `markdown_content` and optional `docx_template_id`.
    * If template provided, download `.docx` from `app_templates_docx`.
    * Convert Markdown → DOCX; populate placeholders if used.
    * Upload to `documents_docx` at `auth.uid()/{document_id}.docx`.
    * Update `docx_storage_path` in `public.documents`.
    * Return success or error.

* **extract-markdown-from-docx-pdf** *(stretch)*

  * **Input**: `{ "document_id": "...", "raw_import_path": "..." }`
  * **Logic**:

    * Validate `document_id` and `auth.uid()`.
    * Download file from `documents_raw_imports`.
    * Extract text/basic Markdown from DOCX/PDF.
    * Update `markdown_content` in `public.documents`.
    * Return success or failure.

### 3.4 Flutter Application Architecture

* **lib/main.dart**: app setup, Supabase init, routing.
* **lib/models/**

  * `document/document.dart` — model for `public.documents` (incl. tags)
  * `category.dart` — model for `public.categories`
  * `template.dart` — model for `public.templates` (incl. `category_id`)
  * `docx_template.dart` — model for DOCX templates table
* **lib/services/**

  * `supabase_service.dart` — auth, DB, Storage wrapper
  * `document/document_repository.dart` — DB/Storage access; `searchDocuments()`, `searchTemplates()`
  * `document_manager.dart` — CRUD, generation, local sync, conflict resolution
  * `local_storage_manager.dart` — local file IO for auto‑save
  * `pdf_generator.dart` — wrapper for `pdf` package
  * `docx_generator.dart` — calls `generate-docx` Edge Function
* **lib/ui/**

  * `auth/auth_screen.dart`
  * `home/home_screen.dart` — orchestrates panels
  * `widgets/document_list_panel.dart` — left panel with search/filters
  * `widgets/document_editor_panel.dart` — center editor & preview
  * `widgets/document_metadata_panel.dart` — right panel
  * `dialogs/conflict_resolution_dialog.dart`
  * `dialogs/file_upload_dialog.dart`
  * `dialogs/confirm_discard_dialog.dart`
  * `document_editor/rich_text_editor_widget.dart` — WYSIWYG (e.g., `flutter_quill`)
  * `document_editor/markdown_preview_widget.dart` — live preview (`flutter_markdown`)
  * `document_editor/editor_toolbar.dart`
  * `template_management/template_management_screen.dart` — admin UI
  * `template_management/template_editor_widget.dart`
  * `shared_widgets/loading_indicator.dart`
  * `shared_widgets/file_picker_buttons.dart`

### 3.5 Local Storage Structure

```
ApplicationSupportDirectory/unsaved_documents/
  {document_id}.md              # raw Markdown of unsaved document
  _local_metadata.json          # list of auto‑saved docs + metadata
```

`_local_metadata.json` example:

```json
{
  "documents": [
    {
      "id": "abc-123-temp-id",
      "title": "My Draft Policy",
      "last_modified_local": "2025-05-25T11:00:00Z"
    },
    {
      "id": "def-456-actual-id",
      "title": "Existing Policy Edit",
      "last_modified_local": "2025-05-25T11:05:30Z"
    }
  ]
}
```

### 3.6 Key Logic Flow for Auto‑Save and Reconciliation

* **DocumentService (central hub)**

  * Manages active Markdown content.
  * Handles auto‑save timer and debouncer.
  * Performs CRUD with Supabase.
  * Uses LocalStorageService for local IO.
  * Triggers PDF/DOCX generation via generators.

* **Auto‑Save**

  * Editor `onChanged` → debounce → `saveDocumentContentLocally(documentId, markdown)`.
  * Periodic timer also persists locally.

* **Reconciliation on Load**

  * Read `_local_metadata.json`.
  * If local file exists for requested document:

    * Fetch remote `updated_at`.
    * Compare with `last_modified_local`.
    * If local newer → show ConflictResolutionDialog.
    * Else → load remote.

* **Cleanup after Sync**

  * After successful INSERT/UPDATE to Supabase, call `deleteDocumentContentLocally(documentId)` to remove temp file and metadata entry, unless user kept a local backup during conflict resolution.

---

## Notes for Consideration

* **WYSIWYG editor choice**: Verify Markdown conversion quality. A custom Markdown↔Delta converter may be needed.
* **DOCX generation**: `generate-docx` is a sizable effort. Identify a Deno‑compatible library for robust DOCX creation and templating.
* **Full‑text search**: Wire `tsvector` searches from Flutter using Supabase text search methods.
* **Error handling & UX**: Use clear, non‑intrusive feedback for network, file processing, and conflicts.
* **Scalability testing**: Load test DB, Storage, and Edge Functions to validate NFRs.
