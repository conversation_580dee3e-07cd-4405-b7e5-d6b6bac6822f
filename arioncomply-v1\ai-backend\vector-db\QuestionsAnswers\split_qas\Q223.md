id: Q223
query: >-
  How do we incorporate lessons learned from incidents or audits into our program?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/10.1"
overlap_ids: []
capability_tags:
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Improvement"
    id: "ISO27001:2022/10.1"
    locator: "Clause 10.1"
ui:
  cards_hint:
    - "Lessons-learned register"
  actions:
    - type: "open_register"
      target: "lessons_learned"
      label: "View Entries"
    - type: "start_workflow"
      target: "continuous_improvement"
      label: "Initiate Improvement"
output_mode: "both"
graph_required: false
notes: "Formalize after every audit or major incident"
---
### 223) How do we incorporate lessons learned from incidents or audits into our program?

**Standard terms)**  
- **Improvement (ISO 27001 Cl.10.1):** requires corrective actions and continual improvement.

**Plain-English answer**  
Capture root-causes and audit observations in a **Lessons-Learned Register**, prioritize by risk, and feed them into your continuous-improvement roadmap to update policies, controls, and training.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 10.1

**Why it matters**  
Addresses systemic issues and prevents recurring problems.

**Do next in our platform**  
- Add entries to **Lessons Learned** register.  
- Run **Continuous Improvement** workflow to assign tasks.

**How our platform will help**  
- **[Workflow]** Automated root-cause templates and task assignments.  
- **[Report]** Trend analysis of recurring issues.

**Likely follow-ups**  
- How often should we review the register?

**Sources**  
- ISO/IEC 27001:2022 Clause 10.1
