<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentScriptType="application/ecmascript" contentStyleType="text/css" height="941px" preserveAspectRatio="none" style="width:1748px;height:941px;" version="1.1" viewBox="0 0 1748 941" width="1748px" zoomAndPan="magnify"><defs/><g><text fill="#000000" font-family="sans-serif" font-size="18" lengthAdjust="spacingAndGlyphs" textLength="358" x="695.5" y="26.708">Ingest / Index Pipeline (LLM 2.0 aligned)</text><rect fill="#FFFFFF" height="325.5313" style="stroke: #000000; stroke-width: 2.0;" width="1295.5" x="440.5" y="453.7109"/><rect fill="#FFFFFF" height="264.1328" style="stroke: none; stroke-width: 1.0;" width="1295.5" x="440.5" y="515.1094"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="61" x2="61" y1="117.25" y2="854.5078"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="264.5" x2="264.5" y1="117.25" y2="854.5078"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="391.5" x2="391.5" y1="117.25" y2="854.5078"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="481.5" x2="481.5" y1="117.25" y2="854.5078"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="741" x2="741" y1="117.25" y2="854.5078"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="847" x2="847" y1="117.25" y2="854.5078"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="988" x2="988" y1="117.25" y2="854.5078"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="1129" x2="1129" y1="117.25" y2="854.5078"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="1381" x2="1381" y1="117.25" y2="854.5078"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="1646" x2="1646" y1="117.25" y2="854.5078"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="100" x="8" y="113.9482">Edge Function</text><ellipse cx="61" cy="47.9531" fill="#F8F8F8" rx="8" ry="8" style="stroke: #383838; stroke-width: 2.0;"/><path d="M61,55.9531 L61,82.9531 M48,63.9531 L74,63.9531 M61,82.9531 L48,97.9531 M61,82.9531 L74,97.9531 " fill="none" style="stroke: #383838; stroke-width: 2.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="100" x="8" y="866.5029">Edge Function</text><ellipse cx="61" cy="879.8047" fill="#F8F8F8" rx="8" ry="8" style="stroke: #383838; stroke-width: 2.0;"/><path d="M61,887.8047 L61,914.8047 M48,895.8047 L74,895.8047 M61,914.8047 L48,929.8047 M61,914.8047 L74,929.8047 " fill="none" style="stroke: #383838; stroke-width: 2.0;"/><rect fill="#F8F8F8" height="46.5938" style="stroke: #383838; stroke-width: 1.5;" width="99" x="215.5" y="69.6563"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="85" x="222.5" y="89.6514">API (FastAPI)</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="48" x="241" y="105.9482">/ingest</text><rect fill="#F8F8F8" height="46.5938" style="stroke: #383838; stroke-width: 1.5;" width="99" x="215.5" y="853.5078"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="85" x="222.5" y="873.5029">API (FastAPI)</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="48" x="241" y="889.7998">/ingest</text><path d="M367.5,90.9531 L416.5,90.9531 C421.5,90.9531 421.5,104.1016 421.5,104.1016 C421.5,104.1016 421.5,117.25 416.5,117.25 L367.5,117.25 C362.5,117.25 362.5,104.1016 362.5,104.1016 C362.5,104.1016 362.5,90.9531 367.5,90.9531 " fill="#F8F8F8" style="stroke: #383838; stroke-width: 2.0;"/><path d="M416.5,90.9531 C411.5,90.9531 411.5,104.1016 411.5,104.1016 C411.5,117.25 416.5,117.25 416.5,117.25 " fill="none" style="stroke: #383838; stroke-width: 2.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="39" x="367.5" y="108.9482">Redis</text><path d="M367.5,853.5078 L416.5,853.5078 C421.5,853.5078 421.5,866.6563 421.5,866.6563 C421.5,866.6563 421.5,879.8047 416.5,879.8047 L367.5,879.8047 C362.5,879.8047 362.5,866.6563 362.5,866.6563 C362.5,866.6563 362.5,853.5078 367.5,853.5078 " fill="#F8F8F8" style="stroke: #383838; stroke-width: 2.0;"/><path d="M416.5,853.5078 C411.5,853.5078 411.5,866.6563 411.5,866.6563 C411.5,879.8047 416.5,879.8047 416.5,879.8047 " fill="none" style="stroke: #383838; stroke-width: 2.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="39" x="367.5" y="871.5029">Redis</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="63" x="450.5" y="85.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="49" x="457.5" y="105.9482">Worker</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="63" x="450.5" y="853.5078"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="49" x="457.5" y="873.5029">Worker</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="52" x="712" y="113.9482">App DB</text><path d="M723,64.9531 C723,54.9531 741,54.9531 741,54.9531 C741,54.9531 759,54.9531 759,64.9531 L759,90.9531 C759,100.9531 741,100.9531 741,100.9531 C741,100.9531 723,100.9531 723,90.9531 L723,64.9531 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M723,64.9531 C723,74.9531 741,74.9531 741,74.9531 C741,74.9531 759,74.9531 759,64.9531 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="52" x="712" y="866.5029">App DB</text><path d="M723,879.8047 C723,869.8047 741,869.8047 741,869.8047 C741,869.8047 759,869.8047 759,879.8047 L759,905.8047 C759,915.8047 741,915.8047 741,915.8047 C741,915.8047 723,915.8047 723,905.8047 L723,879.8047 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M723,879.8047 C723,889.8047 741,889.8047 741,889.8047 C741,889.8047 759,889.8047 759,879.8047 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="128" x="780" y="113.9482">Postgres/pgvector</text><path d="M829,64.9531 C829,54.9531 847,54.9531 847,54.9531 C847,54.9531 865,54.9531 865,64.9531 L865,90.9531 C865,100.9531 847,100.9531 847,100.9531 C847,100.9531 829,100.9531 829,90.9531 L829,64.9531 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M829,64.9531 C829,74.9531 847,74.9531 847,74.9531 C847,74.9531 865,74.9531 865,64.9531 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="128" x="780" y="866.5029">Postgres/pgvector</text><path d="M829,879.8047 C829,869.8047 847,869.8047 847,869.8047 C847,869.8047 865,869.8047 865,879.8047 L865,905.8047 C865,915.8047 847,915.8047 847,915.8047 C847,915.8047 829,915.8047 829,905.8047 L829,879.8047 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M829,879.8047 C829,889.8047 847,889.8047 847,889.8047 C847,889.8047 865,889.8047 865,879.8047 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="123" x="924" y="113.9482">Chroma (optional)</text><path d="M970.5,64.9531 C970.5,54.9531 988.5,54.9531 988.5,54.9531 C988.5,54.9531 1006.5,54.9531 1006.5,64.9531 L1006.5,90.9531 C1006.5,100.9531 988.5,100.9531 988.5,100.9531 C988.5,100.9531 970.5,100.9531 970.5,90.9531 L970.5,64.9531 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M970.5,64.9531 C970.5,74.9531 988.5,74.9531 988.5,74.9531 C988.5,74.9531 1006.5,74.9531 1006.5,64.9531 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="123" x="924" y="866.5029">Chroma (optional)</text><path d="M970.5,879.8047 C970.5,869.8047 988.5,869.8047 988.5,869.8047 C988.5,869.8047 1006.5,869.8047 1006.5,879.8047 L1006.5,905.8047 C1006.5,915.8047 988.5,915.8047 988.5,915.8047 C988.5,915.8047 970.5,915.8047 970.5,905.8047 L970.5,879.8047 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M970.5,879.8047 C970.5,889.8047 988.5,889.8047 988.5,889.8047 C988.5,889.8047 1006.5,889.8047 1006.5,879.8047 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="127" x="1063" y="113.9482">Supabase Storage</text><ellipse cx="1129.5" cy="84.9531" fill="#F8F8F8" rx="12" ry="12" style="stroke: #383838; stroke-width: 2.0;"/><line style="stroke: #383838; stroke-width: 2.0;" x1="1117.5" x2="1141.5" y1="98.9531" y2="98.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="127" x="1063" y="866.5029">Supabase Storage</text><ellipse cx="1129.5" cy="885.8047" fill="#F8F8F8" rx="12" ry="12" style="stroke: #383838; stroke-width: 2.0;"/><line style="stroke: #383838; stroke-width: 2.0;" x1="1117.5" x2="1141.5" y1="899.8047" y2="899.8047"/><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="350" x="1206" y="85.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="336" x="1213" y="105.9482">ETL: detect • convert • OCR • normalize • chunk</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="350" x="1206" y="853.5078"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="336" x="1213" y="873.5029">ETL: detect • convert • OCR • normalize • chunk</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="160" x="1566" y="85.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="146" x="1573" y="105.9482">Embedder (local first)</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="160" x="1566" y="853.5078"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="146" x="1573" y="873.5029">Embedder (local first)</text><polygon fill="#383838" points="253,159.5156,263,163.5156,253,167.5156,257,163.5156" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="61" x2="259" y1="163.5156" y2="163.5156"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="81" x="68" y="143.3169">POST /ingest</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="180" x="68" y="158.4497">(tenant, doc_id, path, meta)</text><polygon fill="#383838" points="729,188.6484,739,192.6484,729,196.6484,733,192.6484" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="265" x2="735" y1="192.6484" y2="192.6484"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="173" x="272" y="187.5825">create job(status=queued)</text><polygon fill="#383838" points="380,217.7813,390,221.7813,380,225.7813,384,221.7813" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="265" x2="386" y1="221.7813" y2="221.7813"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="103" x="272" y="216.7153">enqueue(job_id)</text><polygon fill="#383838" points="72,246.9141,62,250.9141,72,254.9141,68,250.9141" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 2.0,2.0;" x1="66" x2="264" y1="250.9141" y2="250.9141"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="88" x="78" y="245.8481">202 Accepted</text><polygon fill="#383838" points="470,276.0469,480,280.0469,470,284.0469,474,280.0469" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="392" x2="476" y1="280.0469" y2="280.0469"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="66" x="399" y="274.981">job(job_id)</text><polygon fill="#383838" points="1117.5,305.1797,1127.5,309.1797,1117.5,313.1797,1121.5,309.1797" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="482" x2="1123.5" y1="309.1797" y2="309.1797"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="145" x="489" y="304.1138">signed URL (download)</text><polygon fill="#383838" points="1117.5,334.3125,1127.5,338.3125,1117.5,342.3125,1121.5,338.3125" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="482" x2="1123.5" y1="338.3125" y2="338.3125"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="111" x="489" y="333.2466">download to /tmp</text><line style="stroke: #383838; stroke-width: 1.0;" x1="482" x2="524" y1="367.4453" y2="367.4453"/><line style="stroke: #383838; stroke-width: 1.0;" x1="524" x2="524" y1="367.4453" y2="380.4453"/><line style="stroke: #383838; stroke-width: 1.0;" x1="483" x2="524" y1="380.4453" y2="380.4453"/><polygon fill="#383838" points="493,376.4453,483,380.4453,493,384.4453,489,380.4453" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="200" x="489" y="362.3794">checksum • MIME detect • size</text><polygon fill="#383838" points="729,405.5781,739,409.5781,729,413.5781,733,409.5781" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="482" x2="735" y1="409.5781" y2="409.5781"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="170" x="489" y="404.5122">lookup (doc_id, checksum)</text><polygon fill="#383838" points="493,434.7109,483,438.7109,493,442.7109,489,438.7109" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 2.0,2.0;" x1="487" x2="740" y1="438.7109" y2="438.7109"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="116" x="499" y="433.645">exists? (duplicate)</text><path d="M440.5,453.7109 L504.5,453.7109 L504.5,460.7109 L494.5,470.7109 L440.5,470.7109 L440.5,453.7109 " fill="#EEEEEE" style="stroke: #000000; stroke-width: 1.0;"/><rect fill="none" height="325.5313" style="stroke: #000000; stroke-width: 2.0;" width="1295.5" x="440.5" y="453.7109"/><text fill="#000000" font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="19" x="455.5" y="466.7778">alt</text><text fill="#000000" font-family="sans-serif" font-size="11" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="123" x="519.5" y="465.9214">[duplicate content]</text><polygon fill="#383838" points="729,503.1094,739,507.1094,729,511.1094,733,507.1094" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="482" x2="735" y1="507.1094" y2="507.1094"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="178" x="489" y="486.9106">update job(status=skipped)</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="123" x="489" y="502.0435">(reason=duplicate)</text><line style="stroke: #000000; stroke-width: 1.0; stroke-dasharray: 2.0,2.0;" x1="440.5" x2="1736" y1="516.1094" y2="516.1094"/><text fill="#000000" font-family="sans-serif" font-size="11" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="113" x="445.5" y="526.3198">[new or changed]</text><polygon fill="#383838" points="1369,547.0469,1379,551.0469,1369,555.0469,1373,551.0469" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="482" x2="1375" y1="551.0469" y2="551.0469"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="188" x="489" y="545.981">convert/OCR/normalize/chunk</text><polygon fill="#383838" points="493,591.3125,483,595.3125,493,599.3125,489,595.3125" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 2.0,2.0;" x1="487" x2="1380" y1="595.3125" y2="595.3125"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="108" x="499" y="575.1138">chunks[] + meta</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="146" x="499" y="590.2466">(pages, sections, tags)</text><polygon fill="#383838" points="1634,635.5781,1644,639.5781,1634,643.5781,1638,639.5781" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="482" x2="1640" y1="639.5781" y2="639.5781"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="100" x="489" y="619.3794">embed(chunks)</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="174" x="489" y="634.5122">(local /v1; fallback provider)</text><polygon fill="#383838" points="493,664.7109,483,668.7109,493,672.7109,489,668.7109" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 2.0,2.0;" x1="487" x2="1645" y1="668.7109" y2="668.7109"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="47" x="499" y="663.645">vectors</text><polygon fill="#383838" points="835,693.8438,845,697.8438,835,701.8438,839,697.8438" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="482" x2="841" y1="697.8438" y2="697.8438"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="174" x="489" y="692.7778">upsert vectors + metadata</text><polygon fill="#383838" points="976.5,722.9766,986.5,726.9766,976.5,730.9766,980.5,726.9766" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="482" x2="982.5" y1="726.9766" y2="726.9766"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="106" x="489" y="721.9106">upsert (optional)</text><polygon fill="#383838" points="729,767.2422,739,771.2422,729,775.2422,733,771.2422" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="482" x2="735" y1="771.2422" y2="771.2422"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="110" x="489" y="751.0435">update doc + job</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="235" x="489" y="766.1763">(status=completed, counts, timings)</text><polygon fill="#383838" points="1117.5,803.375,1127.5,807.375,1117.5,811.375,1121.5,807.375" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="482" x2="1123.5" y1="807.375" y2="807.375"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="153" x="489" y="802.3091">cleanup temp (optional)</text><polygon fill="#383838" points="276,832.5078,266,836.5078,276,840.5078,272,836.5078" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="270" x2="481" y1="836.5078" y2="836.5078"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="151" x="282" y="831.4419">progress/logs (optional)</text><!--MD5=[172fc33adfa9cdbe9272ea1ae1f41730]
@startuml Ingest / Index (LLM2.0)
title Ingest / Index Pipeline (LLM 2.0 aligned)
skinparam shadowing false
skinparam monochrome true

actor "Edge Function" as Edge
participant "API (FastAPI)\n/ingest" as API
queue Redis
participant Worker as W
database "App DB" as APPDB
database "Postgres/pgvector" as PG
database "Chroma (optional)" as Chroma
entity "Supabase Storage" as ST
participant "ETL: detect • convert • OCR • normalize • chunk" as ETL
participant "Embedder (local first)" as EMB

Edge -> API : POST /ingest\n(tenant, doc_id, path, meta)
API -> APPDB : create job(status=queued)
API -> Redis : enqueue(job_id)
API - -> Edge : 202 Accepted

Redis -> W : job(job_id)
W -> ST : signed URL (download)
W -> ST : download to /tmp
W -> W : checksum • MIME detect • size
W -> APPDB : lookup (doc_id, checksum)
APPDB - -> W : exists? (duplicate)

alt duplicate content
  W -> APPDB : update job(status=skipped)\n(reason=duplicate)
else new or changed
  W -> ETL : convert/OCR/normalize/chunk
  ETL - -> W : chunks[] + meta\n(pages, sections, tags)
  W -> EMB : embed(chunks)\n(local /v1; fallback provider)
  EMB - -> W : vectors
  W -> PG : upsert vectors + metadata
  W -> Chroma : upsert (optional)
  W -> APPDB : update doc + job\n(status=completed, counts, timings)
end

W -> ST : cleanup temp (optional)
W -> API : progress/logs (optional)

@enduml

PlantUML version 1.2020.02(Sun Mar 01 10:22:07 UTC 2020)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Java Version: 17.0.16+8
Operating System: Linux
Default Encoding: UTF-8
Language: en
Country: null
--></g></svg>