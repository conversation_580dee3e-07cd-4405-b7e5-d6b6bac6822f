id: Q103
query: >-
  How do we communicate with customers about privacy changes?
packs:
  - "GDPR:2016"
primary_ids:
  - "GDPR:2016/Art.12"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Report"
flags: []
sources:
  - title: "GDPR — Transparent Information"
    id: "GDPR:2016/Art.12"
    locator: "Article 12"
ui:
  cards_hint:
    - "Privacy notice toolkit"
  actions:
    - type: "start_workflow"
      target: "privacy_notice_update"
      label: "Update Privacy Notices"
    - type: "open_register"
      target: "notification_log"
      label: "View Notifications"
output_mode: "both"
graph_required: false
notes: "Use clear, concise language and give actionable choices"
---
### 103) How do we communicate with customers about privacy changes?

**Standard terms)**  
- **Transparency obligations (GDPR Art. 12):** information must be concise, transparent, and easily accessible.

**Plain-English answer**  
Publish updated privacy notices on your website and app, email existing customers with summary of key changes, and highlight new rights or opt-out options. Use FAQs and webinars for complex updates.

**Applies to**  
- **Primary:** GDPR Article 12

**Why it matters**  
Meets legal transparency requirements and maintains customer trust.

**Do next in our platform**  
- Run **Privacy Notice Update** workflow.  
- Log customer notifications in register.

**How our platform will help**  
- **[Workflow]** Notice drafting and approval.  
- **[Report]** Tracks notification coverage and feedback.

**Likely follow-ups**  
- “Do we need consent again for existing users?” (If processing basis changes)

**Sources**  
- GDPR Article 12
