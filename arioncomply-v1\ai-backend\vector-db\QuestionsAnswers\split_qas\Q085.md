id: Q085
query: >-
  How do we set up incident response when we've never had formal procedures?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/A.5.24"
overlap_ids:
  - "NIS2:2023/Art.21"
capability_tags:
  - "Workflow"
  - "Draft Doc"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Incident Management"
    id: "ISO27001:2022/A.5.24"
    locator: "Annex A.5.24"
  - title: "NIS2 Directive — Incident Notification"
    id: "NIS2:2023/Art.21"
    locator: "Article 21"
ui:
  cards_hint:
    - "Incident response runbook"
  actions:
    - type: "start_workflow"
      target: "incident_response_setup"
      label: "Create Runbook"
    - type: "open_register"
      target: "incident_playbooks"
      label: "View Playbooks"
output_mode: "both"
graph_required: false
notes: "Start with a high-level runbook then detail key scenarios"
---
### 85) How do we set up incident response when we've never had formal procedures?

**Standard terms**  
- **Incident management (ISO 27001 A.5.24):** establish process for incidents.  
- **Notification (NIS2 Art. 21):** reporting timelines to authorities.

**Plain-English answer**  
Begin with a **runbook template**: define roles (detection, triage, escalation), steps (identify, contain, eradicate, recover), and notification criteria. Then flesh out playbooks for common scenarios (phishing, malware).

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.5.24; NIS2 Directive 2022/2555 Article 21

**Why it matters**  
Structured response minimizes impact and meets legal deadlines.

**Do next in our platform**  
- Launch **Incident Response Setup** workflow.  
- Generate initial runbook.

**How our platform will help**  
- **[Draft Doc]** Runbook and playbook templates.  
- **[Workflow]** Guided incident-response steps.  

**Likely follow-ups**  
- “How do we test our runbook?” (Schedule tabletop exercises)

**Sources**  
- ISO/IEC 27001:2022 Annex A.5.24  
- NIS2 Directive 2023 Article 21