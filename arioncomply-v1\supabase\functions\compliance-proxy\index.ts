// File: arioncomply-v1/supabase/functions/compliance-proxy/index.ts
// File Description: Unified compliance-proxy Edge Function (production)
// Purpose: Accept both simple and rich UI requests; call AI backend or provider(s); return normalized response.
// Inputs (Body - camelCase):
//   - Simple: { userQuery: string }
//   - Rich:   { provider?: 'openai'|'claude'|'both', messages: [{role, content}], systemPrompt?: string, parameters?: object }
// Outputs (camelCase):
//   - Single provider: { content: string, model?: string, usage?: any }
//   - Both providers:  { openai: { content, model?, usage? }, claude: { content, model?, usage? } }
// Notes: Keeps backward compatibility; prefers backend forwarding when AI_BACKEND_URL is set.

// @ts-ignore
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
// @ts-ignore
import { corsHeaders } from "../_shared/cors.ts";
import { apiOk, apiError } from "../_shared/errors.ts";
import { triggerDiagnostics } from "../_shared/diagnostics.ts";

type ChatMessage = { role: 'system'|'user'|'assistant'; content: string };

// AI Backend configuration
const AI_BACKEND_URL = Deno.env.get('AI_BACKEND_URL') || '';



serve(async (req: Request) => {
  const requestId = crypto.randomUUID();

  // CORS preflight
  if (req.method === 'OPTIONS') return new Response('ok', { headers: corsHeaders });
  if (req.method !== 'POST') {
    return apiError("METHOD_NOT_ALLOWED", "Only POST method is allowed", 405, undefined, requestId);
  }

  // Basic auth presence (align with current UIs using anon key)
  const authHeader = req.headers.get('Authorization');
  if (!authHeader || !/Bearer\s+.+/i.test(authHeader)) {
    return apiError("AUTHORIZATION_MISSING", "Missing or invalid Authorization header", 401, undefined, requestId);
  }

  let body: any;
  try {
    body = await req.json();
  } catch (error) {
    return apiError("INVALID_JSON", "Invalid JSON body", 400, { originalError: String(error) }, requestId);
  }

  // Normalize inputs - support both camelCase (preferred) and snake_case (legacy)
  const simpleQuery: string | undefined = body?.userQuery || body?.user_query || body?.query;
  const richMessages: ChatMessage[] | undefined = body?.messages;
  const provider: 'openai'|'claude'|'both'|undefined = body?.provider;
  const systemPrompt: string | undefined = body?.systemPrompt || body?.system_prompt;
  const parameters: Record<string, unknown> = body?.parameters || {};

  // Build a messages array for simple case
  let messages: ChatMessage[] = richMessages || [];
  if (!messages?.length && simpleQuery) {
    messages = [{ role: 'user', content: String(simpleQuery) }];
  }
  if (!messages?.length) {
    return apiError(
      "VALIDATION_REQUIRED_FIELD", 
      "Either messages array or userQuery is required", 
      400, 
      { missingFields: ["messages", "userQuery"] }, 
      requestId
    );
  }

  // Check if AI Backend is configured
  if (!AI_BACKEND_URL) {
    return apiError(
      "AI_BACKEND_NOT_CONFIGURED",
      "AI Backend service is not configured",
      503,
      { 
        details: "AI_BACKEND_URL environment variable not set",
        supportAction: "contact_support"
      },
      requestId
    );
  }

  // Forward to AI backend
  try {
    const resp = await fetch(AI_BACKEND_URL, {
      method: 'POST',
      headers: { 
        'content-type': 'application/json',
        // Forward any special headers for provider preferences if specified
        ...(provider && { 'x-provider-preference': provider })
      },
      body: JSON.stringify({ messages, systemPrompt, parameters }),
    });
    
    if (!resp.ok) {
      // AI Backend returned an error - trigger alert
      const errorText = await resp.text();
      console.error(`AI Backend error ${resp.status}: ${errorText}`);
      return apiError(
        "AI_BACKEND_ERROR",
        "AI service temporarily unavailable",
        502,
        { 
          backendStatus: resp.status,
          details: "Please contact support if this persists",
          supportAction: "contact_support"
        },
        requestId
      );
    }

    const data = await resp.json();
    // Normalize response format - ensure camelCase
    const content = data?.content || data?.text || data?.assistant?.text || '';
    const responseData = { content, model: data?.model, usage: data?.usage };
    return apiOk(responseData, requestId);

  } catch (error) {
    // Network error or timeout - trigger alert and diagnostics
    console.error(`AI Backend connection failed: ${error}`);
    
    // Trigger automated diagnostics
    let diagnosticsTriggered = false;
    try {
      await triggerDiagnostics('compliance-proxy', AI_BACKEND_URL, error);
      diagnosticsTriggered = true;
    } catch (diagError) {
      console.error(`Diagnostics trigger failed: ${diagError}`);
    }
    
    return apiError(
      "AI_BACKEND_UNREACHABLE",
      "AI service is currently unreachable",
      503,
      {
        details: "Please contact support - service may be down",
        supportAction: "contact_support",
        diagnosticsTriggered,
        errorType: "connection_failed"
      },
      requestId
    );
  }
});


