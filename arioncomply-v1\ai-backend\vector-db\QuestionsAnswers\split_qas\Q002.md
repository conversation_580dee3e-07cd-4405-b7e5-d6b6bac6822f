id: Q002
query: >-
  What's the difference between a "standard" and a "regulation" and why does it matter?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/1.1"
  - "GDPR:2016/Art.2"
overlap_ids:
  - "EUAI:2024/Art.1"
  - "NIS2:2023/Art.1"
capability_tags:
  - "Register"
  - "Planner"
  - "Dashboard"
  - "NL-Portal"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Introduction and Scope"
    id: "ISO27001:2022/1.1"
    locator: "Section 1.1"
  - title: "GDPR (Regulation 2016/679) — Territorial Scope"
    id: "GDPR:2016/Art.2"
    locator: "Article 2"
ui:
  cards_hint:
    - "Standards vs regulations"
    - "Compliance roadmap setup"
  actions:
    - type: "open_register"
      target: "legal_regulatory_compliance"
      label: "Map Legal Requirements"
    - type: "start_workflow"
      target: "program_readiness"
      label: "Build Compliance Roadmap"
output_mode: "both"
graph_required: true
notes: "Critical distinction for prioritization—legal obligations vs voluntary certifications"
---
### 2) What's the difference between a "standard" and a "regulation" and why does it matter?

**Standard terms)**
- **Standard (ISO/IEC 27001 Section 1.1):** voluntary guidance developed by expert consensus; you can choose to adopt it or customers may require it.
- **Regulation (GDPR Article 2):** binding law with enforcement powers; authorities can fine for non-compliance.
- **Directive:** EU law requiring Member State transposition (e.g., NIS2).

**Plain-English answer**
A standard like ISO 27001 is **voluntary**—you can get certified to demonstrate compliance. A regulation like GDPR is **law**—you **must** comply. You **certify** to standards; you **comply** with regulations. Knowing the difference helps you prioritize what’s legally required versus what’s commercially valuable.

**Applies to**
- **Primary:** ISO/IEC 27001:2022 Section 1.1; GDPR Article 2
- **Also relevant/Overlaps:** EU AI Act Article 1; NIS2 Article 1

**Why it matters**
Understanding what's optional versus legally required changes your implementation priorities and risk assessment.

**Do next in our platform**
- Tag each requirement as **Standard** or **Regulation** in your compliance register.
- Build separate roadmaps for legal obligations versus voluntary certifications.
- Set up monitoring for regulatory deadlines and updates.

**How our platform will help**
- **[Register]** Legal & regulatory compliance tracking with auto-categorization.
- **[Planner]** Separate planning streams for standards vs regulations.
- **[Dashboard]** Visual distinction between legal requirements and voluntary standards.
- **[NL-Portal]** Jump to relevant compliance areas instantly.

**Likely follow-ups**
- “If NIS2 is a Directive, what do we follow today?” (Your Member State’s law **[LOCAL LAW CHECK]**)
- “Can ISO 27001 certification help with GDPR compliance?” (Yes—many controls overlap)

**Sources**
- ISO/IEC 27001:2022 Section 1.1
- GDPR Article 2
