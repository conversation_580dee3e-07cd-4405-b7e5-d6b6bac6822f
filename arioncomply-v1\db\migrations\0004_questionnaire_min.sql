-- File: arioncomply-v1/db/migrations/0004_questionnaire_min.sql
-- Migration 0004: Questionnaire minimal + provenance and KB sources
-- Purpose: Provide the minimal assessment backbone for the wizard-driven flow,
--          with provenance to AIBackend Inputs for traceability.
-- Tables:
--   - questionnaire_templates/sections/questions: authoring and configuration of assessments
--   - questionnaire_instances/responses/evidence: runtime capture of answers and artifacts
--   - kb_sources: catalog of repo sources (manuals, consulting docs, QAs) with checksums
--   - question_citations: link each question to source material for auditability
--   - question_suggestions: follow-up chips to guide users in chat/wizard UX
--   - question_requirements: placeholder mapping to standard requirements (FK later)
-- RLS: org-scoped for tenant data; templates may be global (org_id NULL) for shared templates.
-- JSONB: settings, scoring, thresholds validated via CHECKs for data-driven UI/API.
-- Note: requirement-based mappings will be added later when standards_min is applied;
--       a placeholder table is created without FK for forward compatibility.

BEGIN;

-- Core questionnaire tables (subset of the detailed schema)
CREATE TABLE IF NOT EXISTS questionnaire_templates (
  template_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid REFERENCES organizations(org_id) ON DELETE CASCADE,
  template_name text NOT NULL,
  description text,
  template_type text NOT NULL CHECK (template_type IN ('vendor_assessment','privacy_assessment','security_review','compliance_check','due_diligence','custom')),
  version integer DEFAULT 1,
  status text DEFAULT 'draft' CHECK (status IN ('draft','active','deprecated','archived')),
  instructions text,
  scoring_methodology jsonb,
  risk_thresholds jsonb,
  settings jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid,
  updated_at timestamptz NOT NULL DEFAULT now(),
  updated_by uuid,
  deleted_at timestamptz,
  deleted_by uuid,
  CONSTRAINT chk_qt_scoring CHECK (scoring_methodology IS NULL OR jsonb_typeof(scoring_methodology) = 'object'),
  CONSTRAINT chk_qt_thresholds CHECK (risk_thresholds IS NULL OR jsonb_typeof(risk_thresholds) = 'object'),
  CONSTRAINT chk_qt_settings CHECK (jsonb_typeof(settings) = 'object')
);

CREATE INDEX IF NOT EXISTS idx_qt_org_active ON questionnaire_templates (org_id, status)
  WHERE deleted_at IS NULL;

ALTER TABLE questionnaire_templates ENABLE ROW LEVEL SECURITY;
CREATE POLICY qt_select ON questionnaire_templates FOR SELECT USING (org_id = app_current_org_id() OR org_id IS NULL OR app_has_role('admin'));
CREATE POLICY qt_insert ON questionnaire_templates FOR INSERT WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY qt_update ON questionnaire_templates FOR UPDATE USING (org_id = app_current_org_id() OR app_has_role('admin')) WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY qt_delete ON questionnaire_templates FOR DELETE USING (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE TRIGGER qt_set_updated_at BEFORE UPDATE ON questionnaire_templates FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

CREATE TABLE IF NOT EXISTS questionnaire_sections (
  section_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  template_id uuid NOT NULL REFERENCES questionnaire_templates(template_id) ON DELETE CASCADE,
  org_id uuid REFERENCES organizations(org_id) ON DELETE CASCADE,
  section_name text NOT NULL,
  description text,
  section_order integer NOT NULL,
  display_conditions jsonb,
  settings jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid,
  updated_at timestamptz NOT NULL DEFAULT now(),
  updated_by uuid,
  deleted_at timestamptz,
  deleted_by uuid,
  CONSTRAINT uq_qs_template_order UNIQUE (template_id, section_order),
  CONSTRAINT chk_qs_cond CHECK (display_conditions IS NULL OR jsonb_typeof(display_conditions) = 'object'),
  CONSTRAINT chk_qs_settings CHECK (jsonb_typeof(settings) = 'object')
);

CREATE INDEX IF NOT EXISTS idx_qs_template ON questionnaire_sections (template_id, section_order)
  WHERE deleted_at IS NULL;

ALTER TABLE questionnaire_sections ENABLE ROW LEVEL SECURITY;
CREATE POLICY qs_select ON questionnaire_sections FOR SELECT USING (org_id = app_current_org_id() OR org_id IS NULL OR app_has_role('admin'));
CREATE POLICY qs_insert ON questionnaire_sections FOR INSERT WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY qs_update ON questionnaire_sections FOR UPDATE USING (org_id = app_current_org_id() OR app_has_role('admin')) WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY qs_delete ON questionnaire_sections FOR DELETE USING (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE TRIGGER qs_set_updated_at BEFORE UPDATE ON questionnaire_sections FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

CREATE TABLE IF NOT EXISTS questionnaire_questions (
  question_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  section_id uuid NOT NULL REFERENCES questionnaire_sections(section_id) ON DELETE CASCADE,
  org_id uuid REFERENCES organizations(org_id) ON DELETE CASCADE,
  question_text text NOT NULL,
  question_type text NOT NULL CHECK (question_type IN ('text','textarea','number','date','single_choice','multiple_choice','yes_no','scale','matrix','file_upload')),
  question_order integer NOT NULL,
  is_required boolean DEFAULT true,
  answer_options jsonb,
  validation_rules jsonb,
  scoring_rules jsonb,
  help_text text,
  display_conditions jsonb,
  settings jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid,
  updated_at timestamptz NOT NULL DEFAULT now(),
  updated_by uuid,
  deleted_at timestamptz,
  deleted_by uuid,
  CONSTRAINT uq_qq_section_order UNIQUE (section_id, question_order),
  CONSTRAINT chk_qq_options CHECK (answer_options IS NULL OR jsonb_typeof(answer_options) = 'object'),
  CONSTRAINT chk_qq_validation CHECK (validation_rules IS NULL OR jsonb_typeof(validation_rules) = 'object'),
  CONSTRAINT chk_qq_scoring CHECK (scoring_rules IS NULL OR jsonb_typeof(scoring_rules) = 'object'),
  CONSTRAINT chk_qq_cond CHECK (display_conditions IS NULL OR jsonb_typeof(display_conditions) = 'object'),
  CONSTRAINT chk_qq_settings CHECK (jsonb_typeof(settings) = 'object')
);

CREATE INDEX IF NOT EXISTS idx_qq_section ON questionnaire_questions (section_id, question_order)
  WHERE deleted_at IS NULL;

ALTER TABLE questionnaire_questions ENABLE ROW LEVEL SECURITY;
CREATE POLICY qq_select ON questionnaire_questions FOR SELECT USING (org_id = app_current_org_id() OR org_id IS NULL OR app_has_role('admin'));
CREATE POLICY qq_insert ON questionnaire_questions FOR INSERT WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY qq_update ON questionnaire_questions FOR UPDATE USING (org_id = app_current_org_id() OR app_has_role('admin')) WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY qq_delete ON questionnaire_questions FOR DELETE USING (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE TRIGGER qq_set_updated_at BEFORE UPDATE ON questionnaire_questions FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

-- Instances & responses
CREATE TABLE IF NOT EXISTS questionnaire_instances (
  instance_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid REFERENCES organizations(org_id) ON DELETE CASCADE,
  template_id uuid NOT NULL REFERENCES questionnaire_templates(template_id),
  instance_name text NOT NULL,
  status text DEFAULT 'not_started' CHECK (status IN ('not_started','in_progress','submitted','under_review','approved','rejected','expired')),
  due_date timestamptz,
  submitted_date timestamptz,
  completion_percentage integer DEFAULT 0,
  total_score numeric(5,2),
  created_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid,
  updated_at timestamptz NOT NULL DEFAULT now(),
  updated_by uuid,
  deleted_at timestamptz,
  deleted_by uuid
);

CREATE INDEX IF NOT EXISTS idx_qi_org_status ON questionnaire_instances (org_id, status)
  WHERE deleted_at IS NULL;

ALTER TABLE questionnaire_instances ENABLE ROW LEVEL SECURITY;
CREATE POLICY qi_select ON questionnaire_instances FOR SELECT USING (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY qi_insert ON questionnaire_instances FOR INSERT WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY qi_update ON questionnaire_instances FOR UPDATE USING (org_id = app_current_org_id() OR app_has_role('admin')) WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY qi_delete ON questionnaire_instances FOR DELETE USING (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE TRIGGER qi_set_updated_at BEFORE UPDATE ON questionnaire_instances FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

CREATE TABLE IF NOT EXISTS questionnaire_responses (
  response_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid REFERENCES organizations(org_id) ON DELETE CASCADE,
  instance_id uuid NOT NULL REFERENCES questionnaire_instances(instance_id) ON DELETE CASCADE,
  question_id uuid NOT NULL REFERENCES questionnaire_questions(question_id),
  response_value text,
  response_data jsonb,
  response_score numeric(5,2),
  is_flagged boolean DEFAULT false,
  flag_reason text,
  created_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid,
  updated_at timestamptz NOT NULL DEFAULT now(),
  updated_by uuid,
  deleted_at timestamptz,
  deleted_by uuid,
  CONSTRAINT uq_qr_instance_question UNIQUE (instance_id, question_id),
  CONSTRAINT chk_qr_data CHECK (response_data IS NULL OR jsonb_typeof(response_data) = 'object')
);

CREATE INDEX IF NOT EXISTS idx_qr_instance ON questionnaire_responses (instance_id)
  WHERE deleted_at IS NULL;

ALTER TABLE questionnaire_responses ENABLE ROW LEVEL SECURITY;
CREATE POLICY qr_select ON questionnaire_responses FOR SELECT USING (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY qr_insert ON questionnaire_responses FOR INSERT WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY qr_update ON questionnaire_responses FOR UPDATE USING (org_id = app_current_org_id() OR app_has_role('admin')) WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY qr_delete ON questionnaire_responses FOR DELETE USING (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE TRIGGER qr_set_updated_at BEFORE UPDATE ON questionnaire_responses FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

CREATE TABLE IF NOT EXISTS questionnaire_evidence (
  evidence_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid REFERENCES organizations(org_id) ON DELETE CASCADE,
  response_id uuid NOT NULL REFERENCES questionnaire_responses(response_id) ON DELETE CASCADE,
  evidence_name text NOT NULL,
  evidence_type text NOT NULL CHECK (evidence_type IN ('document','screenshot','certificate','report','url','other')),
  file_path text,
  external_url text,
  verification_status text DEFAULT 'unverified' CHECK (verification_status IN ('unverified','verified','rejected')),
  verification_notes text,
  expiry_date date,
  created_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid,
  deleted_at timestamptz,
  deleted_by uuid
);

CREATE INDEX IF NOT EXISTS idx_qe_response ON questionnaire_evidence (response_id);

ALTER TABLE questionnaire_evidence ENABLE ROW LEVEL SECURITY;
CREATE POLICY qe_select ON questionnaire_evidence FOR SELECT USING (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY qe_insert ON questionnaire_evidence FOR INSERT WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY qe_delete ON questionnaire_evidence FOR DELETE USING (org_id = app_current_org_id() OR app_has_role('admin'));

-- Provenance: knowledge sources catalog (AIBackend Inputs coordination)
CREATE TABLE IF NOT EXISTS kb_sources (
  source_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  source_path text NOT NULL, -- repo-relative path
  title text,
  source_type text NOT NULL CHECK (source_type IN ('manual','consulting','qa','other')),
  framework_tags text[],
  checksum text,
  ingestion_status text CHECK (ingestion_status IN ('pending','ingested','failed')),
  chunk_count integer,
  last_ingested_at timestamptz,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

CREATE UNIQUE INDEX IF NOT EXISTS uq_kb_source_path ON kb_sources (source_path);
CREATE TRIGGER kb_sources_set_updated_at BEFORE UPDATE ON kb_sources FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

-- Citations: tie questions to source material with checksums
CREATE TABLE IF NOT EXISTS question_citations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  question_id uuid NOT NULL REFERENCES questionnaire_questions(question_id) ON DELETE CASCADE,
  source_path text NOT NULL,
  source_anchor text,
  source_checksum text,
  created_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid
);

CREATE INDEX IF NOT EXISTS idx_question_citations_q ON question_citations (question_id);

-- Suggestions: follow-up chips per question
CREATE TABLE IF NOT EXISTS question_suggestions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  question_id uuid NOT NULL REFERENCES questionnaire_questions(question_id) ON DELETE CASCADE,
  suggestion_text text NOT NULL,
  sort_order integer DEFAULT 0
);

CREATE INDEX IF NOT EXISTS idx_question_suggestions_q ON question_suggestions (question_id, sort_order);

-- Placeholder: question to requirement mapping (FK added later in standards migration)
CREATE TABLE IF NOT EXISTS question_requirements (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  question_id uuid NOT NULL REFERENCES questionnaire_questions(question_id) ON DELETE CASCADE,
  requirement_id uuid NOT NULL, -- FK to standard_requirements added in a later migration
  weight numeric(5,2) DEFAULT 1.0,
  UNIQUE (question_id, requirement_id)
);

COMMIT;
-- File: arioncomply-v1/db/migrations/0004_questionnaire_min.sql
