-- File: arioncomply-v1/db/migrations/0015_conversation_message_feedback.sql
-- Migration 0015: Conversation message feedback (thumbs + comment)
-- Purpose: Persist user feedback on assistant messages (per org/session/message)
-- Creates: conversation_message_feedback; indexes on (org_id, session_id, message_id, created_at)
-- Policies: <PERSON><PERSON><PERSON><PERSON> RLS; org-scoped SELECT/INSERT/UPDATE with admin bypass via app_has_role('admin')
-- Security: Inserts via service-role edge function; org_id enforced by RLS
-- Verify: SELECT count(*) FROM conversation_message_feedback;

BEGIN;

CREATE TABLE IF NOT EXISTS conversation_message_feedback (
  feedback_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL,
  user_id uuid,
  session_id uuid NOT NULL,
  message_id uuid NOT NULL,
  rating text NOT NULL CHECK (rating IN ('up','down')),
  reason_code text,
  comment text,
  created_at timestamptz NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS idx_cmf_org_session_msg
  ON conversation_message_feedback (org_id, session_id, message_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_cmf_org_created
  ON conversation_message_feedback (org_id, created_at DESC);

ALTER TABLE conversation_message_feedback ENABLE ROW LEVEL SECURITY;

CREATE POLICY cmf_select ON conversation_message_feedback
  FOR SELECT USING (org_id = app_current_org_id() OR app_has_role('admin'));

CREATE POLICY cmf_insert ON conversation_message_feedback
  FOR INSERT WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));

CREATE POLICY cmf_update ON conversation_message_feedback
  FOR UPDATE USING (org_id = app_current_org_id() OR app_has_role('admin'))
  WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));

COMMIT;

