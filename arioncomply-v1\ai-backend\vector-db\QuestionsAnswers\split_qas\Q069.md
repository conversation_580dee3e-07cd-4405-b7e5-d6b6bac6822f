id: Q069
query: >-
  What's the ongoing time commitment for incident response and reporting?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/A.5.24"
overlap_ids:
  - "NIS2:2023/Art.21"
capability_tags:
  - "Workflow"
  - "Tracker"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Incident Management"
    id: "ISO27001:2022/A.5.24"
    locator: "Annex A.5.24"
  - title: "NIS2 Directive — Incident Notification"
    id: "NIS2:2023/Art.21"
    locator: "Article 21"
ui:
  cards_hint:
    - "Incident response runbook"
  actions:
    - type: "start_workflow"
      target: "incident_management"
      label: "Manage Incidents"
    - type: "open_register"
      target: "incident_log"
      label: "View Incident Log"
output_mode: "both"
graph_required: false
notes: "Expect ~1–3 hours/week for logging, triage, and reporting"
---
### 69) What's the ongoing time commitment for incident response and reporting?

**Standard terms**  
- **Incident management (ISO 27001 A.5.24):** process for handling incidents.  
- **Notification (NIS2 Art. 21):** timeline and content for reporting.

**Plain-English answer**  
Post-certification, your team spends about **1–3 hours per week** on incident triage, logging, periodic drills, and required reporting to authorities.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.5.24; NIS2 Directive 2022/2555 Article 21

**Why it matters**  
Proper time budgeting ensures timely response and compliance with notification deadlines.

**Do next in our platform**  
- Activate **Incident Management** workflow.  
- Review and update incident logs.

**How our platform will help**  
- **[Tracker]** Incident logging.  
- **[Workflow]** Triage and reporting steps.  
- **[Reminder]** Notification deadlines.

**Likely follow-ups**  
- “How do we categorize incident severity?” (Use our built-in severity matrix)

**Sources**  
- ISO/IEC 27001:2022 Annex A.5.24  
- NIS2 Directive 2022/2555 Article 21
