{"id": "Q001", "version": "1.0.0", "artifact": {"type": "qa", "title": "What is ISO 27001 and why does everyone keep talking about it?", "language": "en", "status": "approved", "tags": ["isms", "certification", "foundation"]}, "standard": {"framework": "ISO27001", "version": "2022", "clauses": ["ISO27001:2022/4.1", "ISO27001:2022/A.5"]}, "content": {"qa": {"question": "What is ISO 27001 and why does everyone keep talking about it?", "audiences": ["exec", "security", "all"], "difficulty": "intro"}}, "retrieval": {"embeddings": {"model": "text-embedding-3-large", "chunk_size": 1200, "overlap": 200}, "keywords": ["ISO 27001", "ISMS", "certification", "security management"], "rerank_hint": "foundational concepts vs implementation details"}, "relations": [{"id": "Q010", "type": "see_also"}, {"id": "Q025", "type": "see_also"}, {"id": "Q051", "type": "see_also"}], "governance": {"soa_ids": ["ISO27001:2022/A.5"], "risk_ids": ["ISMS-001"], "control_ids": ["ISO27001:2022/4.1"]}, "lifecycle": {"created_at": "2025-01-15T12:00:00Z", "updated_at": "2025-01-15T12:00:00Z", "review_cycle_days": 180, "owners": ["content-team"]}, "security": {"confidentiality": "internal", "pii": false}, "provenance": {"source": "human", "confidence": 0.95}, "ui": {"cards_hint": ["ISMS foundation setup", "Risk register & SoA"], "actions": [{"type": "start_workflow", "target": "program_readiness", "label": "Start ISMS Program", "params": {"packs": ["ISO27001:2022"], "scope_boundary": "Organization-wide ISMS", "owner_role": "CISO", "target_date": "2025-12-31"}}, {"type": "open_register", "target": "risk", "label": "Open Risk Register"}]}, "packs": ["ISO27001:2022"], "primary_ids": ["ISO27001:2022/4.1", "ISO27001:2022/A.5"], "overlap_ids": ["ISO27002:2022/1.1", "ISO27701:2019/5.1"], "capability_tags": ["NL-Portal", "Register", "Draft Doc", "Workflow", "Dashboard"], "flags": [], "sources": [{"title": "ISO/IEC 27001:2022 — Context of the Organization", "id": "ISO27001:2022/4.1", "locator": "Clause 4.1"}, {"title": "ISO/IEC 27001:2022 — Annex A Controls", "id": "ISO27001:2022/A.5", "locator": "Annex A.5"}], "vector_db_metadata": {"chromadb_collection": "ariocomply-qa-dev", "embedding_model": "text-embedding-3-large", "chunk_strategy": "semantic"}}