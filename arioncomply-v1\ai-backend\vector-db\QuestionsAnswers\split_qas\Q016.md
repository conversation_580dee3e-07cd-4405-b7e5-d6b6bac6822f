id: Q016
query: >-
  We only operate in one country — why are people talking about international regulations?
packs:
  - "GDPR:2016"
primary_ids:
  - "GDPR:2016/Art.3"
overlap_ids:
  - "GDPR:2016/Art.44"
  - "GDPR:2016/Art.49"
capability_tags:
  - "Register"
  - "Dashboard"
flags: []
sources:
  - title: "GDPR (Regulation 2016/679) — Territorial Scope"
    id: "GDPR:2016/Art.3"
    locator: "Article 3"
  - title: "GDPR (Regulation 2016/679) — Transfer Mechanisms"
    id: "GDPR:2016/Art.44"
    locator: "Article 44"
  - title: "GDPR (Regulation 2016/679) — Derogations"
    id: "GDPR:2016/Art.49"
    locator: "Article 49"
ui:
  cards_hint:
    - "Cross-border data map"
    - "International obligations"
  actions:
    - type: "open_register"
      target: "data_transfer_register"
      label: "View Transfer Mechanisms"
output_mode: "both"
graph_required: false
notes: "Data flows—not HQ location—drive obligations"
---
### 16) We only operate in one country — why are people talking about international regulations?

**Standard terms)**
- **Territorial scope (GDPR Article 3):** applies based on subject location.
- **Transfers (GDPR Articles 44–49):** rules for moving data across borders.

**Plain-English answer**
Even if you’re local, your **customers**, **vendors**, or **cloud regions** might be international. If you **target** EU individuals or your data **leaves** the EU, GDPR applies. Contracts often bring in other jurisdictions too.

**Applies to**
- **Primary:** GDPR Article 3; GDPR Article 44; GDPR Article 49

**Why it matters**
Hidden cross-border flows create duties—notice, transfer mechanisms, and records.

**Do next in our platform**
- Map **where** data is stored/processed.
- Check **GDPR applicability** & transfer needs.
- Add transfer mechanisms to your **register**.

**How our platform will help**
- **[Register]** Transfer mechanism catalog.
- **[Dashboard]** Data-flow visualization.

**Likely follow-ups**
- “Do we need SCCs?” (See Q161–Q164)

**Sources**
- GDPR Articles 3, 44, 49
