"""
File: arioncomply-v1/ai-backend/python-backend/services/retrieval/vector_profiles.py
File Description: Vector profiles configuration manager for dual-vector architecture with intent-based routing
Purpose: Parse VECTOR_PROFILES env config and provide routing logic for global/org retrieval blending with weight calculation
Inputs: Environment variables (VECTOR_PROFILES, VECTOR_ROUTING_WEIGHTS, VECTOR_SEARCH_STRATEGY, VECTOR_CONFIDENCE_THRESHOLD)
Outputs: VectorProfile objects, RoutingWeights, search coordination, active profile lists, result weight calculations
Dependencies: json, os, logging, typing, dataclasses, enum (standard library modules for configuration parsing)
Security/RLS: Manages both public (global) and private (org-scoped) vector access with proper data separation and tenant isolation
Notes: Enables parallel global+org search with intelligent result fusion, citation marking, and intent-based routing weights for 7 compliance categories
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class VectorBackend(str, Enum):
    """Supported vector database backends."""
    CHROMADB = "chromadb"
    SUPABASE_VECTOR = "supabase_vector"


class SearchStrategy(str, Enum):
    """Vector search strategies for blending results."""
    PARALLEL_MERGE = "parallel_merge"
    CASCADE_FALLBACK = "cascade_fallback"
    ORG_FIRST = "org_first"
    GLOBAL_FIRST = "global_first"


@dataclass
class VectorProfile:
    """Configuration profile for a vector database backend."""
    name: str
    backend: VectorBackend
    url: str
    key: Optional[str]
    dimension: int
    default_top_k: int
    default_min_score: float
    collections: List[str]
    description: str


@dataclass
class RoutingWeights:
    """Routing weights for intent-based result blending."""
    global_weight: float
    org_weight: float

    def __post_init__(self):
        # Normalize weights to sum to 1.0
        total = self.global_weight + self.org_weight
        if total > 0:
            self.global_weight /= total
            self.org_weight /= total


class VectorProfilesConfig:
    """Configuration manager for dual-vector architecture."""

    def __init__(self):
        """Initialize vector profiles from environment variables.

        Loads configuration for the dual-vector architecture from environment variables.
        Sets up profiles for both global (public) and org (private) vector stores.
        """
        # Initialize empty containers for configuration data
        self.profiles: Dict[str, VectorProfile] = {}         # Available vector store profiles
        self.routing_weights: Dict[str, RoutingWeights] = {} # Intent-based routing weights
        self.search_strategy: SearchStrategy = SearchStrategy.PARALLEL_MERGE  # Default strategy
        self.confidence_threshold: float = 0.75             # Confidence threshold for results

        # Load all configuration from environment variables
        self._load_profiles()        # Load VECTOR_PROFILES config
        self._load_routing_weights() # Load VECTOR_ROUTING_WEIGHTS config
        self._load_search_config()   # Load search strategy and thresholds

    def _load_profiles(self):
        """Load vector profiles from VECTOR_PROFILES environment variable."""
        try:
            profiles_json = os.getenv("VECTOR_PROFILES", "{}")

            # Handle environment variable substitution in JSON (${VAR} -> actual value)
            # This allows secure injection of API keys and URLs without hardcoding
            profiles_json = self._substitute_env_vars(profiles_json)

            # Parse the JSON configuration
            profiles_data = json.loads(profiles_json)

            # Create VectorProfile objects for each configured profile
            for profile_name, profile_config in profiles_data.items():
                profile = VectorProfile(
                    name=profile_name,                                                    # "global" or "org"
                    backend=VectorBackend(profile_config.get("backend", "chromadb")),     # chromadb or supabase_vector
                    url=profile_config.get("url", "local"),                             # Connection URL
                    key=profile_config.get("key"),                                       # API key (optional)
                    dimension=profile_config.get("dimension", 1024),                    # Embedding dimension
                    default_top_k=profile_config.get("defaultTopK", 5),                # Default result count
                    default_min_score=profile_config.get("defaultMinScore", 0.7),       # Minimum similarity
                    collections=profile_config.get("collections", []),                 # Available collections
                    description=profile_config.get("description", "")                   # Human description
                )

                self.profiles[profile_name] = profile
                logger.info(f"Loaded vector profile: {profile_name} ({profile.backend})")

        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.warning(f"Failed to load vector profiles: {e}")
            # Create default profiles if loading fails
            self._create_default_profiles()

    def _load_routing_weights(self):
        """Load routing weights from VECTOR_ROUTING_WEIGHTS environment variable."""
        try:
            weights_json = os.getenv("VECTOR_ROUTING_WEIGHTS", "{}")
            weights_data = json.loads(weights_json)

            for intent_category, weights in weights_data.items():
                global_weight = weights.get("global", 0.5)
                org_weight = weights.get("org", 0.5)

                self.routing_weights[intent_category] = RoutingWeights(
                    global_weight=global_weight,
                    org_weight=org_weight
                )

            logger.info(f"Loaded routing weights for {len(self.routing_weights)} intent categories")

        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.warning(f"Failed to load routing weights: {e}")
            # Create default routing weights
            self._create_default_routing_weights()

    def _load_search_config(self):
        """Load search strategy and configuration."""
        try:
            strategy_str = os.getenv("VECTOR_SEARCH_STRATEGY", "parallel_merge")
            self.search_strategy = SearchStrategy(strategy_str)

            self.confidence_threshold = float(os.getenv("VECTOR_CONFIDENCE_THRESHOLD", "0.75"))

            logger.info(f"Vector search strategy: {self.search_strategy}, confidence threshold: {self.confidence_threshold}")

        except ValueError as e:
            logger.warning(f"Invalid search configuration: {e}")
            self.search_strategy = SearchStrategy.PARALLEL_MERGE
            self.confidence_threshold = 0.75

    def _substitute_env_vars(self, json_str: str) -> str:
        """Substitute ${VAR} environment variable references in JSON string."""
        import re

        def replace_env_var(match):
            var_name = match.group(1)
            return os.getenv(var_name, match.group(0))  # Return original if env var not found

        return re.sub(r'\$\{([^}]+)\}', replace_env_var, json_str)

    def _create_default_profiles(self):
        """Create default vector profiles when environment loading fails."""
        # Default global profile (ChromaDB)
        self.profiles["global"] = VectorProfile(
            name="global",
            backend=VectorBackend.CHROMADB,
            url="local",
            key=None,
            dimension=1024,
            default_top_k=5,
            default_min_score=0.75,
            collections=["standards_knowledge", "assessment_frameworks"],
            description="Public standards and regulatory knowledge"
        )

        # Default org profile (Supabase Vector)
        self.profiles["org"] = VectorProfile(
            name="org",
            backend=VectorBackend.SUPABASE_VECTOR,
            url=os.getenv("VECTOR_SUPABASE_URL", ""),
            key=os.getenv("VECTOR_SUPABASE_SERVICE_KEY"),
            dimension=1024,
            default_top_k=5,
            default_min_score=0.70,
            collections=["org_policies", "org_procedures"],
            description="Organization-specific policies and procedures"
        )

        logger.info("Created default vector profiles")

    def _create_default_routing_weights(self):
        """Create default routing weights for intent categories."""
        default_weights = {
            "standards_inquiry": RoutingWeights(0.8, 0.2),
            "policy_inquiry": RoutingWeights(0.3, 0.7),
            "assessment_inquiry": RoutingWeights(0.6, 0.4),
            "security_inquiry": RoutingWeights(0.4, 0.6),
            "compliance_inquiry": RoutingWeights(0.7, 0.3),
            "implementation_guidance": RoutingWeights(0.6, 0.4),
            "general_inquiry": RoutingWeights(0.5, 0.5)
        }

        self.routing_weights.update(default_weights)
        logger.info("Created default routing weights")

    def get_profile(self, profile_name: str) -> Optional[VectorProfile]:
        """Get vector profile by name."""
        return self.profiles.get(profile_name)

    def get_global_profile(self) -> Optional[VectorProfile]:
        """Get the global (public standards) vector profile."""
        return self.get_profile("global")

    def get_org_profile(self) -> Optional[VectorProfile]:
        """Get the organization-specific vector profile."""
        return self.get_profile("org")

    def get_routing_weights(self, intent_category: str) -> RoutingWeights:
        """Get routing weights for the specified intent category."""
        return self.routing_weights.get(intent_category, RoutingWeights(0.5, 0.5))

    def get_active_profiles(self) -> List[VectorProfile]:
        """Get list of all active vector profiles."""
        active = []

        for profile in self.profiles.values():
            # Check if profile is properly configured
            if profile.backend == VectorBackend.CHROMADB:
                # ChromaDB is always available if enabled
                if os.getenv("CHROMA_ENABLED", "false").lower() == "true":
                    active.append(profile)
            elif profile.backend == VectorBackend.SUPABASE_VECTOR:
                # Supabase Vector needs URL and key
                if profile.url and profile.key:
                    active.append(profile)

        return active

    def should_query_profile(self, profile: VectorProfile, intent_category: str) -> bool:
        """Determine if a profile should be queried based on intent and configuration."""
        if profile.name == "global":
            # Query global profile for standards/regulatory questions
            return intent_category in ["standards_inquiry", "compliance_inquiry", "implementation_guidance", "general_inquiry"]
        elif profile.name == "org":
            # Query org profile for policy/assessment questions
            return intent_category in ["policy_inquiry", "assessment_inquiry", "security_inquiry", "general_inquiry"]

        return True  # Default: query all profiles

    def calculate_result_weights(
        self,
        intent_category: str,
        global_results: List[Dict[str, Any]],
        org_results: List[Dict[str, Any]]
    ) -> Tuple[float, float]:
        """Calculate result weights for blending global and org results."""
        routing = self.get_routing_weights(intent_category)

        # Adjust weights based on result availability
        if not global_results:
            return 0.0, 1.0
        elif not org_results:
            return 1.0, 0.0
        else:
            return routing.global_weight, routing.org_weight


# Global configuration instance
vector_profiles = VectorProfilesConfig()