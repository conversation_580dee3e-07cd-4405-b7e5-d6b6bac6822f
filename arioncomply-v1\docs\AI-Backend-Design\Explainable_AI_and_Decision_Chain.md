# Explainable AI and Decision Chain (Design)

Overview
- Goal: Produce auditable, explainable outcomes with evidence provenance, deterministic rationale, confidence, and HITL hooks.
- Applies across UI → Edge → Backend → DB/Vector/Storage; complements Traceability and Transparency contracts.

Three-Layer Tracing
- Layer 1 – Infrastructure (W3C Trace Context): `traceparent` for service correlation and performance.
- Layer 2 – Compliance Audit Chain: `compliance-chain-id` identifying the compliance topic or control (e.g., ctrl-27001-A121) and the chain of decisions.
- Layer 3 – Explainable AI Context: `decision-context` capturing evidence IDs, applied rules, model call metadata, and confidence.

Headers (ingress/egress)
- `traceparent`: W3C trace header (UI → Edge → Backend → UI).
- `compliance-chain-id`: human/readable control or decision chain key; backend may refine/generate one.
- `decision-context`: optional JSON; UI may provide initial context; backend enriches (do not trust UI blindly).
- Edge forwards both `compliance-chain-id` and `decision-context` to Backend for enrichment.

Event Taxonomy (api_event_logs.event_type)
- `compliance_chain_started`: start of a decision chain; details: { chainId, requirementId?, controlId?, standard? }.
- `evidence_retrieval`: details: { citations: [{ id, type, score, version, ts }], backend, topK, latencyMs }.
- `deterministic_rules_applied`: details: { rules: [{ id, version, outcome, reason }], coverage: %, durationMs }.
- `ai_call`: details: { provider, model, anonymized, inputTokens, outputTokens, costUsd, latencyMs, promptTemplate: { id, version }, promptHash }.
- `decision_composed`: details: { reasoning, confidence: { value, components }, citations: [ids], requiresHumanReview: boolean }.
- `human_review_required` | `human_review_performed`: details: { proposalId, reviewerId, status }.
- `response_sent`: include chainId and sessionId.

DB Model Additions (Pilot target)
- `process_runs` (see Traceability_IDs_and_DB_Model.md): store `process_id`, `kind`, `status`, and attach `compliance_chain_id` in details.
- Evidence provenance: ensure document registry records `version`, `hash`, `ingested_at`; citations reference these.
- Rules registry: `rules(id, version, standard, control_id, expression, rationale, active)`; link to applied rules events by id/version.

Confidence Strategy
- Composite score with components and weights (stored per org):
  - Retrieval confidence (coverage, citation scores)
  - Rules outcome confidence (rule coverage, contradictions)
  - Model confidence (if available: logprobs/hallucination detector) – optional
- Persist components and final value in `decision_composed.details.confidence`.

Explainability Output
- UI Explain panel shows:
  - Evidence: sources with version/timestamp
  - Rules: which fired/failed and why
  - Model: provider/model, template id, anonymized flag, prompt hash
  - Confidence: final score and components
  - Human override: required? status and approver

Phase Plan
- MVP-Assessment: Log `compliance_chain_started` (if control known), `evidence_retrieval` (citations), `ai_call` (provider/model/promptHash), `response_sent`. Confidence minimal (retrieval-based). HITL flag only.
- MVP-Demo-Light: Add `deterministic_rules_applied` + composite confidence; show Explain panel in UI; human_review_required event.
- MVP-Pilot: Introduce rules registry and process ledger; persist `decision_composed` with full rationale and citations; export APIs.
- Production: Append-only logs, checksum of decision summaries, dashboards on confidence and review rates.

Security/Privacy
- Never log raw prompts or full evidence text; store IDs, hashes, versions.
- Anonymization only on GLLM path; record anonymized=true in ai_call.

Open Questions
- Canon for `compliance-chain-id` (naming and scoping) per standard/framework.
- Prompt hashing algorithm and salt policy.
- Confidence weighting defaults per org.

