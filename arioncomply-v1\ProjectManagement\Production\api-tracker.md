<!-- File: arioncomply-v1/ProjectManagement/Production/api-tracker.md -->
# ArionComply API Technical Task Tracker

**Document Version:** 1.0  
**Date:** August 27, 2025  
**Status:** Active  
**Reference Documents:** 
- arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
- arioncomply-v1/docs/functional-definitions/integration/README.md
- arioncomply-v1/docs/Detailed-Plan.md

---

## MVP-Assessment-App Priority Scope (API)

- [ ] Conversation API: start/send/stream with logging and camelCase envelopes. Edge returns `traceparent`; logs `response_sent` and `stream_finished`.
- [ ] Assessment API (MVP): create session, accept answers, compute/get score and progress; return data for heatmap.

### Audit & Logging (MVP-Assessment-App)
- [ ] Implement org-scoped audit log read endpoints (filters: route, status, event_type; range; pagination) — Dependencies: DB 0011 + RLS tightened
- [ ] Document envelopes and OpenAPI for audit endpoints; add sample queries in docs

## MVP-Demo-Light-App Priority Scope (API)

- [ ] Document API (MVP): `documents.generate/get` for SoA/policy using demo templates.
- [ ] Scenario API: reset/init endpoints for demo scenarios.
- [ ] Registration endpoints: business email validation, minimal org profile.
- [ ] Optional: Audit Trail read (org-scoped) for basic log viewing during tests. Include fields to display `traceId`, `sessionId`, and `event_type`.
- [ ] ListView Data API (MVP): metadata-driven list retrieval aligned to registry (filters/sort/pagination).
- [ ] Form submit (MVP): schema-driven create/update with runtime validation.
- [ ] FieldMapper adoption: ensure casing conversion honors explicit overrides across APIs.
- [ ] Runtime validation (Zod/JSON Schema) for MVP endpoints (ListView/Form/Documents/Scenarios) and OpenAPI updates.

## MVP-Pilot Scope (API)

- [ ] Registers APIs: assets, risks, processing activities; evidence linking.
- [ ] Planning/Task APIs: create plans and tasks to maintain certification and proofs.
- [ ] Document set generation for certification prep (policies, procedures, SoA, plans).

## Production Scope (Later Phases)

- [ ] Full Organization/User/Team APIs, feature flags, billing integration.
- [ ] Knowledge base, suggestion APIs, dashboard metrics breadth, webhook/integration APIs.

## Testing Readiness Deployment Checklist (API Layer)

- [ ] Confirm DB migrations 0001–0011 are applied and RLS verified (see database-tracker).
- [ ] Deploy conversation endpoints to Supabase Edge (`conversation-start`, `conversation-send`, `conversation-stream`).
- [ ] Ensure standardized envelopes and camelCase I/O are returned (see `_shared/errors.ts`, `_shared/utils.ts`).
- [ ] Verify logging: `api_request_logs` entries on request start/end and `api_event_logs` for internal/outbound events.
- [ ] Record final function URLs and auth headers for the testing harness configuration.

---

## 1. Core API Infrastructure

### 1.1 API Gateway
- [ ] **Task:** Implement API gateway
  - **Dependencies:** None (foundation component)
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] Request routing
    - [ ] Authentication
    - [ ] Rate limiting
    - [ ] Request validation
    - [ ] Response formatting

### 1.2 Authentication and Authorization
- [ ] **Task:** Implement authentication and authorization
  - **Dependencies:** API Gateway
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] API key management
    - [ ] JWT validation
    - [ ] OAuth integration
    - [ ] Role-based access control
    - [ ] Permission validation

### 1.3 API Documentation
- [ ] **Task:** Implement API documentation
  - **Dependencies:** API Gateway
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] OpenAPI specification
    - [ ] Endpoint documentation
    - [ ] Schema definitions
    - [ ] Authentication guide
    - [ ] Interactive examples

---

## 2. Organization and User Management API

### 2.1 Organization API
- [ ] **Task:** Implement organization API
  - **Dependencies:** Authentication and Authorization
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] Organization CRUD
    - [ ] Settings management
    - [ ] Subscription handling (link to Subscription API)
    - [ ] Usage tracking
    - [ ] Domain verification (business email validation)
    - [ ] Registration flow (multi-step)
    - [ ] Organization profile creation (industry, size, location)

### 2.2 User Management API
- [ ] **Task:** Implement user management API
  - **Dependencies:** Organization API
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] User CRUD
    - [ ] Role assignment
    - [ ] Permission management
    - [ ] Profile handling
    - [ ] Status management

### 2.3 Team Management API
- [ ] **Task:** Implement team management API
  - **Dependencies:** User Management API
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] Team CRUD
    - [ ] Member management
    - [ ] Role assignment
    - [ ] Permission inheritance
    - [ ] Team settings

---

## 3. Compliance Management API

### 3.1 Framework Management API
- [ ] **Task:** Implement framework management API
  - **Dependencies:** Authentication and Authorization
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] Framework retrieval
    - [ ] Framework selection
    - [ ] Version management
    - [ ] Customization handling
    - [ ] Cross-mapping access
    - [ ] Control relationship visualization data
    - [ ] Multi-framework overlap mapping

### 3.2 Assessment API
- [ ] **Task:** Implement assessment API
  - **Dependencies:** Framework Management API
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] Assessment CRUD
    - [ ] Question handling
    - [ ] Response management
    - [ ] Gap analysis
    - [ ] Score calculation
    - [ ] Demo scenario bootstrap/reset endpoints

### 3.3 Evidence Management API
- [ ] **Task:** Implement evidence management API
  - **Dependencies:** Assessment API
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] Evidence CRUD
    - [ ] Metadata management
    - [ ] Control mapping
    - [ ] Version control
    - [ ] Expiration handling

---

## 4. Document Management API

### 4.1 Document API
- [ ] **Task:** Implement document API
  - **Dependencies:** Authentication and Authorization
  - **Verification Document:** arioncomply-v1/docs/Document_Management_System.md
  - **Components:**
    - [ ] Document CRUD
    - [ ] Metadata management
    - [ ] Version control
    - [ ] Search functionality
    - [ ] Access control

### 4.2 Template API
- [ ] **Task:** Implement template API
  - **Dependencies:** Document API
  - **Verification Document:** arioncomply-v1/docs/Document_Management_System.md
  - **Components:**
    - [ ] Template CRUD
    - [ ] Variable management
    - [ ] Framework mapping
    - [ ] Category organization
    - [ ] Version control

### 4.3 Generation API
- [ ] **Task:** Implement generation API
  - **Dependencies:** Template API
  - **Verification Document:** arioncomply-v1/docs/Document_Management_System.md
  - **Components:**
    - [ ] Generation request
    - [ ] Parameter handling
    - [ ] Output format selection
    - [ ] Status tracking
    - [ ] Result retrieval

---

## 5. Workflow and Task API

### 5.1 Workflow API
- [ ] **Task:** Implement workflow API
  - **Dependencies:** Authentication and Authorization
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/EventWorkflow Design/Workflows/CustomerWorkflows/integrated-planning-workflow.md
  - **Components:**
    - [ ] Workflow definition CRUD
    - [ ] Workflow instance CRUD
    - [ ] Stage management
    - [ ] Transition handling
    - [ ] State persistence

### 5.2 Task API
- [ ] **Task:** Implement task API
  - **Dependencies:** Workflow API
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/EventWorkflow Design/Workflows/CustomerWorkflows/integrated-planning-workflow.md
  - **Components:**
    - [ ] Task CRUD
    - [ ] Assignment management
    - [ ] Status updates
    - [ ] Dependency handling
    - [ ] Due date management

### 5.3 Planning API
- [ ] **Task:** Implement planning API
  - **Dependencies:** Task API
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/EventWorkflow Design/Workflows/CustomerWorkflows/integrated-planning-workflow.md
  - **Components:**
    - [ ] Timeline management
    - [ ] Calendar integration
    - [ ] Gantt chart data
    - [ ] Resource allocation
    - [ ] Progress tracking

---

## 6. AI and Knowledge API

### 6.1 Conversation API
- [ ] **Task:** Implement conversation API
  - **Dependencies:** Authentication and Authorization
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Query submission
    - [ ] Response handling
    - [ ] Context management
    - [ ] Card/prose format
    - [ ] Action integration
    - [ ] Streaming (SSE/WebSocket) for real-time responses
    - [ ] Suggestion chips (common follow-ups)

### 6.2 Knowledge Base API
- [ ] **Task:** Implement knowledge base API
  - **Dependencies:** Conversation API
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Content retrieval
    - [ ] Citation access
    - [ ] Evidence linking
    - [ ] Framework mapping
    - [ ] Search functionality

### 6.3 Suggestion API
- [ ] **Task:** Implement suggestion API
  - **Dependencies:** Knowledge Base API
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Next action suggestions
    - [ ] Gap identification
    - [ ] Priority recommendations
    - [ ] Control mapping suggestions
    - [ ] Efficiency improvements

---

## 7. Analytics and Reporting API

### 7.1 Dashboard API
- [ ] **Task:** Implement dashboard API
  - **Dependencies:** Authentication and Authorization
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] Metrics retrieval
    - [ ] Visualization data
    - [ ] Aggregation functions
    - [ ] Timeline data
    - [ ] Status summaries
    - [ ] Heatmap data (compliance by domain)

### 7.2 Reports API
- [ ] **Task:** Implement reports API
  - **Dependencies:** Dashboard API
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/API Design/arioncomply-metadata-api.js
  - **Components:**
    - [ ] Report definition
    - [ ] Report generation
    - [ ] Parameter handling
    - [ ] Format selection
    - [ ] Scheduling

### 7.3 Audit Trail API
- [ ] **Task:** Implement audit trail API
  - **Dependencies:** Authentication and Authorization
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/application_schema_design.md
  - **Components:**
    - [ ] Audit record retrieval
    - [ ] Filtering capabilities
    - [ ] Chronological sorting
    - [ ] Export functionality
    - [ ] Evidence packaging

---

## 8. Integration API

### 8.1 External System API
- [ ] **Task:** Implement external system API
  - **Dependencies:** Authentication and Authorization
  - **Verification Document:** arioncomply-v1/docs/functional-definitions/integration/README.md
  - **Components:**
    - [ ] Connection management
    - [ ] Authentication handling
    - [ ] Data mapping
    - [ ] Synchronization
    - [ ] Status monitoring

### 8.2 Webhook API
- [ ] **Task:** Implement webhook API
  - **Dependencies:** Authentication and Authorization
  - **Verification Document:** arioncomply-v1/docs/functional-definitions/integration/README.md
  - **Components:**
    - [ ] Webhook registration
    - [ ] Event subscription
    - [ ] Payload formatting
    - [ ] Delivery management
    - [ ] Retry handling

### 8.3 Import/Export API
- [ ] **Task:** Implement import/export API
  - **Dependencies:** Authentication and Authorization
  - **Verification Document:** arioncomply-v1/docs/functional-definitions/integration/README.md
  - **Components:**
    - [ ] Data export
    - [ ] Data import
    - [ ] Format conversion
    - [ ] Validation
    - [ ] Error handling

---

## 9. Subscription and Billing API

### 9.1 Subscription API
- [ ] **Task:** Implement subscription management
  - **Dependencies:** Organization API, Authentication and Authorization
  - **Verification Documents:**
    - arioncomply-v1/docs/ArionComplyDesign/SubscriptionManagement/unified_subscription_schema.md
    - arioncomply-v1/docs/ArionComplyDesign/SubscriptionManagement/subscription_edge_functions.md
  - **Components:**
    - [ ] Tier definitions and entitlements
    - [ ] Seat management
    - [ ] Demo → paid conversion workflow
    - [ ] Feature flag exposure for UI

### 9.2 Entitlements / Feature Flags API
- [ ] **Task:** Implement feature flagging
  - **Dependencies:** Subscription API
  - **Verification Documents:**
    - arioncomply-v1/docs/ArionComplyDesign/SubscriptionManagement/metadata_implementation.md
  - **Components:**
    - [ ] Entitlement resolution per user/org
    - [ ] UI gating helpers
    - [ ] Edge enforcement middleware
    - [ ] Premium feature indicators

### 9.3 Billing API (Foundation)
- [ ] **Task:** Implement billing integration hooks
  - **Dependencies:** Subscription API
  - **Components:**
    - [ ] Payment method management
    - [ ] Billing cycle and invoices
    - [ ] Webhook ingestion (payments, status)
    - [ ] Separation of demo vs billable activities
