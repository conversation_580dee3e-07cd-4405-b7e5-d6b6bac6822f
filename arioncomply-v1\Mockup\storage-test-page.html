<!-- File: arioncomply-v1/Mockup/storage-test-page.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArionComply Storage Manager Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #2c3e50;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 8px;
            text-align: center;
        }

        .header h1 {
            margin-bottom: 10px;
        }

        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }

        .status.ready { background: #2ecc71; color: white; }
        .status.loading { background: #f39c12; color: white; }
        .status.error { background: #e74c3c; color: white; }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .test-section h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .test-group {
            margin-bottom: 20px;
        }

        .test-group h3 {
            color: #34495e;
            margin-bottom: 10px;
        }

        .button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: background 0.3s;
        }

        .button:hover {
            background: #2980b9;
        }

        .button.success {
            background: #2ecc71;
        }

        .button.warning {
            background: #f39c12;
        }

        .button.danger {
            background: #e74c3c;
        }

        .button.secondary {
            background: #95a5a6;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .input-group input,
        .input-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .input-group textarea {
            height: 100px;
            resize: vertical;
        }

        .results {
            background: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 5px;
            border-radius: 3px;
        }

        .log-entry.success {
            background: #d5f4e6;
            color: #27ae60;
        }

        .log-entry.error {
            background: #fadbd8;
            color: #e74c3c;
        }

        .log-entry.info {
            background: #d6eaf8;
            color: #3498db;
        }

        .log-entry.warning {
            background: #fdeaa7;
            color: #f39c12;
        }

        .info-panel {
            background: #e8f4f8;
            border: 1px solid #3498db;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .info-panel h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .info-item strong {
            color: #2c3e50;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: #3498db;
            transition: width 0.3s ease;
        }

        .test-results {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }

        .test-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            border-radius: 6px;
            background: #f8f9fa;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            font-size: 12px;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .migration-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .migration-section h3 {
            color: #8b6914;
            margin-bottom: 10px;
        }

        .clear-section {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
        }

        .clear-section h3 {
            color: #721c24;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>ArionComply Storage Manager Test Suite</h1>
            <p>Comprehensive testing for IndexedDB storage with localStorage fallback</p>
            <div class="status loading" id="statusIndicator">Loading...</div>
        </div>

        <!-- Storage Information Panel -->
        <div class="info-panel" id="storageInfo">
            <h3>Storage Information</h3>
            <div id="storageDetails">Loading storage details...</div>
        </div>

        <!-- Test Grid -->
        <div class="test-grid">
            <!-- Basic Operations Test -->
            <div class="test-section">
                <h2>Basic Operations Test</h2>
                
                <div class="test-group">
                    <h3>Data Storage & Retrieval</h3>
                    <div class="input-group">
                        <label for="testKey">Test Key:</label>
                        <input type="text" id="testKey" value="test_key_001" placeholder="Enter storage key">
                    </div>
                    <div class="input-group">
                        <label for="testData">Test Data (JSON):</label>
                        <textarea id="testData" placeholder='{"name": "Test User", "role": "admin"}'></textarea>
                    </div>
                    <button class="button" onclick="testSaveData()">Save Data</button>
                    <button class="button secondary" onclick="testGetData()">Get Data</button>
                    <button class="button danger" onclick="testDeleteData()">Delete Data</button>
                </div>

                <div class="test-group">
                    <h3>ID Generation</h3>
                    <div class="input-group">
                        <label for="idPrefix">ID Prefix:</label>
                        <input type="text" id="idPrefix" value="USER" placeholder="Enter prefix">
                    </div>
                    <button class="button" onclick="testGenerateId()">Generate ID</button>
                    <button class="button secondary" onclick="testMultipleIds()">Generate 5 IDs</button>
                </div>

                <div class="results" id="basicResults"></div>
            </div>

            <!-- Compatibility Test -->
            <div class="test-section">
                <h2>Compatibility Test</h2>
                
                <div class="test-group">
                    <h3>ArionComply Functions</h3>
                    <button class="button" onclick="testUserOperations()">Test User Operations</button>
                    <button class="button" onclick="testCompanyOperations()">Test Company Operations</button>
                    <button class="button" onclick="testSessionOperations()">Test Session Operations</button>
                </div>

                <div class="test-group">
                    <h3>Wizard Data</h3>
                    <button class="button" onclick="testWizardData()">Test Wizard Storage</button>
                    <button class="button" onclick="testSettingsData()">Test Settings Storage</button>
                </div>

                <div class="test-group">
                    <h3>Bulk Operations</h3>
                    <button class="button warning" onclick="testBulkOperations()">Test Bulk Save/Load</button>
                    <button class="button warning" onclick="testPerformance()">Performance Test</button>
                </div>

                <div class="results" id="compatibilityResults"></div>
            </div>

            <!-- Migration Test -->
            <div class="test-section">
                <h2>Migration Test</h2>
                
                <div class="migration-section">
                    <h3>⚠️ Data Migration Testing</h3>
                    <p>This section tests migration from localStorage to IndexedDB</p>
                </div>

                <div class="test-group">
                    <h3>Setup Test Data</h3>
                    <button class="button" onclick="createTestLocalStorageData()">Create Test localStorage Data</button>
                    <button class="button secondary" onclick="checkLocalStorageData()">Check localStorage Data</button>
                </div>

                <div class="test-group">
                    <h3>Migration Actions</h3>
                    <button class="button warning" onclick="testMigration()">Test Migration Process</button>
                    <button class="button" onclick="verifyMigration()">Verify Migration</button>
                </div>

                <div class="results" id="migrationResults"></div>
            </div>

            <!-- Stress Test -->
            <div class="test-section">
                <h2>Stress Test</h2>
                
                <div class="test-group">
                    <h3>Large Data Test</h3>
                    <button class="button warning" onclick="testLargeData()">Test Large Objects</button>
                    <button class="button warning" onclick="testManyRecords()">Test Many Records</button>
                </div>

                <div class="test-group">
                    <h3>Error Handling</h3>
                    <button class="button danger" onclick="testErrorHandling()">Test Error Scenarios</button>
                    <button class="button danger" onclick="testCorruptData()">Test Corrupt Data</button>
                </div>

                <div class="test-group">
                    <h3>Fallback Test</h3>
                    <button class="button secondary" onclick="testFallback()">Test Fallback Mode</button>
                    <button class="button secondary" onclick="testRecovery()">Test Recovery</button>
                </div>

                <div class="results" id="stressResults"></div>
            </div>
        </div>

        <!-- Test Statistics -->
        <div class="test-results">
            <h2>Test Statistics</h2>
            <div class="test-stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalTests">0</div>
                    <div class="stat-label">Total Tests</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="passedTests">0</div>
                    <div class="stat-label">Passed</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="failedTests">0</div>
                    <div class="stat-label">Failed</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="avgTime">0ms</div>
                    <div class="stat-label">Avg Time</div>
                </div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
        </div>

        <!-- Clear Data Section -->
        <div class="clear-section">
            <h3>⚠️ Data Management</h3>
            <p>Use these functions to clear test data and reset the storage system</p>
            <button class="button danger" onclick="clearAllData()">Clear All Data</button>
            <button class="button secondary" onclick="clearTestData()">Clear Test Data Only</button>
            <button class="button" onclick="resetTests()">Reset Test Results</button>
        </div>
    </div>

    <!-- Include the storage manager -->
    <script src="localStorageManager.js"></script>
    
    <script>
        // =============================================================================
        // TEST SUITE GLOBALS
        // =============================================================================
        
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0,
            times: []
        };

        // =============================================================================
        // INITIALIZATION
        // =============================================================================

        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🧪 Starting Storage Manager Test Suite...');
            
            try {
                // Wait for storage manager to initialize
                await new Promise(resolve => {
                    const checkReady = () => {
                        if (window.StorageManager && window.StorageManager.isReady()) {
                            resolve();
                        } else {
                            setTimeout(checkReady, 100);
                        }
                    };
                    checkReady();
                });

                updateStatusIndicator('ready', 'Storage Manager Ready');
                await updateStorageInfo();
                
                // Initialize test data
                document.getElementById('testData').value = JSON.stringify({
                    name: 'Test User',
                    role: 'admin',
                    timestamp: Date.now()
                }, null, 2);

                log('info', 'Test suite initialized successfully');
                
            } catch (error) {
                updateStatusIndicator('error', 'Initialization Failed');
                log('error', `Initialization error: ${error.message}`);
            }
        });

        // =============================================================================
        // UTILITY FUNCTIONS
        // =============================================================================

        function updateStatusIndicator(status, message) {
            const indicator = document.getElementById('statusIndicator');
            indicator.className = `status ${status}`;
            indicator.textContent = message;
        }

        async function updateStorageInfo() {
            try {
                const info = await window.StorageManager.getStorageInfo();
                const detailsDiv = document.getElementById('storageDetails');
                
                detailsDiv.innerHTML = `
                    <div class="info-item">
                        <span><strong>Storage Mode:</strong></span>
                        <span>${info.mode}</span>
                    </div>
                    <div class="info-item">
                        <span><strong>IndexedDB Available:</strong></span>
                        <span>${info.indexedDBAvailable ? '✅' : '❌'}</span>
                    </div>
                    <div class="info-item">
                        <span><strong>localStorage Available:</strong></span>
                        <span>${info.localStorageAvailable ? '✅' : '❌'}</span>
                    </div>
                    <div class="info-item">
                        <span><strong>Cache Size:</strong></span>
                        <span>${info.cacheSize} items</span>
                    </div>
                    <div class="info-item">
                        <span><strong>Database:</strong></span>
                        <span>${info.dbName} v${info.dbVersion}</span>
                    </div>
                    ${info.quota ? `
                    <div class="info-item">
                        <span><strong>Storage Used:</strong></span>
                        <span>${formatBytes(info.usage)} / ${formatBytes(info.quota)} (${info.usagePercentage.toFixed(1)}%)</span>
                    </div>
                    ` : ''}
                `;
            } catch (error) {
                log('error', `Failed to get storage info: ${error.message}`);
            }
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function log(type, message, targetId = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            
            console.log(`${type.toUpperCase()}: ${logEntry}`);
            
            if (targetId) {
                const resultsDiv = document.getElementById(targetId);
                if (resultsDiv) {
                    const entry = document.createElement('div');
                    entry.className = `log-entry ${type}`;
                    entry.textContent = logEntry;
                    resultsDiv.appendChild(entry);
                    resultsDiv.scrollTop = resultsDiv.scrollHeight;
                }
            }
        }

        async function runTest(testName, testFunction) {
            const startTime = Date.now();
            testStats.total++;
            
            try {
                await testFunction();
                testStats.passed++;
                const duration = Date.now() - startTime;
                testStats.times.push(duration);
                log('success', `✅ ${testName} - Passed (${duration}ms)`);
                return true;
            } catch (error) {
                testStats.failed++;
                log('error', `❌ ${testName} - Failed: ${error.message}`);
                return false;
            } finally {
                updateTestStats();
            }
        }

        function updateTestStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('passedTests').textContent = testStats.passed;
            document.getElementById('failedTests').textContent = testStats.failed;
            
            const avgTime = testStats.times.length > 0 ? 
                Math.round(testStats.times.reduce((a, b) => a + b, 0) / testStats.times.length) : 0;
            document.getElementById('avgTime').textContent = avgTime + 'ms';
            
            const percentage = testStats.total > 0 ? 
                (testStats.passed / testStats.total) * 100 : 0;
            document.getElementById('progressFill').style.width = percentage + '%';
        }

        // =============================================================================
        // BASIC OPERATIONS TESTS
        // =============================================================================

        async function testSaveData() {
            const key = document.getElementById('testKey').value;
            const dataStr = document.getElementById('testData').value;
            
            if (!key) {
                log('error', 'Please enter a test key', 'basicResults');
                return;
            }
            
            let data;
            try {
                data = JSON.parse(dataStr);
            } catch (error) {
                log('error', 'Invalid JSON data', 'basicResults');
                return;
            }
            
            await runTest('Save Data', async () => {
                const result = window.saveStoredData(key, data);
                
                // For IndexedDB mode, the function returns true immediately
                // but saves asynchronously. We need to wait a bit and then verify.
                if (window.StorageManager.getMode() === 'indexeddb') {
                    // Wait for async save to complete
                    await new Promise(resolve => setTimeout(resolve, 100));
                    
                    // Verify the data was actually saved by trying to retrieve it
                    const retrievedData = window.getStoredData(key);
                    if (!retrievedData || (Array.isArray(retrievedData) && retrievedData.length === 0)) {
                        // Data might not be in cache yet, try async version
                        const asyncData = await window.StorageManager.async.getStoredData(key);
                        if (!asyncData || (Array.isArray(asyncData) && asyncData.length === 0)) {
                            throw new Error('Data was not saved properly');
                        }
                    }
                } else {
                    // For localStorage and memory mode, result should be true
                    if (!result) {
                        throw new Error('Save operation returned false');
                    }
                }
                
                log('success', `Data saved successfully: ${key}`, 'basicResults');
            });
        }

        async function testGetData() {
            const key = document.getElementById('testKey').value;
            
            if (!key) {
                log('error', 'Please enter a test key', 'basicResults');
                return;
            }
            
            await runTest('Get Data', async () => {
                let result = window.getStoredData(key);
                
                // For IndexedDB mode, try async version if sync returns empty
                if (window.StorageManager.getMode() === 'indexeddb' && 
                    (!result || (Array.isArray(result) && result.length === 0))) {
                    result = await window.StorageManager.async.getStoredData(key);
                }
                
                if (!result || (Array.isArray(result) && result.length === 0)) {
                    throw new Error('No data found for key');
                }
                
                log('success', `Data retrieved: ${JSON.stringify(result)}`, 'basicResults');
            });
        }

        async function testDeleteData() {
            const key = document.getElementById('testKey').value;
            
            if (!key) {
                log('error', 'Please enter a test key', 'basicResults');
                return;
            }
            
            await runTest('Delete Data', async () => {
                const result = await window.deleteStoredData(key);
                if (!result) {
                    throw new Error('Delete operation returned false');
                }
                log('success', `Data deleted successfully: ${key}`, 'basicResults');
            });
        }

        async function testGenerateId() {
            const prefix = document.getElementById('idPrefix').value;
            
            if (!prefix) {
                log('error', 'Please enter an ID prefix', 'basicResults');
                return;
            }
            
            await runTest('Generate ID', async () => {
                const id = window.generateId(prefix);
                if (!id || !id.startsWith(prefix)) {
                    throw new Error('Generated ID is invalid');
                }
                log('success', `Generated ID: ${id}`, 'basicResults');
            });
        }

        async function testMultipleIds() {
            const prefix = document.getElementById('idPrefix').value;
            
            if (!prefix) {
                log('error', 'Please enter an ID prefix', 'basicResults');
                return;
            }
            
            await runTest('Multiple IDs', async () => {
                const ids = [];
                for (let i = 0; i < 5; i++) {
                    const id = window.generateId(prefix);
                    if (!id || !id.startsWith(prefix)) {
                        throw new Error(`Generated ID ${i + 1} is invalid`);
                    }
                    ids.push(id);
                }
                
                // Check for uniqueness
                const uniqueIds = new Set(ids);
                if (uniqueIds.size !== ids.length) {
                    throw new Error('Generated IDs are not unique');
                }
                
                log('success', `Generated 5 unique IDs: ${ids.join(', ')}`, 'basicResults');
            });
        }

        // =============================================================================
        // COMPATIBILITY TESTS
        // =============================================================================

        async function testUserOperations() {
            await runTest('User Operations', async () => {
                // Create test user
                const user = {
                    id: 'test-user-001',
                    name: 'Test User',
                    email: '<EMAIL>',
                    role: 'admin'
                };
                
                // Save user
                const saved = window.saveStoredData('arioncomply_users', [user]);
                
                // Wait a bit for async operations
                await new Promise(resolve => setTimeout(resolve, 100));
                
                // Retrieve users - try both sync and async
                let users = window.getStoredData('arioncomply_users');
                
                if (window.StorageManager.getMode() === 'indexeddb' && 
                    (!users || users.length === 0)) {
                    users = await window.StorageManager.async.getStoredData('arioncomply_users');
                }
                
                if (!Array.isArray(users) || users.length !== 1) {
                    throw new Error('Failed to retrieve users');
                }
                
                if (users[0].email !== user.email) {
                    throw new Error('User data mismatch');
                }
                
                log('success', 'User operations test passed', 'compatibilityResults');
            });
        }

        async function testCompanyOperations() {
            await runTest('Company Operations', async () => {
                // Create test company
                const company = {
                    id: 'test-company-001',
                    name: 'Test Company',
                    type: 'Technology',
                    size: 'Medium'
                };
                
                // Save company
                const saved = window.saveStoredData('arioncomply_companies', [company]);
                
                // Wait a bit for async operations
                await new Promise(resolve => setTimeout(resolve, 100));
                
                // Retrieve companies - try both sync and async
                let companies = window.getStoredData('arioncomply_companies');
                
                if (window.StorageManager.getMode() === 'indexeddb' && 
                    (!companies || companies.length === 0)) {
                    companies = await window.StorageManager.async.getStoredData('arioncomply_companies');
                }
                
                if (!Array.isArray(companies) || companies.length !== 1) {
                    throw new Error('Failed to retrieve companies');
                }
                
                if (companies[0].name !== company.name) {
                    throw new Error('Company data mismatch');
                }
                
                log('success', 'Company operations test passed', 'compatibilityResults');
            });
        }

        async function testSessionOperations() {
            await runTest('Session Operations', async () => {
                // Test session data
                const sessionData = {
                    userId: 'test-user-001',
                    timestamp: Date.now(),
                    active: true
                };
                
                // Save session
                const saved = window.saveStoredData('arioncomply_session', 'active');
                if (!saved) throw new Error('Failed to save session');
                
                // Save user session
                const userSaved = window.saveStoredData('arioncomply_user', sessionData);
                if (!userSaved) throw new Error('Failed to save user session');
                
                // Retrieve session
                const session = window.getStoredData('arioncomply_session');
                if (session !== 'active') {
                    throw new Error('Session data mismatch');
                }
                
                log('success', 'Session operations test passed', 'compatibilityResults');
            });
        }

        async function testWizardData() {
            await runTest('Wizard Data', async () => {
                // Create test wizard data
                const wizardData = {
                    framework: 'iso27001',
                    currentStep: 5,
                    responses: {
                        'q1': 'yes',
                        'q2': 'no',
                        'q3': 'partially'
                    },
                    progress: 75
                };
                
                // Save wizard data
                const saved = window.saveStoredData('compliance_assessment_complete', wizardData);
                if (!saved) throw new Error('Failed to save wizard data');
                
                // Retrieve wizard data
                const retrieved = window.getStoredData('compliance_assessment_complete');
                if (retrieved.framework !== wizardData.framework) {
                    throw new Error('Wizard data mismatch');
                }
                
                log('success', 'Wizard data test passed', 'compatibilityResults');
            });
        }

        async function testSettingsData() {
            await runTest('Settings Data', async () => {
                // Create test settings
                const settings = {
                    theme: 'dark',
                    notifications: true,
                    language: 'en',
                    avatar: 'professional'
                };
                
                // Save settings
                const saved = window.saveStoredData('avatarSettings', settings);
                if (!saved) throw new Error('Failed to save settings');
                
                // Retrieve settings
                const retrieved = window.getStoredData('avatarSettings');
                if (retrieved.theme !== settings.theme) {
                    throw new Error('Settings data mismatch');
                }
                
                log('success', 'Settings data test passed', 'compatibilityResults');
            });
        }

        async function testBulkOperations() {
            await runTest('Bulk Operations', async () => {
                // Create multiple test records
                const records = [];
                for (let i = 0; i < 10; i++) {
                    records.push({
                        id: `record-${i}`,
                        name: `Test Record ${i}`,
                        value: Math.random(),
                        timestamp: Date.now()
                    });
                }
                
                // Save bulk data
                const saved = window.saveStoredData('bulk_test_data', records);
                if (!saved) throw new Error('Failed to save bulk data');
                
                // Retrieve bulk data
                const retrieved = window.getStoredData('bulk_test_data');
                if (!Array.isArray(retrieved) || retrieved.length !== 10) {
                    throw new Error('Bulk data mismatch');
                }
                
                log('success', `Bulk operations test passed (${records.length} records)`, 'compatibilityResults');
            });
        }

        async function testPerformance() {
            await runTest('Performance Test', async () => {
                const startTime = Date.now();
                const iterations = 100;
                
                // Test save performance
                for (let i = 0; i < iterations; i++) {
                    const testData = {
                        id: `perf-${i}`,
                        data: new Array(100).fill(0).map(() => Math.random()),
                        timestamp: Date.now()
                    };
                    
                    const saved = window.saveStoredData(`perf_test_${i}`, testData);
                    if (!saved) throw new Error(`Failed to save record ${i}`);
                }
                
                // Test get performance
                for (let i = 0; i < iterations; i++) {
                    const retrieved = window.getStoredData(`perf_test_${i}`);
                    if (!retrieved || retrieved.id !== `perf-${i}`) {
                        throw new Error(`Failed to retrieve record ${i}`);
                    }
                }
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                const opsPerSec = Math.round((iterations * 2) / (duration / 1000));
                
                log('success', `Performance test passed: ${opsPerSec} ops/sec`, 'compatibilityResults');
            });
        }

        // =============================================================================
        // MIGRATION TESTS
        // =============================================================================

        async function createTestLocalStorageData() {
            try {
                // Create test data in localStorage
                const testData = {
                    'arioncomply_users': [
                        { id: 'user-1', name: 'John Doe', email: '<EMAIL>' },
                        { id: 'user-2', name: 'Jane Smith', email: '<EMAIL>' }
                    ],
                    'arioncomply_companies': [
                        { id: 'company-1', name: 'Test Corp', type: 'Technology' }
                    ],
                    'test_key_migration': { value: 'test migration data' }
                };
                
                Object.entries(testData).forEach(([key, value]) => {
                    localStorage.setItem(key, JSON.stringify(value));
                });
                
                log('success', 'Test localStorage data created', 'migrationResults');
            } catch (error) {
                log('error', `Failed to create test data: ${error.message}`, 'migrationResults');
            }
        }

        async function checkLocalStorageData() {
            try {
                const keys = Object.keys(localStorage);
                const relevantKeys = keys.filter(key => 
                    key.startsWith('arioncomply_') || key.startsWith('test_key_')
                );
                
                log('info', `Found ${relevantKeys.length} relevant localStorage keys:`, 'migrationResults');
                relevantKeys.forEach(key => {
                    const data = localStorage.getItem(key);
                    log('info', `  ${key}: ${data.substring(0, 50)}...`, 'migrationResults');
                });
            } catch (error) {
                log('error', `Failed to check localStorage: ${error.message}`, 'migrationResults');
            }
        }

        async function testMigration() {
            await runTest('Migration Process', async () => {
                // This test assumes localStorage data exists
                const beforeKeys = Object.keys(localStorage);
                const relevantKeys = beforeKeys.filter(key => 
                    key.startsWith('arioncomply_') || key.startsWith('test_key_')
                );
                
                if (relevantKeys.length === 0) {
                    throw new Error('No localStorage data to migrate (run Create Test Data first)');
                }
                
                // The migration happens automatically during initialization
                // We just need to verify it worked
                log('success', `Migration test initiated for ${relevantKeys.length} keys`, 'migrationResults');
            });
        }

        async function verifyMigration() {
            await runTest('Migration Verification', async () => {
                // Check that localStorage data is now accessible via IndexedDB
                const testKeys = [
                    'arioncomply_users',
                    'arioncomply_companies',
                    'test_key_migration'
                ];
                
                for (const key of testKeys) {
                    const data = window.getStoredData(key);
                    if (!data) {
                        throw new Error(`Migration verification failed for ${key}`);
                    }
                }
                
                log('success', 'Migration verification passed', 'migrationResults');
            });
        }

        // =============================================================================
        // STRESS TESTS
        // =============================================================================

        async function testLargeData() {
            await runTest('Large Data Test', async () => {
                // Create a large object
                const largeData = {
                    id: 'large-data-test',
                    data: new Array(10000).fill(0).map((_, i) => ({
                        id: i,
                        value: Math.random(),
                        text: `This is test data item ${i}`,
                        timestamp: Date.now(),
                        nested: {
                            level1: {
                                level2: {
                                    level3: `Deep nesting test ${i}`
                                }
                            }
                        }
                    }))
                };
                
                // Save large data
                const saved = window.saveStoredData('large_data_test', largeData);
                if (!saved) throw new Error('Failed to save large data');
                
                // Retrieve large data
                const retrieved = window.getStoredData('large_data_test');
                if (!retrieved || retrieved.data.length !== largeData.data.length) {
                    throw new Error('Large data retrieval failed');
                }
                
                log('success', `Large data test passed (${largeData.data.length} items)`, 'stressResults');
            });
        }

        async function testManyRecords() {
            await runTest('Many Records Test', async () => {
                const recordCount = 500;
                
                // Create many individual records
                for (let i = 0; i < recordCount; i++) {
                    const record = {
                        id: `record-${i}`,
                        name: `Record ${i}`,
                        data: Math.random(),
                        timestamp: Date.now()
                    };
                    
                    const saved = window.saveStoredData(`many_records_${i}`, record);
                    if (!saved) throw new Error(`Failed to save record ${i}`);
                }
                
                // Verify all records
                for (let i = 0; i < recordCount; i++) {
                    const retrieved = window.getStoredData(`many_records_${i}`);
                    if (!retrieved || retrieved.id !== `record-${i}`) {
                        throw new Error(`Failed to retrieve record ${i}`);
                    }
                }
                
                log('success', `Many records test passed (${recordCount} records)`, 'stressResults');
            });
        }

        async function testErrorHandling() {
            await runTest('Error Handling Test', async () => {
                // Test invalid JSON (circular reference)
                try {
                    const invalidData = { circular: null };
                    invalidData.circular = invalidData; // Create circular reference
                    
                    // This should handle the error gracefully and return false
                    const saved = window.saveStoredData('error_test', invalidData);
                    if (saved !== false) {
                        // If it somehow succeeded, that's also OK - just log it
                        log('info', 'Circular reference handling: data saved despite circular reference', 'stressResults');
                    }
                } catch (error) {
                    // This is expected behavior
                    log('info', 'Circular reference properly caught', 'stressResults');
                }
                
                // Test null/undefined values
                const nullSaved = window.saveStoredData('null_test', null);
                const undefinedSaved = window.saveStoredData('undefined_test', undefined);
                
                // Test empty key
                const emptySaved = window.saveStoredData('', { test: 'data' });
                
                // Test very large key
                const largeKey = 'x'.repeat(1000);
                const largeKeySaved = window.saveStoredData(largeKey, { test: 'data' });
                
                log('success', 'Error handling test passed', 'stressResults');
            });
        }

        async function testCorruptData() {
            await runTest('Corrupt Data Test', async () => {
                // Simulate corrupt data by directly manipulating localStorage
                if (window.StorageManager.getMode() === 'localstorage') {
                    localStorage.setItem('corrupt_test', 'invalid json {');
                    
                    // Try to retrieve corrupt data - should handle gracefully
                    const retrieved = window.getStoredData('corrupt_test');
                    if (!Array.isArray(retrieved)) {
                        throw new Error('Corrupt data not handled gracefully');
                    }
                }
                
                log('success', 'Corrupt data test passed', 'stressResults');
            });
        }

        async function testFallback() {
            await runTest('Fallback Test', async () => {
                // This test depends on the current storage mode
                const mode = window.StorageManager.getMode();
                
                // Test that fallback mechanisms work
                if (mode === 'indexeddb') {
                    log('info', 'Testing IndexedDB mode fallback scenarios', 'stressResults');
                    
                    // Test data persistence across different storage modes
                    const testData = { fallback: 'test', timestamp: Date.now() };
                    const saved = window.saveStoredData('fallback_test', testData);
                    if (!saved) throw new Error('Failed to save fallback test data');
                    
                    const retrieved = window.getStoredData('fallback_test');
                    if (!retrieved || retrieved.fallback !== testData.fallback) {
                        throw new Error('Fallback data mismatch');
                    }
                } else if (mode === 'localstorage') {
                    log('info', 'Testing localStorage mode (already in fallback)', 'stressResults');
                } else {
                    log('info', 'Testing memory mode (ultimate fallback)', 'stressResults');
                }
                
                log('success', `Fallback test passed (mode: ${mode})`, 'stressResults');
            });
        }

        async function testRecovery() {
            await runTest('Recovery Test', async () => {
                // Test system recovery after errors
                const testData = { recovery: 'test', timestamp: Date.now() };
                
                // Save test data
                const saved = window.saveStoredData('recovery_test', testData);
                if (!saved) throw new Error('Failed to save recovery test data');
                
                // Simulate recovery by retrieving data
                const retrieved = window.getStoredData('recovery_test');
                if (!retrieved || retrieved.recovery !== testData.recovery) {
                    throw new Error('Recovery test data mismatch');
                }
                
                // Test storage info accessibility during recovery
                const info = await window.StorageManager.getStorageInfo();
                if (!info || !info.mode) {
                    throw new Error('Storage info not accessible during recovery');
                }
                
                log('success', 'Recovery test passed', 'stressResults');
            });
        }

        // =============================================================================
        // DATA MANAGEMENT FUNCTIONS
        // =============================================================================

        async function clearAllData() {
            try {
                const confirmed = confirm('Are you sure you want to clear ALL data? This cannot be undone.');
                if (!confirmed) return;
                
                const result = await window.clearAllStoredData();
                if (result) {
                    log('success', 'All data cleared successfully', 'basicResults');
                    await updateStorageInfo();
                    
                    // Also clear localStorage for complete reset
                    localStorage.clear();
                    log('info', 'localStorage also cleared', 'basicResults');
                } else {
                    log('error', 'Failed to clear all data', 'basicResults');
                }
            } catch (error) {
                log('error', `Error clearing data: ${error.message}`, 'basicResults');
            }
        }

        async function clearTestData() {
            try {
                const testKeys = [
                    'test_key_001',
                    'bulk_test_data',
                    'large_data_test',
                    'fallback_test',
                    'recovery_test',
                    'error_test',
                    'null_test',
                    'undefined_test',
                    'corrupt_test'
                ];
                
                let cleared = 0;
                for (const key of testKeys) {
                    try {
                        const result = await window.deleteStoredData(key);
                        if (result) cleared++;
                    } catch (error) {
                        log('warning', `Failed to clear ${key}: ${error.message}`, 'basicResults');
                    }
                }
                
                // Clear performance test data
                for (let i = 0; i < 100; i++) {
                    try {
                        await window.deleteStoredData(`perf_test_${i}`);
                        cleared++;
                    } catch (error) {
                        // Ignore errors for performance data
                    }
                }
                
                // Clear many records test data
                for (let i = 0; i < 500; i++) {
                    try {
                        await window.deleteStoredData(`many_records_${i}`);
                        cleared++;
                    } catch (error) {
                        // Ignore errors for many records data
                    }
                }
                
                log('success', `Test data cleared: ${cleared} items`, 'basicResults');
                await updateStorageInfo();
                
            } catch (error) {
                log('error', `Error clearing test data: ${error.message}`, 'basicResults');
            }
        }

        function resetTests() {
            // Clear all result containers
            const resultContainers = [
                'basicResults',
                'compatibilityResults',
                'migrationResults',
                'stressResults'
            ];
            
            resultContainers.forEach(id => {
                const container = document.getElementById(id);
                if (container) {
                    container.innerHTML = '';
                }
            });
            
            // Reset test statistics
            testStats = {
                total: 0,
                passed: 0,
                failed: 0,
                times: []
            };
            
            updateTestStats();
            log('info', 'Test results reset', 'basicResults');
        }

        // =============================================================================
        // AUTOMATED TEST SUITE
        // =============================================================================

        async function runAllTests() {
            log('info', 'Starting automated test suite...', 'basicResults');
            
            try {
                // Basic Operations Tests
                await testSaveData();
                await testGetData();
                await testGenerateId();
                await testMultipleIds();
                
                // Compatibility Tests
                await testUserOperations();
                await testCompanyOperations();
                await testSessionOperations();
                await testWizardData();
                await testSettingsData();
                
                // Performance Tests
                await testBulkOperations();
                
                // Stress Tests
                await testLargeData();
                await testErrorHandling();
                await testFallback();
                await testRecovery();
                
                log('success', 'Automated test suite completed!', 'basicResults');
                
            } catch (error) {
                log('error', `Test suite failed: ${error.message}`, 'basicResults');
            }
        }

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            if (event.ctrlKey && event.key === 'Enter') {
                runAllTests();
            } else if (event.ctrlKey && event.key === 'r') {
                event.preventDefault();
                resetTests();
            }
        });

        // =============================================================================
        // EXPORT TEST RESULTS
        // =============================================================================

        function exportTestResults() {
            const results = {
                timestamp: new Date().toISOString(),
                storageMode: window.StorageManager.getMode(),
                statistics: testStats,
                storageInfo: null,
                testResults: {}
            };
            
            // Get storage info
            window.StorageManager.getStorageInfo().then(info => {
                results.storageInfo = info;
                
                // Get test results from each container
                const containers = [
                    'basicResults',
                    'compatibilityResults', 
                    'migrationResults',
                    'stressResults'
                ];
                
                containers.forEach(containerId => {
                    const container = document.getElementById(containerId);
                    if (container) {
                        results.testResults[containerId] = container.textContent;
                    }
                });
                
                // Export as JSON
                const dataStr = JSON.stringify(results, null, 2);
                const blob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                
                const link = document.createElement('a');
                link.href = url;
                link.download = `arioncomply_storage_test_results_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);
                
                log('success', 'Test results exported successfully', 'basicResults');
            });
        }

        // =============================================================================
        // ADDITIONAL UTILITY FUNCTIONS
        // =============================================================================

        // Add a button to run all tests
        function addRunAllTestsButton() {
            const header = document.querySelector('.header');
            if (header) {
                const button = document.createElement('button');
                button.className = 'button success';
                button.textContent = 'Run All Tests (Ctrl+Enter)';
                button.onclick = runAllTests;
                button.style.marginTop = '10px';
                header.appendChild(button);
                
                const exportButton = document.createElement('button');
                exportButton.className = 'button secondary';
                exportButton.textContent = 'Export Results';
                exportButton.onclick = exportTestResults;
                exportButton.style.marginTop = '10px';
                exportButton.style.marginLeft = '10px';
                header.appendChild(exportButton);
            }
        }

        // Add buttons after DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(addRunAllTestsButton, 1000);
        });

        // =============================================================================
        // CONTINUOUS MONITORING
        // =============================================================================

        let monitoringInterval;

        function startMonitoring() {
            if (monitoringInterval) return;
            
            monitoringInterval = setInterval(async () => {
                try {
                    await updateStorageInfo();
                } catch (error) {
                    console.error('Monitoring error:', error);
                }
            }, 5000);
            
            log('info', 'Started continuous monitoring', 'basicResults');
        }

        function stopMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                log('info', 'Stopped continuous monitoring', 'basicResults');
            }
        }

        // Start monitoring by default
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(startMonitoring, 2000);
        });

        // =============================================================================
        // BROWSER COMPATIBILITY CHECKS
        // =============================================================================

        function checkBrowserCompatibility() {
            const compatibility = {
                indexedDB: !!(window.indexedDB || window.mozIndexedDB || window.webkitIndexedDB || window.msIndexedDB),
                localStorage: typeof(Storage) !== "undefined",
                promises: typeof Promise !== "undefined",
                asyncAwait: (async () => {})().constructor === Promise.prototype.constructor,
                es6: typeof Symbol !== "undefined",
                webWorkers: typeof Worker !== "undefined",
                quotaAPI: !!(navigator.storage && navigator.storage.estimate)
            };
            
            log('info', 'Browser Compatibility Check:', 'basicResults');
            Object.entries(compatibility).forEach(([feature, supported]) => {
                log(supported ? 'success' : 'warning', `  ${feature}: ${supported ? '✅' : '❌'}`, 'basicResults');
            });
            
            return compatibility;
        }

        // Run compatibility check on load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkBrowserCompatibility, 500);
        });

    </script>
</body>
</html>
<!-- File: arioncomply-v1/Mockup/storage-test-page.html -->
