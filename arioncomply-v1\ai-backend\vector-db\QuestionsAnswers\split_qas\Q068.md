id: Q068
query: >-
  How often do we need to update our risk assessments and documentation?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/6.1"
  - "ISO27001:2022/9.2"
overlap_ids:
  - "ISO27701:2019/8"
capability_tags:
  - "Planner"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Risk Assessment"
    id: "ISO27001:2022/6.1"
    locator: "Clause 6.1"
  - title: "ISO/IEC 27001:2022 — Internal Audit"
    id: "ISO27001:2022/9.2"
    locator: "Clause 9.2"
  - title: "ISO/IEC 27701:2019 — Risk Assessment"
    id: "ISO27701:2019/8"
    locator: "Clause 8"
ui:
  cards_hint:
    - "Risk review calendar"
  actions:
    - type: "start_workflow"
      target: "risk_update"
      label: "Schedule Risk Review"
output_mode: "both"
graph_required: false
notes: "At minimum annually or when significant changes occur"
---
### 68) How often do we need to update our risk assessments and documentation?

**Standard terms**  
- **Risk assessment (Cl. 6.1):** plan and perform assessments.  
- **Internal audit (Cl. 9.2):** validates risk and controls.  
- **PIMS risk (ISO 27701 Cl. 8):** privacy risk considerations.

**Plain-English answer**  
You must update risk assessments **at least once a year** or after major changes (new systems, incidents). Documentation (RoPA, SoA, policies) should align with each review.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clauses 6.1; 9.2  
- **Also relevant/Overlaps:** ISO/IEC 27701:2019 Clause 8

**Why it matters**  
Fresh risk data keeps controls appropriate and documentation audit-ready.

**Do next in our platform**  
- Initiate **Risk Review** workflow.  
- Version and publish updated docs.

**How our platform will help**  
- **[Planner]** Risk review scheduler.  
- **[Report]** Risk change logs.

**Likely follow-ups**  
- “What triggers an out-of-cycle review?” (Major incidents or new services)

**Sources**  
- ISO/IEC 27001:2022 Clauses 6.1; 9.2  
- ISO/IEC 27701:2019 Clause 8
