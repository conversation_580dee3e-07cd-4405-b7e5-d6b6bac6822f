"""
File: arioncomply-v1/ai-backend/python-backend/services/ingestion/chromadb_standards_ingestion.py
File Description: ChromaDB ingestion pipeline for shared public standards knowledge
Purpose: Ingest standards, regulations, assessments, and templates to ChromaDB for cross-org sharing
Inputs: Standards documents, assessment frameworks, regulatory content, templates
Outputs: ChromaDB collections with structured public knowledge
Security: No org-scoping (shared public data), content classification metadata
Notes: Specialized for public knowledge curation and assessment question generation
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass
from enum import Enum

from .locling_extractor import extract_blocks
from .chunker import chunk_blocks
from .embedder import embed_texts_async
from .chromadb_client import chroma_client, CHROMA_ENABLED
from .dual_vector_ingestion import DataClassification

logger = logging.getLogger(__name__)


class StandardsType(str, Enum):
    """Types of standards content for specialized processing."""
    ISO_27001 = "iso_27001"
    GDPR = "gdpr"
    CCPA = "ccpa"
    SOX = "sox"
    NIST_CSF = "nist_csf"
    PCI_DSS = "pci_dss"
    HIPAA = "hipaa"
    SOC2 = "soc2"
    GENERIC_REGULATION = "generic_regulation"


class ContentType(str, Enum):
    """Content types for ChromaDB organization."""
    REGULATORY_TEXT = "regulatory_text"          # Raw regulations and standards
    ASSESSMENT_QUESTIONS = "assessment_questions" # Question banks and rubrics
    CONTROL_REQUIREMENTS = "control_requirements" # Specific control requirements
    IMPLEMENTATION_GUIDANCE = "implementation_guidance" # Best practices
    TEMPLATES = "templates"                      # Document templates
    WORKFLOWS = "workflows"                      # Process workflows
    FOLLOW_UP_SUGGESTIONS = "follow_up_suggestions" # Action recommendations


@dataclass
class StandardsIngestionRequest:
    """Structured request for standards ingestion."""
    file_path: str
    standards_type: StandardsType
    content_type: ContentType
    title: str
    version: str
    source: str = "standards_curation"
    metadata: Optional[Dict[str, Any]] = None
    pipeline_name: Optional[str] = None


@dataclass 
class StandardsIngestionResult:
    """Result of standards ingestion with collection mapping."""
    collection_name: str
    chunks_ingested: int
    embeddings_created: int
    pipeline_used: str
    content_classification: Dict[str, int]  # Content type breakdown
    audit_trail: Dict[str, Any]


class ChromaDBStandardsIngestion:
    """Specialized ingestion pipeline for public standards knowledge."""
    
    def __init__(self):
        """Initialize standards ingestion for ChromaDB source data."""
        if not CHROMA_ENABLED:
            raise Exception("ChromaDB is not enabled - required for standards ingestion")
        self.client = chroma_client
        
        # Define collection structure for standards
        self.collection_mapping = {
            # Regulatory collections by standard type
            StandardsType.ISO_27001: "iso_27001_knowledge",
            StandardsType.GDPR: "gdpr_knowledge", 
            StandardsType.CCPA: "ccpa_knowledge",
            StandardsType.SOX: "sox_knowledge",
            StandardsType.NIST_CSF: "nist_csf_knowledge",
            StandardsType.PCI_DSS: "pci_dss_knowledge",
            StandardsType.HIPAA: "hipaa_knowledge",
            StandardsType.SOC2: "soc2_knowledge",
            StandardsType.GENERIC_REGULATION: "regulatory_knowledge",
            
            # Content type collections (cross-standard)
            ContentType.ASSESSMENT_QUESTIONS: "assessment_frameworks",
            ContentType.TEMPLATES: "templates_and_workflows", 
            ContentType.FOLLOW_UP_SUGGESTIONS: "follow_up_suggestions",
            ContentType.IMPLEMENTATION_GUIDANCE: "implementation_guidance"
        }
    
    async def ingest_standards_document(self, request: StandardsIngestionRequest) -> StandardsIngestionResult:
        """
        Ingest standards document to appropriate ChromaDB collection.
        
        Args:
            request: Structured ingestion request
            
        Returns:
            StandardsIngestionResult with collection and audit information
        """
        audit_trail = {
            "start_time": self._get_timestamp(),
            "request": request.__dict__.copy(),
            "steps": []
        }
        
        try:
            # 1. Validate request
            self._validate_standards_request(request)
            audit_trail["steps"].append("request_validated")
            
            # 2. Extract and classify content
            blocks = extract_blocks(request.file_path)
            classified_blocks = self._classify_content_blocks(blocks, request.standards_type, request.content_type)
            audit_trail["steps"].append(f"extracted_and_classified_{len(classified_blocks)}_blocks")
            
            # 3. Chunk with standards-aware logic
            chunks = self._chunk_standards_content(classified_blocks, request.content_type)
            audit_trail["steps"].append(f"created_{len(chunks)}_chunks")
            
            # 4. Generate embeddings
            texts = [c["text"] for c in chunks]
            embedding_result = await embed_texts_async(
                texts,
                pipeline_name=request.pipeline_name,
                trace_id=f"standards_{request.standards_type.value}_{request.content_type.value}"
            )
            audit_trail["steps"].append(f"generated_embeddings_with_{embedding_result.pipeline_name}")
            
            # 5. Determine target collection and prepare metadata
            collection_name = self._get_target_collection(request.standards_type, request.content_type)
            enhanced_chunks = self._enhance_chunks_with_standards_metadata(
                chunks, request, embedding_result
            )
            
            # 6. Upsert to ChromaDB
            success = await self._upsert_to_chromadb_collection(
                collection_name, enhanced_chunks, embedding_result.embeddings
            )
            
            if not success:
                raise Exception(f"ChromaDB upsert failed for collection: {collection_name}")
            
            audit_trail["steps"].append(f"upserted_to_collection_{collection_name}")
            
            # 7. Generate content classification breakdown
            content_classification = self._analyze_content_distribution(enhanced_chunks)
            
            audit_trail["end_time"] = self._get_timestamp()
            audit_trail["success"] = True
            
            result = StandardsIngestionResult(
                collection_name=collection_name,
                chunks_ingested=len(chunks),
                embeddings_created=len(chunks),
                pipeline_used=embedding_result.pipeline_name,
                content_classification=content_classification,
                audit_trail=audit_trail
            )
            
            logger.info(f"Standards ingestion successful: {collection_name} - {len(chunks)} chunks")
            return result
            
        except Exception as e:
            error_msg = f"Standards ingestion failed: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            audit_trail["end_time"] = self._get_timestamp()
            audit_trail["success"] = False
            audit_trail["error"] = error_msg
            
            raise Exception(error_msg)
    
    def _classify_content_blocks(
        self, 
        blocks: List[Dict[str, Any]], 
        standards_type: StandardsType, 
        content_type: ContentType
    ) -> List[Dict[str, Any]]:
        """Apply content classification logic to extracted blocks."""
        
        classified_blocks = []
        
        for block in blocks:
            # Base classification
            block["standards_type"] = standards_type.value
            block["content_type"] = content_type.value
            
            # Content-specific classification
            if content_type == ContentType.ASSESSMENT_QUESTIONS:
                block = self._classify_assessment_content(block)
            elif content_type == ContentType.CONTROL_REQUIREMENTS:
                block = self._classify_control_content(block, standards_type)
            elif content_type == ContentType.IMPLEMENTATION_GUIDANCE:
                block = self._classify_guidance_content(block)
            elif content_type == ContentType.TEMPLATES:
                block = self._classify_template_content(block)
            
            classified_blocks.append(block)
        
        return classified_blocks
    
    def _classify_assessment_content(self, block: Dict[str, Any]) -> Dict[str, Any]:
        """Classify assessment-related content."""
        text = block.get("text", "").lower()
        
        # Identify assessment elements
        if any(keyword in text for keyword in ["question:", "q.", "assess", "evaluate"]):
            block["assessment_element"] = "question"
        elif any(keyword in text for keyword in ["answer:", "response:", "criteria"]):
            block["assessment_element"] = "answer_criteria" 
        elif any(keyword in text for keyword in ["score", "rating", "maturity", "level"]):
            block["assessment_element"] = "scoring_rubric"
        elif any(keyword in text for keyword in ["follow", "next", "action", "recommend"]):
            block["assessment_element"] = "follow_up"
        else:
            block["assessment_element"] = "general_guidance"
        
        return block
    
    def _classify_control_content(self, block: Dict[str, Any], standards_type: StandardsType) -> Dict[str, Any]:
        """Classify control requirements content."""
        text = block.get("text", "").lower()
        
        # Standards-specific control identification
        if standards_type == StandardsType.ISO_27001:
            # ISO 27001 control pattern matching
            if "a." in text and any(char.isdigit() for char in text):
                block["control_reference"] = self._extract_iso_control_reference(text)
        elif standards_type == StandardsType.NIST_CSF:
            # NIST CSF function identification
            for function in ["identify", "protect", "detect", "respond", "recover"]:
                if function in text:
                    block["nist_function"] = function
                    break
        elif standards_type == StandardsType.SOC2:
            # SOC 2 criteria identification
            for criteria in ["security", "availability", "processing integrity", "confidentiality", "privacy"]:
                if criteria in text:
                    block["soc2_criteria"] = criteria
                    break
        
        # Control type classification
        if any(keyword in text for keyword in ["shall", "must", "required", "mandatory"]):
            block["requirement_type"] = "mandatory"
        elif any(keyword in text for keyword in ["should", "recommended", "guidance"]):
            block["requirement_type"] = "recommended" 
        else:
            block["requirement_type"] = "informational"
        
        return block
    
    def _classify_guidance_content(self, block: Dict[str, Any]) -> Dict[str, Any]:
        """Classify implementation guidance content."""
        text = block.get("text", "").lower()
        
        # Guidance type classification
        if any(keyword in text for keyword in ["example", "sample", "template"]):
            block["guidance_type"] = "example"
        elif any(keyword in text for keyword in ["step", "process", "procedure"]):
            block["guidance_type"] = "process"
        elif any(keyword in text for keyword in ["best practice", "recommendation"]):
            block["guidance_type"] = "best_practice"
        elif any(keyword in text for keyword in ["tool", "software", "system"]):
            block["guidance_type"] = "tools"
        else:
            block["guidance_type"] = "general"
        
        return block
    
    def _classify_template_content(self, block: Dict[str, Any]) -> Dict[str, Any]:
        """Classify template and workflow content."""
        text = block.get("text", "").lower()
        
        # Template type identification
        if any(keyword in text for keyword in ["policy", "procedure", "standard"]):
            block["template_type"] = "policy_document"
        elif any(keyword in text for keyword in ["checklist", "assessment", "audit"]):
            block["template_type"] = "checklist"
        elif any(keyword in text for keyword in ["workflow", "process", "flowchart"]):
            block["template_type"] = "workflow"
        elif any(keyword in text for keyword in ["form", "record", "log"]):
            block["template_type"] = "record_form"
        else:
            block["template_type"] = "general_template"
        
        return block
    
    def _chunk_standards_content(
        self, 
        classified_blocks: List[Dict[str, Any]], 
        content_type: ContentType
    ) -> List[Dict[str, Any]]:
        """Apply content-specific chunking strategy."""
        
        if content_type == ContentType.ASSESSMENT_QUESTIONS:
            # Preserve question-answer pairs together
            return self._chunk_assessment_questions(classified_blocks)
        elif content_type == ContentType.CONTROL_REQUIREMENTS:
            # Keep control requirements as atomic units
            return self._chunk_control_requirements(classified_blocks)
        else:
            # Use standard chunking for other content
            return chunk_blocks(classified_blocks)
    
    def _chunk_assessment_questions(self, blocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Specialized chunking for assessment questions."""
        chunks = []
        current_question_group = []
        seq = 1
        
        for block in blocks:
            assessment_element = block.get("assessment_element", "general_guidance")
            
            if assessment_element == "question":
                # Start new question group
                if current_question_group:
                    chunks.append(self._create_assessment_chunk(current_question_group, seq))
                    seq += 1
                current_question_group = [block]
            else:
                # Add to current question group
                current_question_group.append(block)
        
        # Handle last group
        if current_question_group:
            chunks.append(self._create_assessment_chunk(current_question_group, seq))
        
        return chunks
    
    def _create_assessment_chunk(self, blocks: List[Dict[str, Any]], seq: int) -> Dict[str, Any]:
        """Create assessment chunk from grouped blocks."""
        combined_text = "\n".join(block["text"] for block in blocks)
        
        # Extract assessment metadata
        question_block = next((b for b in blocks if b.get("assessment_element") == "question"), blocks[0])
        
        return {
            "seq": seq,
            "text": combined_text,
            "section": question_block.get("section"),
            "heading": question_block.get("heading"),
            "page": question_block.get("page"),
            "assessment_element": question_block.get("assessment_element", "question"),
            "standards_type": question_block.get("standards_type"),
            "content_type": question_block.get("content_type")
        }
    
    def _chunk_control_requirements(self, blocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Specialized chunking for control requirements."""
        # Use standard chunking but preserve control references
        base_chunks = chunk_blocks(blocks)
        
        # Enhance with control metadata
        for chunk in base_chunks:
            # Find original block with control information
            for block in blocks:
                if block["text"] in chunk["text"]:
                    chunk.update({
                        k: v for k, v in block.items() 
                        if k.startswith(("control_", "requirement_", "nist_", "soc2_"))
                    })
                    break
        
        return base_chunks
    
    def _get_target_collection(self, standards_type: StandardsType, content_type: ContentType) -> str:
        """Determine target ChromaDB collection."""
        
        # Prioritize content type collections for cross-standard content
        if content_type in [ContentType.ASSESSMENT_QUESTIONS, ContentType.TEMPLATES, 
                           ContentType.FOLLOW_UP_SUGGESTIONS, ContentType.IMPLEMENTATION_GUIDANCE]:
            return self.collection_mapping[content_type]
        
        # Use standards-specific collection for regulatory text
        return self.collection_mapping.get(standards_type, "regulatory_knowledge")
    
    def _enhance_chunks_with_standards_metadata(
        self, 
        chunks: List[Dict[str, Any]], 
        request: StandardsIngestionRequest,
        embedding_result
    ) -> List[Dict[str, Any]]:
        """Enhance chunks with standards-specific metadata."""
        
        enhanced_chunks = []
        
        for chunk in chunks:
            enhanced_chunk = chunk.copy()
            
            # Add ingestion metadata
            enhanced_chunk["metadata"] = {
                **enhanced_chunk.get("metadata", {}),
                "standards_type": request.standards_type.value,
                "content_type": request.content_type.value,
                "document_title": request.title,
                "document_version": request.version,
                "document_source": request.source,
                "pipeline_used": embedding_result.pipeline_name,
                "embedding_dimension": embedding_result.embedding_dimension,
                "ingestion_timestamp": self._get_timestamp(),
                **{k: v for k, v in chunk.items() if k not in ["seq", "text", "metadata"]}
            }
            
            # Add custom metadata if provided
            if request.metadata:
                enhanced_chunk["metadata"].update(request.metadata)
            
            enhanced_chunks.append(enhanced_chunk)
        
        return enhanced_chunks
    
    async def _upsert_to_chromadb_collection(
        self, 
        collection_name: str, 
        enhanced_chunks: List[Dict[str, Any]], 
        embeddings: List[List[float]]
    ) -> bool:
        """Upsert chunks to ChromaDB collection."""
        
        # Prepare ChromaDB format
        chunk_ids = [f"{collection_name}_{chunk['metadata']['document_title']}_{chunk['seq']}" 
                    for chunk in enhanced_chunks]
        texts = [chunk["text"] for chunk in enhanced_chunks]
        metadatas = [chunk["metadata"] for chunk in enhanced_chunks]
        
        # Upsert to ChromaDB (using shared org_id for public data)
        return self.client.upsert_chunks(
            org_id="shared",
            collection_name=collection_name,
            chunk_ids=chunk_ids,
            embeddings=embeddings,
            texts=texts,
            metadatas=metadatas
        )
    
    def _analyze_content_distribution(self, enhanced_chunks: List[Dict[str, Any]]) -> Dict[str, int]:
        """Analyze content type distribution in ingested chunks."""
        distribution = {}
        
        for chunk in enhanced_chunks:
            content_type = chunk["metadata"].get("content_type", "unknown")
            assessment_element = chunk["metadata"].get("assessment_element")
            guidance_type = chunk["metadata"].get("guidance_type")
            template_type = chunk["metadata"].get("template_type")
            
            # Count by content type
            distribution[content_type] = distribution.get(content_type, 0) + 1
            
            # Count by sub-categories
            if assessment_element:
                key = f"assessment_{assessment_element}"
                distribution[key] = distribution.get(key, 0) + 1
            if guidance_type:
                key = f"guidance_{guidance_type}"
                distribution[key] = distribution.get(key, 0) + 1
            if template_type:
                key = f"template_{template_type}"
                distribution[key] = distribution.get(key, 0) + 1
        
        return distribution
    
    def _extract_iso_control_reference(self, text: str) -> Optional[str]:
        """Extract ISO 27001 control reference (e.g., 'A.5.1.1')."""
        import re
        match = re.search(r'A\.\d+\.\d+(?:\.\d+)?', text)
        return match.group(0) if match else None
    
    def _validate_standards_request(self, request: StandardsIngestionRequest):
        """Validate standards ingestion request."""
        if not request.file_path or not os.path.exists(request.file_path):
            raise ValueError(f"Invalid file_path: {request.file_path}")
        if not isinstance(request.standards_type, StandardsType):
            raise ValueError(f"Invalid standards_type: {request.standards_type}")
        if not isinstance(request.content_type, ContentType):
            raise ValueError(f"Invalid content_type: {request.content_type}")
        if not request.title or not request.version:
            raise ValueError("Title and version are required for standards documents")
    
    def _get_timestamp(self) -> str:
        """Get ISO timestamp."""
        from datetime import datetime
        return datetime.utcnow().isoformat() + "Z"
    
    async def list_collections_status(self) -> Dict[str, Any]:
        """Get status of all standards collections."""
        # This would be implemented based on ChromaDB's actual collection listing API
        return {
            "collections": list(set(self.collection_mapping.values())),
            "total_collections": len(set(self.collection_mapping.values())),
            "standards_supported": [s.value for s in StandardsType],
            "content_types_supported": [c.value for c in ContentType]
        }


# Convenience functions
async def ingest_iso27001_standard(file_path: str, title: str, version: str = "2022") -> StandardsIngestionResult:
    """Ingest ISO 27001 standard document."""
    ingestion = ChromaDBStandardsIngestion()
    request = StandardsIngestionRequest(
        file_path=file_path,
        standards_type=StandardsType.ISO_27001,
        content_type=ContentType.REGULATORY_TEXT,
        title=title,
        version=version
    )
    return await ingestion.ingest_standards_document(request)


async def ingest_assessment_framework(
    file_path: str, 
    standards_type: StandardsType, 
    title: str, 
    version: str = "v1"
) -> StandardsIngestionResult:
    """Ingest assessment question framework."""
    ingestion = ChromaDBStandardsIngestion()
    request = StandardsIngestionRequest(
        file_path=file_path,
        standards_type=standards_type,
        content_type=ContentType.ASSESSMENT_QUESTIONS,
        title=title,
        version=version
    )
    return await ingestion.ingest_standards_document(request)


async def ingest_compliance_template(
    file_path: str, 
    template_name: str, 
    applicable_standards: List[StandardsType],
    version: str = "v1"
) -> List[StandardsIngestionResult]:
    """Ingest compliance template for multiple standards."""
    ingestion = ChromaDBStandardsIngestion()
    results = []
    
    # Ingest template for each applicable standard
    for standards_type in applicable_standards:
        request = StandardsIngestionRequest(
            file_path=file_path,
            standards_type=standards_type,
            content_type=ContentType.TEMPLATES,
            title=template_name,
            version=version,
            metadata={"applicable_standards": [s.value for s in applicable_standards]}
        )
        result = await ingestion.ingest_standards_document(request)
        results.append(result)
    
    return results
