id: Q073
query: >-
  What are the biggest mistakes companies make when starting compliance programs?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/5.1"
overlap_ids:
  - "GDPR:2016/Art.24"
capability_tags:
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Leadership & Commitment"
    id: "ISO27001:2022/5.1"
    locator: "Clause 5.1"
  - title: "GDPR — Accountability"
    id: "GDPR:2016/Art.24"
    locator: "Article 24"
ui:
  cards_hint:
    - "Common pitfalls"
  actions:
    - type: "open_register"
      target: "lessons_learned"
      label: "View Pitfall Register"
output_mode: "both"
graph_required: false
notes: "Lack of leadership buy-in, under-scoping, and ignoring culture"
---
### 73) What are the biggest mistakes companies make when starting compliance programs?

**Standard terms**  
- **Leadership (ISO 27001 Cl. 5.1):** failing to secure executive support.  
- **Accountability (GDPR Art. 24):** not assigning clear roles.  

**Plain-English answer**  
Common errors are: poor executive buy-in, too narrow scope, skipping gap analysis, treating it as a one-off project, and neglecting ongoing culture and training.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 5.1  
- **Also relevant/Overlaps:** GDPR Article 24

**Why it matters**  
Avoiding these ensures smoother, sustainable compliance.

**Do next in our platform**  
- Review **Pitfall Register**.  
- Run **Lessons Learned** analysis.

**How our platform will help**  
- **[Register]** Pitfall tracking.  
- **[Report]** Remediation plans.

**Likely follow-ups**  
- “How do we secure leadership buy-in?” (See Q078)

**Sources**  
- ISO/IEC 27001:2022 Clause 5.1  
- GDPR Article 24