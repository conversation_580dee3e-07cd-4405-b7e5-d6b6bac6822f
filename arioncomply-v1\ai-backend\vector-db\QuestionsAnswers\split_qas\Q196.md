id: Q196
query: >-
  What’s the difference between awareness training and technical training?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/7.3"
overlap_ids: []
capability_tags:
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Training Requirements"
    id: "ISO27001:2022/7.3"
    locator: "Clause 7.3"
ui:
  cards_hint:
    - "Training type definitions"
  actions:
    - type: "open_register"
      target: "training_types"
      label: "View Definitions"
output_mode: "both"
graph_required: false
notes: "Awareness = high-level policy & culture; technical = hands-on tool/configuration skills"
---
### 196) What’s the difference between awareness training and technical training?

**Standard terms**  
- **Training (ISO27001 Cl.7.3):** includes both awareness and skills.

**Plain-English answer**  
- **Awareness training:** introduces policies, user-level risks (phishing, password hygiene).  
- **Technical training:** deep dives on tools, secure coding, incident-response procedures.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 7.3

**Why it matters**  
Aligns training format with learning objectives and role requirements.

**Do next in our platform**  
- Classify each module in **Training Types** register.  
- Tag courses as “awareness” or “technical.”

**How our platform will help**  
- **[Report]** Usage metrics by training type.  
- **[Dashboard]** Skill-gap heatmap.

**Likely follow-ups**  
- Which format has higher completion rates?  
- How to combine both in a blended program?

**Sources**  
- ISO/IEC 27001:2022 Clause 7.3  
