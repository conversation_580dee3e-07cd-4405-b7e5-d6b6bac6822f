# Provider Router and Tools (Placeholder)

Decisions
- `assistant_router` orchestrates moderation → retrieval → prompt → model → post-process.
- Provider selection: SLLM first, fallback to GLLM with anonymization.
- Tool/function-calling to trigger data lookups or proposals, gated by HITL.

Design Points
- Abstraction for providers; uniform interface for chat/stream and usage metrics.
- Anonymization hooks injected before GLLM calls.
- Tool schema: name, input schema, safety constraints, audit logging.

Open Questions
- Minimal tool set for MVP (e.g., getDocuments, createProposal).

Next Steps
- Implement router skeleton in `conversation/send`.
- Add provider adapter interfaces and a basic tool registry.

