# Provider Strategy (Placeholder)

Decisions
- Primary: SLLMs hosted on our servers, quantized to INT8 for efficiency.
- Backup: OpenAI and Anthropic Claude. Strict anonymization before any GLLM call.
- Selection policy: prefer SLLM unless capability gap; fall back per task and SLA.

Controls
- Egress allowlist to model endpoints only.
- Secret management via Supabase config; no secrets in logs.
- Track token usage/cost in `ai_call` events.

Open Questions
- Exact SLLM family and inference stack; hardware sizing.
- Provider-specific safety/mode settings and latency targets.

Next Steps
- Implement provider router with anonymization hooks.
- Add cost/usage estimators per provider.
- Create integration tests exercising fallback paths.

