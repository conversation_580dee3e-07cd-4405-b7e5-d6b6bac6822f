# File: arioncomply-v1/ai-backend/python-backend/services/embedding/__init__.py
# File Description: Public package API for the embedding subsystem
# Purpose: Re-export key classes and helpers; provide package docs and metadata
"""
Multi-pipeline embedding system for ArionComply.

This module provides a flexible, secure, and highly traceable embedding system
supporting multiple embedding models with runtime selection and automatic fallback.

Key Features:
- Multiple embedding pipelines (BGE-ONNX, MPNet, OpenAI, Placeholder)
- Automatic pipeline selection and fallback
- Complete operation audit trail
- Security-first local approach with optional cloud services
- CPU-optimized for broad deployment
- Multi-dimensional vector support

Usage:
    from services.embedding import embed_texts
    
    result = await embed_texts([
        "This is a compliance document excerpt",
        "Another piece of regulatory text"
    ])
    
    embeddings = result.embeddings
    pipeline_used = result.pipeline_name
"""

from .embedding_service import (
    EmbeddingService,
    get_embedding_service,
    embed_texts
)

from .pipeline_interface import (
    EmbeddingPipeline,
    PipelineMetadata,
    EmbeddingResult,
    HealthCheckResult,
    PipelineStatus,
    QualityTier
)

from .pipeline_registry import (
    PipelineRegistry,
    SelectionStrategy,
    PipelineRegistryEntry,
    RegistryConfig
)

# Main public interface
__all__ = [
    # High-level functions
    "embed_texts",
    "get_embedding_service",
    
    # Core classes
    "EmbeddingService",
    "EmbeddingPipeline", 
    "PipelineRegistry",
    
    # Data classes
    "PipelineMetadata",
    "EmbeddingResult",
    "HealthCheckResult",
    "PipelineRegistryEntry",
    "RegistryConfig",
    
    # Enums
    "PipelineStatus",
    "QualityTier",
    "SelectionStrategy"
]

# Version information
__version__ = "1.0.0"
__author__ = "ArionComply"
__description__ = "Multi-pipeline embedding system with security-first design"
