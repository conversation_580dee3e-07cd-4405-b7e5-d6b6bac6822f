id: Q030
query: >-
  Are there any "safe harbors" or exceptions that might protect us?
packs:
  - "GDPR:2016"
  - "CPRA:2023"
primary_ids:
  - "GDPR:2016/Art.42"
  - "CPRA:2023/§1798.185"
overlap_ids: []
capability_tags:
  - "Register"
  - "Report"
flags:
  - "LOCAL LAW_CHECK"
sources:
  - title: "GDPR — Certification Mechanisms"
    id: "GDPR:2016/Art.42"
    locator: "Article 42"
  - title: "CPRA — Risk-Based Assessment Exception"
    id: "CPRA:2023/1798.185"
    locator: "Section 1798.185"
ui:
  cards_hint:
    - "Exception register"
  actions:
    - type: "open_register"
      target: "exceptions"
      label: "View Exceptions"
output_mode: "both"
graph_required: false
notes: "Certification or binding codes can mitigate fine amounts"
---
### 30) Are there any "safe harbors" or exceptions that might protect us?

**Standard terms)**
- **GDPR certification (Art. 42):** voluntary schemes that can reduce fines.
- **CPRA risk exception (§1798.185):** exemptions for small-volume processing.

**Plain-English answer**
GDPR-certification schemes and approved codes of conduct can reduce penalties but don’t replace compliance. CPRA offers limited exceptions for small processors under certain thresholds **[LOCAL LAW_CHECK]**.

**Applies to**
- **Primary:** GDPR Article 42; CPRA Section 1798.185

**Why it matters**
Leveraging exceptions can lower exposure and streamline efforts.

**Do next in our platform**
- Catalog **certifications** and **codes** you hold.
- Check CPRA **thresholds** for small-processor exemptions.

**How our platform will help**
- **[Register]** Exception & certification inventory.
- **[Report]** Fine-mitigation summary.

**Likely follow-ups**
- “How do we enroll in a GDPR certification scheme?” (**[LOCAL LAW_CHECK]**)

**Sources**
- GDPR Article 42; CPRA §1798.185
