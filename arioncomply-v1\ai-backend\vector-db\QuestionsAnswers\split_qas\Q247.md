id: Q247
query: >-
  How do we update our compliance program based on lessons learned from incidents?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/10.1"
overlap_ids: []
capability_tags:
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Improvement"
    id: "ISO27001:2022/10.1"
    locator: "Clause 10.1"
ui:
  cards_hint:
    - "Continuous improvement plan"
  actions:
    - type: "start_workflow"
      target: "incident_improvement"
      label: "Apply Lessons Learned"
output_mode: "both"
graph_required: false
notes: "Close the loop by feeding audit and incident insights back into your ISMS"
---
### 247) How do we update our compliance program based on lessons learned from incidents?

**Standard terms)**  
- **Improvement (ISO27001 Cl.10.1):** requires action on nonconformities.

**Plain-English answer**  
After each incident, capture findings in the **Lessons-Learned Register**, then invoke the **Incident Improvement** workflow to revise policies, controls, and training content based on those insights.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 10.1

**Why it matters**  
Ensures your ISMS evolves and remains effective against real-world threats.

**Do next in our platform**  
- Run **Incident Improvement** workflow.  
- Update all impacted documentation and controls.

**How our platform will help**  
- **[Workflow]** Auto-links incident entries to policy revisions.  
- **[Report]** Tracks completion of improvement actions.

**Likely follow-ups**  
- How do we demonstrate closure to auditors?

**Sources**  
- ISO/IEC 27001:2022 Clause 10.1
