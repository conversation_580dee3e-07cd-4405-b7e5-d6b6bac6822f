# ArionComply Screens/Files List

## Core UI Screens
1. `index.html` - Splash & Settings
2. `dashboard.html` - Main Dashboard
3. `userManagement.html` - User & Company Management

## Workflow Management Suite
4. `workflowList.html` - Workflow List/Management
5. `workflowEngine.html` - Visual Workflow Builder
6. `workflowPreview.html` - Workflow Preview
7. `workflowStepEdit.html` - Workflow Step Editor

## Document & File Management
8. `fileManager.html` - File Management System

## Compliance & Assessment  
9. `assessment-chat.dart` - Chat-Driven Compliance Assessment (Flutter Web/Native)
10. `structured-forms.dart` - Optional Structured Data Collection (Flutter Web/Native)

## Communication & Notifications
11. `notificationCenter.dart` - Notification Center (Flutter Web/Native)
12. `chatInterface.dart` - Primary AI Chat Interface for Assessments (Flutter Web/Native)

## Search & Discovery
12. `searchInterface.html` - Search Interface

## Visualization & Mapping
13. `relationshipMapper.html` - Relationship Mapper
14. `timelineView.html` - Timeline View
15. `timelineEnhancedFeatures.html` - Timeline Enhanced Features
16. `timelineViewEnhacements.html` - Timeline View Enhancements
17. `treeView.html` - Tree View

## Project Management
18. `kanbanBoard.html` - Kanban Board
19. `formBuilder.html` - Form Builder

## Lab & Testing
20. `prototypeIndex.html` - Prototype Screens Index
21. `query-response-test-module.html` - Query Test Module

## Legal & Policy
22. `privacy.html` - Privacy Policy
23. `terms.html` - Terms of Service