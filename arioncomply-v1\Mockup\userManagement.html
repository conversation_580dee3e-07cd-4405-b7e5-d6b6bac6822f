<!-- File: arioncomply-v1/Mockup/userManagement.html -->
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ArionComply - User Management</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link href="mystyles.css" rel="stylesheet" />
    <style>
      .settings-container {
        display: grid;
        grid-template-columns: 280px 1fr;
        gap: 2rem;
        height: calc(100vh - 200px);
      }
      .settings-nav {
        background: var(--bg-white);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-subtle);
        padding: 1.5rem;
        overflow-y: auto;
      }
      .settings-content {
        background: var(--bg-white);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-subtle);
        overflow-y: auto;
      }
      .settings-nav-item {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        margin-bottom: 0.25rem;
        border-radius: var(--border-radius-sm);
        cursor: pointer;
        transition: all 0.15s ease;
        text-decoration: none;
        color: var(--text-gray);
      }
      .settings-nav-item.active {
        background: var(--primary-blue);
        color: white;
      }
      .settings-nav-item:hover {
        background: var(--bg-light);
        color: var(--text-dark);
      }
      .settings-section {
        display: none;
        animation: fadeIn 0.3s ease;
      }
      .settings-section.active {
        display: block;
      }
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 1rem;
      }
      th,
      td {
        border: 1px solid var(--border-light);
        padding: 0.5rem;
        text-align: left;
      }
    </style>
  </head>
  <body>
    <div class="app-container">
      <main class="main-content">
        <div class="page-header">
          <div>
            <h1 class="page-title">User & Role Management</h1>
            <p class="page-subtitle">Manage companies, users and roles</p>
          </div>
        </div>
        <div class="settings-container">
          <div class="settings-nav">
            <div
              class="settings-nav-item active"
              onclick="showSection('companies')"
            >
              <i class="fas fa-building nav-item-icon"></i>
              <span class="nav-item-text">Companies</span>
            </div>
            <div class="settings-nav-item" onclick="showSection('users')">
              <i class="fas fa-users nav-item-icon"></i>
              <span class="nav-item-text">Users</span>
            </div>
          </div>
          <div class="settings-content">
            <div class="settings-section active" id="companies-section">
              <div class="settings-header">
                <h2 class="settings-title">Companies</h2>
              </div>
              <div class="settings-body">
                <table id="companyTable">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Name</th>
                      <th>Actions</th>
                    </tr>
                  </thead>

                  <tbody></tbody>
                </table>
                <form id="companyForm">
                  <div class="form-row">
                    <div class="form-group">
                      <label class="form-label">Company Name</label>
                      <input
                        type="text"
                        class="form-input"
                        id="companyName"
                        required
                      />
                    </div>
                  </div>
                  <button class="btn btn-primary" type="submit">
                    Add Company
                  </button>
                </form>
              </div>
            </div>
            <div class="settings-section" id="users-section">
              <div class="settings-header">
                <h2 class="settings-title">Users</h2>
              </div>
              <div class="settings-body">
                <table id="userTable">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Name</th>
                      <th>Role</th>
                      <th>Company</th>
                      <th>Actions</th>
                    </tr>
                  </thead>

                  <tbody></tbody>
                </table>
                <form id="userForm">
                  <div class="form-row">
                    <div class="form-group">
                      <label class="form-label">Name</label>
                      <input
                        type="text"
                        class="form-input"
                        id="userName"
                        required
                      />
                    </div>
                    <div class="form-group">
                      <label class="form-label">Email</label>
                      <input
                        type="email"
                        class="form-input"
                        id="userEmail"
                        required
                      />
                    </div>
                  </div>
                  <div class="form-row">
                    <div class="form-group">
                      <label class="form-label">Role</label>
                      <select class="form-select" id="userRole">
                        <option value="admin">Admin</option>
                        <option value="manager">Manager</option>
                        <option value="auditor">Auditor</option>
                        <option value="user">User</option>
                      </select>
                    </div>
                    <div class="form-group">
                      <label class="form-label">Company</label>
                      <select class="form-select" id="userCompany"></select>
                    </div>
                  </div>
                  <button class="btn btn-primary" type="submit">
                    Add User
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <script src="navigation-config.js"></script>
    <script src="sidebar-component.js"></script>
    <script src="layout-manager.js"></script>
    <script src="scripts.js"></script>
    <script>
      // -----------------------------------------------------------------------------
      // Helper functions for loading and displaying stored data
      // -----------------------------------------------------------------------------
      function loadCompanies() {
        try {
          const companies = getStoredData("arioncomply_companies");
          const tbody = document.querySelector("#companyTable tbody");
          const companySelect = document.getElementById("userCompany");
          tbody.innerHTML = "";
          companySelect.innerHTML = "";
          companies.forEach((c) => {
            const row = document.createElement("tr");

            row.innerHTML =
              `<td>${c.id}</td><td>${c.name}</td>` +
              `<td><a href="#" onclick="editCompany('${c.id}')">Edit</a> |
                <a href="#" onclick="removeCompany('${c.id}')">Delete</a></td>`;

            tbody.appendChild(row);
            const opt = document.createElement("option");
            opt.value = c.id;
            opt.textContent = c.name;
            companySelect.appendChild(opt);
          });
        } catch (err) {
          console.error("Failed to load companies", err);
        }
      }

      function loadUsers() {
        try {
          const users = getStoredData("arioncomply_users");
          const tbody = document.querySelector("#userTable tbody");
          tbody.innerHTML = "";
          users.forEach((u) => {
            const row = document.createElement("tr");

            row.innerHTML =
              `<td>${u.id}</td><td>${u.name}</td><td>${u.role}</td><td>${u.company}</td>` +
              `<td><a href="userProfile.html?id=${u.id}">Edit</a> | ` +
              `<a href="#" onclick="removeUser('${u.id}')">Delete</a></td>`;

            tbody.appendChild(row);
          });
        } catch (err) {
          console.error("Failed to load users", err);
        }
      }

      // -----------------------------------------------------------------------------
      // Editing and deleting records
      // -----------------------------------------------------------------------------
      function editCompany(id) {
        const record = getCompanyById(id);
        if (!record) return;
        const name = prompt("Edit company name", record.name);
        if (name) {
          record.name = name.trim();
          saveCompany(record);
          loadCompanies();
        }
      }

      function removeCompany(id) {
        if (confirm("Delete this company?")) {
          deleteCompany(id);
          loadCompanies();
        }
      }

      function removeUser(id) {
        if (confirm("Delete this user?")) {
          deleteUser(id);
          loadUsers();
        }
      }

      // Switch between the Companies and Users sections
      function showSection(id) {
        document
          .querySelectorAll(".settings-nav-item")
          .forEach((i) => i.classList.remove("active"));
        document
          .querySelectorAll(".settings-section")
          .forEach((s) => s.classList.remove("active"));
        document
          .querySelector(`[onclick="showSection('${id}')"]`)
          .classList.add("active");
        document.getElementById(id + "-section").classList.add("active");
      }

      // -----------------------------------------------------------------------------
      // Form handling
      // -----------------------------------------------------------------------------
      document
        .getElementById("companyForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();
          try {
            const name = document.getElementById("companyName").value.trim();
            if (!name) return;

            const companies = getStoredData("arioncomply_companies");
            companies.push({ id: generateId("C"), name });
            saveStoredData("arioncomply_companies", companies);

            this.reset();
            loadCompanies();
          } catch (err) {
            console.error("Failed to save company", err);
          }
        });

      document
        .getElementById("userForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();
          try {
            const name = document.getElementById("userName").value.trim();
            const email = document.getElementById("userEmail").value.trim();
            const role = document.getElementById("userRole").value;
            const company = document.getElementById("userCompany").value;
            if (!name || !email) return;

            const users = getStoredData("arioncomply_users");
            users.push({ id: generateId("U"), name, email, role, company });
            saveStoredData("arioncomply_users", users);

            this.reset();
            loadUsers();
          } catch (err) {
            console.error("Failed to save user", err);
          }
        });

      // -----------------------------------------------------------------------------
      // Initialization
      // -----------------------------------------------------------------------------
      document.addEventListener("DOMContentLoaded", function () {
        // Initialize the page using the shared layout manager if available
        if (typeof LayoutManager !== "undefined") {
          LayoutManager.initializePage("userManagement.html");
        }

        // Restrict access to managers and administrators only
        const current = JSON.parse(
          localStorage.getItem("arioncomply_user") || "{}",
        );
        if (!["admin", "manager"].includes(current.role)) {
          alert("Access restricted to managers and admins");
          window.location.href = "routing.html";
          return;
        }

        // Only admins may assign roles
        if (current.role !== "admin") {
          document
            .getElementById("userRole")
            .setAttribute("disabled", "disabled");
        }

        // Load existing records
        loadCompanies();
        loadUsers();
      });
    </script>
  </body>
</html>
<!-- File: arioncomply-v1/Mockup/userManagement.html -->
