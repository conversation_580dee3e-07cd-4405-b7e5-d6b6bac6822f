-- File: arioncomply-v1/db/migrations/0009_access_helpers.sql
-- Migration 0009: Access helper functions
-- Purpose: Permission check helper to be used by RPC/Edge layers

BEGIN;

CREATE OR REPLACE FUNCTION check_permission(
  p_org_id uuid,
  p_user_id uuid,
  p_permission_code text
) RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_has boolean := false;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM user_roles ur
    JOIN role_permissions rp ON ur.role_id = rp.role_id
    JOIN permissions p ON rp.permission_id = p.permission_id
    WHERE ur.user_id = p_user_id
      AND ur.org_id = p_org_id
      AND p.permission_code = p_permission_code
  ) INTO v_has;

  RETURN COALESCE(v_has, false);
END;
$$;

COMMIT;
-- File: arioncomply-v1/db/migrations/0009_access_helpers.sql
