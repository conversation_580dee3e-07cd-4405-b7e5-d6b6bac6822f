openapi: 3.0.3
info:
  title: ArionComply Conversation API (MVP)
  version: 0.1.0
paths:
  /conversation/start:
    post:
      summary: Start a new conversation
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                context:
                  type: object
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  ok: { type: boolean }
                  data:
                    type: object
                    properties:
                      session:
                        type: object
                        properties:
                          sessionId: { type: string }
                          title: { type: string }
                          status: { type: string }
                          metadata: { type: object }
                          createdAt: { type: string, format: date-time }
                      firstMessage:
                        type: object
                        properties:
                          messageId: { type: string }
                          senderType: { type: string }
                          contentType: { type: string }
                          contentText: { type: string }
                          createdAt: { type: string, format: date-time }
  /conversation/send:
    post:
      summary: Send a user message and get a reply
      parameters:
        - in: header
          name: traceparent
          schema: { type: string }
        - in: header
          name: x-correlation-id
          schema: { type: string }
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [sessionId, message]
              properties:
                sessionId: { type: string }
                message: { type: string }
                hintContext: { type: object }
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  ok: { type: boolean }
                  data:
                    type: object
                    properties:
                      userMessage:
                        type: object
                        properties:
                          messageId: { type: string }
                          sessionId: { type: string }
                          contentText: { type: string }
                          createdAt: { type: string, format: date-time }
                      assistantMessage:
                        type: object
                        properties:
                          messageId: { type: string }
                          sessionId: { type: string }
                          contentText: { type: string }
                          createdAt: { type: string, format: date-time }
  /conversation/stream:
    post:
      summary: Stream assistant tokens via SSE
      responses:
        '200':
          description: Server-Sent Events stream
          content:
            text/event-stream: {}
components: {}

