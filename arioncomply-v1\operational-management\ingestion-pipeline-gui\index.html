<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArionComply - Ingestion Pipeline Management</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo-section">
                    <i class="fas fa-cogs"></i>
                    <h1>ArionComply Ingestion Management</h1>
                </div>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="refreshAll()">
                        <i class="fas fa-sync-alt"></i> Refresh All
                    </button>
                    <div class="system-status" id="systemStatus">
                        <span class="status-indicator" id="statusIndicator"></span>
                        <span id="statusText">Loading...</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Navigation Tabs -->
        <nav class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('pipeline-management')">
                <i class="fas fa-network-wired"></i> Pipeline Management
            </button>
            <button class="nav-tab" onclick="showTab('document-ingestion')">
                <i class="fas fa-file-upload"></i> Document Ingestion
            </button>
            <button class="nav-tab" onclick="showTab('testing-tools')">
                <i class="fas fa-vial"></i> Testing & Validation
            </button>
            <button class="nav-tab" onclick="showTab('vector-storage')">
                <i class="fas fa-database"></i> Vector Storage
            </button>
            <button class="nav-tab" onclick="showTab('configuration')">
                <i class="fas fa-cog"></i> Configuration
            </button>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Pipeline Management Tab -->
            <div id="pipeline-management" class="tab-content active">
                <div class="section">
                    <div class="section-header">
                        <h2><i class="fas fa-network-wired"></i> Embedding Pipeline Management</h2>
                        <div class="section-actions">
                            <button class="btn btn-secondary" onclick="runHealthChecks()">
                                <i class="fas fa-heartbeat"></i> Health Check
                            </button>
                        </div>
                    </div>

                    <!-- Current Pipeline Status -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Current Active Pipeline</h3>
                        </div>
                        <div class="card-content">
                            <div class="current-pipeline-info" id="currentPipelineInfo">
                                <div class="pipeline-main-info">
                                    <div class="pipeline-name" id="currentPipelineName">Loading...</div>
                                    <div class="pipeline-status" id="currentPipelineStatus">
                                        <span class="status-badge" id="currentPipelineBadge">Unknown</span>
                                    </div>
                                </div>
                                <div class="pipeline-details" id="currentPipelineDetails">
                                    <!-- Dynamic content will be loaded here -->
                                </div>
                                <div class="pipeline-actions">
                                    <button class="btn btn-outline" onclick="showPipelineMetrics()">
                                        <i class="fas fa-chart-line"></i> View Metrics
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Available Pipelines -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Available Pipelines</h3>
                        </div>
                        <div class="card-content">
                            <div class="pipelines-grid" id="pipelinesGrid">
                                <!-- Pipeline cards will be dynamically loaded -->
                            </div>
                        </div>
                    </div>

                    <!-- Pipeline Performance Metrics -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Performance Overview</h3>
                        </div>
                        <div class="card-content">
                            <div class="metrics-grid" id="performanceMetrics">
                                <!-- Metrics will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Document Ingestion Tab -->
            <div id="document-ingestion" class="tab-content">
                <div class="section">
                    <div class="section-header">
                        <h2><i class="fas fa-file-upload"></i> Document Ingestion</h2>
                        <div class="section-actions">
                            <button class="btn btn-primary" onclick="showUploadDialog()">
                                <i class="fas fa-plus"></i> Upload Documents
                            </button>
                        </div>
                    </div>

                    <!-- Ingestion Queue -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Processing Queue</h3>
                        </div>
                        <div class="card-content">
                            <div class="queue-status" id="ingestionQueue">
                                <!-- Queue items will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Recent Ingestions -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Recent Ingestions</h3>
                        </div>
                        <div class="card-content">
                            <div class="ingestion-history" id="ingestionHistory">
                                <!-- History will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Testing Tools Tab -->
            <div id="testing-tools" class="tab-content">
                <div class="section">
                    <div class="section-header">
                        <h2><i class="fas fa-vial"></i> Testing & Validation</h2>
                    </div>

                    <!-- Embedding Test Tool -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Test Embedding Generation</h3>
                        </div>
                        <div class="card-content">
                            <div class="test-form">
                                <div class="form-group">
                                    <label for="testText">Test Text:</label>
                                    <textarea id="testText" rows="4" placeholder="Enter text to generate embeddings...">This is a sample compliance document for testing embedding generation.</textarea>
                                </div>
                                <div class="form-group">
                                    <label for="testPipeline">Pipeline:</label>
                                    <select id="testPipeline">
                                        <option value="">Use Current Active Pipeline</option>
                                    </select>
                                </div>
                                <div class="form-actions">
                                    <button class="btn btn-primary" onclick="testEmbedding()">
                                        <i class="fas fa-play"></i> Generate Embedding
                                    </button>
                                    <button class="btn btn-secondary" onclick="compareAllPipelines()">
                                        <i class="fas fa-balance-scale"></i> Compare All Pipelines
                                    </button>
                                </div>
                            </div>
                            <div class="test-results" id="testResults">
                                <!-- Results will be shown here -->
                            </div>
                        </div>
                    </div>

                    <!-- Pipeline Comparison -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Pipeline Performance Comparison</h3>
                        </div>
                        <div class="card-content">
                            <div class="comparison-results" id="comparisonResults">
                                <!-- Comparison table will be shown here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Vector Storage Tab -->
            <div id="vector-storage" class="tab-content">
                <div class="section">
                    <div class="section-header">
                        <h2><i class="fas fa-database"></i> Vector Storage Management</h2>
                    </div>

                    <!-- Storage Overview -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Storage Overview</h3>
                        </div>
                        <div class="card-content">
                            <div class="storage-stats" id="storageStats">
                                <!-- Storage statistics will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Dimension Breakdown -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Embeddings by Dimension</h3>
                        </div>
                        <div class="card-content">
                            <div class="dimension-breakdown" id="dimensionBreakdown">
                                <!-- Dimension statistics will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Configuration Tab -->
            <div id="configuration" class="tab-content">
                <div class="section">
                    <div class="section-header">
                        <h2><i class="fas fa-cog"></i> System Configuration</h2>
                        <div class="section-actions">
                            <button class="btn btn-secondary" onclick="exportConfig()">
                                <i class="fas fa-download"></i> Export Config
                            </button>
                        </div>
                    </div>

                    <!-- Deployment Profile -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Deployment Profile</h3>
                        </div>
                        <div class="card-content">
                            <div class="config-form" id="deploymentConfig">
                                <!-- Configuration form will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Pipeline Configuration -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Pipeline Settings</h3>
                        </div>
                        <div class="card-content">
                            <div class="pipeline-config" id="pipelineConfig">
                                <!-- Pipeline configuration will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div id="modalOverlay" class="modal-overlay" onclick="closeModal()">
        <div class="modal" onclick="event.stopPropagation()">
            <div class="modal-header">
                <h3 id="modalTitle">Modal Title</h3>
                <button class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content" id="modalContent">
                <!-- Dynamic modal content -->
            </div>
            <div class="modal-footer" id="modalFooter">
                <!-- Dynamic modal footer -->
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container">
        <!-- Toast notifications will appear here -->
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/components.js"></script>
    <script src="js/main.js"></script>
</body>
</html>