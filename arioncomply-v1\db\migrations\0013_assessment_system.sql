-- File: arioncomply-v1/db/migrations/0013_assessment_system.sql
-- Migration 0013: Assessment System
-- Purpose: Chat-driven compliance assessment infrastructure
-- Author: File Header Style Guide compliant
-- Date: 2025-01-08
-- Dependencies: Requires 0002_org_and_profiles.sql, 0003_conversation_sessions_messages.sql
-- Integration: Works with existing conversation system and RLS policies

BEGIN;

-- Assessment conversations: Track chat-driven compliance assessment sessions
CREATE TABLE IF NOT EXISTS assessment_conversations (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id uuid NOT NULL REFERENCES orgs(id) ON DELETE CASCADE,
    user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    session_id uuid, -- Optional link to existing conversation_sessions if available
    framework_type text NOT NULL, -- 'iso27001', 'gdpr', 'euaiact', 'nis2', 'soc2', etc.
    assessment_title text,
    status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused', 'cancelled')),
    chat_history jsonb DEFAULT '[]'::jsonb, -- Store conversation messages for analysis
    metadata jsonb DEFAULT '{}'::jsonb, -- Additional context (industry, company size, etc.)
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    completed_at timestamptz
);

-- Assessment insights: AI-extracted compliance insights from conversations
CREATE TABLE IF NOT EXISTS assessment_insights (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id uuid NOT NULL REFERENCES assessment_conversations(id) ON DELETE CASCADE,
    insight_type text NOT NULL, -- 'gap_identified', 'control_present', 'risk_area', 'recommendation'
    confidence_score decimal(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    extracted_data jsonb NOT NULL, -- Structured insight data
    source_messages text[], -- Array of message IDs or content that led to this insight
    framework_section text, -- Specific section/control this relates to
    timestamp timestamptz NOT NULL DEFAULT now()
);

-- Assessment scores: Calculated compliance scores and analysis
CREATE TABLE IF NOT EXISTS assessment_scores (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id uuid NOT NULL REFERENCES assessment_conversations(id) ON DELETE CASCADE,
    total_score decimal(5,2), -- Overall compliance score (0-100)
    framework_scores jsonb DEFAULT '{}'::jsonb, -- Scores by framework section
    maturity_level text, -- 'initial', 'managed', 'defined', 'quantitatively_managed', 'optimizing'
    analysis_summary text, -- Human-readable summary of assessment
    recommendations jsonb DEFAULT '[]'::jsonb, -- Array of recommendation objects
    gaps_identified jsonb DEFAULT '[]'::jsonb, -- Array of identified gaps
    strengths_identified jsonb DEFAULT '[]'::jsonb, -- Array of identified strengths
    calculated_at timestamptz NOT NULL DEFAULT now(),
    version integer DEFAULT 1 -- Allow for score recalculation
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_assessment_conversations_org ON assessment_conversations (org_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_assessment_conversations_user ON assessment_conversations (user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_assessment_conversations_framework ON assessment_conversations (framework_type, status);
CREATE INDEX IF NOT EXISTS idx_assessment_conversations_status ON assessment_conversations (status, updated_at DESC);

CREATE INDEX IF NOT EXISTS idx_assessment_insights_conversation ON assessment_insights (conversation_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_assessment_insights_type ON assessment_insights (insight_type, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_assessment_insights_confidence ON assessment_insights (confidence_score DESC);

CREATE INDEX IF NOT EXISTS idx_assessment_scores_conversation ON assessment_scores (conversation_id, version DESC);
CREATE INDEX IF NOT EXISTS idx_assessment_scores_total ON assessment_scores (total_score DESC);
CREATE INDEX IF NOT EXISTS idx_assessment_scores_calculated ON assessment_scores (calculated_at DESC);

-- Enable RLS on all tables
ALTER TABLE assessment_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_scores ENABLE ROW LEVEL SECURITY;

-- RLS Policies for assessment_conversations
CREATE POLICY "Users can access own org's assessment conversations" ON assessment_conversations
    FOR ALL USING (org_id = get_user_org_id());

CREATE POLICY "Users can insert assessment conversations for own org" ON assessment_conversations
    FOR INSERT WITH CHECK (org_id = get_user_org_id());

-- RLS Policies for assessment_insights
CREATE POLICY "Users can access insights from own org's assessments" ON assessment_insights
    FOR ALL USING (
        conversation_id IN (
            SELECT id FROM assessment_conversations 
            WHERE org_id = get_user_org_id()
        )
    );

CREATE POLICY "Users can insert insights for own org's assessments" ON assessment_insights
    FOR INSERT WITH CHECK (
        conversation_id IN (
            SELECT id FROM assessment_conversations 
            WHERE org_id = get_user_org_id()
        )
    );

-- RLS Policies for assessment_scores
CREATE POLICY "Users can access scores from own org's assessments" ON assessment_scores
    FOR ALL USING (
        conversation_id IN (
            SELECT id FROM assessment_conversations 
            WHERE org_id = get_user_org_id()
        )
    );

CREATE POLICY "Users can insert scores for own org's assessments" ON assessment_scores
    FOR INSERT WITH CHECK (
        conversation_id IN (
            SELECT id FROM assessment_conversations 
            WHERE org_id = get_user_org_id()
        )
    );

-- Update trigger for assessment_conversations
CREATE OR REPLACE FUNCTION update_assessment_conversation_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_assessment_conversation_updated_at
    BEFORE UPDATE ON assessment_conversations
    FOR EACH ROW
    EXECUTE FUNCTION update_assessment_conversation_updated_at();

COMMIT;
-- File: arioncomply-v1/db/migrations/0013_assessment_system.sql
