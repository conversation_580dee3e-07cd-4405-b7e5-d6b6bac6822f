# Data Compliance and Anonymization (Placeholder)

Decisions
- Any payload sent to GLLMs (OpenAI/Claude) must be anonymized.
- Logs must avoid sensitive payloads; truncate or hash large bodies; store references.
- Retention policies and SAR exportability will be documented per phase.

Anonymization Strategy
- Detection: lightweight entity detection (names, emails, phones, IDs) + deterministic mapping table per `org_id`/session.
- Techniques: pseudonymization (e.g., `USER_123`), hashing (for joins), selective redaction.
- Rehydration: Only for UI display inside tenant context; never rehydrate in GLLM calls.

Edge Logging Hygiene
- Do not log full prompts/responses by default; store digests and token counts.
- `api_event_logs.details` should include: model, input_len, output_len, cost, redaction_flags; not raw text.

Open Questions
- PII taxonomy scope; region-specific constraints (e.g., GDPR/CCPA/CPRA).
- Retention windows by event type; deletion workflows.

Next Steps
- Implement anonymizer utility and mapping store keyed by `org_id` and `requestId`.
- Add `ai_call` event schema capturing usage metrics but not payloads.
- Write redaction tests against sample data.

