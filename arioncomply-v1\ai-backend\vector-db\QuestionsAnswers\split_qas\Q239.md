id: Q239
query: >-
  What if we need to shut down systems or services for compliance reasons?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/8.1"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Operational Controls"
    id: "ISO27001:2022/8.1"
    locator: "Clause 8.1"
ui:
  cards_hint:
    - "Shutdown playbook"
  actions:
    - type: "start_workflow"
      target: "service_shutdown"
      label: "Initiate Shutdown"
    - type: "open_register"
      target: "shutdown_log"
      label: "Log Actions"
output_mode: "both"
graph_required: false
notes: "Coordinate with BCP to maintain critical services"
---
### 239) What if we need to shut down systems or services for compliance reasons?

**Standard terms)**  
- **Operational controls (Cl.8.1):** implement and manage processes.

**Plain-English answer**  
Follow a **Shutdown Playbook**: notify users, preserve logs, switch to fallback services, and document start/stop times in the **Shutdown Log**. Then run forensic checks or apply urgent patches before restart.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 8.1

**Why it matters**  
Ensures controlled shutdowns limit data loss and service disruption.

**Do next in our platform**  
- Invoke **Service Shutdown** workflow.  
- Update **Shutdown Log** with timestamps and actions.

**How our platform will help**  
- **[Workflow]** Guides notification, shutdown, and restart steps.  
- **[Report]** Tracks downtime and recovery metrics.

**Likely follow-ups**  
- How to communicate planned vs. unplanned shutdowns?

**Sources**  
- ISO/IEC 27001:2022 Clause 8.1
