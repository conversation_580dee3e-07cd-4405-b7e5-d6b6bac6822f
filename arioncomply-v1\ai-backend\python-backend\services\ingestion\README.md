<!-- File: arioncomply-v1/ai-backend/python-backend/services/ingestion/README.md -->
# Ingestion Pipeline (Skeleton)

Stages
- Extract: parse documents (PDF/DOCX/Markdown) to structured text (Locling or placeholder).
- Chunk: split into ~500–800 token windows with overlap (15–20%), attach section/heading/page.
- Embed: generate embeddings (e.g., bge-small/e5-small) → 768-dim vector list[float].
- Insert: upsert `documents`, then `chunks`, then `embeddings` in the vector project (org-scoped), with provenance.
- Graph hints: collect framework/control IDs and action hints; persist in chunk metadata; later used for graph hops and suggestions.

Metadata keys (chunk.metadata)
- section, heading, page
- framework_ids: string[] (e.g., ["ISO27001:A.12.1.2"]) 
- control_ids: string[]
- actions: { type: "start_workflow"|"open_register"|"draft_doc", slug: string }[]

CLI (planned)
- ingest_one: run pipeline on a single file with `--org` and `--source`.
- ingest_dir: run batch on a directory.

Notes
- This is a stub for wiring. Replace placeholder extract/embed implementations with real providers.
- Use `VECTOR_SUPABASE_URL` and `VECTOR_SUPABASE_SERVICE_KEY` for vector writes; org_id required.
