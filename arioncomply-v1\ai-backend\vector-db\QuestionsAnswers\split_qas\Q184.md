id: Q184
query: >-
  How do we audit cloud providers if we can't visit their data centers?
packs:
  - "ISO27017:2015"
  - "ISO27001:2022"
primary_ids:
  - "ISO27017:2015/8.2"
  - "ISO27001:2022/A.15.2.1"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27017:2015 — Code of Practice for Cloud Controls"
    id: "ISO27017:2015/8.2"
    locator: "Section 8.2 (Monitoring)"
  - title: "ISO/IEC 27001:2022 — Supplier Service Monitoring"
    id: "ISO27001:2022/A.15.2.1"
    locator: "Annex A.15.2.1"
ui:
  cards_hint:
    - "Virtual audit plan"
  actions:
    - type: "start_workflow"
      target: "cloud_audit"
      label: "Initiate Virtual Audit"
    - type: "open_register"
      target: "audit_evidence"
      label: "View Audit Evidence"
output_mode: "both"
graph_required: false
notes: "Leverage SOC 2/ISO27017 reports, remote walkthroughs, and secure log sharing"
---
### 184) How do we audit cloud providers if we can't visit their data centers?

**Standard terms)**  
- **Cloud monitoring (ISO27017 §8.2):** guidelines for remote oversight.  
- **Supplier monitoring (ISO27001 A.15.2.1):** continuous evidence collection.

**Plain-English answer**  
Rely on independent audit reports (SOC 2, ISO 27001/27017), request access to control dashboards, schedule virtual walkthroughs via screen-share, and collect logs through secure portals.

**Applies to**  
- **Primary:** ISO/IEC 27017:2015 §8.2; ISO/IEC 27001:2022 Annex A.15.2.1

**Why it matters**  
Maintains assurance without physical access, reducing travel costs and delays.

**Do next in our platform**  
- Trigger **Cloud Audit** workflow.  
- Gather and tag evidence in the **Audit Evidence** register.

**How our platform will help**  
- **[Workflow]** Guided virtual audit steps.  
- **[Report]** Real-time evidence dashboard.

**Likely follow-ups**  
- What if the provider declines remote audits?  
- Can we automate log retrieval?

**Sources**  
- ISO/IEC 27017:2015 §8.2; ISO/IEC 27001:2022 Annex A.15.2.1  
