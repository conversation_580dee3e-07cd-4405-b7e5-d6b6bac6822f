<!-- File: arioncomply-v1/testing/workflow-gui/workflowGUI-Manual.md -->
# ArionComply Workflow GUI - User Manual

## Overview

The ArionComply Workflow GUI is a comprehensive testing interface that validates the complete ArionComply architecture stack. It provides direct testing access to production endpoints while isolating tests from the main application UI.

## System Components Tested

### AI Backend (Python FastAPI)
**Location**: `ai-backend/python-backend/`
**Components Tested**:
- **FastAPI Application** (`app/main.py`): `/ai/chat` endpoint with full request/response cycle
- **Router Service** (`services/router.py`): Business logic orchestration and preprocessing integration
- **Query Preprocessor** (`services/preprocessing/query_preprocessor.py`): 6-stage deterministic pipeline
- **Graph Crawler** (`services/preprocessing/graph_crawler.py`): Multi-hop weighted traversal
- **Mapping Generator** (`services/preprocessing/mapping_generator.py`): Synonym/paraphrase generation
- **Vector Retrieval** (`services/retrieval/supabase_vector.py`): Supabase Vector RPC calls
- **Event Logging** (`services/logging/events.py`): Request/response event persistence

### Supabase Edge Functions
**Location**: `supabase/functions/`
**Functions Tested**:
- **ai-conversation-start**: Session initialization, context validation, first message generation
- **ai-conversation-send**: Message routing through assistant_router.ts to AI backend
- **ai-conversation-stream**: Server-Sent Events streaming implementation
- **compliance-proxy**: Direct provider calls (OpenAI/Anthropic) with parameter passing
- **ai-message-feedback**: User feedback collection and storage
- **ui-suggestions-defaults**: Dynamic suggestion loading
- **ui-shortcuts**: Slash command functionality

### Database Operations
**Supabase Database Tables**:
- **Conversation Sessions**: Session creation, retrieval, and state management
- **Message History**: User/assistant message storage with metadata
- **Event Logs**: Request tracing, preprocessing results, deterministic matches
- **User Feedback**: Rating storage with optional comments
- **Vector Storage**: Document chunks with embeddings and metadata (via Supabase Vector)

### Assistant Router Integration
**Location**: `supabase/functions/_shared/assistant_router.ts`
**Routing Logic Tested**:
- **Header Processing**: `x-output-mode`, `x-pipeline-mode`, `x-framework-hint` routing
- **Backend Forwarding**: AI_BACKEND_URL request forwarding with timeout handling
- **Fallback Behavior**: Echo responses when backend unavailable
- **Response Normalization**: Backend response transformation for Edge consumption

### Vector Database Operations
**Supabase Vector Integration**:
- **Hybrid Retrieval**: ChromaDB (local) + Supabase Vector (production) support
- **RLS (Row Level Security)**: Multi-tenant document isolation
- **Metadata Filtering**: Framework-specific document retrieval
- **Embedding Search**: Vector similarity queries with confidence scoring

## Getting Started

### Starting the GUI
```bash
bash ./start-server.sh
```
- Opens automatically at `http://localhost:10000`
- To stop: `bash ./stop-server.sh`

### Basic Configuration
1. Navigate to the **Config** tab
2. Set your **Supabase URL** (e.g., `http://localhost:54321` for local development)
3. Add **Anon Key** if authentication is required
4. Configure optional headers for advanced routing

## Tab Functions

### 1. Config Tab
**Purpose**: Configure connection settings and routing headers

**Key Settings**:
- **Supabase URL**: Backend endpoint URL
- **Anon Key**: Authentication token (optional)
- **x-provider-order**: Provider preference order (e.g., `sllm,openai,anthropic`)
- **x-allow-gllm**: Enable/disable GLLM providers (`true`/`false`)
- **compliance-chain-id**: Explainability chain identifier
- **decision-context**: Context for compliance decisions

**Actions**:
- **Save Config**: Persists settings in localStorage
- **Toggle Theme**: Switch between light/dark mode

### 2. Conversation Tab
**Purpose**: Test complete conversation flow through Edge Functions → AI Backend → Database

**Database Operations Tested**:
- **Session Table**: INSERT new conversation sessions with metadata
- **Message Table**: INSERT/SELECT user and assistant messages
- **Event Table**: INSERT request/response events for tracing

**Edge Functions Tested**:
- **ai-conversation-start**: Tests session creation, context validation, database writes
- **ai-conversation-send**: Tests message routing, assistant_router integration, response formatting
- **ai-conversation-stream**: Tests SSE streaming implementation, progressive token delivery
- **ai-message-feedback**: Tests feedback storage with session/message correlation

**AI Backend Integration Tested**:
- **FastAPI /ai/chat endpoint**: Full request/response cycle validation
- **Router service**: Message processing, hints/explainability parameter passing
- **Preprocessing pipeline**: Optional deterministic query resolution
- **Event logging**: Backend event emission and Edge Function event storage

**Features**:
- **Start Conversation**: Database session creation + optional context JSON validation
- **Send Message**: Complete Edge→Backend→Database round-trip with response parsing
- **Stream Message**: SSE event parsing with real-time token accumulation
- **Suggestion Chips**: Dynamic suggestion loading from ui-suggestions-defaults endpoint
- **Feedback System**: Message rating storage with optional comment persistence

**Workflow Validates**:
1. Session creation in database with proper metadata
2. Message routing through assistant_router.ts to AI backend
3. Backend preprocessing pipeline execution (if configured)
4. Response formatting and suggestion generation
5. Event logging across all components
6. Feedback collection and database storage

### 3. Compare Tab
**Purpose**: Test compliance-proxy Edge Function with direct provider API integration

**Edge Function Tested**:
- **compliance-proxy**: Direct OpenAI/Anthropic API calls bypassing AI backend
- **Parameter Handling**: Temperature, max_tokens, system prompts per provider
- **Provider Routing**: Single provider vs parallel "both" mode execution
- **Response Normalization**: Provider-specific response format standardization

**API Integration Tested**:
- **OpenAI API**: Direct GPT model calls with parameter validation
- **Anthropic API**: Direct Claude model calls with parameter validation  
- **Error Handling**: Provider-specific timeout and error responses
- **Parallel Execution**: Concurrent API calls with Promise.all coordination

**No Database/Backend Dependencies**:
- Bypasses AI backend preprocessing pipeline
- No session or message storage
- Direct provider API testing only
- Isolated provider performance comparison

**Features**:
- **System Prompt**: Tests provider-specific system message handling
- **Temperature Controls**: Validates parameter passing to each provider API
- **Max Tokens**: Tests token limit enforcement per provider
- **Individual Testing**: Single provider API call validation
- **Parallel Comparison**: Concurrent dual-provider API testing

**Validates**:
- compliance-proxy function parameter processing
- Provider API key configuration and authentication
- Response time and error handling differences between providers
- Output format consistency across OpenAI/Anthropic APIs

### 4. Arena Tab
**Purpose**: Blind A/B testing with randomized provider assignment

**Features**:
- **Randomized Mapping**: Providers assigned to "Model A" and "Model B" randomly
- **Voting System**: Vote for A, B, or Tie/Skip
- **Statistics Tracking**: Win/loss ratios persisted locally
- **Provider Reveal**: Show which provider was A vs B after voting
- **Independent Parameters**: Separate temperature/token settings

**Arena Workflow**:
1. Enter system prompt and question
2. Click **Run Arena** - providers randomly assigned to A/B
3. Review both responses without knowing which is which
4. Vote for preferred response (A/B/Tie)
5. Click **Reveal Models** to see provider mapping
6. Statistics automatically tracked

### 5. Backend Features Tab
**Purpose**: Test AI Backend advanced preprocessing pipeline and output formatting

**AI Backend Components Tested**:
- **Query Preprocessor** (`services/preprocessing/query_preprocessor.py`): Full 6-stage pipeline execution
- **Router Service** (`services/router.py`): Output mode handling and response formatting
- **Vector Retrieval** (`services/retrieval/supabase_vector.py`): Evidence gathering with metadata
- **Event Logging** (`services/logging/events.py`): Pipeline execution event recording

**Database Operations Tested**:
- **Vector Storage**: Supabase Vector queries with RLS and metadata filtering
- **Event Logs**: Preprocessing stage execution tracking and deterministic match recording
- **Framework Filtering**: Document retrieval filtered by compliance framework

**Edge Function Integration Tested**:
- **ai-conversation-send**: Custom header processing (`x-output-mode`, `x-pipeline-mode`, `x-framework-hint`)
- **assistant_router.ts**: Header forwarding to AI backend with hint parameter mapping
- **Response Processing**: Backend response field pass-through (evidence, cards_hint, preprocessing)

**Output Mode Testing** (Tests router.py response formatting):
- **Cards Mode**: `evidence[]` array formatting with confidence scores and canonical IDs
- **Prose Mode**: `text` field with optional `typing_simulation` flag
- **Both Mode**: Combined evidence + text response validation

**Pipeline Configuration Testing**:
- **Pipeline Mode**: `preprocessing`/`retrieval`/`hybrid` routing through backend
- **Framework Hint**: Metadata filtering in vector queries (ISO27001, GDPR, etc.)
- **Deterministic Resolution**: Bypass LLM when exact matches found in preprocessing

**Validates Complete Stack**:
1. **Frontend Headers** → **Edge Functions** → **AI Backend** → **Vector DB** → **Response Processing**
2. Preprocessing pipeline execution with all 6 stages
3. Evidence structuring with graph path metadata
4. Framework-specific document filtering
5. Deterministic vs LLM routing decisions
6. Response format consistency across output modes

### 6. Debug Pipeline Tab
**Purpose**: Deep debugging of AI Backend preprocessing pipeline with detailed execution tracing

**AI Backend Pipeline Components Tested**:
- **Canonical ID Matching** (`query_preprocessor.py:_check_canonical_id_match`): Pattern recognition and direct lookup
- **Synonym Expansion** (`query_preprocessor.py:_expand_synonyms`): Alternative terminology mapping
- **Paraphrase Matching** (`query_preprocessor.py:_match_paraphrases`): Semantic variation detection
- **E-PMI Associations** (`query_preprocessor.py:_calculate_e_pmi_associations`): Statistical relationship scoring
- **Graph Crawling** (`graph_crawler.py:crawl_from_seeds`): Multi-hop weighted traversal with decay factors
- **RAG Preparation** (`query_preprocessor.py:_prepare_for_rag`): Vector query enhancement

**Database Components Tested**:
- **Canonical ID Index**: Direct lookup table for exact pattern matches
- **Synonym Mappings**: Alternative terminology database with framework specificity
- **Paraphrase Index**: Semantic variation mappings with confidence scores
- **E-PMI Associations**: Statistical co-occurrence relationships between terms
- **Graph Structure**: Node relationships with weighted edges and hop distances
- **Vector Storage**: Document embeddings with metadata filtering

**Event Logging Tested**:
- **Pipeline Stage Events**: Each stage execution with timing and results
- **Deterministic Match Events**: Bypass decisions with confidence scores
- **Graph Traversal Events**: Path exploration with decay factors (1.0→0.7→0.4)
- **Query Enhancement Events**: Original vs enhanced query comparison

#### Full Pipeline Testing
- **Complete 6-Stage Execution**: Tests entire preprocessing pipeline end-to-end
- **Stage Bypass Logic**: Validates early termination when deterministic matches found  
- **Query Enhancement Tracking**: Original query vs pipeline-enhanced query comparison
- **Performance Metrics**: Execution time per stage and total preprocessing duration

#### Canonical ID Testing
- **Pattern Recognition**: Tests regex validation for formats like `ISO27001:2022/A.5.1`
- **Direct Database Lookup**: Validates canonical ID index queries with exact matches
- **Confidence Scoring**: Tests scoring algorithm for pattern match certainty (0.0-1.0)
- **Framework Filtering**: Tests ID lookup within specific compliance framework contexts

#### Advanced Debugging Features
- **Stage-by-Stage Execution**: Individual stage results with intermediate data structures
- **Graph Path Visualization**: Multi-hop traversal paths with weighted edge scores
- **Evidence Provenance**: Source tracking from graph nodes to final evidence items
- **Raw Backend Response**: Complete JSON response for technical analysis

**Validates Deep Integration**:
1. **Pipeline Stage Coordination**: Sequential execution with proper data passing
2. **Database Index Performance**: Query execution times and result accuracy
3. **Graph Algorithm Implementation**: Weighted traversal with proper decay factors
4. **Deterministic vs Probabilistic Routing**: Decision logic for LLM bypass
5. **Event Logging Completeness**: All pipeline activities recorded for audit
6. **Framework-Specific Filtering**: Compliance domain isolation in all stages

## Backend Integration

### Preprocessing Pipeline (6 Stages)
1. **Canonical ID Matching**: Direct pattern matching (e.g., `ISO27001:2022/A.5.1`)
2. **Synonym Expansion**: Alternative terminology lookup
3. **Paraphrase Matching**: Semantic variations
4. **E-PMI Associations**: Enhanced Pointwise Mutual Information
5. **Graph Crawling**: Multi-hop traversal with weighted scoring
6. **RAG Preparation**: Final vector retrieval setup

### Deterministic Resolution
- Queries that match deterministically bypass LLM processing
- Returns structured evidence with high confidence scores
- Reduces latency and computational cost for exact matches

### Evidence Structure
```json
{
  "id": "ISO27001:2022/A.5.1",
  "type": "deterministic_match",
  "title": "Information Security Policies",
  "content": "Policy description...",
  "confidence": 0.95,
  "graph_paths": [{"hops": 1, "score": 0.85}]
}
```

## Troubleshooting

### Common Issues
- **Tabs not working**: Ensure JavaScript is enabled and no console errors
- **API errors**: Verify Supabase URL and check browser console
- **Empty responses**: Backend may not be configured; GUI includes simulation mode
- **CORS issues**: Ensure Edge functions are deployed and accessible

### Simulation Mode
When backend is unavailable, GUI provides simulated responses for:
- Cards/Prose output testing
- Preprocessing pipeline results
- Deterministic matching examples

### Debug Console
Press F12 → Console for detailed debugging information including:
- Element references
- API request/response data  
- Error messages
- Preprocessing pipeline execution

## Development Notes

### Local Development
- GUI runs independently of main ArionComply application
- Uses production endpoint contracts
- Safe for testing without affecting production data
- Supports both local (`localhost:54321`) and deployed Supabase instances

### Configuration Persistence
- Settings saved in localStorage as `ac_workflow_gui`
- Arena statistics saved as `ac_arena_stats`
- Theme preference persisted across sessions

### Extension Points
- Additional tabs can be added to `index.html` and `app.js`
- New backend features automatically supported via pass-through design
- Custom headers easily added to `authHeaders()` function

## API Endpoints Used

- `${supabaseUrl}/functions/v1/ai-conversation-start`
- `${supabaseUrl}/functions/v1/ai-conversation-send`  
- `${supabaseUrl}/functions/v1/ai-conversation-stream`
- `${supabaseUrl}/functions/v1/compliance-proxy`
- `${supabaseUrl}/functions/v1/ai-message-feedback`
- `${supabaseUrl}/functions/v1/ui-suggestions-defaults`
- `${supabaseUrl}/functions/v1/ui-shortcuts`

This manual covers all current GUI functionality. The interface is designed to grow with the ArionComply backend capabilities while maintaining ease of use for testing and development.
<!-- File: arioncomply-v1/testing/workflow-gui/workflowGUI-Manual.md -->
