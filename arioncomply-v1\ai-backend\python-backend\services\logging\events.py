# File: arioncomply-v1/ai-backend/python-backend/services/logging/events.py
"""
File Description: Backend event logging client (skeleton)
Purpose: Persist backend events to Supabase logs (api_event_logs) or via Edge gateway
Inputs: Meta (request_id, org_id, user_id, session_id, traceparent), event payload
Outputs: None (fire-and-forget); logs failures to stderr
Security/RLS: Uses service-role key for direct Supabase REST; ensure org_id is always set
Notes: Choose transport via env: AC_LOG_TRANSPORT=direct|edge (default direct)
"""

import json
import os
import time
from typing import Any, Dict, Optional

import requests


SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
LOG_TRANSPORT = os.getenv("AC_LOG_TRANSPORT", "direct")  # or "edge"
EDGE_LOG_URL = os.getenv("EDGE_LOG_URL")  # if using edge gateway transport
SYSTEM_ORG_ID = os.getenv("SYSTEM_ORG_ID")  # canonical org for global/internal logs


def _post_direct(table: str, row: Dict[str, Any]) -> None:
    """POST a single row to Supabase REST for the given table.

    Uses service-role key; best-effort with short timeouts and console diagnostics.
    """
    if not SUPABASE_URL or not SUPABASE_SERVICE_ROLE_KEY:
        print("[log] missing Supabase env; cannot write logs", flush=True)
        return
    url = f"{SUPABASE_URL}/rest/v1/{table}"
    headers = {
        "apikey": SUPABASE_SERVICE_ROLE_KEY,
        "Authorization": f"Bearer {SUPABASE_SERVICE_ROLE_KEY}",
        "Content-Type": "application/json",
        "Prefer": "return=minimal",
    }
    try:
        r = requests.post(url, headers=headers, data=json.dumps(row), timeout=3)
        if r.status_code >= 300:
            print(f"[log] direct insert failed: {r.status_code} {r.text}", flush=True)
    except Exception as e:
        print(f"[log] direct insert exception: {e}", flush=True)


def log_event(
    *,
    request_id: str,
    org_id: Optional[str],
    user_id: Optional[str],
    event_type: str,
    direction: str,
    details: Dict[str, Any],
    session_id: Optional[str] = None,
    trace_id: Optional[str] = None,
) -> None:
    """Insert an application event log with resolved org_id and trace context.

    Ensures internal events can use SYSTEM_ORG_ID when no org is acting.
    Skips insert if org_id cannot be resolved.
    """
    # Resolve org for internal/global jobs when not acting on a specific org.
    # DB enforces NOT NULL org_id; use SYSTEM_ORG_ID if provided.
    if not org_id and direction == "internal" and SYSTEM_ORG_ID:
        org_id = SYSTEM_ORG_ID
    if not org_id:
        print("[log] missing org_id and no SYSTEM_ORG_ID; skipping event", flush=True)
        return
    payload = {
        "request_id": request_id,
        "org_id": org_id,
        "user_id": user_id,
        "event_type": event_type,
        "direction": direction,
        "details": details,
        "session_id": session_id,
        "trace_id": trace_id,
    }
    if LOG_TRANSPORT == "edge" and EDGE_LOG_URL:
        try:
            r = requests.post(EDGE_LOG_URL, json=payload, timeout=3)
            if r.status_code >= 300:
                print(f"[log] edge logging failed: {r.status_code} {r.text}", flush=True)
        except Exception as e:
            print(f"[log] edge logging exception: {e}", flush=True)
    else:
        _post_direct("api_event_logs", payload)
