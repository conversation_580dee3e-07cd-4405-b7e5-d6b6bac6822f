id: Q057
query: >-
  How long do we have to fix issues if the auditor finds problems?
packs:
  - "ISO17021-1:2015"
  - "ISO27001:2022"
primary_ids:
  - "ISO17021-1:2015/8.3"
  - "ISO27001:2022/10.2"
overlap_ids: []
capability_tags:
  - "Reminder"
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 17021-1:2015 — Auditor Action on NCs"
    id: "ISO17021-1:2015/8.3"
    locator: "Clause 8.3"
  - title: "ISO/IEC 27001:2022 — Corrective Action"
    id: "ISO27001:2022/10.2"
    locator: "Clause 10.2"
ui:
  cards_hint:
    - "NC closure timeline"
  actions:
    - type: "start_workflow"
      target: "nc_closure"
      label: "Plan NC Closure"
    - type: "open_register"
      target: "nonconformities"
      label: "View NCs"
output_mode: "both"
graph_required: false
notes: "Typically 90 days for CAP closure, but check CB report"
---
### 57) How long do we have to fix issues if the auditor finds problems?

**Standard terms**  
- **Auditor actions (ISO 17021-1 Cl. 8.3):** sets CAP deadlines.  
- **Corrective action (ISO 27001 Cl. 10.2):** defines CAP process.

**Plain-English answer**  
Auditors typically allow **up to 90 days** to close nonconformities, though minor NCs might be shorter. The CB’s audit report specifies your exact deadline.

**Applies to**  
- **Primary:** ISO/IEC 17021-1:2015 Clause 8.3; ISO/IEC 27001:2022 Clause 10.2

**Why it matters**  
Meeting deadlines avoids losing your certification.

**Do next in our platform**  
- Set CAP due-date reminders.  
- Assign owners and track progress.

**How our platform will help**  
- **[Reminder]** CAP deadline alerts.  
- **[Workflow]** NC closure pipeline.

**Likely follow-ups**  
- “Can we request extensions?” (Subject to CB approval)

**Sources**  
- ISO/IEC 17021-1:2015 Clause 8.3  
- ISO/IEC 27001:2022 Clause 10.2
