// File: arioncomply-v1/testing/workflow-gui/app.js
// File Description: Interactive client-side test harness with trace graph visualization and audit log streaming
// Purpose: Exercise ArionComply Edge endpoints, audit logs, and backend workflows with real-time debugging tools
// Inputs: User configuration (Supabase URL, API keys), form submissions, audit log filters, trace parameters
// Outputs: Live audit logs, interactive Cytoscape.js trace graphs, request/response visualization, workflow debugging
// Dependencies: Cytoscape.js, dagre layout algorithm, localStorage for configuration persistence, modern fetch API
// Security/RLS: Client-side testing tool, localStorage for config only, no sensitive data persistence or transmission
// Notes: Enhanced with requestId/sessionId filtering, interactive trace visualization, real-time audit streaming, workflow debugging

const els = {
  supabaseUrl: () => document.getElementById('supabaseUrl'),
  anonKey: () => document.getElementById('anonKey'),
  providerOrder: () => document.getElementById('providerOrder'),
  allowGllm: () => document.getElementById('allowGllm'),
  chainId: () => document.getElementById('chainId'),
  decisionContext: () => document.getElementById('decisionContext'),
  title: () => document.getElementById('title'),
  context: () => document.getElementById('context'),
  message: () => document.getElementById('message'),
  sessionId: () => document.getElementById('sessionId'),
  reqId: () => document.getElementById('reqId'),
  traceparent: () => document.getElementById('traceparent'),
  userOut: () => document.getElementById('userOut'),
  assistantOut: () => document.getElementById('assistantOut'),
  suggestions: () => document.getElementById('suggestions'),
  suggestionChips: () => document.getElementById('suggestionChips'),
  // compare
  systemPrompt: () => document.getElementById('systemPrompt'),
  compareQuestion: () => document.getElementById('compareQuestion'),
  openaiTemp: () => document.getElementById('openaiTemp'),
  claudeTemp: () => document.getElementById('claudeTemp'),
  maxTokens: () => document.getElementById('maxTokens'),
  gptOut: () => document.getElementById('gptOut'),
  claudeOut: () => document.getElementById('claudeOut'),
  // arena
  arenaSystemPrompt: () => document.getElementById('arenaSystemPrompt'),
  arenaQuestion: () => document.getElementById('arenaQuestion'),
  arenaOpenaiTemp: () => document.getElementById('arenaOpenaiTemp'),
  arenaClaudeTemp: () => document.getElementById('arenaClaudeTemp'),
  arenaMaxTokens: () => document.getElementById('arenaMaxTokens'),
  arenaAOut: () => document.getElementById('arenaAOut'),
  arenaBOut: () => document.getElementById('arenaBOut'),
  arenaMapping: () => document.getElementById('arenaMapping'),
  arenaStats: () => document.getElementById('arenaStats'),
};

// Persist/load config
let defaultSuggestions = [
  { text: 'Start an assessment', action: { type: 'nav', target: 'assessment' } },
  { text: 'What is ISO 27001 and where do I start?', action: { type: 'nav', target: 'standards/iso27001' } },
  { text: 'Show recommended next steps for my organization' },
  { text: 'Explore compliance registers', action: { type: 'nav', target: 'registers' } },
  // secondary fallbacks if needed
  { text: 'Show related documents', action: { type: 'nav', target: 'documents' } },
  { text: 'Explain your reasoning' },
  { text: 'Summarize the key points' },
  { text: 'What is GDPR (overview)?', action: { type: 'nav', target: 'standards/gdpr' } },
];

async function loadDefaultSuggestions() {
  try {
    const res = await fetch(`${baseUrl()}/functions/v1/ui-suggestions-defaults`, { headers: authHeaders() });
    if (!res.ok) throw new Error(await res.text());
    const data = await res.json();
    if (Array.isArray(data?.suggestions) && data.suggestions.length) {
      defaultSuggestions = data.suggestions;
    }
  } catch (e) {
    console.warn('Using built-in default suggestions:', e?.message || e);
  }
}

function renderSuggestions(suggestions) {
  try {
    const container = els.suggestionChips();
    if (!container) return;
    container.innerHTML = '';
    const list = (Array.isArray(suggestions) ? suggestions : []).slice(0, 4);
    list.forEach((s) => {
      const btn = document.createElement('button');
      btn.textContent = s?.text || 'Suggestion';
      btn.style.marginRight = '6px';
      btn.onclick = () => {
        const href = s?.action?.url;
        if (href) {
          window.location.href = href;
          return;
        }
        // For now, prefill the message box; we can switch to auto-send later
        const text = s?.text || '';
        els.message().value = text;
      };
      container.appendChild(btn);
    });
  } catch (e) {
    console.warn('renderSuggestions error', e);
  }
}

window.addEventListener('load', () => {
  const saved = JSON.parse(localStorage.getItem('ac_workflow_gui') || '{}');
  if (saved.supabaseUrl) els.supabaseUrl().value = saved.supabaseUrl;
  if (saved.anonKey) els.anonKey().value = saved.anonKey;
  if (saved.providerOrder) els.providerOrder().value = saved.providerOrder;
  if (typeof saved.allowGllm !== 'undefined') els.allowGllm().value = saved.allowGllm;
  if (saved.chainId) els.chainId().value = saved.chainId;
  if (saved.decisionContext) els.decisionContext().value = saved.decisionContext;
  // Theme
  const theme = saved.theme || 'light';
  document.body.setAttribute('data-theme', theme);
  // Arena stats and UI init
  try { updateArenaStats(); } catch { }
  try { initAutosize(); } catch { }
  try { initTabs(); } catch { }
  // Suggestions: render built-in first, then try loading from endpoint
  try { renderSuggestions(defaultSuggestions); } catch {}
  try { loadDefaultSuggestions().then(() => renderSuggestions(defaultSuggestions)); } catch {}
  // Shortcuts
  try { loadShortcuts(); } catch {}
  // Feedback handlers
  document.getElementById('thumbUpBtn')?.addEventListener('click', () => openFeedbackModal('up'));
  document.getElementById('thumbDownBtn')?.addEventListener('click', () => openFeedbackModal('down'));
  document.getElementById('feedbackCancel')?.addEventListener('click', closeFeedbackModal);
  document.getElementById('feedbackSubmit')?.addEventListener('click', submitFeedback);
});

function saveConfig() {
  const cfg = {
    supabaseUrl: els.supabaseUrl().value.trim(),
    anonKey: els.anonKey().value.trim(),
    providerOrder: els.providerOrder().value.trim(),
    allowGllm: els.allowGllm().value,
    chainId: els.chainId().value.trim(),
    decisionContext: els.decisionContext().value.trim(),
    theme: document.body.getAttribute('data-theme') || 'light',
  };
  localStorage.setItem('ac_workflow_gui', JSON.stringify(cfg));
  alert('Config saved');
}
window.saveConfig = saveConfig;

// Theme toggle
function toggleTheme() {
  const current = document.body.getAttribute('data-theme') || 'light';
  const next = current === 'dark' ? 'light' : 'dark';
  document.body.setAttribute('data-theme', next);
  const saved = JSON.parse(localStorage.getItem('ac_workflow_gui') || '{}');
  saved.theme = next;
  localStorage.setItem('ac_workflow_gui', JSON.stringify(saved));
}
window.toggleTheme = toggleTheme;

// Copy helper
async function copyText(id) {
  try {
    const node = document.getElementById(id);
    const text = node?.textContent || '';
    if (!text) return alert('Nothing to copy');
    await navigator.clipboard.writeText(text);
  } catch (e) {
    console.error(e);
    alert('Copy failed');
  }
}
window.copyText = copyText;

// Tabs helper
function switchTab(e) {
  const tab = e.currentTarget;
  document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
  document.querySelectorAll('.panel').forEach(p => p.classList.remove('active'));
  tab.classList.add('active');
  const id = tab.getAttribute('data-panel');
  document.getElementById(id)?.classList.add('active');
}
window.switchTab = switchTab;

// Textarea auto-resize
function initAutosize() {
  const all = Array.from(document.querySelectorAll('textarea'));
  const resize = (ta) => {
    ta.style.height = 'auto';
    ta.style.height = Math.min(420, Math.max(88, ta.scrollHeight)) + 'px';
  };
  all.forEach((ta) => {
    resize(ta);
    ta.addEventListener('input', () => resize(ta));
  });
}

// Helpers
function baseUrl() {
  const url = els.supabaseUrl().value.trim();
  if (!url) throw new Error('Please set Supabase URL');
  return url.replace(/\/$/, '');
}

function authHeaders() {
  const headers = { 'content-type': 'application/json' };
  const anon = els.anonKey().value.trim();
  if (anon) headers['authorization'] = `Bearer ${anon}`;
  const po = els.providerOrder().value.trim();
  const ag = els.allowGllm().value;
  const cid = els.chainId().value.trim();
  const dc = els.decisionContext().value.trim();
  if (po) headers['x-provider-order'] = po;
  if (ag) headers['x-allow-gllm'] = ag;
  if (cid) headers['compliance-chain-id'] = cid;
  if (dc) headers['decision-context'] = dc;
  return headers;
}

function setTrace(res, body) {
  try { els.reqId().textContent = body?.requestId || ''; } catch { }
  try { els.traceparent().textContent = res.headers.get('traceparent') || ''; } catch { }
}

// Audit panel logic
let auditState = { page: 1, pageSize: 50, hasMore: false };

async function runAuditQuery() {
  try {
    const base = baseUrl();
    const from = document.getElementById('auditFrom')?.value || '';
    const to = document.getElementById('auditTo')?.value || '';
    const route = document.getElementById('auditRoute')?.value?.trim() || '';
    const status = document.getElementById('auditStatus')?.value || '';
    const eventType = document.getElementById('auditEventType')?.value?.trim() || '';
    const requestId = document.getElementById('auditRequestId')?.value?.trim() || '';
    const sessionId = document.getElementById('auditSessionId')?.value?.trim() || '';
    const page = Number(document.getElementById('auditPage')?.value || '1') || 1;
    const pageSize = Number(document.getElementById('auditPageSize')?.value || '50') || 50;
    auditState.page = page; auditState.pageSize = pageSize;

    const u = new URL(`${base}/functions/v1/app-audit-read`);
    if (from) u.searchParams.set('from', new Date(from).toISOString());
    if (to) u.searchParams.set('to', new Date(to).toISOString());
    if (route) u.searchParams.set('route', route);
    if (status) u.searchParams.set('status', String(Number(status)));
    if (eventType) u.searchParams.set('eventType', eventType);
    if (requestId) u.searchParams.set('requestId', requestId);
    if (sessionId) u.searchParams.set('sessionId', sessionId);
    u.searchParams.set('page', String(page));
    u.searchParams.set('pageSize', String(pageSize));

    const res = await fetch(u.toString(), { headers: authHeaders() });
    const body = await res.json();
    if (!res.ok) throw new Error(body?.error || res.statusText);
    renderAuditRows(body.items || []);
    auditState.hasMore = !!body.hasMore;
    const pi = document.getElementById('auditPageInfo');
    if (pi) pi.textContent = `page ${page}, size ${pageSize}${auditState.hasMore ? ' (more…)' : ''}`;
  } catch (e) {
    alert(`Audit query failed: ${e?.message || e}`);
  }
}
window.runAuditQuery = runAuditQuery;

function renderAuditRows(items) {
  const tbody = document.getElementById('auditRows');
  if (!tbody) return;
  tbody.innerHTML = '';
  for (const it of items) {
    const tr = document.createElement('tr');
    tr.innerHTML = `
      <td class="mono">${it.ts ?? ''}</td>
      <td>${it.route ?? ''}</td>
      <td>${it.status ?? ''}</td>
      <td>${it.event_type ?? ''}</td>
      <td>${it.direction ?? ''}</td>
      <td class="mono">${it.request_id ?? ''}</td>
      <td class="mono">${it.trace_id ?? ''}</td>
      <td class="mono">${it.session_id ?? ''}</td>
    `;
    tbody.appendChild(tr);
  }
}

function auditPrev() {
  const inp = document.getElementById('auditPage');
  if (!inp) return;
  const p = Math.max(1, (Number(inp.value) || 1) - 1);
  inp.value = String(p);
  runAuditQuery();
}
window.auditPrev = auditPrev;

function auditNext() {
  const inp = document.getElementById('auditPage');
  if (!inp) return;
  const next = (Number(inp.value) || 1) + 1;
  if (!auditState.hasMore) return;
  inp.value = String(next);
  runAuditQuery();
}
window.auditNext = auditNext;

// Slash shortcuts
let shortcutCache = [];
async function loadShortcuts() {
  try {
    const res = await fetch(`${baseUrl()}/functions/v1/ui-shortcuts`, { headers: authHeaders() });
    const data = await res.json();
    if (Array.isArray(data?.shortcuts)) shortcutCache = data.shortcuts;
  } catch (e) { console.warn('Shortcuts not loaded', e?.message || e); }
}

function interpretSlashCommand(text) {
  if (!text || text[0] !== '/') return null;
  const [cmd, ...args] = text.trim().split(/\s+/);
  const sc = shortcutCache.find(s => s.shortcut === cmd);
  if (!sc) return null;
  if (sc.url) { window.location.href = sc.url; return { handled: true }; }
  if (sc.action_type === 'nav' && sc.target) {
    els.message().value = args.join(' ') || `Go to ${sc.target}`;
    return { handled: true };
  }
  return null;
}

// Conversation: Start
async function startConversation() {
  try {
    const ctxText = els.context().value.trim();
    let ctx = undefined;
    if (ctxText) {
      try { ctx = JSON.parse(ctxText); } catch { return alert('Context must be valid JSON'); }
    }
    const body = { title: els.title().value.trim() || undefined, context: ctx };
    const res = await fetch(`${baseUrl()}/functions/v1/ai-conversation-start`, {
      method: 'POST', headers: authHeaders(), body: JSON.stringify(body),
    });
    const data = await res.json();
    setTrace(res, data);
    if (!res.ok) throw new Error(data?.error || res.statusText);
    const sid = data?.data?.session?.sessionId || data?.session?.sessionId || data?.data?.session?.session_id || data?.session?.session_id;
    els.sessionId().textContent = sid || '(none)';
    const first = data?.data?.firstMessage?.contentText || data?.firstMessage?.contentText || data?.data?.firstMessage?.content_text || data?.firstMessage?.content_text || '';
    els.assistantOut().textContent = first;
    els.userOut().textContent = '';
    els.suggestions().textContent = '';
    renderSuggestions(defaultSuggestions);
  } catch (e) {
    console.error(e);
    alert(`Start error: ${e.message}`);
  }
}
window.startConversation = startConversation;

// Conversation: Send
async function sendMessage() {
  try {
    const sid = els.sessionId().textContent.trim();
    if (!sid || sid === '(none)') return alert('Start a session first');
    const msg = els.message().value.trim();
    const cmd = interpretSlashCommand(msg);
    if (cmd?.handled) return;
    if (!msg) return alert('Enter a message');
    const res = await fetch(`${baseUrl()}/functions/v1/ai-conversation-send`, {
      method: 'POST', headers: authHeaders(), body: JSON.stringify({ sessionId: sid, message: msg })
    });
    const data = await res.json();
    setTrace(res, data);
    if (!res.ok) throw new Error(data?.error || res.statusText);
    const user = data?.data?.userMessage || data?.userMessage || {};
    const asst = data?.data?.assistantMessage || data?.assistantMessage || {};
    const userText = user?.contentText || user?.content_text || '';
    const asstText = asst?.contentText || asst?.content_text || '';
    const sugg = asst?.suggestions || defaultSuggestions;
    // Capture assistant message id for feedback
    const asstId = asst?.messageId || asst?.message_id || null;
    if (asstId) lastAssistantMessageId = asstId;
    els.userOut().textContent = userText;
    els.assistantOut().textContent = asstText;
    els.suggestions().textContent = JSON.stringify(sugg, null, 2);
    renderSuggestions(sugg);
  } catch (e) {
    console.error(e);
    alert(`Send error: ${e.message}`);
  }
}
window.sendMessage = sendMessage;

// Conversation: Stream via SSE (POST)
async function streamMessage() {
  try {
    const sid = els.sessionId().textContent.trim();
    if (!sid || sid === '(none)') return alert('Start a session first');
    const msg = els.message().value.trim();
    if (!msg) return alert('Enter a message');
    const res = await fetch(`${baseUrl()}/functions/v1/ai-conversation-stream`, {
      method: 'POST', headers: authHeaders(), body: JSON.stringify({ sessionId: sid, message: msg })
    });
    if (!res.ok || !res.body) {
      const text = await res.text().catch(() => '');
      throw new Error(text || res.statusText);
    }
    els.userOut().textContent = msg;
    els.assistantOut().textContent = '';
    els.suggestions().textContent = '';
    const reader = res.body.getReader();
    const decoder = new TextDecoder();
    let buf = '';
    while (true) {
      const { value, done } = await reader.read();
      if (done) break;
      buf += decoder.decode(value, { stream: true });
      // Parse SSE chunks
      const parts = buf.split('\n\n');
      buf = parts.pop() || '';
      for (const p of parts) {
        const lines = p.split('\n');
        const eventLine = lines.find((l) => l.startsWith('event:')) || '';
        const dataLine = lines.find((l) => l.startsWith('data:')) || '';
        const event = eventLine.replace('event:', '').trim();
        const dataRaw = dataLine.replace('data:', '').trim();
        if (event === 'token') {
          try { const obj = JSON.parse(dataRaw); els.assistantOut().textContent += obj.text || ''; } catch { els.assistantOut().textContent += dataRaw; }
        }
      }
    }
    // Stream complete; backend stream stub has no suggestions yet
    renderSuggestions(defaultSuggestions);
  } catch (e) {
    console.error(e);
    alert(`Stream error: ${e.message}`);
  }
}
window.streamMessage = streamMessage;

// Feedback UI
let lastAssistantMessageId = null;
let lastRating = null;
function openFeedbackModal(rating) {
  lastRating = rating;
  const modal = document.getElementById('feedbackModal');
  const ta = document.getElementById('feedbackComment');
  if (ta) ta.value = '';
  if (modal) modal.style.display = 'flex';
}
function closeFeedbackModal() {
  const modal = document.getElementById('feedbackModal');
  if (modal) modal.style.display = 'none';
  lastRating = null;
}
async function submitFeedback() {
  try {
    const sid = els.sessionId().textContent.trim();
    if (!sid || sid === '(none)') return alert('Start a session first');
    if (!lastAssistantMessageId) return alert('No assistant message to rate yet');
    if (!lastRating) return closeFeedbackModal();
    const comment = (document.getElementById('feedbackComment')?.value || '').trim();
    const res = await fetch(`${baseUrl()}/functions/v1/ai-message-feedback`, {
      method: 'POST', headers: authHeaders(),
      body: JSON.stringify({ sessionId: sid, messageId: lastAssistantMessageId, rating: lastRating, comment }),
    });
    const data = await res.json().catch(() => ({}));
    if (!res.ok) throw new Error(data?.error || res.statusText);
    closeFeedbackModal();
    alert('Thanks for your feedback!');
  } catch (e) {
    console.error(e);
    alert(`Feedback error: ${e.message}`);
  }
}

// Provider Compare via compliance-proxy
async function compare(mode) {
  try {
    const question = els.compareQuestion().value.trim();
    if (!question) return alert('Enter question');
    const systemPrompt = els.systemPrompt().value.trim();
    
    // Debug: Ensure output elements exist and are visible
    console.log('Compare mode:', mode);
    console.log('OpenAI output element:', els.gptOut());
    console.log('Claude output element:', els.claudeOut());
    
    // Clear previous results and show loading
    if (mode === 'openai' || mode === 'both') {
      els.gptOut().textContent = 'Loading OpenAI response...';
    } else {
      els.gptOut().textContent = '';
    }
    
    if (mode === 'claude' || mode === 'both') {
      els.claudeOut().textContent = 'Loading Claude response...';
    } else {
      els.claudeOut().textContent = '';
    }
    
    const base = baseUrl();
    const headers = authHeaders();
    const tasks = [];
    const bodyBase = { messages: [{ role: 'user', content: question }], systemPrompt };
    
    if (mode === 'openai' || mode === 'both') {
      const b = JSON.stringify({
        ...bodyBase,
        provider: 'openai',
        parameters: { temperature: Number(els.openaiTemp().value), max_tokens: Number(els.maxTokens().value) }
      });
      tasks.push(fetch(`${base}/functions/v1/compliance-proxy`, { method: 'POST', headers, body: b })
        .then(r => r.json())
        .then(d => { 
          console.log('OpenAI response:', d);
          els.gptOut().textContent = d?.content || JSON.stringify(d, null, 2); 
        })
        .catch(err => {
          console.error('OpenAI error:', err);
          els.gptOut().textContent = `Error: ${err.message}`;
        }));
    }
    
    if (mode === 'claude' || mode === 'both') {
      const b = JSON.stringify({
        ...bodyBase,
        provider: 'claude',
        parameters: { temperature: Number(els.claudeTemp().value), max_tokens: Number(els.maxTokens().value) }
      });
      tasks.push(fetch(`${base}/functions/v1/compliance-proxy`, { method: 'POST', headers, body: b })
        .then(r => r.json())
        .then(d => { 
          console.log('Claude response:', d);
          els.claudeOut().textContent = d?.content || JSON.stringify(d, null, 2); 
        })
        .catch(err => {
          console.error('Claude error:', err);
          els.claudeOut().textContent = `Error: ${err.message}`;
        }));
    }
    
    await Promise.all(tasks);
  } catch (e) {
    console.error(e);
    alert(`Compare error: ${e.message}`);
  }
}
window.compare = compare;

// Arena logic
let arenaState = {
  // mapping holds which provider is A/B for the current round, hidden until reveal
  mapping: null, // { A: 'openai'|'claude', B: 'openai'|'claude' }
  lastQuestion: '',
};

function shuffleProviders() {
  const providers = ['openai', 'claude'];
  // simple shuffle
  if (Math.random() < 0.5) providers.reverse();
  return { A: providers[0], B: providers[1] };
}

function arenaHeaders() {
  return authHeaders();
}

function arenaBaseBody(question, systemPrompt) {
  return { messages: [{ role: 'user', content: question }], systemPrompt };
}

async function runArena() {
  try {
    const question = els.arenaQuestion().value.trim();
    if (!question) return alert('Enter question');
    const systemPrompt = els.arenaSystemPrompt().value.trim();
    const base = baseUrl();
    const headers = arenaHeaders();
    // randomize mapping
    arenaState.mapping = shuffleProviders();
    arenaState.lastQuestion = question;
    els.arenaAOut().textContent = '';
    els.arenaBOut().textContent = '';
    els.arenaMapping().textContent = '(hidden)';
    // build two requests with per-provider params
    const bodyBase = arenaBaseBody(question, systemPrompt);
    const openaiBody = JSON.stringify({
      ...bodyBase,
      provider: 'openai',
      parameters: { temperature: Number(els.arenaOpenaiTemp().value), max_tokens: Number(els.arenaMaxTokens().value) }
    });
    const claudeBody = JSON.stringify({
      ...bodyBase,
      provider: 'claude',
      parameters: { temperature: Number(els.arenaClaudeTemp().value), max_tokens: Number(els.arenaMaxTokens().value) }
    });
    // Fire in parallel, then assign results to A/B according to mapping
    const [openaiRes, claudeRes] = await Promise.all([
      fetch(`${base}/functions/v1/compliance-proxy`, { method: 'POST', headers, body: openaiBody }).then(r => r.json()),
      fetch(`${base}/functions/v1/compliance-proxy`, { method: 'POST', headers, body: claudeBody }).then(r => r.json()),
    ]);
    const byProvider = {
      openai: openaiRes?.content || JSON.stringify(openaiRes, null, 2),
      claude: claudeRes?.content || JSON.stringify(claudeRes, null, 2),
    };
    els.arenaAOut().textContent = byProvider[arenaState.mapping.A] || '';
    els.arenaBOut().textContent = byProvider[arenaState.mapping.B] || '';
  } catch (e) {
    console.error(e);
    alert(`Arena error: ${e.message}`);
  }
}
window.runArena = runArena;

function arenaReveal() {
  if (!arenaState.mapping) return alert('Run Arena first');
  els.arenaMapping().textContent = `A=${arenaState.mapping.A} • B=${arenaState.mapping.B}`;
}
window.arenaReveal = arenaReveal;

function updateArenaStats() {
  const stats = JSON.parse(localStorage.getItem('ac_arena_stats') || '{}');
  const a = stats.A || 0;
  const b = stats.B || 0;
  const tie = stats.tie || 0;
  const openai = stats.openai || 0;
  const claude = stats.claude || 0;
  const txt = `A=${a} • B=${b} • tie=${tie} • OpenAI=${openai} • Claude=${claude}`;
  els.arenaStats().textContent = txt;
}

function recordArenaWin(winnerLabel) {
  // If mapping exists and models are known, also increment provider stats
  const stats = JSON.parse(localStorage.getItem('ac_arena_stats') || '{}');
  stats[winnerLabel] = (stats[winnerLabel] || 0) + 1;
  if (arenaState.mapping && (winnerLabel === 'A' || winnerLabel === 'B')) {
    const prov = arenaState.mapping[winnerLabel];
    stats[prov] = (stats[prov] || 0) + 1;
  }
  localStorage.setItem('ac_arena_stats', JSON.stringify(stats));
  updateArenaStats();
}

function arenaVote(which) {
  if (!arenaState.mapping && which !== 'tie') {
    return alert('Run Arena first');
  }
  if (which !== 'A' && which !== 'B' && which !== 'tie') return;
  recordArenaWin(which);
}
window.arenaVote = arenaVote;

// Tabs
function initTabs() {
  const last = localStorage.getItem('ac_active_tab') || 'panel-config';
  const tabs = Array.from(document.querySelectorAll('.tab'));
  const panels = Array.from(document.querySelectorAll('.panel'));
  function setActive(panelId) {
    tabs.forEach(t => t.classList.toggle('active', t.getAttribute('data-panel') === panelId));
    panels.forEach(p => p.classList.toggle('active', p.id === panelId));
    localStorage.setItem('ac_active_tab', panelId);
  }
  // initial
  setActive(last);
  // expose switch function
  window.switchTab = (evt) => {
    const panelId = evt?.currentTarget?.getAttribute('data-panel');
    if (panelId) setActive(panelId);
  };
}

// NEW: Backend Features Testing Functions

// Helper to get output mode testing elements
const outputModeEls = {
  outputMode: () => document.getElementById('outputMode'),
  testPipelineMode: () => document.getElementById('testPipelineMode'),
  testFrameworkHint: () => document.getElementById('testFrameworkHint'),
  outputTestQuestion: () => document.getElementById('outputTestQuestion'),
  cardsOut: () => document.getElementById('cardsOut'),
  proseOut: () => document.getElementById('proseOut'),
  preprocessingOut: () => document.getElementById('preprocessingOut'),
  debugQuery: () => document.getElementById('debugQuery'),
  debugFramework: () => document.getElementById('debugFramework'),
  preprocessingDebugOut: () => document.getElementById('preprocessingDebugOut'),
};

// Test output modes (Cards/Prose/Both)
async function testOutputModes() {
  const outputMode = outputModeEls.outputMode()?.value || 'both';
  const pipelineMode = outputModeEls.testPipelineMode()?.value || 'retrieval';
  const frameworkHint = outputModeEls.testFrameworkHint()?.value || '';
  const question = outputModeEls.outputTestQuestion()?.value?.trim();
  
  if (!question) {
    alert('Please enter a test question');
    return;
  }
  
  // Clear previous results
  clearOutputResults();
  
  try {
    // Show loading state
    outputModeEls.cardsOut().innerHTML = '<div class="muted">Testing Cards format...</div>';
    outputModeEls.proseOut().innerHTML = '<div class="muted">Testing Prose format...</div>';
    outputModeEls.preprocessingOut().innerHTML = '<div class="muted">Executing preprocessing pipeline...</div>';
    
    const headers = authHeaders();
    headers['x-output-mode'] = outputMode;
    headers['x-pipeline-mode'] = pipelineMode;
    if (frameworkHint) headers['x-framework-hint'] = frameworkHint;
    
    const response = await sendConversationMessageWithHeaders(question, headers);
    displayOutputModeResults(response);
    
  } catch (err) {
    logError('Backend features test failed', err);
    outputModeEls.cardsOut().innerHTML = `<div class="error">Error: ${err.message}</div>`;
    outputModeEls.proseOut().innerHTML = `<div class="error">Error: ${err.message}</div>`;
    outputModeEls.preprocessingOut().innerHTML = `<div class="error">Error: ${err.message}</div>`;
  }
}

// Enhanced send function that accepts custom headers
async function sendConversationMessageWithHeaders(message, customHeaders = {}) {
  const sessionId = els.sessionId()?.value || 'test-session';
  const headers = { ...authHeaders(), ...customHeaders };
  
  // For testing without backend, simulate response based on the query
  if (message.toLowerCase().includes('demo') || !baseUrl() || baseUrl().includes('localhost:54321')) {
    return simulateBackendResponse(message, customHeaders);
  }
  
  const response = await fetch(`${baseUrl()}/functions/v1/ai-conversation-send`, {
    method: 'POST',
    headers: {
      ...headers,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      sessionId: sessionId,
      message: message.trim(),
    }),
  });
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${await response.text()}`);
  }
  
  return await response.json();
}

// Simulate backend response for testing when backend is not available
function simulateBackendResponse(message, headers) {
  const outputMode = headers['x-output-mode'] || 'both';
  const isCanonicalId = /^ISO\d+:\d+\/[A-Z0-9\.]+$/i.test(message.trim());
  const isDemoQuery = message.toLowerCase().includes('iso 27001') || message.toLowerCase().includes('demo');
  
  // Simulate different response types based on query
  const assistantMessage = {
    role: 'assistant',
    content: `This is a simulated response for: "${message}". The backend preprocessing pipeline would analyze this query and return structured results.`,
    text: `This is a simulated response for: "${message}". The backend preprocessing pipeline would analyze this query and return structured results.`,
    timestamp: new Date().toISOString(),
    output_mode: outputMode,
  };
  
  // Add Cards data for cards or both mode
  if (outputMode === 'cards' || outputMode === 'both') {
    assistantMessage.evidence = [
      {
        id: 'ISO27001:2022/4.1',
        type: 'deterministic_match',
        title: 'Context of the Organization',
        content: 'ISO 27001 requires organizations to understand their context, including internal and external issues that affect their ISMS.',
        confidence: 0.95,
        metadata: {
          match_type: isCanonicalId ? 'canonical_id_match' : 'synonym_expansion',
          deterministic: true
        }
      },
      {
        id: 'ISO27001:2022/A.5',
        type: 'related_content',
        title: 'Information Security Policies',
        content: 'Management direction for information security through policies and procedures.',
        confidence: 0.78,
        graph_paths: [{ hops: 1, score: 0.85 }]
      }
    ];
    
    assistantMessage.cards_hint = [
      'ISMS foundation setup',
      'Policy development',
      'Risk assessment'
    ];
  }
  
  // Add preprocessing data to show pipeline execution
  if (isCanonicalId) {
    assistantMessage.deterministic = {
      match_type: 'canonical_id_match',
      canonical_id: message.trim(),
      confidence_score: 1.0,
      source_stage: 'canonical_id_matching',
      graph_paths: 0
    };
    
    assistantMessage.preprocessing = {
      stages_executed: ['canonical_id_matching'],
      deterministic_resolved: true,
      query_enhanced: false
    };
  } else if (isDemoQuery) {
    assistantMessage.deterministic = {
      match_type: 'synonym_expansion',
      canonical_id: 'ISO27001:2022/4.1',
      confidence_score: 0.85,
      source_stage: 'synonym_expansion',
      graph_paths: 2
    };
    
    assistantMessage.preprocessing = {
      stages_executed: ['canonical_id_matching', 'synonym_expansion'],
      deterministic_resolved: true,
      query_enhanced: false
    };
  } else {
    assistantMessage.preprocessing = {
      stages_executed: ['canonical_id_matching', 'synonym_expansion', 'paraphrase_matching', 'e_pmi_associations', 'graph_crawling', 'rag_preparation'],
      deterministic_resolved: false,
      query_enhanced: true
    };
  }
  
  assistantMessage.typing_simulation = true;
  
  return Promise.resolve({
    userMessage: {
      role: 'user',
      content: message,
      timestamp: new Date().toISOString()
    },
    assistantMessage: assistantMessage,
    suggestions: [
      { text: 'Start ISMS implementation', action: { type: 'nav', target: 'isms-setup' } },
      { text: 'Learn about risk assessment', action: { type: 'nav', target: 'risk-assessment' } },
      { text: 'Explore ISO 27001 controls', action: { type: 'nav', target: 'controls' } }
    ]
  });
}

// Display results from backend features testing
function displayOutputModeResults(response) {
  console.log('Backend response:', response); // Debug log
  
  const assistant = response.assistantMessage || response;
  
  // Display Cards format
  if (assistant.evidence && assistant.evidence.length > 0) {
    const cardsHtml = formatCardsDisplay(assistant.evidence, assistant.cards_hint);
    outputModeEls.cardsOut().innerHTML = cardsHtml;
  } else if (assistant.output_mode === 'cards' || assistant.output_mode === 'both') {
    outputModeEls.cardsOut().innerHTML = '<div class="muted">Cards mode requested but no evidence returned</div>';
  } else {
    outputModeEls.cardsOut().innerHTML = '<div class="muted">Cards format not requested</div>';
  }
  
  // Display Prose format
  if (assistant.text || assistant.content) {
    const proseHtml = formatProseDisplay(assistant.text || assistant.content, assistant.typing_simulation);
    outputModeEls.proseOut().innerHTML = proseHtml;
  } else if (assistant.output_mode === 'prose' || assistant.output_mode === 'both') {
    outputModeEls.proseOut().innerHTML = '<div class="muted">Prose mode requested but no text returned</div>';
  } else {
    outputModeEls.proseOut().innerHTML = '<div class="muted">Prose format not requested</div>';
  }
  
  // Display Preprocessing results
  if (assistant.preprocessing || assistant.deterministic) {
    const preprocessingHtml = formatPreprocessingDisplay(assistant.preprocessing, assistant.deterministic);
    outputModeEls.preprocessingOut().innerHTML = preprocessingHtml;
  } else {
    outputModeEls.preprocessingOut().innerHTML = '<div class="muted">No preprocessing results returned</div>';
  }
}

// Format Cards display with evidence and cards_hint
function formatCardsDisplay(evidence, cards_hint) {
  let html = '<div class="cards-container">';
  
  // Display cards_hint
  if (cards_hint && cards_hint.length > 0) {
    html += '<div class="cards-hint"><strong>Quick Actions:</strong> ';
    html += cards_hint.map(hint => `<span class="hint-chip">${escapeHtml(hint)}</span>`).join(' ');
    html += '</div>';
  }
  
  // Display evidence
  if (evidence && evidence.length > 0) {
    evidence.forEach((item, idx) => {
      html += `
        <div class="evidence-card">
          <div class="evidence-header">
            <strong>${escapeHtml(item.id || 'Evidence ' + (idx + 1))}</strong>
            <span class="confidence">Confidence: ${(item.confidence || 0).toFixed(2)}</span>
          </div>
          <div class="evidence-content">${escapeHtml(item.content || item.title || 'No content available')}</div>
          ${item.graph_paths && item.graph_paths.length > 0 ? 
            `<div class="graph-paths">Graph paths: ${item.graph_paths.length} (${item.graph_paths.map(p => p.hops || 'N/A').join(', ')} hops)</div>` : ''}
          ${item.metadata ? `<div class="muted" style="font-size: 0.8em;">Type: ${escapeHtml(item.type || 'unknown')}</div>` : ''}
        </div>
      `;
    });
  } else {
    html += '<div class="muted">No evidence items returned</div>';
  }
  
  html += '</div>';
  return html;
}

// Format Prose display with typing simulation info
function formatProseDisplay(content, typing_simulation) {
  let html = '<div class="prose-container">';
  html += `<div class="prose-content">${escapeHtml(content || 'No content available')}</div>`;
  if (typing_simulation) {
    html += '<div class="typing-indicator">✓ Supports typing simulation</div>';
  }
  html += '</div>';
  return html;
}

// Format preprocessing pipeline results
function formatPreprocessingDisplay(preprocessing, deterministic) {
  let html = '<div class="preprocessing-container">';
  
  if (deterministic) {
    html += `
      <div class="deterministic-match">
        <strong>🎯 Deterministic Match Found!</strong>
        <div>Match Type: ${escapeHtml(deterministic.match_type || 'unknown')}</div>
        <div>Canonical ID: ${escapeHtml(deterministic.canonical_id || 'N/A')}</div>
        <div>Confidence: ${(deterministic.confidence_score || 0).toFixed(2)}</div>
        <div>Source Stage: ${escapeHtml(deterministic.source_stage || 'unknown')}</div>
        ${deterministic.graph_paths > 0 ? `<div>Graph paths explored: ${deterministic.graph_paths}</div>` : ''}
      </div>
    `;
  }
  
  if (preprocessing) {
    if (preprocessing.stages_executed && preprocessing.stages_executed.length > 0) {
      html += `
        <div class="pipeline-stages">
          <strong>🔄 Pipeline Stages Executed:</strong>
          <div>${preprocessing.stages_executed.map(stage => escapeHtml(stage)).join(' → ')}</div>
        </div>
      `;
    }
    
    if (preprocessing.deterministic_resolved !== undefined) {
      const resolved = preprocessing.deterministic_resolved ? '✅ Yes' : '❌ No';
      html += `<div><strong>Deterministic Resolution:</strong> ${resolved}</div>`;
    }
    
    if (preprocessing.query_enhanced) {
      html += '<div class="query-enhanced">✅ Query enhanced with preprocessing results</div>';
    }
  }
  
  if (!deterministic && !preprocessing) {
    html += '<div class="muted">No preprocessing information available</div>';
  }
  
  html += '</div>';
  return html;
}

// Test full preprocessing pipeline
async function testFullPipeline() {
  const query = outputModeEls.debugQuery()?.value?.trim();
  const framework = outputModeEls.debugFramework()?.value || '';
  
  if (!query) {
    alert('Please enter a debug query');
    return;
  }
  
  try {
    outputModeEls.preprocessingDebugOut().innerHTML = '<div class="muted">Testing preprocessing pipeline...</div>';
    
    const headers = authHeaders();
    headers['x-output-mode'] = 'both';
    headers['x-pipeline-mode'] = 'preprocessing';
    if (framework) headers['x-framework-hint'] = framework;
    
    const response = await sendConversationMessageWithHeaders(query, headers);
    displayPreprocessingDebug(response);
    
  } catch (err) {
    logError('Preprocessing pipeline test failed', err);
    outputModeEls.preprocessingDebugOut().innerHTML = `<div class="error">Error: ${err.message}</div>`;
  }
}

// Test canonical ID matching specifically
async function testCanonicalId() {
  const query = outputModeEls.debugQuery()?.value?.trim();
  
  if (!query) {
    alert('Please enter a canonical ID to test (e.g., ISO27001:2022/A.5.1)');
    return;
  }
  
  try {
    outputModeEls.preprocessingDebugOut().innerHTML = '<div class="muted">Testing canonical ID matching...</div>';
    
    const headers = authHeaders();
    headers['x-output-mode'] = 'both';
    headers['x-pipeline-mode'] = 'preprocessing';
    
    const response = await sendConversationMessageWithHeaders(query, headers);
    
    const debugHtml = `
      <div class="preprocessing-debug">
        <h4>Canonical ID Test Results</h4>
        <div><strong>Input Query:</strong> ${escapeHtml(query)}</div>
        <div><strong>Deterministic Match:</strong> ${response.assistantMessage?.deterministic ? 'Found' : 'Not Found'}</div>
        ${response.assistantMessage?.deterministic ? 
          `<div><strong>Canonical ID:</strong> ${escapeHtml(response.assistantMessage.deterministic.canonical_id)}</div>
           <div><strong>Confidence:</strong> ${response.assistantMessage.deterministic.confidence_score}</div>` : 
          '<div class="muted">No canonical ID pattern detected</div>'}
      </div>
    `;
    
    outputModeEls.preprocessingDebugOut().innerHTML = debugHtml;
    
  } catch (err) {
    logError('Canonical ID test failed', err);
    outputModeEls.preprocessingDebugOut().innerHTML = `<div class="error">Error: ${err.message}</div>`;
  }
}

// Display detailed preprocessing debug information
function displayPreprocessingDebug(response) {
  const debug = outputModeEls.preprocessingDebugOut();
  const assistant = response.assistantMessage || response;
  
  let html = '<div class="preprocessing-debug">';
  html += '<h4>🔍 Detailed Pipeline Debug</h4>';
  
  // Show preprocessing stages
  if (assistant.preprocessing) {
    html += `<div><strong>Stages Executed:</strong> ${assistant.preprocessing.stages_executed?.join(' → ') || 'None'}</div>`;
    html += `<div><strong>Deterministic Resolved:</strong> ${assistant.preprocessing.deterministic_resolved ? '✅ Yes' : '❌ No'}</div>`;
    html += `<div><strong>Query Enhanced:</strong> ${assistant.preprocessing.query_enhanced ? '✅ Yes' : '❌ No'}</div>`;
  }
  
  // Show deterministic match details
  if (assistant.deterministic) {
    html += `<h5>🎯 Deterministic Match Details</h5>`;
    html += `<div><strong>Match Type:</strong> ${escapeHtml(assistant.deterministic.match_type)}</div>`;
    html += `<div><strong>Canonical ID:</strong> ${escapeHtml(assistant.deterministic.canonical_id)}</div>`;
    html += `<div><strong>Confidence Score:</strong> ${assistant.deterministic.confidence_score.toFixed(3)}</div>`;
    html += `<div><strong>Source Stage:</strong> ${escapeHtml(assistant.deterministic.source_stage)}</div>`;
    if (assistant.deterministic.graph_paths > 0) {
      html += `<div><strong>Graph Paths:</strong> ${assistant.deterministic.graph_paths} paths explored</div>`;
    }
  }
  
  // Show evidence details
  if (assistant.evidence && assistant.evidence.length > 0) {
    html += `<h5>📋 Evidence Summary (${assistant.evidence.length} items)</h5>`;
    assistant.evidence.forEach((item, idx) => {
      html += `<div class="evidence-item">
        <strong>#${idx + 1}:</strong> ${escapeHtml(item.id || 'Unknown')} 
        (confidence: ${item.confidence?.toFixed(2) || 'N/A'}, type: ${escapeHtml(item.type || 'unknown')})
      </div>`;
    });
  }
  
  // Show full response for debugging
  html += '<h5>🔧 Raw Response (Debug)</h5>';
  html += `<pre style="font-size: 0.8em; background: var(--card-bg); padding: 8px; border-radius: 4px; overflow-x: auto;">${escapeHtml(JSON.stringify({
    output_mode: assistant.output_mode,
    preprocessing: assistant.preprocessing,
    deterministic: assistant.deterministic,
    evidence_count: assistant.evidence?.length || 0,
    cards_hint_count: assistant.cards_hint?.length || 0
  }, null, 2))}</pre>`;
  
  html += '</div>';
  debug.innerHTML = html;
}

// Clear output mode test results
function clearOutputResults() {
  outputModeEls.cardsOut().innerHTML = '';
  outputModeEls.proseOut().innerHTML = '';
  outputModeEls.preprocessingOut().innerHTML = '';
}

// Clear debug results
function clearDebugResults() {
  outputModeEls.preprocessingDebugOut().innerHTML = '';
}

// HTML escape utility function
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

// Error logging utility
function logError(message, error) {
  console.error(message, error);
}

// Trace Graph Functions
let traceGraphCy = null;

async function buildTraceGraph() {
  try {
    const requestId = document.getElementById('traceRequestId')?.value?.trim() || '';
    const sessionId = document.getElementById('traceSessionId')?.value?.trim() || '';

    if (!requestId && !sessionId) {
      alert('Please enter either a Request ID or Session ID to build the trace graph');
      return;
    }

    // Fetch audit data for the trace
    const base = baseUrl();
    const u = new URL(`${base}/functions/v1/app-audit-read`);
    if (requestId) u.searchParams.set('requestId', requestId);
    if (sessionId) u.searchParams.set('sessionId', sessionId);
    u.searchParams.set('pageSize', '200'); // Get more events for complete trace

    const res = await fetch(u.toString(), { headers: authHeaders() });
    const body = await res.json();
    if (!res.ok) throw new Error(body?.error || res.statusText);

    const events = body.items || [];
    if (events.length === 0) {
      document.getElementById('traceDetails').textContent = 'No events found for the specified Request ID or Session ID.';
      return;
    }

    // Build graph from events
    buildCytoscapeGraph(events);

  } catch (e) {
    alert(`Failed to build trace graph: ${e?.message || e}`);
    document.getElementById('traceDetails').textContent = `Error: ${e?.message || e}`;
  }
}
window.buildTraceGraph = buildTraceGraph;

function buildCytoscapeGraph(events) {
  const container = document.getElementById('traceGraph');
  if (!container) return;

  // Group events by request_id and sort by timestamp
  const requestGroups = {};
  events.forEach(event => {
    const reqId = event.request_id || 'unknown';
    if (!requestGroups[reqId]) requestGroups[reqId] = [];
    requestGroups[reqId].push(event);
  });

  // Sort events within each request by timestamp
  Object.values(requestGroups).forEach(group => {
    group.sort((a, b) => new Date(a.ts) - new Date(b.ts));
  });

  // Build nodes and edges
  const nodes = [];
  const edges = [];
  let nodeIdCounter = 0;

  Object.entries(requestGroups).forEach(([reqId, reqEvents]) => {
    let prevNodeId = null;

    reqEvents.forEach((event, index) => {
      const nodeId = `${reqId}-${nodeIdCounter++}`;

      // Determine node type and color based on event type
      const eventType = event.event_type || 'unknown';
      let nodeClass = 'default';
      let nodeColor = '#666666';

      if (eventType.includes('intent')) {
        nodeClass = 'intent';
        nodeColor = '#4CAF50';
      } else if (eventType.includes('routing') || eventType.includes('forward')) {
        nodeClass = 'routing';
        nodeColor = '#2196F3';
      } else if (eventType.includes('retrieval')) {
        nodeClass = 'retrieval';
        nodeColor = '#FF9800';
      } else if (eventType.includes('ai_call')) {
        nodeClass = 'ai_call';
        nodeColor = '#9C27B0';
      } else if (eventType.includes('response')) {
        nodeClass = 'response';
        nodeColor = '#607D8B';
      }

      // Create node
      nodes.push({
        data: {
          id: nodeId,
          label: `${eventType}\n${event.direction || ''}`,
          eventData: event,
          nodeClass: nodeClass
        },
        classes: `trace-node-${nodeClass}`,
        style: {
          'background-color': nodeColor,
          'color': 'white',
          'text-wrap': 'wrap',
          'text-max-width': '80px',
          'font-size': '10px',
          'width': '60px',
          'height': '40px'
        }
      });

      // Create edge from previous node
      if (prevNodeId) {
        const edgeColor = event.status >= 400 ? '#F44336' : '#4CAF50';
        edges.push({
          data: {
            id: `${prevNodeId}-${nodeId}`,
            source: prevNodeId,
            target: nodeId
          },
          style: {
            'line-color': edgeColor,
            'target-arrow-color': edgeColor,
            'target-arrow-shape': 'triangle'
          }
        });
      }

      prevNodeId = nodeId;
    });
  });

  // Initialize or update Cytoscape
  if (traceGraphCy) {
    traceGraphCy.destroy();
  }

  traceGraphCy = cytoscape({
    container: container,
    elements: [...nodes, ...edges],
    style: [
      {
        selector: 'node',
        style: {
          'label': 'data(label)',
          'text-valign': 'center',
          'text-halign': 'center',
          'shape': 'roundrectangle'
        }
      },
      {
        selector: 'edge',
        style: {
          'width': 2,
          'curve-style': 'bezier'
        }
      }
    ],
    layout: {
      name: 'dagre',
      rankDir: 'LR',
      spacingFactor: 1.5,
      nodeSep: 20,
      rankSep: 100
    }
  });

  // Add click handlers
  traceGraphCy.on('tap', 'node', function(evt) {
    const node = evt.target;
    const eventData = node.data('eventData');
    showEventDetails(eventData);
  });

  traceGraphCy.on('tap', 'edge', function(evt) {
    const edge = evt.target;
    const sourceData = traceGraphCy.getElementById(edge.data('source')).data('eventData');
    const targetData = traceGraphCy.getElementById(edge.data('target')).data('eventData');
    showEdgeDetails(sourceData, targetData);
  });

  document.getElementById('traceDetails').textContent = `Trace graph built with ${nodes.length} events across ${Object.keys(requestGroups).length} request(s). Click nodes or edges for details.`;
}

function showEventDetails(eventData) {
  const details = document.getElementById('traceDetails');
  if (!details) return;

  details.innerHTML = `
    <div style="font-weight: bold; margin-bottom: 8px;">Event Details</div>
    <div><strong>Type:</strong> ${eventData.event_type || 'N/A'}</div>
    <div><strong>Direction:</strong> ${eventData.direction || 'N/A'}</div>
    <div><strong>Status:</strong> ${eventData.status || 'N/A'}</div>
    <div><strong>Time:</strong> ${eventData.ts || 'N/A'}</div>
    <div><strong>Request ID:</strong> <span class="mono">${eventData.request_id || 'N/A'}</span></div>
    <div><strong>Session ID:</strong> <span class="mono">${eventData.session_id || 'N/A'}</span></div>
    <div><strong>Route:</strong> ${eventData.route || 'N/A'}</div>
    ${eventData.details ? `<div style="margin-top: 8px;"><strong>Details:</strong><pre class="log" style="max-height: 200px; overflow-y: auto;">${JSON.stringify(eventData.details, null, 2)}</pre></div>` : ''}
  `;
}

function showEdgeDetails(sourceData, targetData) {
  const details = document.getElementById('traceDetails');
  if (!details) return;

  const timeDiff = new Date(targetData.ts) - new Date(sourceData.ts);
  details.innerHTML = `
    <div style="font-weight: bold; margin-bottom: 8px;">Flow Details</div>
    <div><strong>From:</strong> ${sourceData.event_type} (${sourceData.direction})</div>
    <div><strong>To:</strong> ${targetData.event_type} (${targetData.direction})</div>
    <div><strong>Time Difference:</strong> ${timeDiff}ms</div>
    <div><strong>Status Change:</strong> ${sourceData.status} → ${targetData.status}</div>
  `;
}

function clearTraceGraph() {
  if (traceGraphCy) {
    traceGraphCy.destroy();
    traceGraphCy = null;
  }
  document.getElementById('traceDetails').textContent = 'Click on nodes or edges in the graph to see detailed event information';
  document.getElementById('traceRequestId').value = '';
  document.getElementById('traceSessionId').value = '';
}
window.clearTraceGraph = clearTraceGraph;

// Expose functions globally for HTML onclick handlers
window.testOutputModes = testOutputModes;
window.testFullPipeline = testFullPipeline;
window.testCanonicalId = testCanonicalId;
window.clearOutputResults = clearOutputResults;
window.clearDebugResults = clearDebugResults;
