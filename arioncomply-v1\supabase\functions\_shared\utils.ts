// File: arioncomply-v1/supabase/functions/_shared/utils.ts
// File Description: Edge utility helpers
// Purpose: JSON response builder, body parsing, nowIso, snake↔camel key conversion.
// Exports: jsonResponse(), readJson<T>(), convert<PERSON><PERSON><PERSON>(), toSnake(), toC<PERSON>()
// Notes: Conversion is generic; FieldMapper overrides may be added later.
// Small helpers for Edge Functions (Deno runtime)

/**
 * Build a JSON Response with sensible headers.
 * @param data - Any serializable payload
 * @param init - Optional ResponseInit (headers/status)
 */
export function jsonResponse(data: unknown, init: ResponseInit = {}): Response {
  const body = JSON.stringify(data, null, 2);
  return new Response(body, {
    headers: {
      "content-type": "application/json; charset=utf-8",
      ...init.headers,
    },
    status: init.status ?? 200,
  });
}

/**
 * Parse Request JSON with typed return and 400 on invalid JSON.
 * @template T
 * @param req - Incoming Request
 * @returns Parsed body as T (empty object if no body)
 */
export async function readJson<T = unknown>(req: Request): Promise<T> {
  const text = await req.text();
  if (!text) return {} as T;
  try {
    return JSON.parse(text) as T;
  } catch {
    throw new Response(
      JSON.stringify({ error: "Invalid JSON body" }),
      { status: 400, headers: { "content-type": "application/json" } },
    );
  }
}

/** Return a 400 JSON error envelope. */
export function badRequest(message: string): Response {
  return jsonResponse({ error: message }, { status: 400 });
}

/** Return a 401 JSON error envelope. */
export function unauthorized(message = "Unauthorized"): Response {
  return jsonResponse({ error: message }, { status: 401 });
}

/** ISO 8601 timestamp for now. */
export function nowIso(): string {
  return new Date().toISOString();
}

// Case conversion helpers (snake_case ↔ camelCase)
const CAMEL_RE = /[A-Z]/g;
const SNAKE_RE = /[_-](\w)/g;

/** Convert camelCase or kebab-case to snake_case. */
export function toSnake(key: string): string {
  return key
    .replace(CAMEL_RE, (m) => `_${m.toLowerCase()}`)
    .replace(/[-\s]+/g, "_")
    .replace(/^_+/, "");
}

/** Convert snake_case to camelCase. */
export function toCamel(key: string): string {
  return key.replace(SNAKE_RE, (_, c: string) => c.toUpperCase());
}

export type CaseDirection = "toCamel" | "toSnake";

/**
 * Recursively convert object keys between snake_case and camelCase.
 * @template T
 * @param value - Input object/array/scalar
 * @param dir - Direction: 'toCamel' | 'toSnake'
 */
export function convertKeys<T = unknown>(value: any, dir: CaseDirection): T {
  if (value === null || value === undefined) return value as T;
  if (Array.isArray(value)) return value.map((v) => convertKeys(v, dir)) as any;
  if (typeof value !== "object") return value as T;

  const out: Record<string, any> = {};
  for (const [k, v] of Object.entries(value)) {
    const nk = dir === "toCamel" ? toCamel(k) : toSnake(k);
    out[nk] = convertKeys(v, dir);
  }
  return out as T;
}
