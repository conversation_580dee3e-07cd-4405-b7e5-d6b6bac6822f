<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentScriptType="application/ecmascript" contentStyleType="text/css" height="587px" preserveAspectRatio="none" style="width:969px;height:587px;" version="1.1" viewBox="0 0 969 587" width="969px" zoomAndPan="magnify"><defs/><g><text fill="#000000" font-family="sans-serif" font-size="18" lengthAdjust="spacingAndGlyphs" textLength="176" x="395" y="26.708">RAG Generate Flow</text><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="23" x2="23" y1="117.25" y2="499.8438"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="202" x2="202" y1="117.25" y2="499.8438"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="350.5" x2="350.5" y1="117.25" y2="499.8438"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="459.5" x2="459.5" y1="117.25" y2="499.8438"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="530.5" x2="530.5" y1="117.25" y2="499.8438"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="692" x2="692" y1="117.25" y2="499.8438"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="784" x2="784" y1="117.25" y2="499.8438"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 5.0,5.0;" x1="908" x2="908" y1="117.25" y2="499.8438"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="13" x="13.5" y="113.9482">UI</text><ellipse cx="23" cy="47.9531" fill="#F8F8F8" rx="8" ry="8" style="stroke: #383838; stroke-width: 2.0;"/><path d="M23,55.9531 L23,82.9531 M10,63.9531 L36,63.9531 M23,82.9531 L10,97.9531 M23,82.9531 L36,97.9531 " fill="none" style="stroke: #383838; stroke-width: 2.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="13" x="13.5" y="511.8389">UI</text><ellipse cx="23" cy="525.1406" fill="#F8F8F8" rx="8" ry="8" style="stroke: #383838; stroke-width: 2.0;"/><path d="M23,533.1406 L23,560.1406 M10,541.1406 L36,541.1406 M23,560.1406 L10,575.1406 M23,560.1406 L36,575.1406 " fill="none" style="stroke: #383838; stroke-width: 2.0;"/><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="50" x="177" y="85.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="36" x="184" y="105.9482">Edge</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="50" x="177" y="498.8438"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="36" x="184" y="518.8389">Edge</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="107" x="297.5" y="85.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="93" x="304.5" y="105.9482">API /generate</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="107" x="297.5" y="498.8438"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="93" x="304.5" y="518.8389">API /generate</text><path d="M435.5,90.9531 L484.5,90.9531 C489.5,90.9531 489.5,104.1016 489.5,104.1016 C489.5,104.1016 489.5,117.25 484.5,117.25 L435.5,117.25 C430.5,117.25 430.5,104.1016 430.5,104.1016 C430.5,104.1016 430.5,90.9531 435.5,90.9531 " fill="#F8F8F8" style="stroke: #383838; stroke-width: 2.0;"/><path d="M484.5,90.9531 C479.5,90.9531 479.5,104.1016 479.5,104.1016 C479.5,117.25 484.5,117.25 484.5,117.25 " fill="none" style="stroke: #383838; stroke-width: 2.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="39" x="435.5" y="108.9482">Redis</text><path d="M435.5,498.8438 L484.5,498.8438 C489.5,498.8438 489.5,511.9922 489.5,511.9922 C489.5,511.9922 489.5,525.1406 484.5,525.1406 L435.5,525.1406 C430.5,525.1406 430.5,511.9922 430.5,511.9922 C430.5,511.9922 430.5,498.8438 435.5,498.8438 " fill="#F8F8F8" style="stroke: #383838; stroke-width: 2.0;"/><path d="M484.5,498.8438 C479.5,498.8438 479.5,511.9922 479.5,511.9922 C479.5,525.1406 484.5,525.1406 484.5,525.1406 " fill="none" style="stroke: #383838; stroke-width: 2.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="39" x="435.5" y="516.8389">Redis</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="63" x="499.5" y="85.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="49" x="506.5" y="105.9482">Worker</text><rect fill="#F8F8F8" height="30.2969" style="stroke: #383838; stroke-width: 1.5;" width="63" x="499.5" y="498.8438"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="49" x="506.5" y="518.8389">Worker</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="20" x="679" y="113.9482">PG</text><path d="M674,64.9531 C674,54.9531 692,54.9531 692,54.9531 C692,54.9531 710,54.9531 710,64.9531 L710,90.9531 C710,100.9531 692,100.9531 692,100.9531 C692,100.9531 674,100.9531 674,90.9531 L674,64.9531 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M674,64.9531 C674,74.9531 692,74.9531 692,74.9531 C692,74.9531 710,74.9531 710,64.9531 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="20" x="679" y="511.8389">PG</text><path d="M674,525.1406 C674,515.1406 692,515.1406 692,515.1406 C692,515.1406 710,515.1406 710,525.1406 L710,551.1406 C710,561.1406 692,561.1406 692,561.1406 C692,561.1406 674,561.1406 674,551.1406 L674,525.1406 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M674,525.1406 C674,535.1406 692,535.1406 692,535.1406 C692,535.1406 710,535.1406 710,525.1406 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="123" x="720" y="113.9482">Chroma (optional)</text><path d="M766.5,64.9531 C766.5,54.9531 784.5,54.9531 784.5,54.9531 C784.5,54.9531 802.5,54.9531 802.5,64.9531 L802.5,90.9531 C802.5,100.9531 784.5,100.9531 784.5,100.9531 C784.5,100.9531 766.5,100.9531 766.5,90.9531 L766.5,64.9531 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M766.5,64.9531 C766.5,74.9531 784.5,74.9531 784.5,74.9531 C784.5,74.9531 802.5,74.9531 802.5,64.9531 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="123" x="720" y="511.8389">Chroma (optional)</text><path d="M766.5,525.1406 C766.5,515.1406 784.5,515.1406 784.5,515.1406 C784.5,515.1406 802.5,515.1406 802.5,525.1406 L802.5,551.1406 C802.5,561.1406 784.5,561.1406 784.5,561.1406 C784.5,561.1406 766.5,561.1406 766.5,551.1406 L766.5,525.1406 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M766.5,525.1406 C766.5,535.1406 784.5,535.1406 784.5,535.1406 C784.5,535.1406 802.5,535.1406 802.5,525.1406 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><rect fill="#F8F8F8" height="46.5938" style="stroke: #383838; stroke-width: 1.5;" width="99" x="859" y="69.6563"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="65" x="876" y="89.6514">Local LLM</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="85" x="866" y="105.9482">(OpenAI API)</text><rect fill="#F8F8F8" height="46.5938" style="stroke: #383838; stroke-width: 1.5;" width="99" x="859" y="498.8438"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="65" x="876" y="518.8389">Local LLM</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="85" x="866" y="535.1357">(OpenAI API)</text><polygon fill="#383838" points="190,144.3828,200,148.3828,190,152.3828,194,148.3828" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="23" x2="196" y1="148.3828" y2="148.3828"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="155" x="30" y="143.3169">POST conversation.send</text><polygon fill="#383838" points="339,173.5156,349,177.5156,339,181.5156,343,177.5156" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="202" x2="345" y1="177.5156" y2="177.5156"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="116" x="209" y="172.4497">forward (auth, ids)</text><polygon fill="#383838" points="448,202.6484,458,206.6484,448,210.6484,452,206.6484" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="351" x2="454" y1="206.6484" y2="206.6484"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="85" x="358" y="201.5825">enqueue(job)</text><polygon fill="#383838" points="519,231.7813,529,235.7813,519,239.7813,523,235.7813" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="460" x2="525" y1="235.7813" y2="235.7813"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="19" x="467" y="230.7153">job</text><polygon fill="#383838" points="772.5,260.9141,782.5,264.9141,772.5,268.9141,776.5,264.9141" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="531" x2="778.5" y1="264.9141" y2="264.9141"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="110" x="538" y="259.8481">query (if present)</text><polygon fill="#383838" points="680,290.0469,690,294.0469,680,298.0469,684,294.0469" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="531" x2="686" y1="294.0469" y2="294.0469"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="137" x="538" y="288.981">query (fallback/filters)</text><line style="stroke: #383838; stroke-width: 1.0;" x1="531" x2="573" y1="323.1797" y2="323.1797"/><line style="stroke: #383838; stroke-width: 1.0;" x1="573" x2="573" y1="323.1797" y2="336.1797"/><line style="stroke: #383838; stroke-width: 1.0;" x1="532" x2="573" y1="336.1797" y2="336.1797"/><polygon fill="#383838" points="542,332.1797,532,336.1797,542,340.1797,538,336.1797" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="110" x="538" y="318.1138">prompt compose</text><polygon fill="#383838" points="896.5,361.3125,906.5,365.3125,896.5,369.3125,900.5,365.3125" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="531" x2="902.5" y1="365.3125" y2="365.3125"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="133" x="538" y="360.2466">/v1/chat/completions</text><polygon fill="#383838" points="542,390.4453,532,394.4453,542,398.4453,538,394.4453" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 2.0,2.0;" x1="536" x2="907.5" y1="394.4453" y2="394.4453"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="93" x="548" y="389.3794">stream tokens</text><polygon fill="#383838" points="362,419.5781,352,423.5781,362,427.5781,358,423.5781" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="356" x2="530" y1="423.5781" y2="423.5781"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="86" x="368" y="418.5122">progress/logs</text><polygon fill="#383838" points="213,448.7109,203,452.7109,213,456.7109,209,452.7109" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="207" x2="350" y1="452.7109" y2="452.7109"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="125" x="219" y="447.645">stream/proxy result</text><polygon fill="#383838" points="34,477.8438,24,481.8438,34,485.8438,30,481.8438" style="stroke: #383838; stroke-width: 1.0;"/><line style="stroke: #383838; stroke-width: 1.0;" x1="28" x2="201" y1="481.8438" y2="481.8438"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="59" x="40" y="476.7778">response</text><!--MD5=[b929b25a496f539ec18b239ecf5e34d9]
@startuml RAG Generate
title RAG Generate Flow
skinparam shadowing false
skinparam monochrome true

actor UI
participant Edge
participant "API /generate" as API
queue Redis
participant Worker as W
database PG
database "Chroma (optional)" as Chroma
participant "Local LLM\n(OpenAI API)" as LLM

UI -> Edge : POST conversation.send
Edge -> API : forward (auth, ids)
API -> Redis : enqueue(job)
Redis -> W : job
W -> Chroma : query (if present)
W -> PG : query (fallback/filters)
W -> W : prompt compose
W -> LLM : /v1/chat/completions
LLM - -> W : stream tokens
W -> API : progress/logs
API -> Edge : stream/proxy result
Edge -> UI : response

@enduml

PlantUML version 1.2020.02(Sun Mar 01 10:22:07 UTC 2020)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Java Version: 17.0.16+8
Operating System: Linux
Default Encoding: UTF-8
Language: en
Country: null
--></g></svg>