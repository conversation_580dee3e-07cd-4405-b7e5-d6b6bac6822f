\n== Headers (repo quality) ==
Header Quality Report
=====================
Total files scanned: 124
Files with header issues: 0
Files with function doc issues: 1


Function doc issues (first 100):
  - arioncomply-v1/ai-backend/python-backend/services/retrieval/vector_profiles.py:
      * missing docstring: def __post_init__()
      * missing docstring: def replace_env_var()
[31mFAIL[0m Headers (repo quality)
