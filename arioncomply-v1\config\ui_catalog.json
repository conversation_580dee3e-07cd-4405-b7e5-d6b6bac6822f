{"version": "2025-01-15T12:00:00Z", "description": "UI action catalog exported from v1 authoring guide Appendix A for v2 JSON metadata validation", "hybrid_vector_support": {"chromadb": "Local vector operations, development, testing", "supabase_vector": "Production vector storage with RLS, multi-tenant isolation"}, "registers": [{"slug": "risk", "label": "Risk Register"}, {"slug": "soa", "label": "Statement of Applicability"}, {"slug": "vendors", "label": "Vendor Register"}, {"slug": "ropa", "label": "Record of Processing Activities"}, {"slug": "transfers", "label": "Data Transfers Register"}, {"slug": "incidents", "label": "Incident Register"}, {"slug": "dsr", "label": "Data Subject Requests"}, {"slug": "assets_systems", "label": "Assets & Systems Register"}, {"slug": "training", "label": "Training Records"}, {"slug": "audits", "label": "Audits Register"}, {"slug": "document_register", "label": "Document Register"}, {"slug": "byod_compliance", "label": "BYOD Compliance Register"}, {"slug": "risk_communications", "label": "Risk Communications Matrix"}, {"slug": "business_impact", "label": "Business Impact Analysis"}, {"slug": "privacy_compliance", "label": "Data Privacy Compliance Register"}, {"slug": "operational_controls", "label": "Operational Planning — Controls"}, {"slug": "access_control_matrix", "label": "Access Control Matrix"}, {"slug": "policy_acknowledgements", "label": "Security Policy Acknowledgments"}, {"slug": "kpi_metrics", "label": "Information Security KPI Tracker"}, {"slug": "legal_regulatory_compliance", "label": "Legal & Regulatory Register"}], "trackers": [{"slug": "cap_nc", "label": "Non-conformities & Corrective Actions"}, {"slug": "change_management", "label": "Change requests/approvals"}, {"slug": "monitoring", "label": "Control monitoring results/exceptions"}, {"slug": "security_assessments", "label": "Security assessments (general)"}, {"slug": "penetration_tests", "label": "Penetration testing activities"}, {"slug": "security_reviews", "label": "Quarterly security reviews"}, {"slug": "findings", "label": "Audit/assessment findings"}, {"slug": "remediation", "label": "Remediation tasks & owners"}, {"slug": "tasks", "label": "General tasks/cross-functional actions"}, {"slug": "issues", "label": "Issues/bugs/defects (non-audit)"}, {"slug": "evidence_requests", "label": "Outstanding evidence asks"}], "workflows": [{"slug": "program_readiness", "label": "Stand up/upgrade a compliance program across selected packs", "params": {"packs": {"type": "array", "required": true, "description": "Framework pack IDs"}, "scope_boundary": {"type": "string", "required": true, "description": "Scope definition"}, "owner_role": {"type": "string", "required": true, "description": "Responsible role"}, "target_date": {"type": "string", "required": true, "description": "Target completion date"}}}, {"slug": "program_external_assessment", "label": "Plan/coordinate any external assessment", "params": {"assessment_scheme": {"type": "string", "required": true, "description": "Assessment type"}, "target_dates": {"type": "string", "required": false, "description": "Target dates"}}}, {"slug": "program_maintenance", "label": "Continuous compliance + surveillance/recert prep", "params": {"cadences": {"type": "object", "required": true, "description": "Risk, audit, policy, training cadences"}}}, {"slug": "gdpr_quick_start", "label": "Guided setup for GDPR pack (future-enabled)", "params": {}}, {"slug": "dpa_pack", "label": "Create DPA/SCC bundle", "params": {"processor_name": {"type": "string", "required": false, "description": "Processor name"}, "transfer_country": {"type": "string", "required": false, "description": "Transfer country"}}}, {"slug": "dpia", "label": "Start a DPIA/TIA workflow", "params": {"system_name": {"type": "string", "required": true, "description": "System name"}, "owner_role": {"type": "string", "required": true, "description": "Owner role"}, "due_in_days": {"type": "integer", "required": true, "description": "Due in days"}}}, {"slug": "incident_breach", "label": "Incident / breach handling", "params": {"incident_title": {"type": "string", "required": true, "description": "Incident title"}, "severity": {"type": "string", "required": true, "description": "Incident severity"}, "owner_role": {"type": "string", "required": true, "description": "Owner role"}}}, {"slug": "internal_audit", "label": "Internal audit cycle", "params": {"scope": {"type": "string", "required": true, "description": "Audit scope"}, "start_date": {"type": "string", "required": true, "description": "Start date"}, "owner_role": {"type": "string", "required": true, "description": "Owner role"}}}, {"slug": "management_review", "label": "Management review meeting cycle", "params": {"period": {"type": "string", "required": true, "description": "Review period"}, "meeting_date": {"type": "string", "required": true, "description": "Meeting date"}}}, {"slug": "exception", "label": "Control/Policy exception request & approval", "params": {"control_id": {"type": "string", "required": true, "description": "Control ID"}, "justification": {"type": "string", "required": true, "description": "Justification"}, "expiry_date": {"type": "string", "required": true, "description": "Expiry date"}}}, {"slug": "vendor_dd", "label": "Vendor due-diligence workflow", "params": {"vendor_name": {"type": "string", "required": true, "description": "Vendor name"}, "risk_tier": {"type": "string", "required": false, "description": "Risk tier"}}}, {"slug": "dsr_fulfillment", "label": "Data subject request fulfillment", "params": {"request_id": {"type": "string", "required": true, "description": "Request ID"}, "request_type": {"type": "string", "required": true, "description": "Request type"}}}, {"slug": "policy_refresh", "label": "Scheduled policy review/refresh", "params": {"policy_id": {"type": "string", "required": true, "description": "Policy ID"}, "review_date": {"type": "string", "required": true, "description": "Review date"}}}, {"slug": "risk_assessment", "label": "Periodic risk assessment", "params": {"scope": {"type": "string", "required": true, "description": "Assessment scope"}, "method": {"type": "string", "required": false, "description": "Assessment method"}}}, {"slug": "bc_dr_plan", "label": "Create/maintain BC/DR plan", "params": {"plan_scope": {"type": "string", "required": true, "description": "Plan scope"}, "owner_role": {"type": "string", "required": true, "description": "Owner role"}}}], "templates": [{"slug": "isms_policy", "category": "policy", "label": "ISMS Policy"}, {"slug": "risk_management_policy", "category": "policy", "label": "Risk Management Policy"}, {"slug": "asset_management_policy", "category": "policy", "label": "Asset Management Policy"}, {"slug": "access_control_policy", "category": "policy", "label": "Access Control Policy"}, {"slug": "change_management_policy", "category": "policy", "label": "Change Management Policy"}, {"slug": "incident_response_plan", "category": "plan", "label": "Incident Response Plan"}, {"slug": "logging_monitoring_policy", "category": "policy", "label": "Logging & Monitoring Policy"}, {"slug": "malware_protection_policy", "category": "policy", "label": "Malware Protection Policy"}, {"slug": "backup_dr_policy", "category": "policy", "label": "Backup & DR Policy"}, {"slug": "bc_dr_plan", "category": "plan", "label": "BC/DR Plan"}, {"slug": "vendor_security_policy", "category": "policy", "label": "Vendor Security Policy"}, {"slug": "encryption_policy", "category": "policy", "label": "Encryption Policy"}, {"slug": "statement_of_applicability", "category": "report", "label": "Statement of Applicability"}, {"slug": "internal_audit_plan", "category": "plan", "label": "Internal Audit Plan"}, {"slug": "internal_audit_report", "category": "report", "label": "Internal Audit Report"}, {"slug": "management_review_minutes", "category": "record", "label": "Management Review Minutes"}, {"slug": "training_policy", "category": "policy", "label": "Training Policy"}, {"slug": "security_awareness_materials", "category": "template", "label": "Security Awareness Materials"}, {"slug": "kpi_metrics_report", "category": "report", "label": "KPI Metrics Report"}, {"slug": "privacy_policy_external_notice", "category": "notice", "label": "Privacy Policy External Notice"}, {"slug": "privacy_internal_policy", "category": "policy", "label": "Privacy Internal Policy"}, {"slug": "data_inventory_ropa_export", "category": "register_export", "label": "Data Inventory ROPA Export"}, {"slug": "dpia_template", "category": "template", "label": "DPIA Template"}, {"slug": "dsr_procedure", "category": "procedure", "label": "DSR Procedure"}, {"slug": "retention_schedule", "category": "policy", "label": "Retention Schedule"}, {"slug": "dpa_pack", "category": "contract", "label": "DPA Pack"}, {"slug": "nda_mutual", "category": "contract", "label": "NDA (Mutual)"}, {"slug": "nda_unilateral", "category": "contract", "label": "NDA (Unilateral)"}, {"slug": "supplier_requirements_addendum", "category": "contract", "label": "Supplier Requirements Addendum"}, {"slug": "secure_development_policy", "category": "policy", "label": "Secure Development Policy"}, {"slug": "vulnerability_management_procedure", "category": "procedure", "label": "Vulnerability Management Procedure"}, {"slug": "access_review_procedure", "category": "procedure", "label": "Access Review Procedure"}, {"slug": "change_advisory_board_tor", "category": "policy", "label": "Change Advisory Board ToR"}], "evidence_buckets": ["malware_protection_controls", "endpoint_protection_logs", "patch_management_reports", "backup_and_restore_procedures", "backup_test_results", "edr_policies_exports", "siem_alert_exports", "iam_access_reviews", "privileged_access_logs", "vulnerability_scan_reports", "pentest_reports", "vendor_dd_packages", "incident_tickets", "breach_notifications", "training_completion_exports", "policy_acknowledgments", "risk_assessment_records", "risk_treatment_approvals", "soa_evidence_pack", "change_approvals", "document_versions", "audit_reports", "management_review_minutes", "bc_dr_exercise_reports", "data_inventory_exports", "access_control_matrix_exports", "nda_signed_copies", "employee_nda_acknowledgments", "dpa_signed_pack"], "naming_rules": {"evidence_buckets": {"case": "snake_case", "format": "nouns only, avoid verbs", "preference": "system/control-oriented names over project names"}, "workflow_params": {"validation": "All required params must be provided", "types": ["string", "integer", "array", "object"]}}, "vector_db_compatibility": {"chromadb": {"metadata_fields": ["framework_ids", "control_ids", "actions"], "search_support": "Local development, embedding testing", "limitations": "No RLS, single-tenant only"}, "supabase_vector": {"metadata_fields": ["framework_ids", "control_ids", "actions", "org_id"], "search_support": "Production multi-tenant with RLS", "rls_required": true, "org_isolation": "Strict org_id-based isolation"}}}