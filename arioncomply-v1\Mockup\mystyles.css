/* File: arioncomply-v1/Mockup/mystyles.css */
/* ArionComply - Shared Styles */
/* Modern Clean Design System */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-blue: #2563eb;
  --ai-purple: #7c3aed;
  --success-green: #10b981;
  --warning-amber: #f59e0b;
  --danger-red: #ef4444;
  --bg-white: #ffffff;
  --bg-light: #f8fafc;
  --bg-gray: #f1f5f9;
  --text-dark: #1e293b;
  --text-gray: #64748b;
  --border-light: #e2e8f0;
  --shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
  --border-radius-sm: 6px;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
  background: var(--bg-light);
  color: var(--text-dark);
  line-height: 1.5;
}

/* Layout Structure */
.app-container {
  display: flex;
  min-height: 100vh;
}

/* Sidebar - Completely Hidden by Default */
.sidebar {
  width: 250px;
  background: var(--bg-white);
  border-right: 1px solid var(--border-light);
  position: fixed;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 101;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.sidebar.open {
  transform: translateX(0);
}

/* Sidebar backdrop */
.sidebar-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 100;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.sidebar-backdrop.active {
  opacity: 1;
  visibility: visible;
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-light);
  white-space: nowrap;
}

.logo {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-blue);
}

.nav-menu {
  list-style: none;
  padding: 1rem 0;
  overflow-y: auto;
  flex: 1;
}

.nav-item {
  margin: 0.25rem 0;
}

.nav-item.has-sub > .nav-link::after {
  content: "\25BC";
  margin-left: auto;
  font-size: 0.75rem;
  transition: transform 0.2s;
}

.nav-item.has-sub.open > .nav-link::after {
  transform: rotate(180deg);
}

.sub-menu {
  list-style: none;
  padding-left: 1.5rem;
  display: none;
}

.nav-item.has-sub.open > .sub-menu {
  display: block;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--text-gray);
  text-decoration: none;
  transition: all 0.15s ease;
  white-space: nowrap;
}

.nav-link:hover,
.nav-link.active {
  background: var(--bg-gray);
  color: var(--primary-blue);
}

.nav-link i {
  width: 20px;
  margin-right: 0.75rem;
  text-align: center;
}

/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Header */
.header {
  background: var(--bg-white);
  border-bottom: 1px solid var(--border-light);
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.menu-toggle {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--border-radius-sm);
  color: var(--text-gray);
  transition: all 0.15s ease;
}

.menu-toggle:hover {
  background: var(--bg-gray);
  color: var(--primary-blue);
}

.breadcrumb {
  color: var(--text-gray);
  font-size: 0.875rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-bar {
  display: flex;
  align-items: center;
  background: var(--bg-light);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-sm);
  padding: 0.5rem 1rem;
  width: 300px;
}

.search-bar input {
  border: none;
  background: none;
  outline: none;
  flex: 1;
  margin-left: 0.5rem;
}

/* Notification system */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  color: white;
  font-weight: 500;
  z-index: 2000;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification.show {
  opacity: 1;
  transform: translateX(0);
}

.notification-success {
  background: var(--success-green);
}

.notification-error {
  background: var(--danger-red);
}

.notification-info {
  background: var(--primary-blue);
}

.notification-warning {
  background: var(--warning-amber);
}

.notification-btn,
.user-menu {
  position: relative;
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: background 0.15s ease;
}

.notification-btn:hover,
.user-menu:hover {
  background: var(--bg-gray);
}

.notification-badge {
  position: absolute;
  top: 2px;
  right: 2px;
  background: var(--danger-red);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Content Area */
.content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

/* Cards and Components */
.card {
  background: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-subtle);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.kpi-card {
  background: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-subtle);
  padding: 1.5rem;
  text-align: center;
}

.kpi-value {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.kpi-label {
  color: var(--text-gray);
  font-size: 0.875rem;
}

.kpi-card.success .kpi-value {
  color: var(--success-green);
}
.kpi-card.warning .kpi-value {
  color: var(--warning-amber);
}
.kpi-card.danger .kpi-value {
  color: var(--danger-red);
}
.kpi-card.primary .kpi-value {
  color: var(--primary-blue);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-sm);
  border: none;
  cursor: pointer;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.15s ease;
  gap: 0.5rem;
}

.btn-primary {
  background: var(--primary-blue);
  color: white;
}

.btn-primary:hover {
  background: #1d4ed8;
}

.btn-secondary {
  background: var(--bg-gray);
  color: var(--text-dark);
}

.btn-secondary:hover {
  background: #e2e8f0;
}

.btn-success {
  background: var(--success-green);
  color: white;
}

.btn-ai {
  background: var(--ai-purple);
  color: white;
}

/* Tables */
.table-container {
  background: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-subtle);
  overflow: hidden;
}

.table-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 0.75rem 1.5rem;
  text-align: left;
  border-bottom: 1px solid var(--border-light);
}

.table th {
  font-weight: 600;
  color: var(--text-gray);
  font-size: 0.875rem;
}

.table tr:hover {
  background: var(--bg-light);
}

/* Status badges */
.badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-success {
  background: #dcfce7;
  color: #166534;
}

.badge-warning {
  background: #fef3c7;
  color: #92400e;
}

.badge-danger {
  background: #fee2e2;
  color: #991b1b;
}

.badge-info {
  background: #dbeafe;
  color: #1e40af;
}

.badge-ai {
  background: #ede9fe;
  color: #6b21a8;
}

/* Filter panels - hidden by default */
.filter-panel {
  background: var(--bg-white);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  display: none;
}

.filter-panel.active {
  display: block;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-gray);
}

.form-select,
.form-input {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-sm);
  outline: none;
}

.form-select:focus,
.form-input:focus {
  border-color: var(--primary-blue);
}

/* Page title and actions */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.page-subtitle {
  color: var(--text-gray);
  font-size: 0.875rem;
}

.page-actions {
  display: flex;
  gap: 1rem;
}

/* Charts placeholder */
.chart-placeholder {
  background: var(--bg-light);
  border: 2px dashed var(--border-light);
  border-radius: var(--border-radius);
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-gray);
  font-style: italic;
}

/* Risk Heat Map Styling */
.risk-heatmap {
  display: grid;
  grid-template-columns: 100px repeat(5, 1fr);
  grid-template-rows: 50px repeat(5, 1fr);
  gap: 0.5rem;
  max-width: 600px;
  margin: 1rem 0;
}

.risk-axis-label {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-gray);
}

.risk-cell {
  width: 70px;
  height: 70px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.15s ease;
  position: relative;
  border: 2px solid transparent;
}

.risk-cell:hover {
  transform: scale(1.05);
  border-color: var(--primary-blue);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.risk-cell.very-low {
  background: #22c55e;
  color: white;
}

.risk-cell.low {
  background: #84cc16;
  color: white;
}

.risk-cell.medium {
  background: #fbbf24;
  color: #92400e;
}

.risk-cell.high {
  background: #f59e0b;
  color: white;
}

.risk-cell.very-high {
  background: #ef4444;
  color: white;
}

.risk-cell.critical {
  background: #dc2626;
  color: white;
}

/* Tooltip styling */
.tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--text-dark);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.15s ease;
  z-index: 1000;
  pointer-events: none;
  max-width: 200px;
  white-space: normal;
  text-align: center;
}

.tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: var(--text-dark);
}

.risk-cell:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

/* AI Chat Popup */
.chat-trigger {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 60px;
  height: 60px;
  background: var(--ai-purple);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
  transition: all 0.15s ease;
  z-index: 1000;
}

.chat-trigger:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(124, 58, 237, 0.4);
}

/* Chat popup improvements */
.chat-popup {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 400px;
  height: 600px;
  background: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  display: none;
  flex-direction: column;
  resize: both;
  overflow: auto;
  min-width: 250px;
  min-height: 300px;
  z-index: 1001;
}

.chat-popup.active {
  display: flex;
}

.chat-iframe {
  border: none;
  width: 100%;
  height: 100%;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}
.avatar-toggle {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin: 0.5rem 1rem;
}

.avatar-container {
  display: none;
  justify-content: center;
  padding: 0.5rem;
}

.avatar-mode .avatar-container {
  display: flex;
}

.microphone-button {
  display: none;
}

.avatar-mode .microphone-button {
  display: inline-block;
}

.chat-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: move; /* allow dragging the chat popup */
}

.chat-title {
  font-weight: 600;
  color: var(--ai-purple);
}

.chat-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--border-radius-sm);
}

.chat-close:hover {
  background: var(--bg-gray);
}

.chat-messages {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

.chat-message {
  margin-bottom: 1rem;
}

.chat-message.ai {
  text-align: left;
}

.chat-message.user {
  text-align: right;
}

.message-bubble {
  display: inline-block;
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  max-width: 80%;
}

.chat-message.ai .message-bubble {
  background: var(--bg-gray);
  color: var(--text-dark);
}

.chat-message.user .message-bubble {
  background: var(--ai-purple);
  color: white;
}

.chat-input-area {
  padding: 1rem;
  border-top: 1px solid var(--border-light);
}

.chat-search {
  margin: 0 0 1rem;
  width: 100%;
}

.chat-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-sm);
  resize: none;
  outline: none;
}

.chat-input:focus {
  border-color: var(--ai-purple);
}

/* AI-specific styling */
.ai-section {
  border-left: 4px solid var(--ai-purple);
  background: linear-gradient(135deg, #faf5ff 0%, #ffffff 100%);
}

.transparency-chain {
  background: var(--bg-light);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin: 1rem 0;
}

.confidence-score {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

/* Confidence bar fixes */
.confidence-bar {
  width: 100px;
  height: 8px;
  background: var(--border-light);
  border-radius: 4px;
  overflow: hidden;
}

.confidence-fill {
  height: 100%;
  background: var(--success-green);
  transition: width 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    width: calc(100vw - 2rem);
    margin: 1rem;
  }

  .chat-popup {
    width: calc(100vw - 2rem);
    height: calc(100vh - 4rem);
    bottom: 1rem;
    right: 1rem;
  }
  .kpi-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }
  .search-bar {
    width: 200px;
  }

  .header {
    padding: 1rem;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .page-actions {
    margin-left: 0;
    width: 100%;
  }
}

/* Splash Screen Styles */
.splash-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--bg-light);
  gap: 1rem;
}

.splash-logo {
  font-size: 3rem;
  color: var(--primary-blue);
  font-weight: bold;
}

.splash-spinner {
  border: 4px solid var(--bg-gray);
  border-top: 4px solid var(--primary-blue);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Avatar Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 1002;
}

.modal.active {
  display: flex;
}

.modal-content {
  background: var(--bg-white);
  border-radius: var(--border-radius);
  width: 400px;
  max-width: 90vw;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--border-light);
  background: var(--ai-purple);
  color: white;
}

.modal-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--border-radius-sm);
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

.modal-body {
  padding: 1.5rem;
  line-height: 1.6;
}
/* Info link styling */
.info-link {
  color: var(--ai-purple);
  text-decoration: none;
  margin-left: 0.5rem;
  font-size: 0.875rem;
}

.info-link:hover {
  color: var(--primary-blue);
}

.avatar-options {
  display: flex;
  gap: 0.5rem;
  justify-content: space-between;
}

.avatar-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  cursor: pointer;
}

.avatar-option input {
  margin-bottom: 0.25rem;
}

.modal-footer {
  padding: 0.75rem;
  border-top: 1px solid var(--border-light);
  text-align: right;
}

/* Landing Page */
.auth-container {
  display: none;
  flex-direction: column;
  align-items: center;
  padding-top: 4rem;
}

.auth-form {
  width: 300px;
}

.settings-container {
  display: none;
  padding: 2rem;
}
/* ==========================================================================
   ENHANCED DEMO SECTION STYLES
   Styles for the improved demo experience and demo mode features
   ========================================================================== */

/* Enhanced Demo Section */
.demo-section {
  text-align: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
  border-radius: var(--border-radius);
  padding: 2rem;
  margin: 1rem 0;
  border: 1px solid #c7d2fe;
}

.demo-hero {
  margin-bottom: 1.5rem;
}

.demo-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-blue), var(--ai-purple));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.demo-icon i {
  font-size: 1.5rem;
  color: white;
}

.demo-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.demo-description {
  color: var(--text-gray);
  font-size: 0.875rem;
  line-height: 1.5;
  max-width: 400px;
  margin: 0 auto;
}

.demo-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  margin: 1.5rem 0;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.demo-feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: white;
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-gray);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.demo-feature i {
  color: var(--primary-blue);
  font-size: 0.875rem;
}

.demo-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin: 1.5rem 0;
}

.demo-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 1rem 1.5rem;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-weight: 600;
  text-align: center;
  transition: all 0.15s ease;
  position: relative;
  overflow: hidden;
}

.demo-primary {
  background: linear-gradient(135deg, var(--primary-blue), var(--ai-purple));
  color: white;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.demo-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

.demo-primary:active {
  transform: translateY(0);
}

.demo-secondary {
  background: white;
  color: var(--primary-blue);
  border: 2px solid var(--primary-blue);
}

.demo-secondary:hover {
  background: var(--primary-blue);
  color: white;
}

.demo-button i {
  font-size: 1.25rem;
}

.demo-button span {
  font-size: 1rem;
}

.demo-button small {
  font-size: 0.75rem;
  opacity: 0.8;
  font-weight: 400;
}

.demo-disclaimer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding: 0.75rem;
  background: rgba(37, 99, 235, 0.1);
  border-radius: var(--border-radius-sm);
  border: 1px solid rgba(37, 99, 235, 0.2);
}

.demo-disclaimer i {
  color: var(--primary-blue);
}

.demo-disclaimer small {
  color: var(--text-gray);
  font-size: 0.75rem;
  text-align: center;
  line-height: 1.4;
}

/* Demo button loading state */
.demo-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
}

.demo-button:disabled:hover {
  transform: none;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

/* Demo mode indicators */
.demo-badge {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  font-size: 0.625rem;
  padding: 0.125rem 0.375rem;
  border-radius: 9999px;
  margin-left: 0.5rem;
  font-weight: 500;
}

.demo-indicator-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Demo data highlighting */
tr[data-demo="true"],
.demo-data {
  border-left: 3px solid #f59e0b !important;
  background: rgba(245, 158, 11, 0.05);
}

/* Mobile responsive */
@media (max-width: 640px) {
  .demo-section {
    padding: 1.5rem;
    margin: 0.5rem 0;
  }

  .demo-features {
    grid-template-columns: 1fr;
    max-width: 250px;
  }

  .demo-feature {
    justify-content: center;
  }
}

/* Workflow Builder */
.workflow-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.workflow-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-sm);
}
/* File: arioncomply-v1/Mockup/mystyles.css */
