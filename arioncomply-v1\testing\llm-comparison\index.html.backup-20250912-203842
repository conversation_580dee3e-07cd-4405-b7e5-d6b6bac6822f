<!-- File Description: LLM comparison test harness UI
     Purpose: Provide a browser UI to test Edge functions and LLM outputs
     Notes: Non-production; used for feature validation and demos -->
<!-- File path: index.html (in project root) -->
<!-- Replace the entire contents of your index.html file with this code -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Compliance Assistant: <PERSON> vs GPT</title>
  <link rel="stylesheet" href="style.css">
  <script src="frontend-config.js"></script>
  <style>
    body { font-family: sans-serif; margin: 1em; line-height: 1.5; }
    
    /* Resizable text areas with improved styling */
    textarea { 
      width: 100%; 
      font-size: 1em; 
      margin-top: 1em; 
      font-family: inherit;
      line-height: 1.4;
      padding: 0.75em;
      border: 2px solid #ddd;
      border-radius: 4px;
      resize: both; /* Allow both horizontal and vertical resizing */
      min-height: 100px;
      box-sizing: border-box;
      transition: border-color 0.2s ease;
    }
    
    textarea:focus {
      border-color: #4CAF50;
      outline: none;
      box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
    }
    
    textarea:hover {
      border-color: #aaa;
    }
    
    /* Add a subtle resize indicator */
    textarea::after {
      content: "↘";
      position: absolute;
      bottom: 2px;
      right: 2px;
      color: #ccc;
      pointer-events: none;
    }
    
    /* System prompt specific styling */
    #systemPrompt {
      min-height: 120px;
      background-color: #f9f9f9;
    }
    
    /* User input specific styling */
    #userInput {
      min-height: 80px;
      font-size: 1.1em;
    }
    
    /* Results section */
    #results { 
      display: flex; 
      gap: 1em; 
      margin-top: 1em; 
    }
    
    .panel { 
      width: 50%; 
      background: #f8f8f8; 
      padding: 1em; 
      border: 1px solid #ccc; 
      border-radius: 6px;
      min-height: 200px;
    }
    
    /* Response output areas - resizable and with proper text wrapping */
    .response-output {
      width: 100%;
      min-height: 150px;
      max-height: 600px;
      padding: 0.75em;
      border: 2px solid #e0e0e0;
      border-radius: 4px;
      background: white;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-size: 0.95em;
      line-height: 1.5;
      white-space: pre-wrap; /* Preserve line breaks but allow wrapping */
      word-wrap: break-word; /* Break long words if necessary */
      overflow-y: auto;
      resize: both; /* Make resizable */
      box-sizing: border-box;
      margin-top: 0.5em;
      transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }
    
    .response-output:hover {
      border-color: #bbb;
    }
    
    .response-output:focus {
      outline: none;
      border-color: #4CAF50;
      box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
    }
    
    .response-output.loading {
      background-color: #f0f8ff;
      border-color: #87CEEB;
      font-style: italic;
      color: #666;
    }
    
    .response-output.error {
      background-color: #fff5f5;
      border-color: #ff6b6b;
      color: #d32f2f;
    }
    
    .response-output.success {
      background-color: #f8fff8;
      border-color: #4CAF50;
    }
    
    /* Config section */
    #config input { 
      margin-right: 1em; 
      padding: 0.5em;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    
    /* Button styling */
    button {
      padding: 0.6em 1.2em;
      margin: 0.25em;
      border: none;
      border-radius: 4px;
      background-color: #4CAF50;
      color: white;
      cursor: pointer;
      font-size: 0.95em;
      transition: background-color 0.2s;
    }
    
    button:hover {
      background-color: #45a049;
    }
    
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    
    /* Save form styling */
    #saveTestForm {
      background: #f5f5f5;
      border: 2px solid #ddd;
      border-radius: 8px;
    }
    
    #saveTestForm label {
      display: block;
      margin-bottom: 0.5em;
      font-weight: 500;
    }
    
    #saveTestForm input, #saveTestForm select, #saveTestForm textarea {
      width: 100%;
      margin-top: 0.25em;
      margin-bottom: 0.5em;
      padding: 0.5em;
      border: 2px solid #ddd;
      border-radius: 4px;
      font-family: inherit;
    }
    
    #saveTestForm textarea {
      resize: both;
      min-height: 80px;
      line-height: 1.4;
    }
    
    #saveTestForm input:focus, #saveTestForm select:focus, #saveTestForm textarea:focus {
      border-color: #4CAF50;
      outline: none;
      box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
    }
    
    /* Responsive design for smaller screens */
    @media (max-width: 768px) {
      #results {
        flex-direction: column;
      }
      
      .panel {
        width: 100%;
      }
      
      /* Adjust textarea sizes for mobile */
      textarea {
        min-height: 80px;
      }
      
      .response-output {
        min-height: 120px;
      }
    }
    
    /* Saved tests response areas */
    .saved-test-response {
      background: #f8f8f8; 
      padding: 0.5em; 
      border-radius: 3px; 
      max-height: 150px; 
      overflow-y: auto;
      white-space: pre-wrap;
      word-wrap: break-word;
      font-family: inherit;
      line-height: 1.4;
      border: 1px solid #e0e0e0;
      resize: vertical;
      min-height: 60px;
    }
    
    .saved-test-error {
      background: #ffe6e6; 
      padding: 0.5em; 
      border-radius: 3px; 
      color: #d32f2f;
      white-space: pre-wrap;
      word-wrap: break-word;
      border: 1px solid #ffcdd2;
    }
    /* Parameter testing additions - minimal styling */
.parameter-section {
  background: #f8f9fa;
  padding: 1em;
  margin: 1em 0;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.parameter-controls {
  display: none;
  margin-top: 1em;
}

.parameter-controls.expanded {
  display: block;
}

.parameter-row {
  display: flex;
  gap: 1em;
  margin: 0.5em 0;
  align-items: center;
}

.parameter-input {
  width: 80px;
  padding: 0.3em;
  border: 1px solid #ccc;
  border-radius: 3px;
}

.toggle-btn {
  background: #007acc;
  color: white;
  border: none;
  padding: 0.5em 1em;
  border-radius: 3px;
  cursor: pointer;
}

.toggle-btn:hover {
  background: #005a99;
}
  </style>
  <script src="extracted-functions.js"></script>
</head>
<body>
  <h1>Compliance Assistant Tester (Claude vs GPT)</h1>

  <div id="config">
    <label>Supabase URL: <input type="text" id="supabaseUrl" placeholder="https://your-project.supabase.co" /></label>
    <button onclick="saveConfig()">Save Config</button>
  </div>

  <!-- Add this section to your index.html after the config section and before the system prompt -->

<!-- Model Selection GUI -->
<div class="model-selection-section" style="background: #f8f9fa; padding: 1em; margin: 1em 0; border-radius: 4px; border: 1px solid #dee2e6;">
  <h3 style="margin: 0 0 1em 0;">🤖 Model Selection</h3>
  
  <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2em;">
    <!-- Left Model Selection -->
    <div style="border: 1px solid #ddd; padding: 1em; border-radius: 4px; background: white;">
      <h4 style="margin: 0 0 0.5em 0; color: #333;">Left Panel Model</h4>
      <div style="margin-bottom: 1em;">
        <label style="display: block; margin-bottom: 0.5em; font-weight: bold;">Provider:</label>
        <select id="left-provider" onchange="updateModelOptions('left')" style="width: 100%; padding: 0.5em; border: 1px solid #ccc; border-radius: 3px;">
          <option value="anthropic">Anthropic (Claude)</option>
          <option value="openai">OpenAI (GPT)</option>
        </select>
      </div>
      <div>
        <label style="display: block; margin-bottom: 0.5em; font-weight: bold;">Model:</label>
        <select id="left-model" style="width: 100%; padding: 0.5em; border: 1px solid #ccc; border-radius: 3px;">
          <option value="claude-3-5-sonnet-20241022">Claude 3.5 Sonnet</option>
        </select>
      </div>
    </div>
    
    <!-- Right Model Selection -->
    <div style="border: 1px solid #ddd; padding: 1em; border-radius: 4px; background: white;">
      <h4 style="margin: 0 0 0.5em 0; color: #333;">Right Panel Model</h4>
      <div style="margin-bottom: 1em;">
        <label style="display: block; margin-bottom: 0.5em; font-weight: bold;">Provider:</label>
        <select id="right-provider" onchange="updateModelOptions('right')" style="width: 100%; padding: 0.5em; border: 1px solid #ccc; border-radius: 3px;">
          <option value="openai">OpenAI (GPT)</option>
          <option value="anthropic">Anthropic (Claude)</option>
        </select>
      </div>
      <div>
        <label style="display: block; margin-bottom: 0.5em; font-weight: bold;">Model:</label>
        <select id="right-model" style="width: 100%; padding: 0.5em; border: 1px solid #ccc; border-radius: 3px;">
          <option value="gpt-4o">GPT-4o</option>
        </select>
      </div>
    </div>
  </div>
  
  <!-- Quick Preset Buttons -->
  <div style="margin-top: 1em; text-align: center; padding-top: 1em; border-top: 1px solid #ddd;">
    <button type="button" onclick="setModelPreset('claude-vs-gpt')" style="margin: 0.25em;">Claude vs GPT-4o</button>
    <button type="button" onclick="setModelPreset('claude-vs-claude')" style="margin: 0.25em;">Claude 3.5 vs Claude Opus</button>
    <button type="button" onclick="setModelPreset('gpt-vs-gpt')" style="margin: 0.25em;">GPT-4o vs GPT-4 Turbo</button>
    <button type="button" onclick="updateInterfaceFromSelection()" style="margin: 0.25em; background: #28a745;">Apply Selection</button>
  </div>
</div>


  <textarea id="systemPrompt" rows="6" placeholder="Define the assistant's role and boundaries here...">
You are an ISO 27001 and GDPR compliance assistant.
- Provide clear, concise guidance based on ISO/IEC 27001:2022 and GDPR.
- Ask clarifying questions before answering.
- Use bullet points, section references, and avoid speculation.
Restrictions:
- Do NOT provide legal advice.
- Do NOT guess or hallucinate.
- Do NOT answer non-compliance questions.
  </textarea>
<!-- Parameter Testing Section -->
<div class="parameter-section">
  <button type="button" onclick="toggleParams()" style="background: #007acc; color: white; border: none; padding: 0.5em 1em; border-radius: 3px; cursor: pointer;">
    🔧 Parameter Testing
  </button>
  
  <div id="param-controls" style="display: none; margin-top: 1em; background: #f8f9fa; padding: 1em; border-radius: 4px;">
    <div style="display: flex; gap: 1em; margin: 0.5em 0; align-items: center;">
      <label>OpenAI Temp:</label>
      <input type="number" id="openai-temp" min="0" max="1" step="0.1" value="0.7" style="width: 80px; padding: 0.3em;">
      
      <label>Claude Temp:</label>
      <input type="number" id="claude-temp" min="0" max="1" step="0.1" value="0.7" style="width: 80px; padding: 0.3em;">
      
      <label>Max Tokens:</label>
      <input type="number" id="max-tokens" min="100" max="2000" value="1024" style="width: 80px; padding: 0.3em;">
      
      <button type="button" onclick="resetParams()">Reset</button>
    </div>
  </div>
</div>
  <textarea id="userInput" placeholder="Ask your compliance question..."></textarea>
  <div style="display: flex; gap: 1em; margin-top: 0.5em;">
    <button onclick="submitPrompt('both')">Submit to Both</button>
    <button onclick="submitPrompt('claude')">Claude Only</button>
    <button onclick="submitPrompt('openai')">OpenAI Only</button>
    <button onclick="saveCurrentTest()" id="saveTestBtn" disabled>Save Test Results</button>
    <button onclick="loadSavedTests()">View Saved Tests</button>
  </div>



  <!-- Save Test Form (hidden by default) -->
  <div id="saveTestForm" style="display: none; background: #f0f0f0; padding: 1em; margin: 1em 0; border-radius: 5px;">
    <h3>Save Test Results</h3>
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1em;">
      <div>
        <label>Test Name: <input type="text" id="testName" placeholder="e.g., ISO 27001 Risk Assessment" /></label>
        <label>Category: 
          <select id="testCategory">
            <option value="general">General</option>
            <option value="iso27001">ISO 27001</option>
            <option value="gdpr">GDPR</option>
            <option value="risk-assessment">Risk Assessment</option>
            <option value="documentation">Documentation</option>
            <option value="compliance">Compliance</option>
          </select>
        </label>
      </div>
      <div>
        <label>Rating (1-5): 
          <select id="testRating">
            <option value="">No rating</option>
            <option value="5">5 - Excellent</option>
            <option value="4">4 - Good</option>
            <option value="3">3 - Average</option>
            <option value="2">2 - Poor</option>
            <option value="1">1 - Very Poor</option>
          </select>
        </label>
        <label>Preferred Response: 
          <select id="preferredResponse">
            <option value="">No preference</option>
            <option value="claude">Claude</option>
            <option value="openai">OpenAI</option>
            <option value="both">Both equally good</option>
            <option value="neither">Neither satisfactory</option>
          </select>
        </label>
      </div>
    </div>
    <label>Notes: <textarea id="testNotes" rows="3" placeholder="Additional observations, feedback, or analysis..."></textarea></label>
    <div style="margin-top: 1em;">
      <button onclick="confirmSaveTest()">Save to Database</button>
      <button onclick="cancelSaveTest()">Cancel</button>
    </div>
  </div>

  <div id="results">
    <div class="panel">
      <h2>Claude Response</h2>
      <div id="claudeOutput" class="response-output" tabindex="0">...</div>
    </div>
    <div class="panel">
      <h2>OpenAI GPT Response</h2>
      <div id="gptOutput" class="response-output" tabindex="0">...</div>
    </div>
  </div>

  <div style="text-align: center; margin-top: 1em; color: #666; font-size: 0.9em;">
    💡 Tip: All text areas are resizable - drag the corner to make them larger
  </div>

  <!-- Saved Tests Section -->
  <div id="savedTestsSection" style="display: none; margin-top: 2em;">
    <h2>Saved Compliance Tests</h2>
    <div style="margin-bottom: 1em;">
      <label>Filter by category: 
        <select id="categoryFilter" onchange="loadSavedTests()">
          <option value="">All categories</option>
          <option value="general">General</option>
          <option value="iso27001">ISO 27001</option>
          <option value="gdpr">GDPR</option>
          <option value="risk-assessment">Risk Assessment</option>
          <option value="documentation">Documentation</option>
          <option value="compliance">Compliance</option>
        </select>
      </label>
      <button onclick="closeSavedTests()" style="float: right;">Close</button>
    </div>
    <div id="savedTestsList" style="max-height: 400px; overflow-y: auto;">
      Loading...
    </div>
  </div>

</body>
</html>
