"""
File: arioncomply-v1/ai-backend/python-backend/services/ingestion/supabase_private_ingestion.py
File Description: Supabase Vector ingestion pipeline for private organizational data
Purpose: Ingest company-specific documents with strict org-scoped isolation and RLS
Inputs: Company policies, assessment results, proprietary documents, internal procedures
Outputs: Supabase Vector DB with org-scoped embeddings and strict tenant isolation
Security: Row-Level Security (RLS), org_id validation, data classification enforcement
Notes: Specialized for private organizational content with audit trails and compliance tracking
"""

import os
import json
import logging
import hashlib
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass
from enum import Enum
import aiohttp

from .locling_extractor import extract_blocks
from .chunker import chunk_blocks
from .embedder import embed_texts_async
from .dual_vector_ingestion import DataClassification

logger = logging.getLogger(__name__)

VECTOR_SUPABASE_URL = os.getenv("VECTOR_SUPABASE_URL")
VECTOR_SUPABASE_SERVICE_KEY = os.getenv("VECTOR_SUPABASE_SERVICE_KEY")


class PrivateDocumentType(str, Enum):
    """Types of private organizational documents."""
    COMPANY_POLICY = "company_policy"              # Corporate policies
    SECURITY_PROCEDURE = "security_procedure"      # Security procedures
    COMPLIANCE_ASSESSMENT = "compliance_assessment" # Assessment results
    INCIDENT_REPORT = "incident_report"            # Security incidents
    AUDIT_REPORT = "audit_report"                  # Internal/external audits
    TRAINING_MATERIAL = "training_material"        # Internal training
    VENDOR_AGREEMENT = "vendor_agreement"          # Third-party agreements
    BUSINESS_PROCESS = "business_process"          # Business procedures
    TECHNICAL_DOCUMENTATION = "technical_documentation" # Technical specs
    PROPRIETARY_RESEARCH = "proprietary_research"  # Internal research


class SensitivityLevel(str, Enum):
    """Data sensitivity classification for access control."""
    INTERNAL = "internal"           # General internal use
    CONFIDENTIAL = "confidential"   # Limited access
    RESTRICTED = "restricted"       # Highly restricted
    PUBLIC_INTERNAL = "public_internal" # Internal but non-sensitive


@dataclass
class PrivateIngestionRequest:
    """Structured request for private document ingestion."""
    org_id: str
    file_path: str
    document_type: PrivateDocumentType
    sensitivity_level: SensitivityLevel
    title: str
    owner_department: str
    version: str = "v1"
    source: str = "internal_upload"
    retention_period_days: Optional[int] = None
    compliance_frameworks: Optional[List[str]] = None  # Which frameworks this applies to
    metadata: Optional[Dict[str, Any]] = None
    pipeline_name: Optional[str] = None


@dataclass
class PrivateIngestionResult:
    """Result of private document ingestion."""
    org_id: str
    doc_id: str
    chunks_ingested: int
    embeddings_created: int
    pipeline_used: str
    document_type: PrivateDocumentType
    sensitivity_level: SensitivityLevel
    content_hash: str
    compliance_tags: List[str]
    audit_trail: Dict[str, Any]
    retention_metadata: Dict[str, Any]


class SupabasePrivateIngestion:
    """Specialized ingestion pipeline for private organizational data."""
    
    def __init__(self):
        """Initialize private ingestion with default clients/config."""
        if not VECTOR_SUPABASE_URL or not VECTOR_SUPABASE_SERVICE_KEY:
            raise Exception("Supabase Vector configuration missing - required for private ingestion")
        
        self.supabase_url = VECTOR_SUPABASE_URL
        self.service_key = VECTOR_SUPABASE_SERVICE_KEY
        
        # Define compliance framework mappings
        self.framework_mappings = {
            "iso_27001": ["information_security", "access_control", "incident_management"],
            "gdpr": ["data_privacy", "data_protection", "consent_management"],
            "sox": ["financial_controls", "audit_trails", "segregation_of_duties"],
            "hipaa": ["healthcare_privacy", "phi_protection", "access_controls"],
            "pci_dss": ["payment_security", "cardholder_data", "network_security"],
            "nist_csf": ["identify", "protect", "detect", "respond", "recover"],
            "soc2": ["security", "availability", "processing_integrity", "confidentiality", "privacy"]
        }
    
    async def ingest_private_document(self, request: PrivateIngestionRequest) -> PrivateIngestionResult:
        """
        Ingest private organizational document with strict tenant isolation.
        
        Args:
            request: Structured private ingestion request
            
        Returns:
            PrivateIngestionResult with org-scoped audit trail
        """
        audit_trail = {
            "org_id": request.org_id,
            "start_time": self._get_timestamp(),
            "request": self._sanitize_request_for_audit(request),
            "steps": [],
            "security_validations": []
        }
        
        try:
            # 1. Security validation and request verification
            await self._validate_org_access(request.org_id)
            self._validate_private_request(request)
            audit_trail["steps"].append("security_validation_passed")
            audit_trail["security_validations"].append("org_access_verified")
            
            # 2. Calculate content hash for integrity
            content_hash = self._calculate_file_hash(request.file_path)
            audit_trail["content_hash"] = content_hash
            audit_trail["steps"].append("content_hash_calculated")
            
            # 3. Extract and classify content with privacy awareness
            blocks = extract_blocks(request.file_path)
            classified_blocks = self._classify_private_content(
                blocks, request.document_type, request.sensitivity_level
            )
            audit_trail["steps"].append(f"extracted_and_classified_{len(classified_blocks)}_blocks")
            
            # 4. Apply privacy-aware chunking
            chunks = self._chunk_private_content(classified_blocks, request.sensitivity_level)
            audit_trail["steps"].append(f"created_{len(chunks)}_privacy_chunks")
            
            # 5. Generate embeddings with audit trail
            texts = [c["text"] for c in chunks]
            embedding_result = await embed_texts_async(
                texts,
                pipeline_name=request.pipeline_name,
                trace_id=f"private_{request.org_id}_{request.document_type.value}"
            )
            audit_trail["steps"].append(f"generated_embeddings_with_{embedding_result.pipeline_name}")
            audit_trail["embedding_metadata"] = {
                "pipeline": embedding_result.pipeline_name,
                "dimension": embedding_result.embedding_dimension,
                "model_version": getattr(embedding_result, 'model_version', 'unknown')
            }
            
            # 6. Create document record with retention metadata
            doc_id = await self._create_private_document_record(request, content_hash, audit_trail)
            
            # 7. Insert chunks with org-scoped security
            await self._insert_private_chunks_and_embeddings(
                request.org_id, doc_id, chunks, embedding_result, request
            )
            audit_trail["steps"].append("chunks_and_embeddings_inserted")
            
            # 8. Generate compliance tags and metadata
            compliance_tags = self._generate_compliance_tags(request, classified_blocks)
            retention_metadata = self._generate_retention_metadata(request)
            
            # 9. Create audit record
            await self._create_ingestion_audit_record(request.org_id, doc_id, audit_trail)
            
            audit_trail["end_time"] = self._get_timestamp()
            audit_trail["success"] = True
            
            result = PrivateIngestionResult(
                org_id=request.org_id,
                doc_id=doc_id,
                chunks_ingested=len(chunks),
                embeddings_created=len(chunks),
                pipeline_used=embedding_result.pipeline_name,
                document_type=request.document_type,
                sensitivity_level=request.sensitivity_level,
                content_hash=content_hash,
                compliance_tags=compliance_tags,
                audit_trail=audit_trail,
                retention_metadata=retention_metadata
            )
            
            logger.info(f"Private ingestion successful: org={request.org_id}, doc={doc_id}, chunks={len(chunks)}")
            return result
            
        except Exception as e:
            error_msg = f"Private ingestion failed: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            audit_trail["end_time"] = self._get_timestamp()
            audit_trail["success"] = False
            audit_trail["error"] = error_msg
            
            # Create error audit record if possible
            try:
                await self._create_error_audit_record(request.org_id, audit_trail)
            except:
                pass  # Don't let audit failures block error reporting
            
            raise Exception(error_msg)
    
    async def _validate_org_access(self, org_id: str):
        """Validate organization access and existence."""
        # This would integrate with the organization management system
        # For now, we'll do a basic format validation
        if not org_id or len(org_id) < 10:  # Assuming UUID format
            raise ValueError(f"Invalid org_id format: {org_id}")
        
        # In a real implementation, this would:
        # 1. Verify org exists in the application database
        # 2. Check user permissions for this org
        # 3. Validate org is active/not suspended
        # 4. Check storage quotas and limits
        
        logger.debug(f"Organization access validated: {org_id}")
    
    def _classify_private_content(
        self, 
        blocks: List[Dict[str, Any]], 
        document_type: PrivateDocumentType,
        sensitivity_level: SensitivityLevel
    ) -> List[Dict[str, Any]]:
        """Apply privacy-aware content classification."""
        
        classified_blocks = []
        
        for block in blocks:
            # Base classification
            block["document_type"] = document_type.value
            block["sensitivity_level"] = sensitivity_level.value
            
            # Apply document-type-specific classification
            if document_type == PrivateDocumentType.COMPANY_POLICY:
                block = self._classify_policy_content(block)
            elif document_type == PrivateDocumentType.COMPLIANCE_ASSESSMENT:
                block = self._classify_assessment_results(block)
            elif document_type == PrivateDocumentType.INCIDENT_REPORT:
                block = self._classify_incident_content(block)
            elif document_type == PrivateDocumentType.AUDIT_REPORT:
                block = self._classify_audit_content(block)
            
            # Apply sensitivity-based classification
            block = self._apply_sensitivity_classification(block, sensitivity_level)
            
            classified_blocks.append(block)
        
        return classified_blocks
    
    def _classify_policy_content(self, block: Dict[str, Any]) -> Dict[str, Any]:
        """Classify company policy content."""
        text = block.get("text", "").lower()
        
        # Policy section classification
        if any(keyword in text for keyword in ["purpose", "objective", "scope"]):
            block["policy_section"] = "purpose_and_scope"
        elif any(keyword in text for keyword in ["responsibility", "role", "accountability"]):
            block["policy_section"] = "roles_and_responsibilities"
        elif any(keyword in text for keyword in ["procedure", "process", "step"]):
            block["policy_section"] = "procedures"
        elif any(keyword in text for keyword in ["violation", "non-compliance", "penalty"]):
            block["policy_section"] = "enforcement"
        elif any(keyword in text for keyword in ["review", "update", "revision"]):
            block["policy_section"] = "maintenance"
        else:
            block["policy_section"] = "general_content"
        
        # Identify compliance requirements
        frameworks = []
        for framework in self.framework_mappings.keys():
            if framework.replace("_", " ") in text or framework in text:
                frameworks.append(framework)
        block["applicable_frameworks"] = frameworks
        
        return block
    
    def _classify_assessment_results(self, block: Dict[str, Any]) -> Dict[str, Any]:
        """Classify compliance assessment results."""
        text = block.get("text", "").lower()
        
        # Assessment result classification
        if any(keyword in text for keyword in ["pass", "compliant", "satisfactory", "adequate"]):
            block["assessment_result"] = "compliant"
        elif any(keyword in text for keyword in ["fail", "non-compliant", "deficient", "inadequate"]):
            block["assessment_result"] = "non_compliant"
        elif any(keyword in text for keyword in ["partial", "limited", "needs improvement"]):
            block["assessment_result"] = "partial_compliance"
        else:
            block["assessment_result"] = "informational"
        
        # Risk level identification
        if any(keyword in text for keyword in ["critical", "high risk", "severe"]):
            block["risk_level"] = "critical"
        elif any(keyword in text for keyword in ["medium", "moderate", "significant"]):
            block["risk_level"] = "medium"
        elif any(keyword in text for keyword in ["low", "minor", "negligible"]):
            block["risk_level"] = "low"
        else:
            block["risk_level"] = "informational"
        
        return block
    
    def _classify_incident_content(self, block: Dict[str, Any]) -> Dict[str, Any]:
        """Classify incident report content."""
        text = block.get("text", "").lower()
        
        # Incident classification
        if any(keyword in text for keyword in ["data breach", "unauthorized access", "privacy"]):
            block["incident_category"] = "data_breach"
        elif any(keyword in text for keyword in ["malware", "virus", "ransomware"]):
            block["incident_category"] = "malware"
        elif any(keyword in text for keyword in ["phishing", "social engineering"]):
            block["incident_category"] = "social_engineering"
        elif any(keyword in text for keyword in ["system failure", "outage", "downtime"]):
            block["incident_category"] = "system_failure"
        else:
            block["incident_category"] = "general_security"
        
        # Severity assessment
        if any(keyword in text for keyword in ["critical", "severe", "major impact"]):
            block["incident_severity"] = "critical"
        elif any(keyword in text for keyword in ["high", "significant impact"]):
            block["incident_severity"] = "high"
        elif any(keyword in text for keyword in ["medium", "moderate impact"]):
            block["incident_severity"] = "medium"
        else:
            block["incident_severity"] = "low"
        
        return block
    
    def _apply_sensitivity_classification(
        self, 
        block: Dict[str, Any], 
        sensitivity_level: SensitivityLevel
    ) -> Dict[str, Any]:
        """Apply sensitivity-based access controls and metadata."""
        
        block["access_level"] = sensitivity_level.value
        
        # Add access control metadata based on sensitivity
        if sensitivity_level == SensitivityLevel.RESTRICTED:
            block["access_controls"] = {
                "requires_approval": True,
                "audit_access": True,
                "time_limited": True,
                "purpose_required": True
            }
        elif sensitivity_level == SensitivityLevel.CONFIDENTIAL:
            block["access_controls"] = {
                "requires_approval": False,
                "audit_access": True,
                "time_limited": False,
                "purpose_required": False
            }
        else:
            block["access_controls"] = {
                "requires_approval": False,
                "audit_access": False,
                "time_limited": False,
                "purpose_required": False
            }
        
        return block
    
    def _chunk_private_content(
        self, 
        classified_blocks: List[Dict[str, Any]], 
        sensitivity_level: SensitivityLevel
    ) -> List[Dict[str, Any]]:
        """Apply privacy-aware chunking strategies."""
        
        # Use standard chunking as base
        base_chunks = chunk_blocks(classified_blocks)
        
        # Enhance with privacy metadata
        for chunk in base_chunks:
            chunk["privacy_metadata"] = {
                "sensitivity_level": sensitivity_level.value,
                "chunk_id_hash": self._generate_chunk_hash(chunk["text"]),
                "requires_redaction": sensitivity_level in [SensitivityLevel.RESTRICTED, SensitivityLevel.CONFIDENTIAL]
            }
        
        return base_chunks
    
    async def _create_private_document_record(
        self, 
        request: PrivateIngestionRequest, 
        content_hash: str, 
        audit_trail: Dict[str, Any]
    ) -> str:
        """Create document record in Supabase with org-scoped security."""
        
        doc_row = {
            "org_id": request.org_id,
            "title": request.title,
            "source": request.source,
            "version": request.version,
            "hash": content_hash,
            "metadata": {
                "document_type": request.document_type.value,
                "sensitivity_level": request.sensitivity_level.value,
                "owner_department": request.owner_department,
                "compliance_frameworks": request.compliance_frameworks or [],
                "retention_period_days": request.retention_period_days,
                "ingestion_audit_id": audit_trail.get("audit_id"),
                "custom_metadata": request.metadata or {}
            }
        }
        
        doc_id = await self._post_supabase_with_rls("documents", doc_row, request.org_id)
        audit_trail["doc_id"] = doc_id
        return doc_id
    
    async def _insert_private_chunks_and_embeddings(
        self, 
        org_id: str, 
        doc_id: str, 
        chunks: List[Dict[str, Any]], 
        embedding_result,
        request: PrivateIngestionRequest
    ):
        """Insert chunks and embeddings with strict org-scoped isolation."""
        
        for i, chunk in enumerate(chunks):
            # Insert chunk with privacy metadata
            chunk_row = {
                "org_id": org_id,
                "doc_id": doc_id,
                "seq": chunk["seq"],
                "text": chunk["text"],
                "tokens": len(chunk["text"].split()),
                "metadata": {
                    **chunk.get("privacy_metadata", {}),
                    "section": chunk.get("section"),
                    "heading": chunk.get("heading"),
                    "page": chunk.get("page"),
                    "document_type": chunk.get("document_type"),
                    "sensitivity_level": chunk.get("sensitivity_level"),
                    "access_controls": chunk.get("access_controls", {}),
                    **{k: v for k, v in chunk.items() 
                       if k.endswith(("_section", "_result", "_category", "_severity", "_level"))}
                }
            }
            
            chunk_id = await self._post_supabase_with_rls("chunks", chunk_row, org_id)
            
            # Insert embedding with org isolation
            embedding_row = {
                "org_id": org_id,
                "chunk_id": chunk_id,
                "model": embedding_result.pipeline_name,
                "dim": embedding_result.embedding_dimension,
                "embedding": embedding_result.embeddings[i],
                "data_classification": "private"
            }
            
            await self._post_supabase_with_rls("embeddings", embedding_row, org_id)
    
    async def _post_supabase_with_rls(self, table: str, row: Dict[str, Any], org_id: str) -> str:
        """Post to Supabase with RLS headers."""
        url = f"{self.supabase_url}/rest/v1/{table}"
        headers = {
            "apikey": self.service_key,
            "Authorization": f"Bearer {self.service_key}",
            "Content-Type": "application/json",
            "Prefer": "return=representation",
            "X-Client-Info": f"arioncomply-private-ingestion/org-{org_id}"  # For audit trails
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=row) as response:
                if response.status >= 300:
                    error_text = await response.text()
                    raise Exception(f"Supabase POST failed: {response.status} - {error_text}")
                
                data = await response.json()
                return (data[0]["id"] if isinstance(data, list) and data else data.get("id")) or ""
    
    def _generate_compliance_tags(
        self, 
        request: PrivateIngestionRequest, 
        classified_blocks: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate compliance-related tags from content analysis."""
        tags = set()
        
        # Add explicit framework tags
        if request.compliance_frameworks:
            tags.update(request.compliance_frameworks)
        
        # Extract framework tags from content
        for block in classified_blocks:
            frameworks = block.get("applicable_frameworks", [])
            tags.update(frameworks)
        
        # Add document type tags
        tags.add(request.document_type.value)
        tags.add(f"sensitivity_{request.sensitivity_level.value}")
        
        # Add department tag
        tags.add(f"dept_{request.owner_department.lower().replace(' ', '_')}")
        
        return sorted(list(tags))
    
    def _generate_retention_metadata(self, request: PrivateIngestionRequest) -> Dict[str, Any]:
        """Generate retention and lifecycle metadata."""
        from datetime import datetime, timedelta
        
        created_at = datetime.utcnow()
        
        retention_metadata = {
            "created_at": created_at.isoformat(),
            "retention_period_days": request.retention_period_days,
            "owner_department": request.owner_department,
            "document_type": request.document_type.value
        }
        
        # Calculate retention expiry if specified
        if request.retention_period_days:
            expiry_date = created_at + timedelta(days=request.retention_period_days)
            retention_metadata["retention_expiry"] = expiry_date.isoformat()
            retention_metadata["retention_warning_date"] = (expiry_date - timedelta(days=30)).isoformat()
        
        # Set default retention based on document type and sensitivity
        default_retention = self._get_default_retention(request.document_type, request.sensitivity_level)
        if not request.retention_period_days and default_retention:
            retention_metadata["default_retention_days"] = default_retention
            expiry_date = created_at + timedelta(days=default_retention)
            retention_metadata["retention_expiry"] = expiry_date.isoformat()
        
        return retention_metadata
    
    def _get_default_retention(
        self, 
        document_type: PrivateDocumentType, 
        sensitivity_level: SensitivityLevel
    ) -> Optional[int]:
        """Get default retention period based on document type and sensitivity."""
        
        # Document type defaults (in days)
        type_retention = {
            PrivateDocumentType.COMPANY_POLICY: 2555,  # 7 years
            PrivateDocumentType.COMPLIANCE_ASSESSMENT: 2190,  # 6 years
            PrivateDocumentType.INCIDENT_REPORT: 2555,  # 7 years
            PrivateDocumentType.AUDIT_REPORT: 2555,  # 7 years
            PrivateDocumentType.TRAINING_MATERIAL: 1095,  # 3 years
            PrivateDocumentType.VENDOR_AGREEMENT: 2555,  # 7 years
            PrivateDocumentType.BUSINESS_PROCESS: 1825,  # 5 years
        }
        
        base_retention = type_retention.get(document_type, 1095)  # Default 3 years
        
        # Adjust for sensitivity
        if sensitivity_level == SensitivityLevel.RESTRICTED:
            return min(base_retention, 1825)  # Max 5 years for restricted
        
        return base_retention
    
    async def _create_ingestion_audit_record(self, org_id: str, doc_id: str, audit_trail: Dict[str, Any]):
        """Create audit record for ingestion process."""
        audit_record = {
            "org_id": org_id,
            "document_id": doc_id,
            "operation": "document_ingestion",
            "details": audit_trail,
            "success": audit_trail.get("success", False),
            "created_at": self._get_timestamp()
        }
        
        # This would typically go to a separate audit table
        # For now, we'll log it
        logger.info(f"Ingestion audit: org={org_id}, doc={doc_id}, success={audit_record['success']}")
    
    async def _create_error_audit_record(self, org_id: str, audit_trail: Dict[str, Any]):
        """Create audit record for failed ingestion."""
        error_record = {
            "org_id": org_id,
            "operation": "document_ingestion_failed",
            "error_details": audit_trail,
            "created_at": self._get_timestamp()
        }
        
        logger.error(f"Ingestion error audit: org={org_id}, error={audit_trail.get('error', 'Unknown')}")
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA-256 hash of file content."""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def _generate_chunk_hash(self, text: str) -> str:
        """Generate hash for chunk content."""
        return hashlib.sha256(text.encode('utf-8')).hexdigest()[:16]
    
    def _sanitize_request_for_audit(self, request: PrivateIngestionRequest) -> Dict[str, Any]:
        """Sanitize request for audit trail (remove sensitive paths)."""
        sanitized = request.__dict__.copy()
        if 'file_path' in sanitized:
            sanitized['file_path'] = os.path.basename(sanitized['file_path'])
        return sanitized
    
    def _validate_private_request(self, request: PrivateIngestionRequest):
        """Validate private ingestion request."""
        if not request.org_id:
            raise ValueError("org_id is required for private documents")
        if not request.file_path or not os.path.exists(request.file_path):
            raise ValueError(f"Invalid file_path: {request.file_path}")
        if not isinstance(request.document_type, PrivateDocumentType):
            raise ValueError(f"Invalid document_type: {request.document_type}")
        if not isinstance(request.sensitivity_level, SensitivityLevel):
            raise ValueError(f"Invalid sensitivity_level: {request.sensitivity_level}")
        if not request.title or not request.owner_department:
            raise ValueError("Title and owner_department are required")
    
    def _get_timestamp(self) -> str:
        """Get ISO timestamp."""
        from datetime import datetime
        return datetime.utcnow().isoformat() + "Z"


# Convenience functions for different document types
async def ingest_company_policy(
    org_id: str, 
    file_path: str, 
    title: str, 
    owner_department: str,
    sensitivity_level: SensitivityLevel = SensitivityLevel.INTERNAL,
    compliance_frameworks: Optional[List[str]] = None
) -> PrivateIngestionResult:
    """Ingest company policy document."""
    ingestion = SupabasePrivateIngestion()
    request = PrivateIngestionRequest(
        org_id=org_id,
        file_path=file_path,
        document_type=PrivateDocumentType.COMPANY_POLICY,
        sensitivity_level=sensitivity_level,
        title=title,
        owner_department=owner_department,
        compliance_frameworks=compliance_frameworks
    )
    return await ingestion.ingest_private_document(request)


async def ingest_assessment_results(
    org_id: str, 
    file_path: str, 
    assessment_title: str, 
    owner_department: str,
    compliance_frameworks: List[str],
    sensitivity_level: SensitivityLevel = SensitivityLevel.CONFIDENTIAL
) -> PrivateIngestionResult:
    """Ingest compliance assessment results."""
    ingestion = SupabasePrivateIngestion()
    request = PrivateIngestionRequest(
        org_id=org_id,
        file_path=file_path,
        document_type=PrivateDocumentType.COMPLIANCE_ASSESSMENT,
        sensitivity_level=sensitivity_level,
        title=assessment_title,
        owner_department=owner_department,
        compliance_frameworks=compliance_frameworks,
        retention_period_days=2190  # 6 years for assessment results
    )
    return await ingestion.ingest_private_document(request)


async def ingest_incident_report(
    org_id: str, 
    file_path: str, 
    incident_title: str, 
    security_department: str = "Security",
    sensitivity_level: SensitivityLevel = SensitivityLevel.RESTRICTED
) -> PrivateIngestionResult:
    """Ingest security incident report."""
    ingestion = SupabasePrivateIngestion()
    request = PrivateIngestionRequest(
        org_id=org_id,
        file_path=file_path,
        document_type=PrivateDocumentType.INCIDENT_REPORT,
        sensitivity_level=sensitivity_level,
        title=incident_title,
        owner_department=security_department,
        retention_period_days=2555  # 7 years for incident reports
    )
    return await ingestion.ingest_private_document(request)
