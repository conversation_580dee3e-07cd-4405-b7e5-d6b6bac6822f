id: Q169
query: >-
  How should we handle government data-access requests in multiple jurisdictions?
packs:
  - "GDPR:2016"
primary_ids:
  - "GDPR:2016/Art.29"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Tracker"
flags:
  - "LOCAL LAW_CHECK"
sources:
  - title: "GDPR — Processing by Public Authorities"
    id: "GDPR:2016/Art.29"
    locator: "Article 29"
ui:
  cards_hint:
    - "Government request log"
  actions:
    - type: "start_workflow"
      target: "gov_request_handling"
      label: "Process Request"
    - type: "open_register"
      target: "gov_requests"
      label: "View Requests"
output_mode: "both"
graph_required: false
notes: "Document authority, scope, and legal basis before disclosing data"
---
### 169) How should we handle government data-access requests in multiple jurisdictions?

**Standard terms)**  
- **Public-authority processing (GDPR Art. 29):** requests must have legal basis.

**Plain-English answer**  
Log every government request, **verify legal validity**, and seek counsel if unclear. Disclose only the minimum required, note data categories, and update the request log.

**Applies to**  
- **Primary:** GDPR Article 29

**Why it matters**  
Unauthorised disclosure breaches confidentiality and privacy laws.

**Do next in our platform**  
- Run **Gov-Request Handling** workflow.  
- Store approvals and redacted disclosures in **Gov Requests** register.

**How our platform will help**  
- **[Workflow]** Validity checklist and disclosure tracker.  
- **[Tracker]** SLA timers and escalation paths.

**Likely follow-ups**  
- Can we legally refuse a request? (Depends on jurisdiction **[LOCAL LAW_CHECK]**)

**Sources**  
- GDPR Article 29

**Legal note:** Engage counsel for each jurisdiction’s rules and privilege considerations.  
