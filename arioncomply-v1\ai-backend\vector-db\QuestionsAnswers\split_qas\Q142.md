id: Q142
query: >-
  Can we fail an audit or is it more like getting a report card with improvement areas?
packs:
  - "ISO17021-1:2015"
  - "ISO27001:2022"
primary_ids:
  - "ISO17021-1:2015/7.3"
  - "ISO27001:2022/9.2"
overlap_ids: []
capability_tags:
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 17021-1:2015 — Surveillance Requirements"
    id: "ISO17021-1:2015/7.3"
    locator: "Clause 7.3"
  - title: "ISO/IEC 27001:2022 — Internal Audit"
    id: "ISO27001:2022/9.2"
    locator: "Clause 9.2"
ui:
  cards_hint:
    - "Audit result categories"
  actions:
    - type: "start_workflow"
      target: "audit_response"
      label: "Handle Audit Findings"
output_mode: "both"
graph_required: false
notes: "Nonconformities must be closed; certificate withdrawn only if critical are unresolved"
---
### 142) Can we fail an audit or is it more like getting a report card with improvement areas?

**Standard terms)**  
- **Surveillance (ISO 17021-1 Cl.7.3):** auditor may raise nonconformities.  
- **Internal audit (ISO 27001 Cl.9.2):** categorizes findings by severity.

**Plain-English answer**  
Auditors issue nonconformities (minor or major). You don’t “fail” unless major issues aren’t closed in time—then your certificate can be suspended or withdrawn. Most audits resemble a report card with required fixes.

**Applies to**  
- **Primary:** ISO/IEC 17021-1:2015 Clause 7.3; ISO/IEC 27001:2022 Clause 9.2

**Why it matters**  
Understanding severity helps prioritize remediation.

**Do next in our platform**  
- Launch **Audit Response** workflow.  
- Track findings in the **Nonconformance Register**.

**How our platform will help**  
- **[Report]** Severity dashboards.  
- **[Workflow]** Automated remediation task creation.

**Likely follow-ups**  
- “What distinguishes a major vs minor finding?” (See audit criteria guide)

**Sources**  
- ISO/IEC 17021-1:2015 Clause 7.3; ISO/IEC 27001:2022 Clause 9.2  
