// File: arioncomply-v1/supabase/functions/_shared/diagnostics.ts
// Purpose: Automated diagnostics and alerting when AI Backend services fail
// Usage: Import and call triggerDiagnostics() when service failures detected

export interface DiagnosticsConfig {
  diagnosticsServiceUrl?: string;
  opsWebhookUrl?: string;
  timeout?: number;
}

export interface DiagnosticsPayload {
  timestamp: string;
  service: string;
  target: string;
  errorType: string;
  error: string;
  diagnostics: {
    requestedTests: string[];
    priority: 'low' | 'medium' | 'high' | 'critical';
    autoRemediation: boolean;
  };
  alerting: {
    supportTicket: boolean;
    opsTeamNotification: boolean;
    escalationLevel: 'normal' | 'urgent' | 'immediate';
  };
}

/**
 * Trigger remote diagnostics service and notify ops when a service is unreachable.
 * Non-blocking: logs errors but does not throw to avoid masking primary failures.
 */
export async function triggerDiagnostics(
  service: string,
  targetUrl: string, 
  error: unknown,
  config?: DiagnosticsConfig
): Promise<void> {
  const diagnosticsPayload: DiagnosticsPayload = {
    timestamp: new Date().toISOString(),
    service,
    target: targetUrl,
    errorType: 'service_unreachable',
    error: String(error),
    diagnostics: {
      requestedTests: [
        'network_connectivity',
        'dns_resolution', 
        'ssl_certificate_validation',
        'service_health_check',
        'response_time_measurement',
        'dependency_chain_validation'
      ],
      priority: 'high',
      autoRemediation: true
    },
    alerting: {
      supportTicket: true,
      opsTeamNotification: true,
      escalationLevel: 'immediate'
    }
  };

  const diagnosticsServiceUrl = config?.diagnosticsServiceUrl || Deno.env.get('DIAGNOSTICS_SERVICE_URL');
  const opsWebhookUrl = config?.opsWebhookUrl || Deno.env.get('OPS_WEBHOOK_URL');
  const timeout = config?.timeout || 5000;

  // Trigger diagnostics service
  if (diagnosticsServiceUrl) {
    try {
      await fetch(diagnosticsServiceUrl, {
        method: 'POST',
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(diagnosticsPayload),
        signal: AbortSignal.timeout(timeout)
      });
      console.log(`Diagnostics service triggered successfully for ${service}`);
    } catch (diagError) {
      console.error(`Failed to trigger diagnostics service: ${diagError}`);
    }
  } else {
    console.warn('DIAGNOSTICS_SERVICE_URL not configured - diagnostics not triggered');
  }

  // Send immediate alert to ops team
  if (opsWebhookUrl) {
    try {
      await fetch(opsWebhookUrl, {
        method: 'POST',
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify({
          alert: `${service} Service Unreachable`,
          service,
          severity: 'critical',
          details: diagnosticsPayload,
          timestamp: new Date().toISOString()
        }),
        signal: AbortSignal.timeout(Math.min(timeout, 3000))
      });
      console.log('Ops team notified successfully');
    } catch (opsError) {
      console.error(`Failed to notify ops team: ${opsError}`);
    }
  } else {
    console.warn('OPS_WEBHOOK_URL not configured - ops team not notified');
  }
}

// Remove createErrorResponse as it's not compatible with standard error format
// Use the standard apiError() function from _shared/errors.ts instead
