id: Q189
query: >-
  How do we handle compliance for mobile apps vs. web applications?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/A.14.2.5"
overlap_ids: []
capability_tags:
  - "Report"
  - "Draft Doc"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Secure Development Lifecycle"
    id: "ISO27001:2022/A.14.2.5"
    locator: "Annex A.14.2.5"
ui:
  cards_hint:
    - "App compliance checklist"
  actions:
    - type: "start_workflow"
      target: "app_compliance"
      label: "Launch App Compliance"
output_mode: "both"
graph_required: false
notes: "Consider platform-specific threats: mobile-OS permissions vs browser-based attacks"
---
### 189) How do we handle compliance for mobile apps vs. web applications?

**Standard terms**  
- **Secure development (A.14.2.5):** integrate security in SDLC for all platforms.

**Plain-English answer**  
Use **platform-specific controls**: enforce secure storage and permissions on mobile (e.g., keychain, secure enclave) and XSS/CSRF protections for web. Maintain separate threat models and testing schedules.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.14.2.5

**Why it matters**  
Different attack surfaces require tailored controls.

**Do next in our platform**  
- Start **App Compliance** workflow and choose “mobile” or “web.”  
- Apply the **App Compliance Checklist** to each build.

**How our platform will help**  
- **[Draft Doc]** Generates platform-specific policy templates.  
- **[Report]** Tracks test results and remediation status.

**Likely follow-ups**  
- What mobile-app pentests are required?  
- How often to scan web apps for vulnerabilities?

**Sources**  
- ISO/IEC 27001:2022 Annex A.14.2.5  
