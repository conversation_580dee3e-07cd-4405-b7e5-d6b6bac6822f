// File: arioncomply-v1/supabase/functions/_shared/assistant_router.ts
// File Description: Forward-only chat router from Edge to Python backend
// Purpose: Accept Edge requests, enrich with hints/context, and forward to AI backend
// Notes: Falls back to echo stub when not configured or forwardOnly=false
import { loadProviderConfig } from "./config.ts";

type ChatResponse = {
  provider: string;
  model?: string;
  text: string;
  usage?: { inputTokens?: number; outputTokens?: number; costUsd?: number };
};

type RouterInput = {
  messages: { role: "user" | "system" | "assistant"; content: string }[];
  sessionId?: string;
  orgId?: string | null;
  userId?: string | null;
  headers?: Headers;
};

function getHeader(h: Headers | undefined, key: string): string | undefined {
  return h?.get(key) ?? h?.get(key.toLowerCase()) ?? undefined;
}

/**
 * Forward chat requests to the configured AI backend with optional hints.
 * - If backend is not configured or forwardOnly=false, returns a local echo stub.
 */
export async function assistantRouter(input: RouterInput): Promise<ChatResponse> {
  const cfg = loadProviderConfig();
  const forwardOnly = cfg.forwardOnly !== false;
  const backendUrl = cfg.backend?.url ?? Deno.env.get("AI_BACKEND_URL");
  const timeoutMs = Number(Deno.env.get("AI_BACKEND_TIMEOUT_MS") ?? 12000);

  // Safe fallback for dev if backend not configured
  if (!forwardOnly || !backendUrl) {
    const lastUser = [...input.messages].reverse().find((m) => m.role === "user")?.content ?? "";
    return { provider: "edge-stub", model: "echo", text: lastUser ? `Echo: ${lastUser}` : "" };
  }

  const payload = {
    sessionId: input.sessionId ?? null,
    orgId: input.orgId ?? null,
    userId: input.userId ?? null,
    messages: input.messages,
    hints: {
      pipelineMode: getHeader(input.headers, "x-pipeline-mode"),
      retrievalBackend: getHeader(input.headers, "x-retrieval-backend"),
      outputMode: getHeader(input.headers, "x-output-mode") || "both",
      frameworkHint: getHeader(input.headers, "x-framework-hint"),
      providerOrder: getHeader(input.headers, "x-provider-order"),
      allowGllm: getHeader(input.headers, "x-allow-gllm"),
    },
    explainability: {
      complianceChainId: getHeader(input.headers, "compliance-chain-id"),
      decisionContext: getHeader(input.headers, "decision-context"),
    },
  };

  try {
    const controller = new AbortController();
    const t = setTimeout(() => controller.abort(), Math.max(1000, timeoutMs));
    const resp = await fetch(backendUrl, {
      method: "POST",
      headers: {
        "content-type": "application/json",
        ...(input.orgId ? { "x-org-id": String(input.orgId) } : {}),
        ...(input.userId ? { "x-user-id": String(input.userId) } : {}),
        ...(input.sessionId ? { "x-session-id": String(input.sessionId) } : {}),
        ...(getHeader(input.headers, "compliance-chain-id") ? { "compliance-chain-id": String(getHeader(input.headers, "compliance-chain-id")) } : {}),
        ...(getHeader(input.headers, "decision-context") ? { "decision-context": String(getHeader(input.headers, "decision-context")) } : {}),
      },
      body: JSON.stringify(payload),
      signal: controller.signal,
    });
    clearTimeout(t);
    if (!resp.ok) {
      return { provider: "backend", model: "n/a", text: `Backend error ${resp.status}` };
    }
    const data = await resp.json();
    const text = data?.assistant?.text ?? data?.text ?? data?.data?.text ?? "";
    return {
      provider: data?.provider ?? data?.assistant?.provider ?? "backend",
      model: data?.model ?? data?.assistant?.model ?? undefined,
      text,
      usage: data?.usage ?? undefined,
    };
  } catch (e) {
    const reason = (e as any)?.name === "AbortError" ? `timeout ${timeoutMs}ms` : "request failed";
    return { provider: "backend", model: "n/a", text: `Backend ${reason}` };
  }
}
