#!/usr/bin/env bash
# File: arioncomply-v1/ai-backend/LLMs/collectSLLMinfo.sh
# File Description: Local SLLM endpoint probe and summary
# Purpose: Discover and validate local Small LLM (SLLM) servers (e.g., llama.cpp/TGI wrapped with OpenAI‑style APIs),
#          print a quick status table for humans, and write a JSON file that the testing GUI can read.
# Inputs: CLI flags and environment variables (see Usage below)
#   - Flags:
#       --map "model:port:label,..."  Define models to probe (id, TCP port, launchctl label)
#       --home <dir>                  LLMS_HOME; used to find logs and optional service plists
#       --json [path]                 Write JSON summary to path; omit path to use default location
#       --log-file <path>             Append human logs to a file in addition to stdout/stderr
#       --timeout <sec>               HTTP timeout per probe (default 3)
#       --fix                         If a service looks down, attempt a launchctl restart
#       --force                       With --fix, also restart when status is "starting"
#       -v|--verbose                  Extra debug logs
#       -q|--quiet                    Suppress INFO logs on stdout; warnings/errors still on stderr
#       --deep                        Include extended metadata (server header, TGI /info, plist runtime flags) and a system block
#       -h|--help                     Print usage and exit
#   - Environment (optional overrides):
#       SLLM_HOME                     Same as --home; defaults to "$HOME/llms" if present
#       SLLM_MODELS                   Comma list filter of models to keep (e.g., "mistral7b,phi3"); can also define new models entirely
#       SLLM_PORT_<model>             Port for a model defined via SLLM_MODELS
#       SLLM_LABEL_<model>            launchctl label for that model
#       SLLM_LOG_FILE                 Default log file path (all levels)
#       SLLM_ERROR_LOG_FILE           If set, also append ERROR logs here (defaults next to JSON output)
#       SLLM_HTTP_TIMEOUT             Default HTTP timeout seconds
#       LLM_CTRL_KEY                  If set, also probe a local controller at 127.0.0.1:8090
# Outputs:
#   - Human: A table with columns MODEL, PORT, LAUNCHCTL, HEALTH, ACTION
#   - JSON: A summary file with discovered endpoints for wiring the test GUI
#       Default JSON path (latest pointer):
#         - $LLMS_HOME/config/local_sllm_endpoints.json (if SLLM_HOME/LLMS_HOME is set or $HOME/llms exists)
#         - Otherwise: $HOME/llms/config/local_sllm_endpoints.json (directory will be created)
#       Rotation: Each run also writes a timestamped history file next to the latest, e.g., local_sllm_endpoints_20250101T121314Z.json
#       Error log: ERRORs are appended to a file alongside the JSON (collectSLLMinfo.error.log) unless SLLM_ERROR_LOG_FILE overrides
#       Shape (base): {
#         _description, version, generatedAt, llmsHome,
#         localModels: [{ id, port, base, label, launchctl, health, openai_compatible }]
#       }
#       When --deep is provided:
#         - Adds top-level system {...}
#         - Adds per-model meta {...} with fields like server, tgi {version/limits}, runtime {ctx/threads/gpuLayers/batch/flashAttn}
# Dependencies: curl (required), launchctl (macOS for service status), nc (optional TCP probe), jq (optional for TGI/system parsing)
# Security/RLS: N/A (local dev tooling only)
# Notes: Designed for local development; safe to run on macOS without elevated privileges.
set -euo pipefail
set -o errtrace

# Simple SLLM status collector (overview)
# - Prints a human table: MODEL PORT LAUNCHCTL HEALTH ACTION
# - Writes a JSON summary for the test GUI (see header above)
# - Configure models via --map "model:port:label,..." or via env variables

# Defaults (bash 3.2 compatible; no associative arrays)
MODELS_DEF=("smollm3" "mistral7b" "phi3")
PORTS_DEF=(8081 8082 8083)
LABELS_DEF=(
  "llms.smollm3"
  "llms.mistral7b"
  "llms.phi3"
)

# Allow override via environment (examples):
#   SLLM_MODELS="mistral7b,phi3"
#   SLLM_PORT_mistral7b=8082
#   SLLM_LABEL_mistral7b=llms.mistral7b
#   SLLM_PORT_phi3=8083
#   SLLM_LABEL_phi3=llms.phi3
MODELS_ENV="${SLLM_MODELS:-}"

MAP_ARG=""
LOG_FILE="${SLLM_LOG_FILE:-}"
LOG_IS_DEFAULT=0
OUT_JSON=""
OUT_IS_DEFAULT=0
VERBOSE=false
QUIET=false
HTTP_TIMEOUT="${SLLM_HTTP_TIMEOUT:-3}"
LLMS_HOME="${SLLM_HOME:-}"
FIX=0
FORCE=0
DEEP=0
ERR_LOG_FILE="${SLLM_ERROR_LOG_FILE:-}"
ERR_IS_DEFAULT=0
# Parse CLI flags (bash 3.2 compatible)
while [[ $# -gt 0 ]]; do
  case "$1" in
    --map) MAP_ARG="$2"; shift 2;;
    --log-file) LOG_FILE="$2"; shift 2;;
    --json)
      if [[ $# -gt 1 && "${2#-}" != "$2" ]]; then
        # next token is another flag, no path provided
        OUT_JSON=""
        shift
      elif [[ $# -gt 1 ]]; then
        OUT_JSON="$2"; shift 2
      else
        OUT_JSON=""; shift
      fi;;
    -v|--verbose) VERBOSE=true; shift;;
    -q|--quiet) QUIET=true; shift;;
    --timeout) HTTP_TIMEOUT="$2"; shift 2;;
    --home) LLMS_HOME="$2"; shift 2;;
    --fix) FIX=1; shift;;
    --force) FIX=1; FORCE=1; shift;;
    --deep) DEEP=1; shift;;
    --help|-h)
      echo "Usage: $0 [--map \"model:port:label,...\"] [--home <llms_dir>] [--fix] [--force] [--log-file <path>] [--json [path]] [-v|--verbose] [-q|--quiet] [--timeout <sec>]"
      echo "Env: SLLM_HOME, SLLM_MODELS, SLLM_PORT_<model>, SLLM_LABEL_<model>, SLLM_LOG_FILE, SLLM_HTTP_TIMEOUT, LLM_CTRL_KEY"
      echo "Flags: --deep (include extra metadata and system block)"
      exit 0;;
    *)
      echo "Usage: $0 [--map \"model:port:label,...\"] [--home <llms_dir>] [--fix] [--force] [--log-file <path>] [--json [path]] [-v|--verbose] [-q|--quiet] [--timeout <sec>]" >&2
      echo "Unknown arg: $1" >&2
      exit 1;;
  esac
done

# Parse configuration into arrays (MODELS, PORTS, LABELS)
MODELS=()
PORTS=()
LABELS=()

# Add one model tuple to arrays
add_model() {
  local m="$1" p="$2" l="$3"
  MODELS+=("$m")
  PORTS+=("$p")
  LABELS+=("$l")
}

# Return index of a model name in MODELS[] or -1
idx_of_model() {
  local name="$1"
  local i
  for i in "${!MODELS[@]}"; do
    if [[ "${MODELS[$i]}" == "$name" ]]; then echo "$i"; return 0; fi
  done
  echo -1; return 1
}

if [[ -n "$MAP_ARG" ]]; then
  IFS=',' read -r -a triples <<< "$MAP_ARG"
  for t in "${triples[@]}"; do
    IFS=':' read -r m p l <<< "$t"
    [[ -z "${m:-}" || -z "${p:-}" || -z "${l:-}" ]] && { log_error "Invalid --map entry: $t (expected model:port:label)"; exit 1; }
    add_model "$m" "$p" "$l"
  done
else
  # Seed with defaults
  i=0
  for m in "${MODELS_DEF[@]}"; do
    add_model "$m" "${PORTS_DEF[$i]}" "${LABELS_DEF[$i]}"
    i=$((i+1))
  done
fi

# Apply env overrides and optional SLLM_MODELS filter
if [[ -n "$MODELS_ENV" ]]; then
  # Filter to only the specified models
  FILTERED_MODELS=()
  FILTERED_PORTS=()
  FILTERED_LABELS=()
  IFS=',' read -r -a wanted <<< "$MODELS_ENV"
  for m in "${wanted[@]}"; do
    m_trim="${m//[[:space:]]/}"
    idx=$(idx_of_model "$m_trim") || true
    if [[ "$idx" != -1 ]]; then
      FILTERED_MODELS+=("$m_trim")
      FILTERED_PORTS+=("${PORTS[$idx]}")
      FILTERED_LABELS+=("${LABELS[$idx]}")
    else
      # allow defining completely new via env
      p_var="SLLM_PORT_${m_trim}"
      l_var="SLLM_LABEL_${m_trim}"
      p_val="${!p_var:-}"
      l_val="${!l_var:-}"
      if [[ -n "$p_val" && -n "$l_val" ]]; then
        FILTERED_MODELS+=("$m_trim")
        FILTERED_PORTS+=("$p_val")
        FILTERED_LABELS+=("$l_val")
      else
        log_warn "$m_trim not known and no $p_var/$l_var provided; skipping"
      fi
    fi
  done
  MODELS=("${FILTERED_MODELS[@]}")
  PORTS=("${FILTERED_PORTS[@]}")
  LABELS=("${FILTERED_LABELS[@]}")
fi

# Logging helpers (INFO to stdout unless quiet; WARN/ERROR to stderr)
ts() { date -u +"%Y-%m-%dT%H:%M:%SZ"; }
_emit_log() {
  local level="$1"; shift
  local msg="$*"
  local line
  line="$(ts) [$level] $msg"
  # Info logs to stdout unless quiet; others to stderr.
  if [[ "$level" == INFO ]]; then
    if ! $QUIET; then printf "%s\n" "$line"
    fi
  else
    printf "%s\n" "$line" >&2
  fi
  if [[ -n "${LOG_FILE}" ]]; then
    # Best-effort append
    { printf "%s\n" "$line" >> "$LOG_FILE"; } 2>/dev/null || true
  fi
  # Additionally mirror ERRORs to the dedicated error log if configured
  if [[ "$level" == ERROR && -n "${ERR_LOG_FILE:-}" ]]; then
    { printf "%s\n" "$line" >> "$ERR_LOG_FILE"; } 2>/dev/null || true
  fi
}
log_info() { _emit_log INFO "$@"; }
log_warn() { _emit_log WARN "$@"; WARN_COUNT=$((WARN_COUNT+1)); }
log_error(){ _emit_log ERROR "$@"; ERR_COUNT=$((ERR_COUNT+1)); }
log_debug(){ if [[ "$VERBOSE" == true ]]; then _emit_log DEBUG "$@"; fi }

# Trap unexpected errors with context
on_error() {
  local exit_code=$?
  local src="${BASH_SOURCE[1]:-main}"
  local line_no="${BASH_LINENO[0]:-?}"
  log_error "Unexpected error at ${src}:${line_no}; last cmd: '${BASH_COMMAND}'; exit=${exit_code}"
  if [[ -n "${LOG_FILE}" ]]; then
    log_error "See log file: ${LOG_FILE}"
  fi
  exit "$exit_code"
}
trap on_error ERR
trap 'log_warn "Interrupted by user (SIGINT)"; exit 130' INT

WARN_COUNT=0
ERR_COUNT=0

# Dependencies (soft for nc)
require() { command -v "$1" >/dev/null 2>&1 || { log_error "Missing dependency: $1"; exit 1; }; }
require curl

has_nc=false
if command -v nc >/dev/null 2>&1; then has_nc=true; else log_debug "'nc' not found; TCP probe disabled"; fi

# Defaults for LLMS_HOME and log file locations
if [[ -z "$LLMS_HOME" && -d "$HOME/llms" ]]; then
  LLMS_HOME="$HOME/llms"
fi
if [[ -z "$LOG_FILE" && -n "$LLMS_HOME" && -d "$LLMS_HOME/logs" ]]; then
  LOG_FILE="$LLMS_HOME/logs/collectSLLMinfo.log"; LOG_IS_DEFAULT=1
fi

# Default JSON output path next to the LLMs (LLMS_HOME)
if [[ -z "$OUT_JSON" ]]; then
  if [[ -n "$LLMS_HOME" ]]; then
    OUT_JSON="$LLMS_HOME/config/local_sllm_endpoints.json"
  elif [[ -d "$HOME/llms" ]]; then
    OUT_JSON="$HOME/llms/config/local_sllm_endpoints.json"
  else
    # Fall back to $HOME/llms even if it doesn't yet exist; directory will be created on write
    OUT_JSON="$HOME/llms/config/local_sllm_endpoints.json"
  fi
  OUT_IS_DEFAULT=1
fi

# After OUT_JSON is known, define default error log path if not already provided
if [[ -z "${ERR_LOG_FILE:-}" && -n "$OUT_JSON" ]]; then
  ERR_LOG_FILE="$(dirname "$OUT_JSON")/collectSLLMinfo.error.log"; ERR_IS_DEFAULT=1
fi

# Safety guard: never write inside the repository tree
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
# Helpers to resolve absolute paths without requiring directories to exist
resolve_abs() {
  local p="$1"
  if [[ -z "$p" ]]; then printf "\n"; return 0; fi
  if [[ "$p" == /* ]]; then printf "%s\n" "$p"; else printf "%s/%s\n" "$(pwd)" "$p"; fi
}
is_under_repo() {
  local p="$1"
  if [[ -z "$p" || -z "$REPO_ROOT" ]]; then return 1; fi
  case "$p" in
    "$REPO_ROOT"|"$REPO_ROOT"/*) return 0;;
    *) return 1;;
  esac
}
find_repo_root() {
  local d="$SCRIPT_DIR"
  while [[ "$d" != "/" ]]; do
    if [[ -d "$d/.git" ]]; then printf "%s\n" "$d"; return 0; fi
    d="$(dirname "$d")"
  done
  return 1
}
REPO_ROOT="$(find_repo_root || true)"
if [[ -n "$REPO_ROOT" ]]; then
  # OUT_JSON safety
  if [[ -n "$OUT_JSON" ]]; then
    OUT_ABS="$(resolve_abs "$OUT_JSON")"
    if is_under_repo "$OUT_ABS"; then
      if [[ "$OUT_IS_DEFAULT" -eq 1 ]]; then
        # Redirect defaults away from repo into $HOME/llms/config (never into repo)
        SAFE_DIR="$HOME/llms/config"
        mkdir -p "$SAFE_DIR" 2>/dev/null || true
        NEW_OUT="$SAFE_DIR/$(basename "$OUT_JSON")"
        log_warn "Default OUT_JSON resolved inside repo; redirecting to $NEW_OUT"
        OUT_JSON="$NEW_OUT"; OUT_ABS="$NEW_OUT"
        # Update error log path accordingly if default-derived
        if [[ "$ERR_IS_DEFAULT" -eq 1 ]]; then
          ERR_LOG_FILE="$(dirname "$OUT_JSON")/collectSLLMinfo.error.log"
        fi
      else
        log_error "Refusing to write OUT_JSON inside repo: $OUT_ABS. Supply --json outside the repo."
        exit 1
      fi
    fi
  fi
  # LOG_FILE safety
  if [[ -n "${LOG_FILE:-}" ]]; then
    LOG_ABS="$(resolve_abs "$LOG_FILE")"
    if is_under_repo "$LOG_ABS"; then
      if [[ "$LOG_IS_DEFAULT" -eq 1 ]]; then
        SAFE_DIR="$HOME/llms/logs"
        mkdir -p "$SAFE_DIR" 2>/dev/null || true
        NEW_LOG="$SAFE_DIR/$(basename "$LOG_FILE")"
        log_warn "Default LOG_FILE resolved inside repo; redirecting to $NEW_LOG"
        LOG_FILE="$NEW_LOG"
      else
        log_error "Refusing to write LOG_FILE inside repo: $LOG_ABS. Use --log-file outside the repo."
        exit 1
      fi
    fi
  fi
  # ERR_LOG_FILE safety
  if [[ -n "${ERR_LOG_FILE:-}" ]]; then
    ERR_ABS="$(resolve_abs "$ERR_LOG_FILE")"
    if is_under_repo "$ERR_ABS"; then
      if [[ "$ERR_IS_DEFAULT" -eq 1 ]]; then
        SAFE_DIR="$HOME/llms/config"
        mkdir -p "$SAFE_DIR" 2>/dev/null || true
        NEW_ERR="$SAFE_DIR/$(basename "$ERR_LOG_FILE")"
        log_warn "Default error log path resolved inside repo; redirecting to $NEW_ERR"
        ERR_LOG_FILE="$NEW_ERR"
      else
        log_error "Refusing to write error log inside repo: $ERR_ABS. Set SLLM_ERROR_LOG_FILE outside the repo."
        exit 1
      fi
    fi
  fi
fi

# Output formatting helper for the table
col() {
  # printf with fixed widths: MODEL(10) PORT(6) LAUNCHCTL(10) HEALTH(7) ACTION(8)
  printf "%-10s %-6s %-10s %-7s %-8s\n" "$@"
}

# Determine launchctl status (macOS); returns a simple string like running/unknown/stopped
is_launchctl_running() {
  local label="$1"
  if command -v launchctl >/dev/null 2>&1; then
    log_debug "Checking launchctl label '$label'"
    local uid
    uid="$(id -u)"
    # Prefer detailed state via 'print'
    if out="$(launchctl print "gui/$uid/$label" 2>/dev/null)"; then
      state="$(printf "%s" "$out" | awk -F'= ' '/state =/{print $2; exit}')"
      [[ -z "$state" ]] && state="running" # fallback if present
      log_debug "launchctl '$label' state (print): $state"
      echo "$state"; return 0
    fi
    # Fallback to list presence
    if launchctl list | grep -q -- "$label"; then log_debug "launchctl '$label' present in list"; echo "running"; return 0; fi
    log_debug "Label '$label' not found in launchctl"
    echo "stopped"; return 1
  else
    log_debug "launchctl not available on this system"
    echo "unknown"
    return 2
  fi
}

# HTTP/TCP health checks; tolerant to 503 "Loading model" responses
check_health() {
  local port="$1"
  local base="http://127.0.0.1:$port"
  # Try common health endpoints; detect ok/starting
  log_debug "Health: trying $base/health (timeout=${HTTP_TIMEOUT}s)"
  # Important: do NOT use -f here, we want 503 bodies ("Loading model")
  if resp="$(curl -sS -m "$HTTP_TIMEOUT" --connect-timeout "$HTTP_TIMEOUT" "$base/health" 2>/dev/null || true)"; then
    if printf "%s" "$resp" | grep -q '"status"\s*:\s*"ok"'; then echo "ok"; return 0; fi
    if printf "%s" "$resp" | grep -qi 'Loading model\|starting'; then echo "starting"; return 1; fi
    if [[ -n "$resp" ]]; then echo "alive"; return 0; fi
  fi
  # Try OpenAI-like model list
  log_debug "Health: trying $base/v1/models"
  if curl -fsS -m "$HTTP_TIMEOUT" --connect-timeout "$HTTP_TIMEOUT" "$base/v1/models" >/dev/null 2>&1; then
    echo "ok"; return 0
  fi
  # Try generic root
  log_debug "Health: trying $base/"
  if curl -fsS -m "$HTTP_TIMEOUT" --connect-timeout "$HTTP_TIMEOUT" "$base" >/dev/null 2>&1; then
    echo "ok"; return 0
  fi
  # Fallback: TCP probe
  if $has_nc && nc -z -G "$HTTP_TIMEOUT" 127.0.0.1 "$port" >/dev/null 2>&1; then
    echo "alive"; return 0
  fi
  log_debug "Health checks failed for port $port ($base)"
  echo "down"; return 1
}

# Optional OpenAI compatibility probe
# Does the server expose an OpenAI-compatible /v1/models endpoint?
check_openai_compat() {
  local port="$1"; local base="http://127.0.0.1:$port"
  if curl -fsS -m "$HTTP_TIMEOUT" --connect-timeout "$HTTP_TIMEOUT" "$base/v1/models" >/dev/null 2>&1; then
    echo true; return 0
  fi
  echo false; return 0
}

# Deep probe helpers (enabled with --deep)
# 1) Server header from OpenAI-like endpoint (often reveals engine/version)
# Try to read a Server header (e.g., llama.cpp/x.y, tgi/x.y)
server_header() {
  local port="$1"; local base="http://127.0.0.1:$port"
  local hdr
  if hdr="$(curl -sSI -m "$HTTP_TIMEOUT" --connect-timeout "$HTTP_TIMEOUT" "$base/v1/models" 2>/dev/null | awk -F': ' 'BEGIN{IGNORECASE=1} /^Server:/{sub(/\r$/,"",$0); print substr($0,index($0,$2))}' | head -n1 || true)" && [[ -n "$hdr" ]]; then
    log_debug "Server header on $base: $hdr"
    echo "$hdr"
  else
    log_debug "No Server header available on $base"
    echo ""
  fi
}

# 2) TGI /info if present (version/limits/device)
# Fetch TGI /info JSON if the endpoint is actually a TGI server
tgi_info_json() {
  local port="$1"; local base="http://127.0.0.1:$port"
  local body
  if body="$(curl -sS -m "$HTTP_TIMEOUT" --connect-timeout "$HTTP_TIMEOUT" "$base/info" 2>/dev/null || true)" && [[ -n "$body" ]]; then
    log_debug "TGI /info detected at $base/info"
    echo "$body"
  else
    log_debug "No TGI /info at $base/info"
    echo ""
  fi
}

# 3) Runtime flags from launchctl plist ProgramArguments (macOS only)
# Parse basic runtime flags from a launchctl plist ProgramArguments
plist_runtime_json() {
  local label="$1"
  local plist=""
  if [[ -f "$HOME/Library/LaunchAgents/$label.plist" ]]; then
    plist="$HOME/Library/LaunchAgents/$label.plist"
  elif [[ -n "$LLMS_HOME" && -f "$LLMS_HOME/svc/$label.plist" ]]; then
    plist="$LLMS_HOME/svc/$label.plist"
  fi
  [[ -z "$plist" ]] && { echo ""; return 0; }

  local args_text="" json_text=""
  if command -v plutil >/dev/null 2>&1; then
    json_text="$(plutil -extract ProgramArguments json -o - "$plist" 2>/dev/null || true)"
    # If json_text looks like a JSON array, prefer it
    if printf "%s" "$json_text" | grep -q '^\s*\['; then
      args_text="$json_text"
    else
      # Fallback to raw (some macOS versions)
      args_text="$(plutil -extract ProgramArguments raw -o - "$plist" 2>/dev/null || true)"
    fi
  fi
  if [[ -z "$args_text" ]] && command -v /usr/libexec/PlistBuddy >/dev/null 2>&1; then
    args_text="$(/usr/libexec/PlistBuddy -c 'Print :ProgramArguments' "$plist" 2>/dev/null | sed '1,2d' | sed 's/^\s*//')"
  fi
  [[ -z "$args_text" ]] && { echo ""; return 0; }

  # Normalize ProgramArguments to plain space-separated tokens
  # - If JSON array, strip [ ], quotes, commas
  # - Else handle raw/plistbuddy formats by removing punctuation and collapsing whitespace
  local norm
  if printf "%s" "$args_text" | grep -q '^\s*\['; then
    norm="$(printf "%s" "$args_text" | tr -d '\n' \
      | sed -E 's/^\s*\[|\]\s*$//g; s/\"//g; s/,/ /g' \
      | sed -E 's#\\/#/#g; s/\\\\/\\/g' \
      | tr -s ' ')"
  else
    norm="$(printf "%s" "$args_text" | tr '\n' ' ' | sed -E 's/[(),:]//g; s/\"//g' | tr -s ' ')"
  fi

  local ctx threads gpu batch flash
  # Extract values using awk on normalized tokens
  ctx="$(printf "%s" "$norm" | awk '{for(i=1;i<=NF;i++){if($i ~ /^(--ctx-size|--ctx|--n-ctx|-c)$/){print $(i+1); exit}}}')"
  threads="$(printf "%s" "$norm" | awk '{for(i=1;i<=NF;i++){if($i == "--threads" || $i == "-t"){print $(i+1); exit}}}')"
  gpu="$(printf "%s" "$norm" | awk '{for(i=1;i<=NF;i++){if($i == "--n-gpu-layers" || $i == "-ngl"){print $(i+1); exit}}}')"
  batch="$(printf "%s" "$norm" | awk '{for(i=1;i<=NF;i++){if($i == "--batch-size" || $i == "-b"){print $(i+1); exit}}}')"
  if printf "%s" "$norm" | grep -q -- '--flash-attn'; then flash=true; else flash=false; fi

  {
    printf '{'
    printf '"ctx":%s,' "${ctx:-null}"
    printf '"threads":%s,' "${threads:-null}"
    printf '"gpuLayers":%s,' "${gpu:-null}"
    printf '"batch":%s,' "${batch:-null}"
    printf '"flashAttn":%s' "$flash"
    printf '}'
  }
  log_debug "Parsed runtime flags for $label: ctx=${ctx:-} threads=${threads:-} gpuLayers=${gpu:-} batch=${batch:-} flashAttn=$flash"
}

# 4) System block (macOS-friendly; safe fallbacks)
# Lightweight system snapshot included only when --deep is used
system_block_json() {
  local os kernel cpu mem cores pcores gjson gpu name vram
  os="$(sw_vers 2>/dev/null | awk -F':\t' '{print $2}' | paste -sd' ' - || uname -s)"
  kernel="$(uname -a)"
  cpu="$(sysctl -n machdep.cpu.brand_string 2>/dev/null || echo "unknown")"
  mem="$(sysctl -n hw.memsize 2>/dev/null || echo "0")"
  cores="$(sysctl -n hw.ncpu 2>/dev/null || echo "0")"
  pcores="$(sysctl -n hw.physicalcpu_max 2>/dev/null || echo "0")"
  if command -v system_profiler >/dev/null 2>&1; then
    gjson="$(system_profiler SPDisplaysDataType -json 2>/dev/null || true)"
    name="$(printf "%s" "$gjson" | jq -r '..|objects|select(has("spdisplays_vendor")).spdisplays_vendor // empty' 2>/dev/null || true)"
    vram="$(printf "%s" "$gjson" | jq -r '..|objects|select(has("spdisplays_vram")).spdisplays_vram // empty' 2>/dev/null || true)"
  fi
  {
    printf '{'
    printf '"os":"%s",' "$os"
    printf '"kernel":"%s",' "$kernel"
    printf '"cpuBrand":"%s",' "$cpu"
    printf '"cpuCores":%s,' "$cores"
    printf '"cpuPhysicalCores":%s,' "$pcores"
    printf '"memBytes":%s,' "$mem"
    printf '"gpuName":"%s",' "${name:-}"
    printf '"gpuVram":"%s"' "${vram:-}"
    printf '}'
  }
}

# Output header and configuration echo (when verbose)
log_debug "Configured HTTP timeout: ${HTTP_TIMEOUT}s"
if $VERBOSE; then
  for i in "${!MODELS[@]}"; do
    log_debug "Config: model=${MODELS[$i]} port=${PORTS[$i]} label=${LABELS[$i]}"
  done
fi

col "MODEL" "PORT" "LAUNCHCTL" "HEALTH" "ACTION"

# Iterate configured models and build both the table rows and the JSON array
MODEL_JSON="["
first=1
for i in "${!MODELS[@]}"; do
  m="${MODELS[$i]}"
  port="${PORTS[$i]}"
  label="${LABELS[$i]}"
  if [[ -z "${port:-}" ]]; then log_warn "No port configured for $m"; fi
  if [[ -z "${label:-}" ]]; then log_warn "No launchctl label configured for $m"; fi
  log_debug "Probing $m on port $port (label=$label)"
  lc_status="$(is_launchctl_running "$label" || true)"
  health="$(check_health "$port" || true)"

  action="-"
  if [[ "$health" == "starting" ]]; then
    action="waiting"
  elif [[ "$lc_status" != running* || "$health" == "down" ]]; then
    action="start"
  fi

  # Optional fix: restart via launchctl with plist under user LaunchAgents or $LLMS_HOME/svc
  if [[ "$action" != "-" && $FIX -eq 1 ]]; then
    plist=""
    uid="$(id -u)"
    if [[ -f "$HOME/Library/LaunchAgents/$label.plist" ]]; then
      plist="$HOME/Library/LaunchAgents/$label.plist"
    elif [[ -n "$LLMS_HOME" && -f "$LLMS_HOME/svc/$label.plist" ]]; then
      plist="$LLMS_HOME/svc/$label.plist"
    fi
    if [[ -z "$plist" ]]; then
      log_warn "No plist found for $label in LaunchAgents or $LLMS_HOME/svc; cannot restart"
    else
      if [[ "$health" == "starting" && $FORCE -ne 1 ]]; then
        log_info "$m is starting; skipping restart (use --force to override)"
      else
        log_info "Restarting $label using $plist"
        launchctl bootout "gui/$uid/$label" >/dev/null 2>&1 || true
        launchctl bootstrap "gui/$uid" "$plist" >/dev/null 2>&1 || true
        launchctl kickstart -k "gui/$uid/$label" >/dev/null 2>&1 || true
        # Wait briefly for ok
        sleep 1
        health="$(check_health "$port" || true)"
        lc_status="$(is_launchctl_running "$label" || true)"
        action="restarted"
      fi
    fi
  fi

  col "$m" "$port" "$lc_status" "$health" "$action"
  openai="$(check_openai_compat "$port")"
  base="http://127.0.0.1:$port"
  log_debug "Probe summary $m: launchctl=$lc_status health=$health openai=$openai action=$action"
  # Build optional meta block when --deep is set
  meta_json=""
  if [[ $DEEP -eq 1 ]]; then
    shdr="$(server_header "$port")"
    tgi="$(tgi_info_json "$port")"
    prt="$(plist_runtime_json "$label")"
    meta_json='{'
    first_k=1
    if [[ -n "$shdr" ]]; then
      meta_json+="\"server\":\"${shdr//\"/\\\"}\""; first_k=0
    fi
    if [[ -n "$tgi" ]]; then
      if [[ $first_k -eq 0 ]]; then meta_json+=","; fi
      tgi_limits="$(printf '%s' "$tgi" | jq -c '{version, sha, device_type, dtype, max_input_length, max_total_tokens, max_batch_total_tokens, num_shard}' 2>/dev/null || true)"
      if [[ -n "$tgi_limits" ]]; then meta_json+="\"tgi\":$tgi_limits"; first_k=0; fi
    fi
    if [[ -n "$prt" ]]; then
      if [[ $first_k -eq 0 ]]; then meta_json+=","; fi
      meta_json+="\"runtime\":$prt"; first_k=0
    fi
    meta_json+="}"
    [[ "$meta_json" == '{}' ]] && meta_json=""
  fi

  item="{\"id\":\"$m\",\"port\":$port,\"base\":\"$base\",\"label\":\"$label\",\"launchctl\":\"$lc_status\",\"health\":\"$health\",\"openai_compatible\":$openai"
  if [[ -n "$meta_json" ]]; then
    item+=",\"meta\":$meta_json"
  fi
  item+="}"
  if [[ $first -eq 1 ]]; then
    MODEL_JSON+="$item"; first=0
  else
    MODEL_JSON+=",$item"
  fi
done
MODEL_JSON+="]"

# Usage tips if labels are placeholders
echo ""
echo "Note: Adjust launchctl labels via --map or SLLM_LABEL_<model> if these don't match your system."

if (( WARN_COUNT > 0 || ERR_COUNT > 0 )); then
  echo "" >&2
  log_warn "Summary: warnings=$WARN_COUNT errors=$ERR_COUNT"
  if [[ -n "${LOG_FILE}" ]]; then
    log_info "Detailed logs written to: ${LOG_FILE}"
  fi
fi

# Optional controller status row (only shown if LLM_CTRL_KEY is set)
if [[ -n "${LLM_CTRL_KEY:-}" ]]; then
  ctrl_state="down"
  if curl -fsS -m "$HTTP_TIMEOUT" --connect-timeout "$HTTP_TIMEOUT" -H "X-API-Key: $LLM_CTRL_KEY" http://127.0.0.1:8090/status >/dev/null 2>&1; then
    ctrl_state="ok"
  fi
  col "controller" "8090" "-" "$ctrl_state" "-"
fi

# Write JSON summary for wiring the test GUI
if [[ -n "$OUT_JSON" ]]; then
  ts_now="$(ts)"
  ts_file="$(date -u +%Y%m%dT%H%M%SZ)"
  out_dir="$(dirname "$OUT_JSON")"
  out_base="$(basename "$OUT_JSON" .json)"
  out_hist="$out_dir/${out_base}_${ts_file}.json"
  mkdir -p "$out_dir" 2>/dev/null || true
  # Write once to the timestamped history file
  {
    printf '{\n'
    if [[ $DEEP -eq 1 ]]; then
      printf '  "_description": "Discovered local SLLM endpoints with extended metadata for test GUI wiring",\n'
    else
      printf '  "_description": "Discovered local SLLM endpoints for test GUI wiring",\n'
    fi
    printf '  "version": "1.0",\n'
    printf '  "generatedAt": "%s",\n' "$ts_now"
    printf '  "llmsHome": "%s",\n' "${LLMS_HOME:-}"
    if [[ $DEEP -eq 1 ]]; then
      sblk="$(system_block_json)"
      if [[ -n "$sblk" ]]; then
        printf '  "system": %s,\n' "$sblk"
      fi
    fi
    printf '  "localModels": %s\n' "$MODEL_JSON"
    printf '}\n'
  } > "$out_hist" && wrote=1 || { wrote=0; log_error "Failed writing JSON to $out_hist"; }
  if [[ ${wrote:-0} -eq 1 ]]; then
    log_info "Wrote JSON summary to: $out_hist"
    # Also update the non-timestamped 'latest' file for the GUI to read
    if cp -f "$out_hist" "$OUT_JSON" 2>/dev/null; then
      log_info "Updated latest JSON at: $OUT_JSON"
    else
      log_warn "Could not update latest at $OUT_JSON (check permissions)"
    fi
  fi
fi
