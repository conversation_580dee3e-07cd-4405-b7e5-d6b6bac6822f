# File: arioncomply-v1/ai-backend/python-backend/services/embedding/pipelines/__init__.py
# File Description: Package exposing concrete embedding pipeline implementations
# Purpose: Provide import surface for specific pipeline classes used by the service
"""
Embedding pipeline implementations.

This module contains all the individual embedding pipeline implementations:
- PlaceholderPipeline: Testing and fallback only (no semantic understanding)
- BGEOnnxPipeline: Primary high-quality pipeline with CPU optimization
- MPNetPipeline: Secondary reliable pipeline for fallback
- OpenAIPipeline: Optional cloud-based high-quality embeddings (security risk)
"""

from .placeholder_pipeline import PlaceholderPipeline
from .bge_onnx_pipeline import BGEOnnxPipeline
from .mpnet_pipeline import MPNetPipeline
from .openai_pipeline import OpenAIPipeline

__all__ = [
    "PlaceholderPipeline",
    "BGEOnnxPipeline", 
    "MPNetPipeline",
    "OpenAIPipeline"
]
