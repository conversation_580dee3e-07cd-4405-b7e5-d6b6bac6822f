// File: arioncomply-v1/supabase/functions/_shared/cors.ts
// File Description: CORS headers for Supabase Edge Functions
// Purpose: Provide permissive CORS defaults for browser-based testing clients.
// Notes: Adjust origins/headers/methods before production tightening.
// Create this file if it doesn't exist, or replace the contents

export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS, PUT, DELETE',
}
