-- File: arioncomply-v1/db/migrations/0002_org_and_profiles.sql
-- Migration 0002: Organizations and User Profiles (multi-tenant foundation)
-- Purpose: Introduce core tenant tables with RLS and audit fields.
-- Tables:
--   - organizations: a tenant record (org_id, org_name, domain, JSON settings)
--   - organization_settings: key/value JSON settings per org
--   - user_profiles: app-level user profile tied to org_id (distinct from auth.users)
-- RLS model: org-scoped with admin bypass; soft-delete fields present.
-- Indices: partial indexes for active rows.
-- Triggers: updated_at maintenance.
-- Aligns with:
--   - Design principles and application schema design
--   - RLS: org-scoped access with admin bypass

BEGIN;

-- Organizations
CREATE TABLE IF NOT EXISTS organizations (
  org_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_name text NOT NULL,
  org_domain text,
  settings jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid,
  updated_at timestamptz NOT NULL DEFAULT now(),
  updated_by uuid,
  deleted_at timestamptz,
  deleted_by uuid,
  CONSTRAINT chk_organizations_settings_json CHECK (jsonb_typeof(settings) = 'object')
);

CREATE INDEX IF NOT EXISTS idx_organizations_active ON organizations (org_name)
  WHERE deleted_at IS NULL;

ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- Read: same org or admin; Write: same org or admin; Insert: org_id must equal current org unless admin
CREATE POLICY org_select ON organizations
  FOR SELECT USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE POLICY org_update ON organizations
  FOR UPDATE USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  ) WITH CHECK (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE POLICY org_delete ON organizations
  FOR DELETE USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE POLICY org_insert ON organizations
  FOR INSERT WITH CHECK (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE TRIGGER organizations_set_updated_at
  BEFORE UPDATE ON organizations
  FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

-- Organization settings (key/value style)
CREATE TABLE IF NOT EXISTS organization_settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL REFERENCES organizations(org_id) ON DELETE CASCADE,
  setting_key text NOT NULL,
  setting_value jsonb,
  created_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid,
  updated_at timestamptz NOT NULL DEFAULT now(),
  updated_by uuid,
  deleted_at timestamptz,
  deleted_by uuid,
  CONSTRAINT uq_org_settings UNIQUE (org_id, setting_key),
  CONSTRAINT chk_org_settings_json CHECK (setting_value IS NULL OR jsonb_typeof(setting_value) IN ('object','array','string','number','boolean','null'))
);

CREATE INDEX IF NOT EXISTS idx_org_settings_active ON organization_settings (org_id, setting_key)
  WHERE deleted_at IS NULL;

ALTER TABLE organization_settings ENABLE ROW LEVEL SECURITY;

CREATE POLICY org_settings_select ON organization_settings
  FOR SELECT USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE POLICY org_settings_update ON organization_settings
  FOR UPDATE USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  ) WITH CHECK (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE POLICY org_settings_delete ON organization_settings
  FOR DELETE USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE POLICY org_settings_insert ON organization_settings
  FOR INSERT WITH CHECK (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE TRIGGER organization_settings_set_updated_at
  BEFORE UPDATE ON organization_settings
  FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

-- User profiles (app-level, separate from auth.users)
CREATE TABLE IF NOT EXISTS user_profiles (
  user_id uuid PRIMARY KEY,
  org_id uuid NOT NULL REFERENCES organizations(org_id) ON DELETE CASCADE,
  display_name text,
  email text,
  attributes jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz NOT NULL DEFAULT now(),
  created_by uuid,
  updated_at timestamptz NOT NULL DEFAULT now(),
  updated_by uuid,
  deleted_at timestamptz,
  deleted_by uuid,
  CONSTRAINT chk_user_profiles_attributes CHECK (jsonb_typeof(attributes) = 'object')
);

CREATE INDEX IF NOT EXISTS idx_user_profiles_org ON user_profiles (org_id)
  WHERE deleted_at IS NULL;

ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY user_profiles_select ON user_profiles
  FOR SELECT USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE POLICY user_profiles_update ON user_profiles
  FOR UPDATE USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  ) WITH CHECK (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE POLICY user_profiles_delete ON user_profiles
  FOR DELETE USING (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE POLICY user_profiles_insert ON user_profiles
  FOR INSERT WITH CHECK (
    org_id = app_current_org_id() OR app_has_role('admin')
  );

CREATE TRIGGER user_profiles_set_updated_at
  BEFORE UPDATE ON user_profiles
  FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();

COMMIT;
-- File: arioncomply-v1/db/migrations/0002_org_and_profiles.sql
