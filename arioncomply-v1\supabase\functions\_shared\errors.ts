// File: arioncomply-v1/supabase/functions/_shared/errors.ts
// File Description: API envelopes and error helpers
// Purpose: Define standardized success/error response formats and builders.
// Exports: apiOk<T>(), apiError(), ApiSuccess<T>, ApiError
// Notes: Used by all Edge handlers; responses are camelCase.
// Standardized API error and response envelope helpers

import { jsonResponse } from "./utils.ts";

export type ApiError = {
  ok: false;
  code: string; // machine-friendly error code
  message: string; // human-friendly description
  details?: Record<string, unknown> | null;
  requestId?: string;
  timestamp: string; // ISO time
};

export type ApiSuccess<T> = {
  ok: true;
  data: T;
  requestId?: string;
  timestamp: string;
};

/** Build a standardized error response envelope (camelCase). */
export function apiError(
  code: string,
  message: string,
  status = 400,
  details?: Record<string, unknown> | null,
  requestId?: string,
): Response {
  const err: ApiError = {
    ok: false,
    code,
    message,
    details: details ?? null,
    requestId,
    timestamp: new Date().toISOString(),
  };
  return jsonResponse(err, { status });
}

/** Build a standardized success response envelope (camelCase). */
export function apiOk<T>(data: T, requestId?: string, init?: ResponseInit): Response {
  const res: ApiSuccess<T> = {
    ok: true,
    data,
    requestId,
    timestamp: new Date().toISOString(),
  };
  return jsonResponse(res, init);
}
