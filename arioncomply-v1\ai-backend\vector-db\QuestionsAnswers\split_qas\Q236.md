id: Q236
query: >-
  How do we handle customer or partner concerns during compliance problems?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/A.16.1.5"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Communication of Security Events"
    id: "ISO27001:2022/A.16.1.5"
    locator: "Annex A.16.1.5"
ui:
  cards_hint:
    - "Stakeholder concern log"
  actions:
    - type: "open_register"
      target: "stakeholder_concerns"
      label: "View Concerns"
    - type: "start_workflow"
      target: "concern_response"
      label: "Address Concern"
output_mode: "both"
graph_required: false
notes: "Log inquiries, provide status updates, and document resolutions"
---
### 236) How do we handle customer or partner concerns during compliance problems?

**Standard terms)**  
- **Communication of security events (A.16.1.5):** defines stakeholder notification.

**Plain-English answer**  
Capture each inquiry in a **Stakeholder Concern Log**, respond promptly with factual updates, set expectations for remediation timelines, and document the resolution in a formal record.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.16.1.5

**Why it matters**  
Transparent communication maintains trust and limits churn.

**Do next in our platform**  
- Review **Stakeholder Concerns** register.  
- Launch **Concern Response** workflow for each case.

**How our platform will help**  
- **[Workflow]** Pre-built notification templates and status tracking.  
- **[Report]** Response time metrics and satisfaction surveys.

**Likely follow-ups**  
- Should we offer compensation or SLA credits?

**Sources**  
- ISO/IEC 27001:2022 Annex A.16.1.5
