id: Q139
query: >-
  How do we explain our program to auditors during an investigation?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/9.2"
  - "GDPR:2016/Art.58"
overlap_ids: []
capability_tags:
  - "Report"
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Internal Audit"
    id: "ISO27001:2022/9.2"
    locator: "Clause 9.2"
  - title: "GDPR — Powers of Supervisory Authorities"
    id: "GDPR:2016/Art.58"
    locator: "Article 58"
ui:
  cards_hint:
    - "Audit prep checklist"
  actions:
    - type: "start_workflow"
      target: "audit_investigation_prep"
      label: "Prepare for Audit"
    - type: "open_register"
      target: "audit_evidence"
      label: "View Evidence Register"
output_mode: "both"
graph_required: false
notes: "Provide clear evidence trail: policies, logs, risk registers"
---
### 139) How do we explain our program to auditors during an investigation?

**Standard terms**  
- **Internal audit (ISO 27001 Cl. 9.2):** demonstrates ongoing self-assessment.  
- **Supervisory powers (GDPR Art. 58):** authorities can inspect and request info.

**Plain-English answer**  
Present your **Evidence Register**: link each policy, risk register entry, and incident log to the relevant control. Use the **Audit Prep Checklist** to ensure documents, dashboards, and workflows are accessible and up-to-date.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 9.2; GDPR Article 58

**Why it matters**  
Structured presentation minimizes findings and demonstrates due diligence.

**Do next in our platform**  
- Launch **Audit Investigation Prep** workflow.  
- Review and tag your **Audit Evidence**.

**How our platform will help**  
- **[Workflow]** Guided evidence collection and labeling.  
- **[Report]** Pre-packaged audit packs with versioning.

**Likely follow-ups**  
- “What if auditors request real-time system access?” (Provide sandbox or screen-share)

**Sources**  
- ISO/IEC 27001:2022 Clause 9.2; GDPR Article 58
