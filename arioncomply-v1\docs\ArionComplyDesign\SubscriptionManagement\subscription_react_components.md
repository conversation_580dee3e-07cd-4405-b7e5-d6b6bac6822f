# Subscription Management UI Components

## Overview

This document outlines the logical components and workflows needed for ArionComply's subscription management user interface. Rather than providing specific implementation details, it focuses on the component logic, workflows, and use cases to allow flexibility in the final UI implementation.

## Prerequisites

Before implementing these UI components, ensure the following:

1. The database schema from `arioncomply-v1/docs/ApplicationDatabase/Unified-Subscription-Management-Schema-Design.md` has been applied
2. The database functions and triggers from `arioncomply-v1/docs/ApplicationDatabase/Subscription-Management-Functions-Triggers.md` have been implemented
3. The metadata-driven implementation from `arioncomply-v1/docs/ApplicationDatabase/Unified-Subscription-Management-Metadata-Implementation.md` has been configured
4. The edge functions from `arioncomply-v1/docs/API/Subscription-Management-Edge-Functions.md` have been deployed

## Component Architecture

The subscription management UI consists of several key logical components:

1. **SubscriptionOverview**: Main dashboard showing current subscription status
2. **SubscriptionPlans**: Display available plans for selection
3. **PlanDetails**: Detailed view of a specific plan
4. **SubscriptionLimits**: Shows usage against plan limits
5. **SubscriptionHistory**: Displays subscription change history
6. **ChangePlanModal**: Modal for changing subscription plans
7. **CancelSubscriptionModal**: Modal for canceling subscriptions

## Required API Functionality

The UI will need to interact with several API endpoints:

1. **Get Active Subscription**: Retrieves the current active subscription for the organization
   - Endpoint: `/subscription/active`
   - Method: `GET`
   - Returns: Subscription details including plan information

2. **Get Available Plans**: Retrieves all available subscription plans
   - Endpoint: `/subscription/plans`
   - Method: `GET`
   - Parameters: 
     - `all`: Boolean to show all plans or only public ones
     - `type`: Filter by plan type (demo, basic, professional, etc.)
   - Returns: List of subscription plans

3. **Change Plan**: Changes the current subscription to a new plan
   - Endpoint: `/subscription/change-plan`
   - Method: `POST`
   - Body: 
     - `plan_id`: ID of the new plan
     - `reason`: Reason for changing plans
   - Returns: Success/failure message

4. **Cancel Subscription**: Cancels the current subscription
   - Endpoint: `/subscription/cancel`
   - Method: `POST`
   - Body:
     - `reason`: Reason for cancellation
   - Returns: Success/failure message

5. **Get Usage Data**: Retrieves usage data for the current subscription
   - Endpoint: `/subscription/usage`
   - Method: `GET`
   - Returns: Usage statistics and limits

6. **Get Subscription History**: Retrieves the subscription change history
   - Endpoint: `/subscription/history`
   - Method: `GET`
   - Returns: List of subscription changes

## Component Logic and Workflows

### SubscriptionOverview Component

**Purpose**: Provide a high-level view of the organization's current subscription status.

**Logic flow**:
1. On component mount, fetch the active subscription
2. If no active subscription exists, show a message to select a plan
3. If an active subscription exists:
   - Display the plan name, type, and status
   - Show time remaining for time-limited subscriptions
   - Show auto-renewal status for recurring subscriptions
   - Display key features and limits of the current plan
   - Provide options to change or cancel the plan

**User interactions**:
- View subscription details
- Click to change plan (opens ChangePlanModal)
- Click to cancel subscription (opens CancelSubscriptionModal)
- View usage against limits (links to SubscriptionLimits)

**Conditional display logic**:
- For demo subscriptions, highlight the time remaining and provide prominent upgrade options
- For paid subscriptions, show billing details and renewal information
- For expired subscriptions, display reactivation options

**Permission requirements**:
- User must have `view:subscription` permission to see this component
- User must have `update:subscription` permission to change plans
- User must have `cancel:subscription` permission to cancel subscriptions

### SubscriptionPlans Component

**Purpose**: Display available subscription plans for selection.

**Logic flow**:
1. On component mount, fetch all available plans
2. Filter plans based on current context (e.g., upgrade path, organization size)
3. Display plans in a grid or list format
4. Highlight the current active plan if applicable
5. Provide comparison functionality

**User interactions**:
- View plan details
- Compare multiple plans
- Select a plan to view more details or purchase
- Filter plans by type, features, or price range

**Conditional display logic**:
- For users on demo plans, highlight recommended upgrade paths
- For users on paid plans, highlight potential upgrade options
- Always show the current plan with a visual indicator

**Permission requirements**:
- User must have `view:subscription` permission to see basic plans
- User must have `view:all_plans` permission to see non-public plans

### PlanDetails Component

**Purpose**: Show detailed information about a specific subscription plan.

**Logic flow**:
1. Receive plan ID as input
2. Fetch detailed plan information
3. Display comprehensive plan details including:
   - Features included/excluded
   - Usage limits
   - Pricing information
   - Billing cycle
   - Comparison with current plan (if applicable)

**User interactions**:
- View all plan details
- Compare with current plan
- Select plan (if not current plan)
- View terms and conditions

**Conditional display logic**:
- If viewing current plan, show current usage statistics
- If viewing potential plan, show comparison with current plan
- Highlight features that would be gained or lost when changing plans

**Permission requirements**:
- User must have `view:subscription` permission to see plan details

### SubscriptionLimits Component

**Purpose**: Display current usage against the limits defined in the subscription plan.

**Logic flow**:
1. Fetch usage data for the organization
2. Fetch limits from the current subscription plan
3. Calculate usage percentages for each metric
4. Display visual representations of usage vs. limits
5. Highlight metrics approaching or exceeding limits

**User interactions**:
- View usage data
- Filter by feature type
- View historical usage trends (if implemented)
- Get recommendations for optimizing usage

**Conditional display logic**:
- Use warning colors for metrics approaching limits (e.g., >80%)
- Use error colors for metrics exceeding limits
- For unlimited metrics, show special indicator instead of percentage

**Permission requirements**:
- User must have `view:usage` permission to see this component

### SubscriptionHistory Component

**Purpose**: Show the history of subscription changes for the organization.

**Logic flow**:
1. Fetch subscription transition history
2. Sort transitions by date (newest first)
3. Display transitions with key information:
   - From/to plan names
   - Transition date
   - Reason for change
   - User who made the change
   - Transition source (user, system, etc.)

**User interactions**:
- View transition details
- Filter by date range or transition type
- Sort by different columns

**Conditional display logic**:
- Highlight transitions that resulted in data loss
- Use different icons/colors for different types of transitions (upgrade, downgrade, cancellation)

**Permission requirements**:
- User must have `view:subscription_history` permission to see this component

### ChangePlanModal Component

**Purpose**: Provide a modal interface for changing subscription plans.

**Logic flow**:
1. Display current plan and selected new plan
2. Show differences between plans (features gained/lost)
3. Explain implications of changing plans
4. Provide field for entering reason for change
5. Submit change request to API
6. Handle success/failure response

**User interactions**:
- Review plan differences
- Enter reason for change
- Confirm or cancel the change

**Conditional display logic**:
- For downgrades, show warnings about potential feature or data loss
- For upgrades, highlight new features that will be available
- For plan type changes (e.g., monthly to annual), explain billing implications

**Permission requirements**:
- User must have `update:subscription` permission to use this component

### CancelSubscriptionModal Component

**Purpose**: Provide a modal interface for canceling subscriptions.

**Logic flow**:
1. Display current subscription details
2. Explain implications of cancellation
3. Provide field for entering reason for cancellation
4. Submit cancellation request to API
5. Handle success/failure response

**User interactions**:
- Review cancellation implications
- Enter reason for cancellation
- Confirm or cancel the operation

**Conditional display logic**:
- For paid subscriptions, explain billing implications
- For all subscriptions, explain data retention policies

**Permission requirements**:
- User must have `cancel:subscription` permission to use this component

## State Management Considerations

The subscription management UI should manage several key states:

1. **Subscription State**:
   - Current active subscription details
   - Loading/error states for subscription data
   - Functions to refresh, change, or cancel subscription

2. **Plans State**:
   - Available plans list
   - Loading/error states for plans data
   - Filtering and sorting state
   - Selected plan for comparison or viewing

3. **Usage State**:
   - Current usage metrics
   - Usage limits from plan
   - Loading/error states for usage data
   - Warning states for metrics approaching limits

4. **UI State**:
   - Modal open/closed states
   - Selected tabs or views
   - Form input values
   - Validation states

State management should be implemented according to the frontend architecture of the application, whether that's using React Context, Redux, Zustand, or another state management solution.

## Permission Handling

UI components should respect the user's permissions:

1. **View Permission**: Components should only be visible if the user has the appropriate view permission
2. **Action Permission**: Interactive elements (buttons, forms) should only be enabled if the user has the appropriate action permission
3. **Error Handling**: If a user attempts an action without permission, provide clear error messages
4. **Graceful Degradation**: Components should degrade gracefully when permissions are missing (e.g., show read-only view instead of editable view)

## Error Handling Patterns

The UI should handle errors consistently:

1. **API Errors**: Display user-friendly error messages for API failures
2. **Validation Errors**: Provide inline validation with clear error messages
3. **Permission Errors**: Explain permission requirements when actions are denied
4. **Loading States**: Show appropriate loading indicators during API calls
5. **Retry Logic**: Provide retry options for transient failures
6. **Fallback Content**: Display fallback content when data cannot be loaded

## Notification Patterns

Use notifications to keep users informed:

1. **Success Notifications**: Confirm successful actions (plan changes, cancellations)
2. **Error Notifications**: Alert users to failures that require attention
3. **Warning Notifications**: Warn about approaching limits or impending subscription expirations
4. **Information Notifications**: Provide helpful context about subscription features or changes

## Conclusion

This document has outlined the logical components, workflows, and considerations for implementing the subscription management UI. By focusing on the logic and use cases rather than specific implementation details, it provides flexibility for different frontend frameworks or design systems while ensuring that all required functionality is addressed.

The key principles to follow in the implementation are:

1. **User-Centric Design**: Focus on making subscription management intuitive and transparent
2. **Permission-Aware UI**: Respect user permissions in all components
3. **Clear Feedback**: Provide clear feedback for all user actions
4. **Responsive Design**: Ensure components work well on all device sizes
5. **Progressive Enhancement**: Start with core functionality and enhance as needed

## Next Steps

After implementing these UI components, the final step is to integrate them into the subscription management workflows described in `arioncomply-v1/docs/Workflows/Subscription-Management-Workflows.md`.