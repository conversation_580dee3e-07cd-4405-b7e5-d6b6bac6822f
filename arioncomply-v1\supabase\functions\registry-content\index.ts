// File: arioncomply-v1/supabase/functions/registry-content/index.ts
// File Description: Registry content Edge function
// Purpose: Serve content/{id}/metadata.json files as API endpoint
// Input: GET /registry-content/{id} (e.g., /registry-content/Q001)
// Output: Content metadata JSON for specific content ID
// Notes: Dynamic content loading with proper error handling and caching

import { serve } from "https://deno.land/std@0.224.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { apiOk, apiError } from "../_shared/errors.ts";
import { getClientMeta, logRequestStart, logRequestEnd, logEvent } from "../_shared/logger.ts";
import { convertKeys } from "../_shared/utils.ts";

serve(async (req) => {
  const requestId = crypto.randomUUID();
  const meta = getClientMeta(req, requestId);
  await logRequestStart(meta, req.headers);

  // CORS preflight
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  if (req.method !== "GET") {
    const res = apiError("METHOD_NOT_ALLOWED", "Only GET method is allowed", 405, undefined, requestId);
    await logRequestEnd(meta, 405);
    return res;
  }

  // Extract content ID from URL path
  const url = new URL(req.url);
  const pathSegments = url.pathname.split("/").filter(Boolean);
  
  // Expect path like /functions/v1/registry-content/{id}
  const contentId = pathSegments[pathSegments.length - 1];
  
  if (!contentId) {
    const res = apiError(
      "VALIDATION_REQUIRED_FIELD",
      "Content ID is required in path",
      400,
      { example: "/registry-content/Q001" },
      requestId
    );
    await logRequestEnd(meta, 400);
    return res;
  }

  // Validate content ID format (basic sanity check)
  if (!/^[A-Z0-9][A-Z0-9_-]*[A-Z0-9]?$/.test(contentId) || contentId.length > 50) {
    const res = apiError(
      "VALIDATION_INVALID_FORMAT",
      "Invalid content ID format",
      400,
      { 
        contentId,
        expectedFormat: "Alphanumeric with dashes/underscores, 1-50 chars"
      },
      requestId
    );
    await logRequestEnd(meta, 400);
    return res;
  }

  try {
    // Attempt to load content metadata dynamically
    // Note: In a real deployment, this would read from filesystem or embedded assets
    let contentMetadata;
    
    try {
      // Try to import the content metadata file
      // This is a simplified approach - in production, you'd want proper file system access
      contentMetadata = await import(`../../../content/ISO27001/qa/${contentId}/metadata.json`, {
        assert: { type: "json" }
      });
      contentMetadata = contentMetadata.default;
    } catch (importError) {
      // If direct import fails, return not found
      const res = apiError(
        "CONTENT_NOT_FOUND",
        `Content metadata not found for ID: ${contentId}`,
        404,
        { 
          contentId,
          searchPath: `content/ISO27001/qa/${contentId}/metadata.json`,
          hint: "Check if content exists and metadata.json is present"
        },
        requestId
      );
      await logRequestEnd(meta, 404);
      return res;
    }

    // Log content access for analytics
    await logEvent(meta, {
      eventType: "registry_content_accessed",
      direction: "internal",
      status: "ok", 
      details: {
        contentId,
        artifactType: contentMetadata.artifact?.type,
        framework: contentMetadata.standard?.framework,
        version: contentMetadata.version,
        hasSuggestions: !!(contentMetadata.ui?.actions?.length),
      }
    });

    // Convert to camelCase for API consistency  
    const camelCaseData = convertKeys(contentMetadata, "toCamel");
    
    const res = apiOk(camelCaseData, requestId, {
      headers: {
        ...corsHeaders,
        "Cache-Control": "public, max-age=600", // 10-minute cache for content
        "Content-Type": "application/json"
      }
    });
    
    await logRequestEnd(meta, 200);
    return res;
    
  } catch (error) {
    console.error(`Registry content error for ${contentId}:`, error);
    
    const res = apiError(
      "REGISTRY_CONTENT_ERROR",
      "Failed to load content metadata",
      500,
      { 
        contentId,
        error: String(error)
      },
      requestId
    );
    
    await logRequestEnd(meta, 500);
    return res;
  }
});