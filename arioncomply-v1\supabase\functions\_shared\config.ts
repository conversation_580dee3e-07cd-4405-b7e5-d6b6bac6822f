// File: arioncomply-v1/supabase/functions/_shared/config.ts
// File Description: Configuration loader for AI backend
// Purpose: Allow switching between local SLLMs and GLLMs (OpenAI/Anthropic)
// Env: AC_CONFIG_JSON, OPENAI_API_KEY, ANTHROPIC_API_KEY, SLLM_DEFAULT_BASE_URL, SLLM_DEFAULT_MODEL

export type ProviderKey = "sllm" | "openai" | "anthropic";

export type RouterPolicy = {
  // Preferred provider order for a task; will fallback in order
  taskDefault: ProviderKey[]; // e.g., ["sllm", "openai", "anthropic"]
};

export type ProviderConfig = {
  primaryProvider: ProviderKey;
  allowFallbacks: boolean;
  policy: RouterPolicy;
  // Edge operates as a thin gateway when forwardOnly=true
  forwardOnly?: boolean;
  backend?: {
    url?: string; // e.g., http://localhost:9000/ai/chat
  };
  features?: {
    // Toggleable stages without code changes
    // Note: Retrieval/preprocess should be backend responsibilities. Edge ignores these when forwardOnly=true
    enablePreprocess?: boolean;
    enableRetrieval?: boolean;
    retrievalBackend?: "supabase" | "chromedb";
  };
  sllm?: {
    // One or more local models/endpoints; OpenAI-compatible if possible
    defaultBaseUrl?: string; // e.g., http://localhost:8000/v1
    defaultModel?: string; // e.g., llama3-8b-instruct-int8
    apiKey?: string; // optional if local server ignores
  };
  openai?: {
    apiKey?: string;
    baseUrl?: string; // optional override
    model?: string;   // e.g., gpt-4o-mini
  };
  anthropic?: {
    apiKey?: string;
    baseUrl?: string; // optional override
    model?: string;   // e.g., claude-3-haiku-20240307
  };
};

const defaultConfig: ProviderConfig = {
  primaryProvider: "sllm",
  allowFallbacks: true,
  policy: { taskDefault: ["sllm", "openai", "anthropic"] },
  forwardOnly: (Deno.env.get("FORWARD_ONLY") ?? "true").toLowerCase() === "true",
  backend: {
    url: Deno.env.get("AI_BACKEND_URL") ?? undefined,
  },
  features: {
    enablePreprocess: false,
    enableRetrieval: true,
    retrievalBackend: (Deno.env.get("RETRIEVAL_BACKEND") as any) ?? "supabase",
  },
  sllm: {
    defaultBaseUrl: Deno.env.get("SLLM_DEFAULT_BASE_URL") ?? "http://localhost:8000/v1",
    defaultModel: Deno.env.get("SLLM_DEFAULT_MODEL") ?? "llama3-8b-instruct-int8",
    apiKey: Deno.env.get("SLLM_API_KEY") ?? undefined,
  },
  openai: {
    apiKey: Deno.env.get("OPENAI_API_KEY") ?? undefined,
    baseUrl: Deno.env.get("OPENAI_BASE_URL") ?? undefined,
    model: Deno.env.get("OPENAI_MODEL") ?? undefined,
  },
  anthropic: {
    apiKey: Deno.env.get("ANTHROPIC_API_KEY") ?? undefined,
    baseUrl: Deno.env.get("ANTHROPIC_BASE_URL") ?? undefined,
    model: Deno.env.get("ANTHROPIC_MODEL") ?? undefined,
  },
};

/** Load provider/router configuration from env (AC_CONFIG_JSON) merged with defaults. */
export function loadProviderConfig(): ProviderConfig {
  const raw = Deno.env.get("AC_CONFIG_JSON");
  if (!raw) return defaultConfig;
  try {
    const parsed = JSON.parse(raw) as Partial<ProviderConfig>;
    return {
      ...defaultConfig,
      ...parsed,
      sllm: { ...defaultConfig.sllm, ...(parsed.sllm ?? {}) },
      openai: { ...defaultConfig.openai, ...(parsed.openai ?? {}) },
      anthropic: { ...defaultConfig.anthropic, ...(parsed.anthropic ?? {}) },
    };
  } catch {
    return defaultConfig;
  }
}
