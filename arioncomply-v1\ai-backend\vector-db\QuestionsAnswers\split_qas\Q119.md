id: Q119
query: >-
  How do we handle compliance for seasonal businesses or project-based work?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/8.1"
  - "ISO27001:2022/9.1"
overlap_ids: []
capability_tags:
  - "Planner"
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Operational Planning & Control"
    id: "ISO27001:2022/8.1"
    locator: "Clause 8.1"
  - title: "ISO/IEC 27001:2022 — Monitoring & Evaluation"
    id: "ISO27001:2022/9.1"
    locator: "Clause 9.1"
ui:
  cards_hint:
    - "Seasonal compliance scheduler"
  actions:
    - type: "start_workflow"
      target: "seasonal_compliance"
      label: "Configure Seasonal Plan"
output_mode: "both"
graph_required: false
notes: "Use project-based scopes and temporary registers refreshed each cycle"
---
### 119) How do we handle compliance for seasonal businesses or project-based work?

**Standard terms**  
- **Operational control (Cl. 8.1):** planning for changes in operations.  
- **Monitoring (Cl. 9.1):** review performance at each phase.

**Plain-English answer**  
Define a **project-specific scope** for each season or project, maintain separate registers, and archive or refresh controls at cycle end. Monitor compliance metrics per cycle.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clauses 8.1; 9.1

**Why it matters**  
Keeps compliance lean and relevant to active operations.

**Do next in our platform**  
- Launch **Seasonal Compliance** workflow.  
- Configure seasonal scopes and tasks.

**How our platform will help**  
- **[Planner]** Cycle-based scheduling.  
- **[Report]** Cycle performance dashboards.

**Likely follow-ups**  
- “Can we automate scope roll-over?” (Yes—use templates and cloning features)

**Sources**  
- ISO/IEC 27001:2022 Clauses 8.1; 9.1
