# File: arioncomply-v1/ai-backend/python-backend/services/embedding/pipelines/mpnet_pipeline.py
# File Description: MPNet-based local embedding pipeline implementation
# Purpose: Provide reliable local embeddings and act as a high-quality fallback
"""
all-mpnet-base-v2 pipeline for reliable local embeddings.

Secondary embedding pipeline providing good quality/performance balance
with excellent reliability and broad compatibility.
"""

import asyncio
import logging
import time
import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any

from ..pipeline_interface import (
    EmbeddingPipeline,
    PipelineMetadata,
    EmbeddingResult,
    HealthCheckResult,
    PipelineStatus,
    QualityTier
)

logger = logging.getLogger(__name__)

try:
    from sentence_transformers import SentenceTransformer
    import torch
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"MPNet dependencies not available: {e}")
    DEPENDENCIES_AVAILABLE = False


class MPNetPipeline(EmbeddingPipeline):
    """
    all-mpnet-base-v2 embedding pipeline.
    
    Features:
    - Reliable local embeddings with good quality
    - 768-dimensional embeddings
    - Mean pooling with L2 normalization
    - Excellent fallback option
    - Fast loading and inference
    """
    
    MODEL_NAME = "sentence-transformers/all-mpnet-base-v2"
    DIMENSION = 768
    CONTEXT_LENGTH = 384
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize MPNet pipeline with runtime configuration."""
        super().__init__(config)
        self._name = "all-mpnet-base-v2"
        
        # Configuration
        self.cache_dir = Path(config.get("cache_dir", "~/.cache/sentence-transformers")).expanduser()
        self.device = config.get("device", "cpu")
        self.batch_size = config.get("batch_size", 32)
        
        # Model
        self.model = None
        
        # Performance tracking
        self._load_time_ms = None
        self._avg_inference_time_ms = None
        
        # Metadata
        self._metadata = PipelineMetadata(
            name=self._name,
            model_name=self.MODEL_NAME,
            dimension=self.DIMENSION,
            quality_tier=QualityTier.GOOD,
            is_local=True,
            version="2.0.0",
            provider="sentence-transformers",
            creation_date=datetime(2021, 8, 30),  # Model release date
            memory_mb=400,
            inference_time_ms=75,  # Estimated
            context_length=self.CONTEXT_LENGTH,
            config={
                "pooling": "mean",
                "normalization": "l2",
                "device": self.device,
                "batch_size": self.batch_size
            }
        )
    
    @property
    def name(self) -> str:
        """Return pipeline name."""
        return self._name
    
    @property
    def metadata(self) -> PipelineMetadata:
        """Return pipeline metadata with dynamic performance metrics."""
        # Update dynamic metrics
        if self._load_time_ms:
            self._metadata.config["actual_load_time_ms"] = self._load_time_ms
        if self._avg_inference_time_ms:
            self._metadata.inference_time_ms = int(self._avg_inference_time_ms)
        
        return self._metadata
    
    async def load(self) -> None:
        """Load all-mpnet-base-v2 model."""
        if not DEPENDENCIES_AVAILABLE:
            raise RuntimeError("MPNet dependencies not available. Install: pip install sentence-transformers torch")
        
        if self._is_loaded:
            return
        
        start_time = time.time()
        logger.info(f"Loading MPNet pipeline: {self.MODEL_NAME}")
        
        try:
            # Ensure cache directory exists
            self.cache_dir.mkdir(parents=True, exist_ok=True)
            
            # Load model
            logger.info("Loading sentence-transformers model...")
            self.model = SentenceTransformer(
                self.MODEL_NAME,
                cache_folder=str(self.cache_dir),
                device=self.device
            )
            
            # Configure model settings
            self.model.max_seq_length = self.CONTEXT_LENGTH
            
            # Warm up with a test encoding
            logger.info("Warming up model...")
            _ = self.model.encode(["warmup"], convert_to_numpy=True)
            
            self._load_time_ms = (time.time() - start_time) * 1000
            self._is_loaded = True
            
            logger.info(f"MPNet pipeline loaded successfully in {self._load_time_ms:.1f}ms")
            
        except Exception as e:
            logger.error(f"Failed to load MPNet pipeline: {e}")
            raise
    
    async def embed_texts(self, texts: List[str], trace_id: Optional[str] = None) -> EmbeddingResult:
        """Generate embeddings using all-mpnet-base-v2."""
        start_time = time.time()
        operation_id = str(uuid.uuid4())
        
        try:
            # Validate inputs
            self.validate_inputs(texts)
            
            if not self._is_loaded:
                await self.load()
            
            # Prepare texts (truncate if necessary)
            processed_texts = []
            for text in texts:
                # Rough truncation based on character count
                if len(text) > self.CONTEXT_LENGTH * 4:
                    text = text[:self.CONTEXT_LENGTH * 4]
                processed_texts.append(text)
            
            # Generate embeddings
            logger.debug(f"Generating MPNet embeddings for {len(processed_texts)} texts")
            
            # Use asyncio to run blocking operation in thread pool
            embeddings_array = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.model.encode(
                    processed_texts,
                    batch_size=self.batch_size,
                    show_progress_bar=False,
                    convert_to_numpy=True,
                    normalize_embeddings=True  # L2 normalization
                )
            )
            
            # Convert to list format
            embeddings_list = embeddings_array.tolist()
            
            processing_time_ms = (time.time() - start_time) * 1000
            
            # Update average inference time
            if self._avg_inference_time_ms is None:
                self._avg_inference_time_ms = processing_time_ms
            else:
                self._avg_inference_time_ms = (self._avg_inference_time_ms * 0.9) + (processing_time_ms * 0.1)
            
            logger.debug(f"MPNet embedding completed in {processing_time_ms:.1f}ms")
            
            return self._create_embedding_result(
                operation_id=operation_id,
                input_texts=texts,
                embeddings=embeddings_list,
                processing_time_ms=processing_time_ms,
                success=True,
                trace_id=trace_id
            )
            
        except Exception as e:
            processing_time_ms = (time.time() - start_time) * 1000
            logger.error(f"MPNet embedding failed: {e}")
            
            return self._create_embedding_result(
                operation_id=operation_id,
                input_texts=texts,
                embeddings=[],
                processing_time_ms=processing_time_ms,
                success=False,
                error_message=str(e),
                trace_id=trace_id
            )
    
    async def health_check(self) -> HealthCheckResult:
        """Perform comprehensive health check."""
        start_time = time.time()
        
        try:
            # Check dependencies
            if not DEPENDENCIES_AVAILABLE:
                return HealthCheckResult(
                    pipeline_name=self.name,
                    status=PipelineStatus.FAILED,
                    timestamp=datetime.utcnow(),
                    error_message="Dependencies not available: sentence-transformers, torch",
                    dependencies_available=False
                )
            
            # Check if model is loaded
            if not self._is_loaded:
                try:
                    await self.load()
                except Exception as load_error:
                    return HealthCheckResult(
                        pipeline_name=self.name,
                        status=PipelineStatus.FAILED,
                        timestamp=datetime.utcnow(),
                        error_message=f"Failed to load model: {load_error}",
                        dependencies_available=True,
                        is_model_loaded=False
                    )
            
            # Test embedding generation
            test_text = "This is a test for MPNet embedding quality and reliability."
            test_result = await self.embed_texts([test_text])
            
            if not test_result.success:
                return HealthCheckResult(
                    pipeline_name=self.name,
                    status=PipelineStatus.FAILED,
                    timestamp=datetime.utcnow(),
                    error_message=f"Test embedding failed: {test_result.error_message}",
                    is_model_loaded=self._is_loaded,
                    dependencies_available=True
                )
            
            # Validate embedding dimensions
            if len(test_result.embeddings[0]) != self.DIMENSION:
                return HealthCheckResult(
                    pipeline_name=self.name,
                    status=PipelineStatus.FAILED,
                    timestamp=datetime.utcnow(),
                    error_message=f"Invalid embedding dimension: got {len(test_result.embeddings[0])}, expected {self.DIMENSION}",
                    is_model_loaded=self._is_loaded,
                    dependencies_available=True
                )
            
            # Check if device is available
            device_status = self._check_device_availability()
            if not device_status["available"]:
                status = PipelineStatus.DEGRADED
                error_message = f"Preferred device '{self.device}' not available: {device_status['error']}"
            else:
                status = PipelineStatus.HEALTHY
                error_message = None
            
            return HealthCheckResult(
                pipeline_name=self.name,
                status=status,
                timestamp=datetime.utcnow(),
                load_time_ms=self._load_time_ms,
                test_inference_time_ms=test_result.processing_time_ms,
                memory_usage_mb=self._estimate_memory_usage(),
                error_message=error_message,
                is_model_loaded=self._is_loaded,
                dependencies_available=True
            )
            
        except Exception as e:
            return HealthCheckResult(
                pipeline_name=self.name,
                status=PipelineStatus.FAILED,
                timestamp=datetime.utcnow(),
                error_message=str(e),
                error_details={"exception_type": type(e).__name__},
                is_model_loaded=self._is_loaded,
                dependencies_available=DEPENDENCIES_AVAILABLE
            )
    
    def _check_device_availability(self) -> Dict[str, Any]:
        """Check if the configured device is available."""
        try:
            if self.device == "cpu":
                return {"available": True, "error": None}
            elif self.device.startswith("cuda"):
                if torch.cuda.is_available():
                    device_count = torch.cuda.device_count()
                    device_id = int(self.device.split(":")[-1]) if ":" in self.device else 0
                    if device_id < device_count:
                        return {"available": True, "error": None}
                    else:
                        return {"available": False, "error": f"CUDA device {device_id} not found (only {device_count} devices available)"}
                else:
                    return {"available": False, "error": "CUDA not available"}
            else:
                return {"available": False, "error": f"Unknown device: {self.device}"}
                
        except Exception as e:
            return {"available": False, "error": str(e)}
    
    def _estimate_memory_usage(self) -> float:
        """Estimate current memory usage in MB."""
        base_memory = 400.0  # MB for all-mpnet-base-v2
        
        if self.device != "cpu" and torch.cuda.is_available():
            # Add GPU memory overhead
            base_memory += 100.0
        
        return base_memory
    
    async def unload(self) -> None:
        """Unload the model to free resources."""
        logger.info("Unloading MPNet pipeline")
        
        if self.model:
            # Clear model from memory
            del self.model
            self.model = None
            
            # Clear GPU cache if using CUDA
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        
        self._is_loaded = False
        logger.info("MPNet pipeline unloaded")
