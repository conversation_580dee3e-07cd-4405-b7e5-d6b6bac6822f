# ArionComply AI Backend Technical Task Tracker

**Document Version:** 1.0  
**Date:** August 27, 2025  
**Status:** Active  
**Reference Documents:** 
- arioncomply-v1/docs/Detailed-Plan.md
- arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/AIBackend/vector-db/Inputs/QuestionsAnswers/Q1to160.md
- arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/AIBackend/vector-db/Inputs/QuestionsAnswers/split_qas/Q104.md

---

## MVP-Assessment-App Priority Scope (AI Backend)

- [ ] Provide deterministic/stubbed responses for conversation.send/stream while wiring full backend.
- [ ] Minimal retrieval context or canned answers for assessment questions and document generation prompts.
- [ ] Ensure outputs align with standardized envelopes; log `ai_call` events (provider/model/tokens/cost/anonymized) when integrated.
- [ ] Adopt W3C `traceparent` (UI → Edge → Backend) and echo/propagate IDs; include `traceId/spanId` in backend-emitted events (JSON details for MVP).
- [ ] Enforce org policy: `allow_gllm=false` by default; anonymization only on GLLM fallback path.

### Audit & Logging (MVP-Assessment-App)
- [ ] Pass through `requestId`/`correlationId` to backend; include in responses for traceability — Dependencies: Edge forwards IDs
- [ ] On error or timeout, surface structured error to Edge and emit `ai_call` with `status=error` and details

## MVP-Demo-Light-App Priority Scope (AI Backend)

- [ ] Canned scenario responses and summaries to support demo scripts.
- [ ] Implement hybrid retrieval (Supabase Vector first); emit `retrieval_run` with citations, backend, topK, latencyMs.
- [ ] Provider routing: SLLM primary; GLLM fallback when allowed; record `ai_call` with anonymized flag.
- [ ] Explainability: accept `compliance-chain-id` and optional `decision-context` headers; log `compliance_chain_started` and enrich context.
- [ ] App telemetry minimal: accept and persist `ui_action: send_message` and `navigation` (design pending).

## MVP-Pilot Scope (AI Backend)

- [ ] Retrieval + LLM integration sufficient to generate certification-ready documents and register updates with citations and explainability.
- [ ] Add rules registry; emit `deterministic_rules_applied` with rule ids/versions/outcomes.
- [ ] Add process ledger (`process_runs`) and export traces by requestId/sessionId/processId/correlationId.

## Production Scope (Later Phases)

- [ ] Full retrieval pipeline (chunkers, indexers, multi-index retrieval, ranking, evidence).
- [ ] Prompt engineering and inference service integration to external models.
- [ ] Quality evaluation, explainability, and feedback processing loops.
- [ ] SLO dashboards and provider selection metrics; vector tenancy automation; HITL proposal lifecycle events.

## Testing Readiness Deployment Checklist (AI Backend)

- [ ] Confirm target environment for AI backend tests (local vs hosted).
- [ ] Prepare minimal seed data for retrieval scenarios (documents/templates as needed).
- [ ] Expose an integration point from Edge Functions to AI backend (HTTP or internal call), or use the current stub responses for initial tests.
- [ ] Define environment variables and secrets required by the backend (model keys, feature flags) and load them in the deployment environment.
- [ ] Smoke test with known queries; verify structured envelopes and logging of outbound events via `api_event_logs` (event_type like `ai_call`).

---

## 1. Core AI Infrastructure

### 1.1 Local LLM Runtime Service
- [ ] **Task:** Implement local LLM runtime service
  - **Dependencies:** None (foundation component)
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Model loader
    - [ ] Inference engine
    - [ ] Memory management
    - [ ] CPU optimization
    - [ ] Batch processing

### 1.2 Query Service
- [ ] **Task:** Implement rank query service
  - **Dependencies:** None (foundation component)
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Query pre-processing
    - [ ] BM25 ranking
    - [ ] Hybrid search
    - [ ] Result combination
    - [ ] Relevance scoring

### 1.3 Graph Service
- [ ] **Task:** Implement graph service
  - **Dependencies:** None (foundation component)
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Graph representation
    - [ ] Relationship traversal
    - [ ] Path finding
    - [ ] Centrality metrics
    - [ ] Knowledge graph navigation

---

## 2. Data Ingestion Pipeline

### 2.1 Document Chunker
- [ ] **Task:** Implement document chunker
  - **Dependencies:** None (foundation component)
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Document parsing
    - [ ] Chunk strategy
    - [ ] Metadata extraction
    - [ ] Overlap management
    - [ ] Context preservation

### 2.2 Indexer Service
- [ ] **Task:** Implement indexer service
  - **Dependencies:** Document Chunker
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Term indexing
    - [ ] Ngram indexing
    - [ ] Entity indexing
    - [ ] Numeric indexing
    - [ ] ID indexing

### 2.3 Knowledge Graph Builder
- [ ] **Task:** Implement knowledge graph builder
  - **Dependencies:** Document Chunker
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Entity extraction
    - [ ] Relationship identification
    - [ ] Graph construction
    - [ ] Metadata enrichment
    - [ ] Graph validation

---

## 3. Retrieval Components

### 3.1 Multi-Index Retrieval
- [ ] **Task:** Implement multi-index retrieval
  - **Dependencies:** Indexer Service
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Term retrieval
    - [ ] Ngram retrieval
    - [ ] Entity retrieval
    - [ ] Numeric retrieval
    - [ ] ID retrieval
    - [ ] Multi-index fusion

### 3.2 Context Assembly
- [ ] **Task:** Implement context assembly
  - **Dependencies:** Multi-Index Retrieval
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Relevance sorting
    - [ ] Context window management
    - [ ] Deduplication
    - [ ] Coherence enhancement
    - [ ] Citation tracking

### 3.3 Evidence Collection
- [ ] **Task:** Implement evidence collection
  - **Dependencies:** Multi-Index Retrieval, Context Assembly
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Source tracking
    - [ ] Evidence scoring
    - [ ] Citation generation
    - [ ] Evidence validation
    - [ ] Contextual enrichment

---

## 4. LLM Integration Components

### 4.1 Prompt Engineering
- [ ] **Task:** Implement prompt engineering system
  - **Dependencies:** Context Assembly
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Template management
    - [ ] Context injection
    - [ ] Instruction formatting
    - [ ] Few-shot example selection
    - [ ] Parameter optimization

### 4.2 Inference Service
- [ ] **Task:** Implement inference service
  - **Dependencies:** Local LLM Runtime Service, Prompt Engineering
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Request handling
    - [ ] Inference execution
    - [ ] Response formatting
    - [ ] Error handling
    - [ ] Performance monitoring

### 4.3 Output Parsing
- [ ] **Task:** Implement output parsing
  - **Dependencies:** Inference Service
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Structure extraction
    - [ ] Format validation
    - [ ] Fallback handling
    - [ ] Post-processing
    - [ ] Response enrichment

---

## 5. Output Generation

### 5.1 Card Generation
- [ ] **Task:** Implement card generation
  - **Dependencies:** Output Parsing, Evidence Collection
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/AIBackend/vector-db/Inputs/QuestionsAnswers/split_qas/Q104.md
  - **Components:**
    - [ ] Standard term extraction
    - [ ] Plain-English answer generation
    - [ ] Applicability determination
    - [ ] Platform action linking
    - [ ] Follow-up suggestion

### 5.2 Prose Generation
- [ ] **Task:** Implement prose generation
  - **Dependencies:** Output Parsing, Evidence Collection
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/AIBackend/vector-db/Inputs/QuestionsAnswers/split_qas/Q104.md
  - **Components:**
    - [ ] Narrative construction
    - [ ] Citation integration
    - [ ] Coherence enhancement
    - [ ] Tone consistency
    - [ ] Format structuring

### 5.3 Action Generation
- [ ] **Task:** Implement action generation
  - **Dependencies:** Card Generation, Prose Generation
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/AIBackend/vector-db/Inputs/QuestionsAnswers/split_qas/Q104.md
  - **Components:**
    - [ ] Action identification
    - [ ] Workflow linking
    - [ ] Parameter extraction
    - [ ] Action validation
    - [ ] Follow-up integration

---

## 6. Explainability and Evaluation

### 6.1 Explanation Generation
- [ ] **Task:** Implement explanation generation
  - **Dependencies:** Evidence Collection, Output Parsing
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Decision trace creation
    - [ ] Confidence scoring
    - [ ] Source attribution
    - [ ] Reasoning path visualization
    - [ ] Uncertainty identification

### 6.2 Quality Evaluation
- [ ] **Task:** Implement quality evaluation
  - **Dependencies:** Card Generation, Prose Generation
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Accuracy assessment
    - [ ] Completeness checking
    - [ ] Relevance evaluation
    - [ ] Coherence measurement
    - [ ] Citation validity

### 6.3 Feedback Processing
- [ ] **Task:** Implement feedback processing
  - **Dependencies:** Quality Evaluation
  - **Verification Document:** arioncomply-v1/docs/Detailed-Plan.md
  - **Components:**
    - [ ] Feedback collection
    - [ ] Error categorization
    - [ ] Improvement identification
    - [ ] Training data generation
    - [ ] System optimization

---

## 7. Framework-Specific Components

### 7.1 ISO 27001 Module
- [ ] **Task:** Implement ISO 27001 module
  - **Dependencies:** Card Generation, Prose Generation
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/AIBackend/vector-db/Inputs/ConsultingInputDocs/iso_consulting_master.md
  - **Components:**
    - [ ] Control interpretation
    - [ ] Implementation guidance
    - [ ] Evidence requirements
    - [ ] Audit preparation
    - [ ] Gap analysis

### 7.2 GDPR Module
- [ ] **Task:** Implement GDPR module
  - **Dependencies:** Card Generation, Prose Generation
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/AIBackend/vector-db/Inputs/ConsultingInputDocs/gdpr_consulting_master.md
  - **Components:**
    - [ ] Article interpretation
    - [ ] Implementation guidance
    - [ ] Evidence requirements
    - [ ] DPO assistance
    - [ ] Breach response guidance

### 7.3 EU AI Act Module
- [ ] **Task:** Implement EU AI Act module
  - **Dependencies:** Card Generation, Prose Generation
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/AIBackend/vector-db/Inputs/ConsultingInputDocs/eu_ai_act_consulting_master.md
  - **Components:**
    - [ ] Risk classification
    - [ ] Implementation guidance
    - [ ] Documentation requirements
    - [ ] Conformity assessment
    - [ ] Ongoing monitoring

### 7.4 SOC 2 Module
- [ ] **Task:** Implement SOC 2 module
  - **Dependencies:** Card Generation, Prose Generation
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/AIBackend/vector-db/Inputs/ConsultingInputDocs/cloud_security_consulting_master.md
  - **Components:**
    - [ ] TSC interpretation
    - [ ] Implementation guidance
    - [ ] Evidence requirements
    - [ ] Audit preparation
    - [ ] Gap analysis

---

## 8. Advanced Features

### 8.1 Cross-Standard Mapping
- [ ] **Task:** Implement cross-standard mapping
  - **Dependencies:** Framework-Specific Modules
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/AIBackend/vector-db/Inputs/QuestionsAnswers/Q1to160.md
  - **Components:**
    - [ ] Control mapping
    - [ ] Requirement alignment
    - [ ] Evidence sharing
    - [ ] Gap identification
    - [ ] Efficiency optimization

### 8.2 Recommendation Engine
- [ ] **Task:** Implement recommendation engine
  - **Dependencies:** Framework-Specific Modules
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/AIBackend/vector-db/Inputs/QuestionsAnswers/split_qas/Q207.md
  - **Components:**
    - [ ] Control prioritization
    - [ ] Implementation sequencing
    - [ ] Resource allocation
    - [ ] Efficiency optimization
    - [ ] Risk-based suggestions

### 8.3 Compliance Forecasting
- [ ] **Task:** Implement compliance forecasting
  - **Dependencies:** Recommendation Engine
  - **Verification Document:** arioncomply-v1/docs/ArionComplyDesign/DesignGuidanceAndPrinciples/AIBackend/vector-db/Inputs/QuestionsAnswers/split_qas/Q207.md
  - **Components:**
    - [ ] Progress projection
    - [ ] Timeline estimation
    - [ ] Resource forecasting
    - [ ] Risk prediction
    - [ ] Compliance scoring
