id: Q222
query: >-
  What metrics should we track to measure compliance program success?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/9.1"
overlap_ids: []
capability_tags:
  - "Report"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Monitoring & Measurement"
    id: "ISO27001:2022/9.1"
    locator: "Clause 9.1"
ui:
  cards_hint:
    - "KPIs catalog"
  actions:
    - type: "open_register"
      target: "kpi_library"
      label: "View KPIs"
output_mode: "both"
graph_required: false
notes: "Combine quantitative and qualitative KPIs"
---
### 222) What metrics should we track to measure compliance program success?

**Standard terms)**  
- **Monitoring & measurement (Cl.9.1):** defines performance indicators.

**Plain-English answer**  
Key KPIs include: number of open nonconformities, average time to close corrective actions, percentage of controls tested, audit finding severity, training completion rates, and breach-prevention effectiveness.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 9.1

**Why it matters**  
Balanced metrics show program health and areas needing attention.

**Do next in our platform**  
- Browse **KPI Library**.  
- Select and configure target thresholds.

**How our platform will help**  
- **[Report]** Pre-built KPI dashboards.  
- **[Reminder]** Alerts when KPIs fall below targets.

**Likely follow-ups**  
- How do we benchmark our KPIs against industry averages?

**Sources**  
- ISO/IEC 27001:2022 Clause 9.1
