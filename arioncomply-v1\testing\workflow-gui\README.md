<!-- File: arioncomply-v1/testing/workflow-gui/README.md -->
# ArionComply Workflow GUI (Parallel Test Harness)

Purpose

- Standalone test GUI to exercise production-style endpoints without touching the existing LLM comparison UI.
- Supports starting/sending conversations via Edge Functions, streaming via SSE, and ad-hoc provider comparisons via the existing `compliance-proxy`.

Contract Alignment
- This GUI targets the production `compliance-proxy` unified contract. It works with both the simple body (`{ user_query }`) and the rich body (`{ provider?, messages, systemPrompt?, parameters? }`).

Features (MVP)

- Configure `supabaseUrl` and optional `anonKey` from the page.
- Conversation Mode: `ai-conversation-start` and `ai-conversation-send`.
- Streaming Mode: `ai-conversation-stream` (SSE parsing in-browser).
- Compare Mode: Call `compliance-proxy` for OpenAI/Anthropic with basic parameters.
- Headers: Send optional `x-provider-order`, `x-allow-gllm`, `compliance-chain-id`, `decision-context`.
- Trace: Shows `requestId` (from JSON) and `traceparent` (response header).

Enhanced UI/UX

- Arena Compare: Blind A/B evaluation inspired by LLM Arena
  - Randomized provider mapping to Model A/B
  - Vote A / Vote B / Tie, with per-provider tallies
  - Reveal actual provider mapping on demand
  - Copy-to-clipboard for each output
- Dark Mode: Toggle theme with persisted preference
- Quality-of-life: Copy helpers, compact cards, improved layout

Run Locally

- Start server and open browser automatically:
  - `bash testing/workflow-gui/start-server.sh` (serves on [`http://localhost:10000`](http://localhost:10000))
  - Stop with `bash testing/workflow-gui/stop-server.sh`
- Or manually:
  - `cd testing/workflow-gui && python3 -m http.server 10000`
  - Open [`http://localhost:10000`](http://localhost:10000) in your browser
  - Set `Supabase URL` (e.g., [`http://localhost:54321`](http://localhost:54321)) and `Anon Key` if needed.

Message/Data Flows
- Conversation Start (`ai-conversation-start`)
  - Request: POST `${supabaseUrl}/functions/v1/ai-conversation-start` with `{ title?, context? }`.
  - Edge: Validates, logs request/events, returns `{ session: {...}, firstMessage: {...} }` (camelCase).
  - Data Path: Browser → Supabase Edge → (DB later) → Browser.

- Conversation Send (`ai-conversation-send`)
  - Request: POST `${supabaseUrl}/functions/v1/ai-conversation-send` with `{ sessionId, message }`.
  - Edge: Validates, logs, forwards to assistant router.
  - Router: If configured, forwards to AI backend (`AI_BACKEND_URL`) with `{ messages, hints, explainability }`.
  - Response: `{ userMessage, assistantMessage { contentText, suggestions[] } }`.
  - Data Path: Browser → Edge → (Assistant Router → AI Backend → Retrieval/Model) → Edge → Browser.

- Conversation Stream (`ai-conversation-stream`)
  - Request: POST `${supabaseUrl}/functions/v1/ai-conversation-stream` with `{ sessionId, message }`.
  - Edge: Sends SSE events `start`, repeated `token` chunks, then `end`.
  - Data Path: Browser (SSE) ↔ Edge (stream) → Browser updates progressively.

- Provider Compare via `compliance-proxy` (Rich Body)
  - Request (single provider): POST `${supabaseUrl}/functions/v1/compliance-proxy` with `{ provider: 'openai'|'claude', messages: [...], systemPrompt?, parameters? }`.
  - Proxy: Calls provider directly using API keys; returns `{ content, model?, usage? }`.
  - Request (both providers): `{ provider: 'both', ... }` is supported by proxy and returns `{ openai: {...}, claude: {...} }`.
  - Note: This GUI typically issues two separate calls (one per provider) for side-by-side rendering.

- Provider Compare via `compliance-proxy` (Simple Body)
  - Request: `{ user_query: '...' }`.
  - Proxy: If `AI_BACKEND_URL` is set, forwards to AI backend and normalizes to `{ content, model?, usage? }`. Otherwise echoes last user text (dev fallback).

Headers & Auth
- Authorization: Sends `Authorization: Bearer <anonKey>` from the UI when provided.
- Optional Hints: You can set headers like `x-provider-order`, `x-allow-gllm`, `compliance-chain-id`, `decision-context` for routing/explainability.

Endpoints Used

- `${supabaseUrl}/functions/v1/ai-conversation-start`
- `${supabaseUrl}/functions/v1/ai-conversation-send`
- `${supabaseUrl}/functions/v1/ai-conversation-stream`
- `${supabaseUrl}/functions/v1/compliance-proxy`

Notes

- This UI is intentionally separate to avoid breaking `testing/llm-comparison`.
- Document generation hooks can be added later once the `document-generation` function is implemented.

Arena Compare How-To

1. Set `Supabase URL` (+ `Anon Key` if required) at the top.
2. In the Arena card, enter an optional System Prompt and a Question.
3. Click `Run Arena` to fetch both providers via `compliance-proxy` with randomized A/B mapping.
4. Review Model A and Model B outputs; vote `A`, `B`, or `Tie / Skip`.
5. Click `Reveal Models` to see which provider was A vs B.
6. Stats (A/B/tie and per-provider wins) are persisted in localStorage.

Troubleshooting

- Ensure your Supabase Edge functions are running and reachable.
- If requests fail, check CORS and that `compliance-proxy` is deployed.
- The Arena uses the same `compliance-proxy` endpoint as the basic compare.
<!-- File: arioncomply-v1/testing/workflow-gui/README.md -->
