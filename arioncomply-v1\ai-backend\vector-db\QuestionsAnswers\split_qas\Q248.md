id: Q248
query: >-
  What if incidents reveal fundamental problems with our compliance approach?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/6.1"
overlap_ids:
  - "ISO27001:2022/9.3"
capability_tags:
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Risk Assessment & Treatment"
    id: "ISO27001:2022/6.1"
    locator: "Clause 6.1"
  - title: "ISO/IEC 27001:2022 — Management Review"
    id: "ISO27001:2022/9.3"
    locator: "Clause 9.3"
ui:
  cards_hint:
    - "Program overhaul plan"
  actions:
    - type: "start_workflow"
      target: "program_overhaul"
      label: "Initiate Overhaul"
output_mode: "both"
graph_required: false
notes: "Escalate to leadership review and possibly re-baseline scope and objectives"
---
### 248) What if incidents reveal fundamental problems with our compliance approach?

**Standard terms)**  
- **Risk assessment (Cl.6.1):** reevaluate threats and controls.  
- **Management review (Cl.9.3):** top-level program evaluation.

**Plain-English answer**  
If root causes point to gaps in your overall ISMS design, invoke a **Program Overhaul**: re-perform your context analysis, update scope and objectives, engage leadership in a fresh management review, and rebaseline your risk treatment plan.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clauses 6.1, 9.3

**Why it matters**  
Prevents wasting effort on ineffective controls and aligns the program with current realities.

**Do next in our platform**  
- Launch the **Program Overhaul** workflow.  
- Schedule a full **Management Review** session.

**How our platform will help**  
- **[Workflow]** Step-by-step context and scope redefinition.  
- **[Report]** Comparative analysis of old vs new baseline metrics.

**Likely follow-ups**  
- How long will a program overhaul typically take?

**Sources**  
- ISO/IEC 27001:2022 Clauses 6.1 and 9.3
