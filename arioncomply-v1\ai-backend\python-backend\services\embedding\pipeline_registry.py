"""
# File: arioncomply-v1/ai-backend/python-backend/services/embedding/pipeline_registry.py
# File Description: Registry for embedding pipelines with health monitoring and selection
# Purpose: Register pipelines, track health, select/fallback, and expose current pipeline
"""
"""
Pipeline registry and selection system for multi-pipeline embedding architecture.
"""

import asyncio
import json
import logging
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass, asdict

from .pipeline_interface import (
    EmbeddingPipeline, 
    PipelineStatus, 
    HealthCheckResult,
    QualityTier
)

logger = logging.getLogger(__name__)


class SelectionStrategy(Enum):
    """Pipeline selection strategies."""
    QUALITY_FIRST = "quality_first"
    SECURITY_FIRST = "security_first" 
    PERFORMANCE_FIRST = "performance_first"
    MANUAL = "manual"


@dataclass
class PipelineRegistryEntry:
    """Registry entry for a pipeline."""
    name: str
    class_path: str
    enabled: bool
    priority: int
    config: Dict
    
    # Runtime status
    status: PipelineStatus = PipelineStatus.NOT_LOADED
    last_health_check: Optional[datetime] = None
    consecutive_failures: int = 0
    
    # Performance metrics
    avg_latency_ms: Optional[float] = None
    success_rate: Optional[float] = None


@dataclass
class RegistryConfig:
    """Configuration for the pipeline registry."""
    default_pipeline: str
    selection_strategy: SelectionStrategy
    auto_fallback: bool = True
    health_check_interval_seconds: int = 300
    max_consecutive_failures: int = 3
    fallback_order: List[str] = None
    
    def __post_init__(self):
        """Ensure fallback_order is initialized to an empty list."""
        if self.fallback_order is None:
            self.fallback_order = []


class PipelineRegistry:
    """
    Central registry for managing multiple embedding pipelines.
    
    Provides pipeline registration, health monitoring, automatic selection,
    and fallback capabilities with complete audit trail.
    """
    
    def __init__(self, config_path: Optional[Path] = None):
        """Initialize registry structures and optional config path."""
        self.config_path = config_path
        self.pipelines: Dict[str, EmbeddingPipeline] = {}
        self.registry: Dict[str, PipelineRegistryEntry] = {}
        self.config: Optional[RegistryConfig] = None
        self.current_pipeline_name: Optional[str] = None
        self._health_check_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
    
    async def initialize(self, config: Dict) -> None:
        """Initialize the registry with configuration."""
        self.config = self._parse_config(config)
        await self._load_pipeline_definitions(config.get("pipelines", {}))
        
        # Start health monitoring
        if self.config.health_check_interval_seconds > 0:
            self._health_check_task = asyncio.create_task(self._health_monitor_loop())
        
        logger.info(f"Pipeline registry initialized with {len(self.registry)} pipelines")
    
    def _parse_config(self, config: Dict) -> RegistryConfig:
        """Parse configuration into RegistryConfig object."""
        strategy_str = config.get("selection_strategy", "quality_first")
        try:
            strategy = SelectionStrategy(strategy_str)
        except ValueError:
            logger.warning(f"Unknown selection strategy '{strategy_str}', using quality_first")
            strategy = SelectionStrategy.QUALITY_FIRST
        
        return RegistryConfig(
            default_pipeline=config.get("default_pipeline"),
            selection_strategy=strategy,
            auto_fallback=config.get("auto_fallback", True),
            health_check_interval_seconds=config.get("health_check_interval", 300),
            max_consecutive_failures=config.get("max_consecutive_failures", 3),
            fallback_order=config.get("fallback_order", [])
        )
    
    async def _load_pipeline_definitions(self, pipelines_config: Dict) -> None:
        """Load pipeline definitions from configuration."""
        self.registry.clear()
        
        for name, pipeline_config in pipelines_config.items():
            if not pipeline_config.get("enabled", True):
                logger.info(f"Pipeline '{name}' is disabled, skipping")
                continue
            
            entry = PipelineRegistryEntry(
                name=name,
                class_path=pipeline_config["class_path"],
                enabled=pipeline_config.get("enabled", True),
                priority=pipeline_config.get("priority", 100),
                config=pipeline_config.get("config", {})
            )
            
            self.registry[name] = entry
            logger.info(f"Registered pipeline: {name}")
    
    async def register_pipeline(self, pipeline: EmbeddingPipeline) -> None:
        """Register a pipeline instance."""
        async with self._lock:
            self.pipelines[pipeline.name] = pipeline
            
            # Update registry entry if it exists
            if pipeline.name in self.registry:
                self.registry[pipeline.name].status = PipelineStatus.NOT_LOADED
            
            logger.info(f"Pipeline instance registered: {pipeline.name}")
    
    async def get_pipeline(self, name: Optional[str] = None) -> Optional[EmbeddingPipeline]:
        """Get a pipeline by name or the current active pipeline."""
        if name is None:
            name = self.current_pipeline_name
        
        if name is None:
            return None
        
        return self.pipelines.get(name)
    
    async def select_best_pipeline(self) -> Optional[str]:
        """Select the best available pipeline based on strategy."""
        if not self.config:
            return None
        
        available_pipelines = await self._get_healthy_pipelines()
        if not available_pipelines:
            logger.warning("No healthy pipelines available")
            return None
        
        if self.config.selection_strategy == SelectionStrategy.QUALITY_FIRST:
            return self._select_by_quality(available_pipelines)
        elif self.config.selection_strategy == SelectionStrategy.SECURITY_FIRST:
            return self._select_by_security(available_pipelines)
        elif self.config.selection_strategy == SelectionStrategy.PERFORMANCE_FIRST:
            return self._select_by_performance(available_pipelines)
        else:  # MANUAL
            return self.config.default_pipeline if self.config.default_pipeline in available_pipelines else None
    
    async def _get_healthy_pipelines(self) -> Set[str]:
        """Get set of healthy pipeline names."""
        healthy = set()
        
        for name, entry in self.registry.items():
            if (entry.enabled and 
                entry.status == PipelineStatus.HEALTHY and
                entry.consecutive_failures < self.config.max_consecutive_failures):
                healthy.add(name)
        
        return healthy
    
    def _select_by_quality(self, available: Set[str]) -> Optional[str]:
        """Select pipeline prioritizing quality."""
        quality_order = [
            ("bge-large-onnx", QualityTier.SOTA),
            ("openai", QualityTier.HIGH),
            ("all-mpnet-base-v2", QualityTier.GOOD),
            ("placeholder", QualityTier.TESTING_ONLY)
        ]
        
        for pipeline_name, _ in quality_order:
            if pipeline_name in available:
                return pipeline_name
        
        # Fallback to any available
        return next(iter(available)) if available else None
    
    def _select_by_security(self, available: Set[str]) -> Optional[str]:
        """Select pipeline prioritizing security (local-only)."""
        local_priority = ["bge-large-onnx", "all-mpnet-base-v2", "placeholder"]
        
        for pipeline_name in local_priority:
            if pipeline_name in available:
                return pipeline_name
        
        return next(iter(available)) if available else None
    
    def _select_by_performance(self, available: Set[str]) -> Optional[str]:
        """Select pipeline prioritizing performance."""
        performance_order = ["placeholder", "bge-large-onnx", "all-mpnet-base-v2", "openai"]
        
        for pipeline_name in performance_order:
            if pipeline_name in available:
                return pipeline_name
        
        return next(iter(available)) if available else None
    
    async def set_current_pipeline(self, name: str) -> bool:
        """Set the current active pipeline."""
        if name not in self.pipelines:
            logger.error(f"Pipeline '{name}' not found in registry")
            return False
        
        pipeline = self.pipelines[name]
        
        # Ensure pipeline is loaded
        if not pipeline.is_loaded:
            try:
                await pipeline.load()
            except Exception as e:
                logger.error(f"Failed to load pipeline '{name}': {e}")
                return False
        
        # Health check
        health_result = await pipeline.health_check()
        if health_result.status != PipelineStatus.HEALTHY:
            logger.warning(f"Pipeline '{name}' health check failed: {health_result.error_message}")
            return False
        
        self.current_pipeline_name = name
        logger.info(f"Current pipeline set to: {name}")
        return True
    
    async def attempt_fallback(self) -> bool:
        """Attempt to fallback to next available pipeline."""
        if not self.config or not self.config.auto_fallback:
            return False
        
        current = self.current_pipeline_name
        logger.warning(f"Attempting fallback from failed pipeline: {current}")
        
        # Try fallback order first
        for fallback_name in self.config.fallback_order:
            if fallback_name != current and await self.set_current_pipeline(fallback_name):
                logger.info(f"Successful fallback to: {fallback_name}")
                return True
        
        # Try any healthy pipeline
        best_pipeline = await self.select_best_pipeline()
        if best_pipeline and best_pipeline != current:
            if await self.set_current_pipeline(best_pipeline):
                logger.info(f"Successful fallback to: {best_pipeline}")
                return True
        
        logger.error("All fallback attempts failed")
        return False
    
    async def get_system_status(self) -> Dict:
        """Get comprehensive system status."""
        status = {
            "active_pipeline": self.current_pipeline_name,
            "total_pipelines": len(self.registry),
            "healthy_pipelines": len(await self._get_healthy_pipelines()),
            "config": asdict(self.config) if self.config else None,
            "pipelines": {}
        }
        
        for name, entry in self.registry.items():
            pipeline_status = {
                "enabled": entry.enabled,
                "status": entry.status.value,
                "priority": entry.priority,
                "consecutive_failures": entry.consecutive_failures,
                "last_health_check": entry.last_health_check.isoformat() if entry.last_health_check else None
            }
            
            # Add performance metrics if available
            if entry.avg_latency_ms is not None:
                pipeline_status["avg_latency_ms"] = entry.avg_latency_ms
            if entry.success_rate is not None:
                pipeline_status["success_rate"] = entry.success_rate
            
            # Add pipeline metadata if loaded
            if name in self.pipelines:
                pipeline = self.pipelines[name]
                pipeline_status["metadata"] = asdict(pipeline.metadata) if hasattr(pipeline, 'metadata') else None
            
            status["pipelines"][name] = pipeline_status
        
        return status
    
    async def _health_monitor_loop(self) -> None:
        """Background health monitoring loop."""
        while True:
            try:
                await asyncio.sleep(self.config.health_check_interval_seconds)
                await self._perform_health_checks()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health monitor error: {e}")
    
    async def _perform_health_checks(self) -> None:
        """Perform health checks on all registered pipelines."""
        for name, entry in self.registry.items():
            if not entry.enabled:
                continue
            
            if name not in self.pipelines:
                continue
            
            try:
                pipeline = self.pipelines[name]
                health_result = await pipeline.health_check()
                
                # Update registry entry
                entry.status = health_result.status
                entry.last_health_check = health_result.timestamp
                
                if health_result.status == PipelineStatus.HEALTHY:
                    entry.consecutive_failures = 0
                else:
                    entry.consecutive_failures += 1
                    logger.warning(f"Pipeline '{name}' health check failed: {health_result.error_message}")
                
                # Update performance metrics
                if health_result.test_inference_time_ms:
                    entry.avg_latency_ms = health_result.test_inference_time_ms
                
            except Exception as e:
                logger.error(f"Health check error for pipeline '{name}': {e}")
                entry.consecutive_failures += 1
                entry.status = PipelineStatus.FAILED
    
    async def shutdown(self) -> None:
        """Shutdown the registry and cleanup resources."""
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # Unload all pipelines
        for pipeline in self.pipelines.values():
            try:
                await pipeline.unload()
            except Exception as e:
                logger.error(f"Error unloading pipeline '{pipeline.name}': {e}")
        
        self.pipelines.clear()
        self.registry.clear()
        logger.info("Pipeline registry shutdown complete")
