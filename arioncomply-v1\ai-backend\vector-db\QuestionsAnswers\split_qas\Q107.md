id: Q107
query: >-
  What if compliance requirements conflict with customer expectations?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/4.1"
  - "GDPR:2016/Art.6"
overlap_ids: []
capability_tags:
  - "Report"
  - "Workflow"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Context of the Organization"
    id: "ISO27001:2022/4.1"
    locator: "Clause 4.1"
  - title: "GDPR — Lawfulness of Processing"
    id: "GDPR:2016/Art.6"
    locator: "Article 6"
ui:
  cards_hint:
    - "Conflict resolution log"
  actions:
    - type: "open_register"
      target: "requirement_conflicts"
      label: "Record Conflicts"
    - type: "start_workflow"
      target: "conflict_resolution"
      label: "Resolve Conflicts"
output_mode: "both"
graph_required: false
notes: "Use risk-based analysis and document decisions for audit"
---
### 107) What if compliance requirements conflict with customer expectations?

**Standard terms**  
- **Context (Cl. 4.1):** internal/external issues.  
- **Lawfulness (GDPR Art. 6):** bases for processing.

**Plain-English answer**  
Log the conflict, assess legal and business risk, then decide whether to adapt process, seek consent waivers, or provide alternative services. Document decisions and communicate trade-offs transparently.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 4.1; GDPR Article 6

**Why it matters**  
Demonstrates reasoned, audit-ready decision-making.

**Do next in our platform**  
- Record the issue in **Conflicts Register**.  
- Run **Conflict Resolution** workflow.

**How our platform will help**  
- **[Register]** Tracks conflict details and resolution status.  
- **[Workflow]** Guides risk assessment and stakeholder approval steps.

**Likely follow-ups**  
- “Can we document customer waivers?” (Yes—platform supports waiver records)

**Sources**  
- ISO/IEC 27001:2022 Clause 4.1; GDPR Article 6
