<!-- File: arioncomply-v1/docs/AI-Backend-Design/Message_Flow_Backend_Edge.md -->
<!--
File Description: End-to-end message flow between Supabase Edge functions and the Python backend
Purpose: Help engineers trace how a chat request enters the system, which files run, what gets logged, and how the response returns
Inputs: HTTP requests to Edge endpoints (conversation start/send/stream), Authorization bearer JW<PERSON>, optional headers (x-provider-order, x-allow-gllm, compliance-chain-id, decision-context)
Outputs: JSON envelopes to the browser (session, userMessage, assistantMessage, suggestions), SSE token stream for streaming
Dependencies: supabase/functions/* endpoints, _shared/logger.ts (request/event logs), _shared/assistant_router.ts (backend forwarder), backend app/main.py and services/router.py, vector RPC
Security/RLS: Logging tables are org-scoped with RLS; logger enforces org presence; traceparent propagated for observability
Notes: Diagrams reflect current repository structure (MVP stubs in places); suggestions default to educational/platform guidance when none are derived
-->

# Backend ↔ Edge Message Flow

This document shows how chat messages travel through the Edge functions and the Python backend, which files participate at each step, and what is logged for traceability. Diagrams focus on concrete files in this repository.

## Conversation Send (Full Path)

```mermaid
sequenceDiagram
    autonumber
    participant B as Browser (Workflow GUI)
    participant E as Edge ai-conversation-send (index.ts)
    participant L as Edge Logger (logger.ts)
    participant R as Edge Assistant Router (assistant_router.ts)
    participant F as FastAPI /ai/chat (app/main.py)
    participant S as Backend Router (services/router.py)
    participant V as Vector Retrieval (retrieval/supabase_vector.py)
    participant P as Prompt Registry (prompts/registry.py)
    participant BL as Backend Logger (services/logging/events.py)
    participant DB as App DB Logs (api_request_logs/api_event_logs)
    participant VD as Vector DB (RPC match_chunks)

    B->>E: POST /functions/v1/ai-conversation-send {sessionId,message}
    Note over E: supabase/functions/ai-conversation-send/index.ts
    E->>L: getClientMeta() + logRequestStart()
    Note over L: _shared/logger.ts (writes api_request_logs)
    E->>R: assistantRouter({messages, sessionId, headers})
    Note over R: _shared/assistant_router.ts (forwards to backend)

    R->>F: POST /ai/chat {messages, hints, explainability}
    Note over F: ai-backend/python-backend/app/main.py ai_chat()
    F->>S: handle_chat(messages, meta, hints, explainability)
    Note over S: services/router.py

    S->>BL: log_event(deterministic_preprocessing, details)
    alt Deterministic match
      S-->>F: deterministic response {text, suggestions?}
    else No deterministic match
      S->>S: QueryPreprocessor.preprocess_query(...)
      S->>V: supabase_vector.search(org_id, qvec, k)
      Note over V: retrieval/supabase_vector.py (calls VD RPC)
      V-->>S: rows (citations, actions)
      S->>BL: log_event(retrieval_run, details)
      S->>P: compile_messages(template_id="qna", ...)
      Note over P: prompts/registry.py
      S->>S: simulate LLM (stub) _simple_echo()
      S->>BL: log_event(ai_call, details)
      S->>BL: log_event(deterministic_rules_applied, details)
      S-->>F: {provider, model, text, suggestions}
    end

    F-->>R: {provider, model, text, suggestions}
    R-->>E: ChatResponse {provider, model, text}
    E->>L: logEvent(user_message_received)
    E->>E: Build payload (user_message, assistant_message, suggestions)
    E->>L: logEvent(assistant_message_generated)
    E->>L: logEvent(response_sent) + logRequestEnd()
    Note over L: _shared/logger.ts (writes api_event_logs)
    E-->>B: 200 { userMessage, assistantMessage, suggestions } (+traceparent)
```

## Conversation Start (Stub Path)

```mermaid
sequenceDiagram
    autonumber
    participant B as Browser
    participant E as Edge ai-conversation-start (index.ts)
    participant L as Edge Logger (logger.ts)
    participant DB as App DB Logs

    B->>E: POST /functions/v1/ai-conversation-start {title?, context?}
    Note over E: supabase/functions/ai-conversation-start/index.ts
    E->>L: getClientMeta() + logRequestStart()
    E->>L: logEvent(session_initialized)
    E->>L: logEvent(response_sent) + logRequestEnd()
    E-->>B: 200 { session, firstMessage } (+traceparent)
```

## Conversation Stream (SSE)

```mermaid
sequenceDiagram
    autonumber
    participant B as Browser (SSE client)
    participant E as Edge ai-conversation-stream (index.ts)
    participant L as Edge Logger (logger.ts)

    B->>E: POST /functions/v1/ai-conversation-stream {sessionId,message}
    Note over E: supabase/functions/ai-conversation-stream/index.ts
    E->>L: getClientMeta() + logRequestStart()
    E->>L: logEvent(stream_request_received)
    E->>L: logEvent(stream_started) + logRequestEnd(200)
    E-->>B: SSE start
    loop streaming
      E-->>B: SSE token { text }
    end
    E-->>B: SSE end
    E->>L: logEvent(stream_finished)
```

## File Map (by role)

- Edge entrypoints
  - `supabase/functions/ai-conversation-start/index.ts`
  - `supabase/functions/ai-conversation-send/index.ts`
  - `supabase/functions/ai-conversation-stream/index.ts`
- Edge shared modules
  - `supabase/functions/_shared/logger.ts` (request/event logging, W3C traceparent)
  - `supabase/functions/_shared/assistant_router.ts` (forward to backend)
  - `supabase/functions/_shared/{utils.ts,errors.ts,jwt.ts,trace.ts}`
- Backend API and orchestration
  - `ai-backend/python-backend/app/main.py` (FastAPI `/ai/chat`)
  - `ai-backend/python-backend/services/router.py` (preprocess → retrieve → prompt → stub LLM)
- Backend helpers
  - `services/preprocessing/query_preprocessor.py`, `graph_crawler.py`
  - `services/retrieval/supabase_vector.py` (RPC `match_chunks`)
  - `services/prompts/registry.py` (compile_messages/template_meta)
  - `services/logging/events.py` (logs to `api_event_logs`)
- Databases
  - Logs (App DB): `db/migrations/0011, 0012, 0014`
  - Vector DB (RPC): separate Supabase project with `match_chunks`

## Notes

- Suggestions: Edge and Backend can supply up to 4 educational/platform-oriented suggestions when none are derived from retrieval.
- Traceability: All requests include `request_id` and W3C `traceparent`; logs carry `org_id`, `session_id`, and `trace_id` where applicable.

### Viewing diagrams
- Mermaid is supported by many Markdown renderers; if your IDE doesn’t render it inline, paste the code blocks into a Mermaid live editor.
- Keep this file close to code changes; update file references when endpoints or modules move.
