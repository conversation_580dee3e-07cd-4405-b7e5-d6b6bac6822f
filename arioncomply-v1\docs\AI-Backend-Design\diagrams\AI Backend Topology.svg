<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentScriptType="application/ecmascript" contentStyleType="text/css" height="752px" preserveAspectRatio="none" style="width:1758px;height:752px;" version="1.1" viewBox="0 0 1758 752" width="1758px" zoomAndPan="magnify"><defs/><g><text fill="#000000" font-family="sans-serif" font-size="18" lengthAdjust="spacingAndGlyphs" textLength="187" x="789" y="16.708">AI Backend Topology</text><!--MD5=[98e1fd96dbbd6bd713eb95259eee3258]
cluster SUPA--><path d="M90.108,478.8017 C97.472,466.4227 109.2562,467.8141 116.8855,478.4936 C127.2829,464.6467 136.7225,463.9504 147.3349,478.2611 C153.2848,463.8252 167.3345,462.3495 177.2789,473.6054 C184.3098,459.8004 196.9794,462.0112 202.9945,474.5953 C209.7889,459.4146 221.0572,457.3688 232.3695,469.7867 C242.5953,459.1912 253.1955,459.0687 260.9778,472.6751 C268.5015,460.4837 280.3263,458.5726 289.6996,470.4696 C297.5938,455.2936 313.704,450.8232 323.4062,468.6214 C332.679,455.5579 343.0257,455.8234 351.3326,469.6254 C360.6211,458.8266 372.488,458.8681 379.3211,472.4447 C388.0925,462.4102 399.3085,460.6052 405.3798,475.291 C413.5937,462.0908 424.9903,459.4249 434.2958,474.161 C444.3625,459.8196 459.7379,459.252 467.9532,476.1555 C481.6232,465.688 492.8235,466.8826 499,484.4531 C502.5301,483.5796 503.9976,485.6232 503.2128,488.8675 C513.8348,497.64 513.68,507.4153 502.0244,515.1816 C519.2784,521.9649 519.9658,537.104 504.7308,546.6789 C516.5484,555.8874 517.2254,567.4437 503.0735,575.14 C515.9014,581.968 515.2741,591.7046 503.2933,598.7634 C519.2902,605.8826 520.2227,616.7391 506.0011,626.7791 C519.5175,637.3135 515.3087,650.8716 501.4432,657.3194 C515.7385,666.1555 519.0704,676.5406 503.7423,687.6241 C515.2777,702.8708 510.2307,714.8629 492,718.9531 C495.5966,718.3025 497.8247,719.2472 497.1383,723.4695 C486.2455,738.2637 476.1324,735.5889 467.8769,720.8559 C460.5446,736.3643 446.4735,734.3448 439.2583,720.7409 C432.2062,738.5328 416.939,740.1113 406.6479,723.842 C398.379,736.6479 383.1165,738.8077 376.2351,722.3977 C368.3885,734.3729 357.2311,737.3448 348.6802,723.1389 C341.4574,738.0175 330.1288,735.2816 321.934,724.138 C311.0815,736.7366 301.1995,737.027 290.5953,723.8363 C281.0953,737.1405 270.176,736.4792 262.7032,721.8147 C256.9639,736.4228 243.5695,738.429 234.4247,725.2847 C224.9424,740.1331 213.1489,740.0637 205.3485,723.7171 C195.5025,738.2824 184.5492,735.0825 175.5124,722.8173 C166.4606,735.6572 155.2853,736.7793 145.4251,723.7577 C137.8651,734.1227 124.4568,736.1001 119.0949,721.4445 C103.1233,740.4496 85.3468,733.607 79,711.9531 C80.9197,712.3151 81.5533,713.7258 80.4904,715.3837 C66.5838,707.959 61.9381,696.6045 76.7541,685.5792 C64.3261,677.4454 63.9345,666.9644 74.7439,657.1854 C61.0303,649.529 63.741,637.5837 75.0007,630.1645 C61.9561,622.7958 58.6665,610.618 71.4847,600.2132 C59.0326,589.3942 57.8621,576.9011 75.4305,570.003 C63.8595,562.4275 65.9287,550.7716 77.1233,544.7204 C63.3383,534.5311 65.7602,520.7155 81.316,514.6119 C65.005,501.7618 67.7962,485.9526 86,477.4531 C87.7877,475.7284 89.7261,476.2441 90.108,478.8017 " fill="#FFFFFF" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="77" x="250.5" y="495.4482">Supabase</text><!--MD5=[b4e09d266be9964199f2622adcc056fa]
cluster Backend--><polygon fill="#FFFFFF" points="649,54.9531,659,44.9531,833,44.9531,833,399.9531,823,409.9531,649,409.9531,649,54.9531" style="stroke: #000000; stroke-width: 1.5;"/><line style="stroke: #000000; stroke-width: 1.5;" x1="823" x2="832" y1="54.9531" y2="45.9531"/><line style="stroke: #000000; stroke-width: 1.5;" x1="649" x2="823" y1="54.9531" y2="54.9531"/><line style="stroke: #000000; stroke-width: 1.5;" x1="823" x2="823" y1="54.9531" y2="409.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="87" x="693.5" y="70.9482">AI Backend</text><!--MD5=[1ca2c587574be9e3b4ae36f3065bb2c9]
cluster LLMs--><polygon fill="#FFFFFF" points="1164,479.9531,1174,469.9531,1457,469.9531,1457,567.9531,1447,577.9531,1164,577.9531,1164,479.9531" style="stroke: #000000; stroke-width: 1.5;"/><line style="stroke: #000000; stroke-width: 1.5;" x1="1447" x2="1456" y1="479.9531" y2="470.9531"/><line style="stroke: #000000; stroke-width: 1.5;" x1="1164" x2="1447" y1="479.9531" y2="479.9531"/><line style="stroke: #000000; stroke-width: 1.5;" x1="1447" x2="1447" y1="479.9531" y2="577.9531"/><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="86" x="1263.5" y="495.9482">Local LLMs</text><!--MD5=[26f6ee68a4ff6eb1ae6bf4da619ad5f5]
entity Edge--><polygon fill="#F8F8F8" points="87,514.4531,97,504.4531,235,504.4531,235,557.0469,225,567.0469,87,567.0469,87,514.4531" style="stroke: #000000; stroke-width: 1.5;"/><line style="stroke: #000000; stroke-width: 1.5;" x1="225" x2="234" y1="514.4531" y2="505.4531"/><line style="stroke: #000000; stroke-width: 1.5;" x1="87" x2="225" y1="514.4531" y2="514.4531"/><line style="stroke: #000000; stroke-width: 1.5;" x1="225" x2="225" y1="514.4531" y2="567.0469"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="108" x="102" y="537.4482">Edge Functions</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="48" x="102" y="553.7451">(Deno)</text><!--MD5=[a413ddb1d19a8eac209fe681c5bcae91]
entity APPDB--><path d="M409,658.9531 C409,648.9531 450,648.9531 450,648.9531 C450,648.9531 491,648.9531 491,658.9531 L491,700.5469 C491,710.5469 450,710.5469 450,710.5469 C450,710.5469 409,710.5469 409,700.5469 L409,658.9531 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M409,658.9531 C409,668.9531 450,668.9531 450,668.9531 C450,668.9531 491,668.9531 491,658.9531 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="62" x="419" y="685.9482">Postgres</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="62" x="419" y="702.2451">(App DB)</text><!--MD5=[f6b3c4ec609d704a8f29c3992fcc8101]
entity PG--><path d="M270.5,514.9531 C270.5,504.9531 316,504.9531 316,504.9531 C316,504.9531 361.5,504.9531 361.5,514.9531 L361.5,556.5469 C361.5,566.5469 316,566.5469 316,566.5469 C316,566.5469 270.5,566.5469 270.5,556.5469 L270.5,514.9531 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M270.5,514.9531 C270.5,524.9531 316,524.9531 316,524.9531 C316,524.9531 361.5,524.9531 361.5,514.9531 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="62" x="280.5" y="541.9482">Postgres</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="71" x="280.5" y="558.2451">(pgvector)</text><!--MD5=[cf25d34b6f9695ab689953484c0b6ae1]
entity Storage--><polygon fill="#F8F8F8" points="397,522.9531,407,512.9531,491,512.9531,491,549.25,481,559.25,397,559.25,397,522.9531" style="stroke: #000000; stroke-width: 1.5;"/><line style="stroke: #000000; stroke-width: 1.5;" x1="481" x2="490" y1="522.9531" y2="513.9531"/><line style="stroke: #000000; stroke-width: 1.5;" x1="397" x2="481" y1="522.9531" y2="522.9531"/><line style="stroke: #000000; stroke-width: 1.5;" x1="481" x2="481" y1="522.9531" y2="559.25"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="54" x="412" y="545.9482">Storage</text><!--MD5=[645d8587175d8f7ad110cd370775931d]
entity API--><rect fill="#F8F8F8" height="36.2969" style="stroke: #383838; stroke-width: 1.5;" width="105" x="693.5" y="84.9531"/><rect fill="#F8F8F8" height="5" style="stroke: #383838; stroke-width: 1.5;" width="10" x="688.5" y="89.9531"/><rect fill="#F8F8F8" height="5" style="stroke: #383838; stroke-width: 1.5;" width="10" x="688.5" y="111.25"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="85" x="703.5" y="107.9482">API (FastAPI)</text><!--MD5=[b16314add75d60042ff9f9e1effea895]
entity WORK--><rect fill="#F8F8F8" height="36.2969" style="stroke: #383838; stroke-width: 1.5;" width="152" x="665" y="357.9531"/><rect fill="#F8F8F8" height="5" style="stroke: #383838; stroke-width: 1.5;" width="10" x="660" y="362.9531"/><rect fill="#F8F8F8" height="5" style="stroke: #383838; stroke-width: 1.5;" width="10" x="660" y="384.25"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="132" x="675" y="380.9482">Worker (RQ/Celery)</text><!--MD5=[f96d690baea4a0823d36c98ba0c3a2b4]
entity Redis--><path d="M707.5,223.9531 L756.5,223.9531 C761.5,223.9531 761.5,237.1016 761.5,237.1016 C761.5,237.1016 761.5,250.25 756.5,250.25 L707.5,250.25 C702.5,250.25 702.5,237.1016 702.5,237.1016 C702.5,237.1016 702.5,223.9531 707.5,223.9531 " fill="#F8F8F8" style="stroke: #383838; stroke-width: 1.5;"/><path d="M756.5,223.9531 C751.5,223.9531 751.5,237.1016 751.5,237.1016 C751.5,250.25 756.5,250.25 756.5,250.25 " fill="none" style="stroke: #383838; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="39" x="707.5" y="241.9482">Redis</text><!--MD5=[e4d5dad924fa3ef69c8769110d64be61]
entity OpenAI-compatible endpoints--><rect fill="#F8F8F8" height="36.2969" style="stroke: #383838; stroke-width: 1.5;" width="225" x="1207.5" y="517.9531"/><rect fill="#F8F8F8" height="5" style="stroke: #383838; stroke-width: 1.5;" width="10" x="1202.5" y="522.9531"/><rect fill="#F8F8F8" height="5" style="stroke: #383838; stroke-width: 1.5;" width="10" x="1202.5" y="544.25"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="205" x="1217.5" y="540.9482">OpenAI-compatible endpoints</text><!--MD5=[fee662a292a0353a69bd162820bae93b]
entity User--><ellipse cx="57" cy="207.4531" fill="#F8F8F8" rx="8" ry="8" style="stroke: #383838; stroke-width: 1.5;"/><path d="M57,215.4531 L57,242.4531 M44,223.4531 L70,223.4531 M57,242.4531 L44,257.4531 M57,242.4531 L70,257.4531 " fill="none" style="stroke: #383838; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="32" x="41" y="272.9482">User</text><!--MD5=[c42ed81cef7fdd2e33758127945b9a5b]
entity UI--><polygon fill="#F8F8F8" points="6,362.9531,16,352.9531,108,352.9531,108,389.25,98,399.25,6,399.25,6,362.9531" style="stroke: #000000; stroke-width: 1.5;"/><line style="stroke: #000000; stroke-width: 1.5;" x1="98" x2="107" y1="362.9531" y2="353.9531"/><line style="stroke: #000000; stroke-width: 1.5;" x1="6" x2="98" y1="362.9531" y2="362.9531"/><line style="stroke: #000000; stroke-width: 1.5;" x1="98" x2="98" y1="362.9531" y2="399.25"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="62" x="21" y="385.9482">Web App</text><!--MD5=[2ba405fba669f6f7c1e341baf3e2371a]
entity Chroma--><path d="M889.5,523.4531 C889.5,513.4531 961,513.4531 961,513.4531 C961,513.4531 1032.5,513.4531 1032.5,523.4531 L1032.5,548.75 C1032.5,558.75 961,558.75 961,558.75 C961,558.75 889.5,558.75 889.5,548.75 L889.5,523.4531 " fill="#F8F8F8" style="stroke: #000000; stroke-width: 1.5;"/><path d="M889.5,523.4531 C889.5,533.4531 961,533.4531 961,533.4531 C961,533.4531 1032.5,533.4531 1032.5,523.4531 " fill="none" style="stroke: #000000; stroke-width: 1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="123" x="899.5" y="550.4482">Chroma (optional)</text><path d="M976.5,659.9531 L976.5,700.2188 L1367.5,700.2188 L1367.5,669.9531 L1357.5,659.9531 L976.5,659.9531 " fill="#ECECEC" style="stroke: #383838; stroke-width: 1.0;"/><path d="M1357.5,659.9531 L1357.5,669.9531 L1367.5,669.9531 L1357.5,659.9531 " fill="#ECECEC" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="370" x="982.5" y="677.02">Selection via $HOME/llms/config/local_sllm_endpoints.json</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="260" x="982.5" y="692.1528">Fallback to cloud provider if none healthy</text><path d="M1481.5,515.9531 L1481.5,556.2188 L1746.5,556.2188 L1746.5,525.9531 L1736.5,515.9531 L1481.5,515.9531 " fill="#ECECEC" style="stroke: #383838; stroke-width: 1.0;"/><path d="M1736.5,515.9531 L1736.5,525.9531 L1746.5,525.9531 L1736.5,515.9531 " fill="#ECECEC" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="244" x="1487.5" y="533.02">Edge issues signed URLs; Worker uses</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="174" x="1487.5" y="548.1528">them to read/write artifacts</text><!--MD5=[48af56c9a0e5a8a77220d910b4b4e159]
link User to UI--><path d="M57,276.1131 C57,298.8831 57,327.2631 57,347.7631 " fill="none" id="User-&gt;UI" style="stroke: #383838; stroke-width: 1.0;"/><polygon fill="#383838" points="57,352.8031,61,343.8031,57,347.8031,53,343.8031,57,352.8031" style="stroke: #383838; stroke-width: 1.0;"/><!--MD5=[db2d859f7477eee64c6be86476bfa545]
link UI to Edge--><path d="M66.86,398.9831 C73.37,412.8231 82.44,430.8631 92,445.9531 C103.86,464.6831 118.75,484.2831 131.66,500.3031 " fill="none" id="UI-&gt;Edge" style="stroke: #383838; stroke-width: 1.0;"/><polygon fill="#383838" points="134.9,504.3031,132.3502,494.79,131.7557,500.4156,126.1301,499.821,134.9,504.3031" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="41" x="93" y="442.02">HTTPS</text><!--MD5=[ee717b99967e7bf2a6d942cb726bac07]
link Edge to API--><path d="M175.75,504.0731 C187.63,481.4331 205.95,450.9331 228,428.9531 C377.37,280.0731 606.83,166.5531 702.14,123.1631 " fill="none" id="Edge-&gt;API" style="stroke: #383838; stroke-width: 1.0;"/><polygon fill="#383838" points="706.85,121.0231,697.0014,121.0919,702.2952,123.0857,700.3014,128.3795,706.85,121.0231" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="142" x="377" y="319.02">forward requests (JWT)</text><!--MD5=[bad684f33a5cdf7e5fd6e6f6bc804f0d]
link API to Redis--><path d="M744.17,121.2131 C741.48,146.5231 736.51,193.4531 733.83,218.7431 " fill="none" id="API-&gt;Redis" style="stroke: #383838; stroke-width: 1.0;"/><polygon fill="#383838" points="733.29,223.7931,738.2191,215.2665,733.8186,218.8211,730.264,214.4207,733.29,223.7931" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="86" x="741" y="164.02">enqueue jobs</text><!--MD5=[32f3f11f10d69bedf2a24e55429370ff]
link Redis to WORK--><path d="M732.8,250.1131 C734.31,273.0431 737.59,323.0131 739.51,352.3031 " fill="none" id="Redis-&gt;WORK" style="stroke: #383838; stroke-width: 1.0;"/><polygon fill="#383838" points="739.85,357.5231,743.2495,348.2796,739.5212,352.5339,735.2668,348.8056,739.85,357.5231" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="19" x="738" y="319.02">job</text><!--MD5=[22c9aac835a5fac6a02d6af5d800288f]
link WORK to Storage--><path d="M664.84,378.7531 C582.38,382.4931 458.64,393.9631 429,428.9531 C410.53,450.7531 419.66,484.6031 429.89,508.1631 " fill="none" id="WORK-&gt;Storage" style="stroke: #383838; stroke-width: 1.0;"/><polygon fill="#383838" points="431.95,512.7331,431.8929,502.8844,429.8928,508.1759,424.6013,506.1759,431.95,512.7331" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="255" x="430" y="442.02">download/upload artifacts (signed URLs)</text><!--MD5=[9f7fecbd35fa3ffc4f56c843ce342aef]
link WORK to PG--><path d="M664.98,379.3231 C531.63,384.2831 266.96,397.9431 239,428.9531 C233.94,434.5631 235.31,439.3531 239,445.9531 C242.31,451.8731 246.93,449.4431 252,453.9531 C267.3,467.5631 281.69,485.1531 292.94,500.4731 " fill="none" id="WORK-&gt;PG" style="stroke: #383838; stroke-width: 1.0;"/><polygon fill="#383838" points="296.03,504.7231,294.0058,495.0845,293.1036,500.669,287.5191,499.7668,296.03,504.7231" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="171" x="240" y="442.02">embeddings upsert/search</text><!--MD5=[532dffc26a0cc5092d49d22d717dca6d]
link WORK to APPDB--><path d="M729.57,393.9931 C719.69,408.3331 704.71,429.1331 690,445.9531 C623.7,521.8031 537.04,601.8531 488.07,645.5731 " fill="none" id="WORK-&gt;APPDB" style="stroke: #383838; stroke-width: 1.0;"/><polygon fill="#383838" points="484.32,648.9131,493.6977,645.9034,488.0499,645.5833,488.3701,639.9355,484.32,648.9131" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="140" x="681" y="540.52">job updates (optional)</text><!--MD5=[200dfd0907e049f3d810661e04f709c3]
link API to APPDB--><path d="M798.65,111.3331 C901.47,127.2331 1119,168.8031 1119,235.9531 C1119,235.9531 1119,235.9531 1119,536.9531 C1119,664.4931 647.89,677.9731 496.35,679.0131 " fill="none" id="API-&gt;APPDB" style="stroke: #383838; stroke-width: 1.0;"/><polygon fill="#383838" points="491.09,679.0431,500.1127,682.9916,496.0899,679.0146,500.067,674.9918,491.09,679.0431" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="119" x="1120" y="380.52">configs/status/logs</text><!--MD5=[108317dfcadeb43e1b2953f2cc6f9e72]
link Edge to APPDB--><path d="M207.5,567.6031 C221.84,576.5531 237.84,586.0431 253,593.9531 C303.3,620.2031 363.48,645.3031 403.98,661.3331 " fill="none" id="Edge-&gt;APPDB" style="stroke: #383838; stroke-width: 1.0;"/><polygon fill="#383838" points="408.82,663.2431,401.9306,656.205,404.1727,661.3985,398.9792,663.6406,408.82,663.2431" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="199" x="301" y="615.02">minimal request logs (optional)</text><!--MD5=[e6a84ff47b66674f979146a78c73c1e6]
link API to LLMs--><path d="M798.79,104.1331 C894.11,105.4231 1090.19,112.7931 1145,150.9531 C1249.38,223.6331 1309.4,306.5631 1248,417.9531 C1233.54,444.1931 1208.38,429.5531 1191,453.9531 C1188.4025,457.6006 1186.1555,461.5525 1184.2121,465.6815 C1183.7263,466.7138 1183.2594,467.7571 1182.8108,468.8096 C1182.6986,469.0727 1182.5876,469.3363 1182.4777,469.6005 " fill="none" id="API-&gt;LLMs" style="stroke: #383838; stroke-width: 1.0;"/><polygon fill="#383838" points="1182.4777,469.6005,1189.6271,462.8266,1184.3978,464.9839,1182.2405,459.7545,1182.4777,469.6005" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="122" x="1273" y="319.02">OpenAI /v1/* (chat)</text><!--MD5=[c9a06d7fa4d0b38473e611195e381b2c]
link WORK to LLMs--><path d="M817.03,388.4231 C881.07,399.8131 974.12,420.5031 1050,453.9531 C1079.315,466.8731 1109.3275,486.6981 1132.2488,503.5006 C1143.7094,511.9019 1153.3972,519.5475 1160.3386,525.1966 C1161.2063,525.9027 1162.031,526.5776 1162.811,527.2189 C1163.201,527.5396 1163.5797,527.8519 1163.9471,528.1554 " fill="none" id="WORK-&gt;LLMs" style="stroke: #383838; stroke-width: 1.0;"/><polygon fill="#383838" points="1163.9471,528.1554,1159.5573,519.3389,1160.0928,524.9704,1154.4613,525.5058,1163.9471,528.1554" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="144" x="1027" y="442.02">embeddings (optional)</text><!--MD5=[dbefef2edf4bbedc282d9231163c7a0c]
link WORK to Chroma--><path d="M742.42,394.1831 C744.74,410.3331 750.86,433.7531 767,445.9531 C783.9,458.7231 842.36,446.0031 862,453.9531 C891.91,466.0731 919.76,490.5231 938.26,509.4131 " fill="none" id="WORK-&gt;Chroma" style="stroke: #383838; stroke-width: 1.0;"/><polygon fill="#383838" points="941.83,513.1131,938.4438,503.8647,938.3522,509.5208,932.6961,509.4292,941.83,513.1131" style="stroke: #383838; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="138" x="768" y="442.02">local vector (optional)</text><!--MD5=[2009ea16f5e5761fe88465c1c645bbcc]
link LLMs to GMN29--><path d="M1172,578.3498 C1172,579.1176 1172,579.891 1172,580.6696 C1172,582.2268 1172,583.8046 1172,585.3994 C1172,610.9156 1172,640.7681 1172,659.7631 " fill="none" id="LLMs-GMN29" style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 7.0,7.0;"/><!--MD5=[560eb36187c5356d35b224ea7da5fbdb]
link Storage to GMN32--><path d="M485.77,512.7831 C532.88,488.1031 612.57,450.6031 686.5,435.9531 C767.7,419.8631 1351.31,415.4231 1431.5,435.9531 C1491.87,451.4131 1553.75,491.4431 1587.69,515.9131 " fill="none" id="Storage-GMN32" style="stroke: #383838; stroke-width: 1.0; stroke-dasharray: 7.0,7.0;"/><!--MD5=[c31ebefe003dcadf358c29c9d5bf2063]
@startuml AI Backend Topology
title AI Backend Topology
skinparam shadowing false
skinparam monochrome true

actor User
node "Web App" as UI

cloud "Supabase" as SUPA {
  node "Edge Functions\n(Deno)" as Edge
  database "Postgres\n(App DB)" as APPDB
  database "Postgres\n(pgvector)" as PG
  node "Storage" as Storage
}

node "AI Backend" as Backend {
  component "API (FastAPI)" as API
  component "Worker (RQ/Celery)" as WORK
  queue "Redis" as Redis
}

node "Local LLMs" as LLMs {
  [OpenAI-compatible endpoints]
}

database "Chroma (optional)" as Chroma

User - -> UI
UI - -> Edge : HTTPS
Edge - -> API : forward requests (JWT)
API - -> Redis : enqueue jobs
Redis - -> WORK : job

WORK - -> Storage : download/upload artifacts (signed URLs)
WORK - -> PG : embeddings upsert/search
WORK - -> APPDB : job updates (optional)

API - -> APPDB : configs/status/logs
Edge - -> APPDB : minimal request logs (optional)

API - -> LLMs : OpenAI /v1/* (chat)
WORK - -> LLMs : embeddings (optional)
WORK - -> Chroma : local vector (optional)

note bottom of LLMs
  Selection via $HOME/llms/config/local_sllm_endpoints.json
  Fallback to cloud provider if none healthy
end note

note right of Storage
  Edge issues signed URLs; Worker uses
  them to read/write artifacts
end note

@enduml

PlantUML version 1.2020.02(Sun Mar 01 10:22:07 UTC 2020)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Java Version: 17.0.16+8
Operating System: Linux
Default Encoding: UTF-8
Language: en
Country: null
--></g></svg>