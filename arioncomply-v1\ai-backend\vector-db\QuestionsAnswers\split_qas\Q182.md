id: Q182
query: >-
  What’s a “shared responsibility model” and how do we know what parts we’re responsible for?
packs:
  - "ISO27001:2022"
primary_ids:
  - "ISO27001:2022/A.14.2.7"
overlap_ids: []
capability_tags:
  - "Register"
  - "Draft Doc"
flags: []
sources:
  - title: "ISO/IEC 27001:2022 — Controls for Cloud Services"
    id: "ISO27001:2022/A.14.2.7"
    locator: "Annex A.14.2.7"
ui:
  cards_hint:
    - "Responsibility matrix template"
  actions:
    - type: "start_workflow"
      target: "shared_responsibility_setup"
      label: "Set Up Model"
    - type: "open_register"
      target: "responsibility_matrix"
      label: "View Matrix"
output_mode: "both"
graph_required: false
notes: "Clarifies which security and compliance duties fall to you vs the provider"
---
### 182) What’s a “shared responsibility model” and how do we know what parts we’re responsible for?

**Standard terms)**  
- **Shared responsibility:** delineation of security/compliance tasks between cloud provider and customer.

**Plain-English answer**  
In a shared model, **the provider** secures the physical datacenter, hardware, and hypervisor. **You** secure the guest OS, applications, data encryption, identity/access management, and legal safeguards (e.g., SCCs). Providers publish their responsibility matrices—compare theirs to your needs.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Annex A.14.2.7

**Why it matters**  
Prevents blind spots and ensures clear ownership of every control.

**Do next in our platform**  
- Run **Shared Responsibility Setup** workflow.  
- Customize the **Responsibility Matrix** for each service.

**How our platform will help**  
- **[Draft Doc]** Generates a service-specific matrix.  
- **[Register]** Tracks completion of customer tasks.

**Likely follow-ups**  
- How often should we review the matrix?  
- Where do we find each CSP’s published model?

**Sources**  
- ISO/IEC 27001:2022 Annex A.14.2.7  
