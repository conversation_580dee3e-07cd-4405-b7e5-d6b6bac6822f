id: Q158
query: >-
  What if different countries have conflicting requirements?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/4.1"
  - "GDPR:2016/Art.4"
overlap_ids: []
capability_tags:
  - "Report"
  - "Workflow"
flags:
  - "LOCAL LAW_CHECK"
sources:
  - title: "ISO/IEC 27001:2022 — Context"
    id: "ISO27001:2022/4.1"
    locator: "Clause 4.1"
  - title: "GDPR — Definitions"
    id: "GDPR:2016/Art.4"
    locator: "Article 4"
ui:
  cards_hint:
    - "Conflict resolution log"
  actions:
    - type: "open_register"
      target: "reg_conflicts"
      label: "Log Conflicts"
    - type: "start_workflow"
      target: "resolve_conflicts"
      label: "Resolve Regulatory Conflicts"
output_mode: "both"
graph_required: false
notes: "Document decision rationale and seek legal advice for overrides"
---
### 158) What if different countries have conflicting requirements?

**Standard terms**  
- **Context (ISO 27001 Cl.4.1):** understand internal/external drivers.  
- **Definitions (GDPR Art.4):** clarifies scope and terms.

**Plain-English answer**  
Log conflicts in a **Regulatory Conflicts Register**, perform a risk-based analysis, and adopt the stricter requirement or segment services by region. Document the rationale and consult local counsel where needed **[LOCAL LAW_CHECK]**.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 4.1; GDPR Article 4

**Why it matters**  
Ensures defensible decisions and audit trails.

**Do next in our platform**  
- Populate **Reg Conflicts** register.  
- Launch **Resolve Regulatory Conflicts** workflow.

**How our platform will help**  
- **[Register]** Tracks conflicts and resolutions.  
- **[Workflow]** Guides legal/ risk review and decision logging.

**Likely follow-ups**  
- “How do we document exceptions?” (Use exception request forms)

**Sources**  
- ISO/IEC 27001:2022 Clause 4.1; GDPR Article 4  
