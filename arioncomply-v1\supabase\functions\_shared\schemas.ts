// File: arioncomply-v1/supabase/functions/_shared/schemas.ts
// File Description: Minimal JSON Schemas and validator
// Purpose: Provide lightweight runtime validation for Edge handlers (MVP)
// Inputs: Data objects to validate; JSON schema-like definitions (subset)
// Outputs: { valid: boolean, errors?: string[] }
// Notes: Subset validator: type, required, properties; extend as needed

type JSONSchema = {
  type: 'object' | 'string' | 'number' | 'boolean' | 'array' | 'null';
  required?: string[];
  properties?: Record<string, JSONSchema>;
};

export const ConversationStartSchema: JSONSchema = {
  type: 'object',
  properties: {
    title: { type: 'string' },
    context: { type: 'object' },
  },
};

export const ConversationSendSchema: JSONSchema = {
  type: 'object',
  required: ['sessionId', 'message'],
  properties: {
    sessionId: { type: 'string' },
    message: { type: 'string' },
    hintContext: { type: 'object' },
  },
};

// Optional: Audit read query validation (kept minimal for flexibility)
export const AuditReadQuerySchema: JSONSchema = {
  type: 'object',
  properties: {
    page: { type: 'number' },
    pageSize: { type: 'number' },
    from: { type: 'string' },
    to: { type: 'string' },
    route: { type: 'string' },
    status: { type: 'number' },
    eventType: { type: 'string' },
  },
};

/**
 * Validate a value against a minimal JSONSchema subset.
 * Supports: type, required (object), and nested properties.
 */
export function validate(data: any, schema: JSONSchema): { valid: boolean; errors?: string[] } {
  const errors: string[] = [];
  function check(value: any, sch: JSONSchema, path = ''): void {
    // type
    const t = Array.isArray(value) ? 'array' : (value === null ? 'null' : typeof value);
    if (sch.type !== t && !(sch.type === 'object' && t === 'undefined')) {
      if (t !== 'undefined') errors.push(`${path || '$'}: expected ${sch.type}, got ${t}`);
      if (t === 'undefined' && sch.required && sch.required.length) errors.push(`${path || '$'}: missing required`);
      return;
    }
    if (sch.type === 'object' && value && sch.properties) {
      // required
      if (sch.required) {
        for (const k of sch.required) {
          if (!(k in value)) errors.push(`${path || '$'}.${k}: required`);
        }
      }
      for (const [k, sub] of Object.entries(sch.properties)) {
        if (k in value) check(value[k], sub, `${path || '$'}.${k}`);
      }
    }
  }
  check(data, schema);
  return { valid: errors.length === 0, errors: errors.length ? errors : undefined };
}
