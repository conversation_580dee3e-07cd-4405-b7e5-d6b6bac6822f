name: Render PlantUML diagrams

on:
  push:
    # Render on any branch when any .puml changes anywhere
    branches: [ '**' ]
    paths:
      - '**/*.puml'
      - '.github/workflows/plantuml-render.yml'
  workflow_dispatch:

permissions:
  contents: write

concurrency:
  group: plantuml-${{ github.ref }}
  cancel-in-progress: true

jobs:
  render:
    # Avoid infinite loop when committing SVGs
    if: ${{ github.actor != 'github-actions[bot]' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: List PUML files (repo-wide)
        id: list_puml
        shell: bash
        run: |
          set -euo pipefail
          echo "PUML files:" >&2
          cnt=0
          while IFS= read -r -d '' f; do echo " - $f"; cnt=$((cnt+1)); done < <(find . -type f -name '*.puml' -print0)
          echo "Found $cnt .puml files" >&2
          echo "count=$cnt" >> "$GITHUB_OUTPUT"

      - name: Install PlantUML and Graphviz
        if: ${{ steps.list_puml.outputs.count != '0' }}
        run: |
          sudo apt-get update
          sudo apt-get install -y default-jre-headless plantuml graphviz

      - name: Verify toolchain
        if: ${{ steps.list_puml.outputs.count != '0' }}
        run: |
          set -e
          echo "Java version:" && java -version || true
          echo "PlantUML version:" && plantuml -version || true
          echo "Graphviz dot version:" && dot -V || true
          echo "PlantUML testdot:" && plantuml -testdot || true

      - name: Render PlantUML to SVG (repo-wide)
        if: ${{ steps.list_puml.outputs.count != '0' }}
        shell: bash
        run: |
          set -euo pipefail
          echo "Rendering PUML files to SVG..."
          failed=0
          while IFS= read -r -d '' f; do
            echo "::group::plantuml $f"
            if ! plantuml -tsvg "$f"; then
              echo "::warning file=$f::PlantUML render failed"
              failed=1
            fi
            echo "::endgroup::"
          done < <(find . -type f -name '*.puml' -print0)
          # Do not fail the job on partial failures; artifacts/commits still useful for review
          if [[ "$failed" -ne 0 ]]; then
            echo "::warning::One or more PlantUML files failed to render; see logs above."
          fi

      - name: List generated SVGs
        if: ${{ steps.list_puml.outputs.count != '0' }}
        shell: bash
        run: |
          echo "Generated/updated SVG files:"
          find . -type f -name '*.svg' -print | sort || true

      - name: Show git status before commit
        shell: bash
        run: |
          echo "Git status (changes to commit):"
          git status -s || true

      - name: Commit rendered SVGs (push events)
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: '[skip ci] docs(diagrams): update PlantUML SVG renders (repo-wide)'
          # Commit any new/changed SVGs generated by this run
          # (omit file_pattern to include all modified files)
# File: .github/workflows/plantuml-render.yml
# File Description: CI workflow to render PlantUML diagrams
# Purpose: Generate PNG/SVG outputs on push for documentation
