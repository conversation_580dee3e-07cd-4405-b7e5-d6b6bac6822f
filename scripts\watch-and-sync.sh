#!/bin/bash
#
# File: scripts/watch-and-sync.sh
# File Description: Real-time file watcher for immediate GitHub synchronization
# Purpose: Monitor file system changes and trigger auto-sync within seconds
#
# Usage: ./scripts/watch-and-sync.sh
#
# Setup Instructions:
#   1. Install fswatch: brew install fswatch
#   2. Created by Claude Code for real-time sync (optional)
#   3. Made executable with: chmod +x scripts/watch-and-sync.sh
#   4. Run manually or as background process when needed
#
# Background execution options:
#   - Manual background: nohup ./scripts/watch-and-sync.sh > .git/watch.log 2>&1 &
#   - Add to shell profile for auto-start on terminal open
#   - Create macOS LaunchAgent for system-level service
#
# How it works:
#   - Uses fswatch to monitor file system changes
#   - Excludes git metadata, node_modules, and system files
#   - Debounces changes for 10 seconds to avoid rapid commits
#   - Calls auto-sync.sh when changes are detected
#   - Provides real-time sync (within seconds vs 15-minute cron)
#
# When to use:
#   - Optional third layer of sync (post-commit + cron already provide good coverage)
#   - Useful for immediate sync needs or when working intensively
#   - Can be started/stopped as needed per work session
#   - Most users won't need this - cron + post-commit is sufficient
#
# Dependencies: fswatch (brew install fswatch), auto-sync.sh script
# Security/RLS: Uses existing git credentials through auto-sync.sh
# Notes: Part of comprehensive 3-tier sync strategy (optional real-time layer)
#

cd "$(dirname "$0")/.." || exit 1

if ! command -v fswatch >/dev/null 2>&1; then
    echo "fswatch not found. Install with: brew install fswatch"
    echo "This script provides real-time file watching (optional)"
    echo "Your cron + post-commit hook setup already provides good sync coverage"
    exit 1
fi

echo "Watching for file changes in $(pwd)..."
echo "Real-time sync active (complements cron + post-commit hooks)"
echo "Press Ctrl+C to stop"

# Watch for changes, debounce for 10 seconds to avoid rapid commits
fswatch -o . \
    --exclude='.git/' \
    --exclude='node_modules/' \
    --exclude='.DS_Store' \
    --exclude='*.log' \
    --latency=10 | while read -r; do
    
    echo "$(date): Changes detected, syncing..."
    ./scripts/auto-sync.sh
done