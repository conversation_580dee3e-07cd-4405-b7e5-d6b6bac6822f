id: Q178
query: >-
  Do we need special security controls for AI systems vs. regular software?
packs:
  - "EUAI:2024"
  - "ISO27001:2022"
primary_ids:
  - "EUAI:2024/Art.15"
  - "ISO27001:2022/A.14.2.8"
overlap_ids:
  - "ISO27002:2022/14.2"
capability_tags:
  - "Register"
  - "Draft Doc"
flags: []
sources:
  - title: "EU AI Act — Cybersecurity Requirements"
    id: "EUAI:2024/Art.15"
    locator: "Article 15"
  - title: "ISO/IEC 27001:2022 — System Testing"
    id: "ISO27001:2022/A.14.2.8"
    locator: "Annex A 14.2.8"
ui:
  cards_hint:
    - "AI security control library"
  actions:
    - type: "open_register"
      target: "security_controls"
      label: "View Controls"
    - type: "start_workflow"
      target: "ai_security_review"
      label: "Review AI Security"
output_mode: "both"
graph_required: false
notes: "Map AI-specific threats to existing controls and add new tests"
---
### 178) Do we need special security controls for AI systems vs. regular software?

**Standard term(s)**  
- **Cybersecurity requirements (EU AI Act Art. 15):** mandates that high-risk AI systems be resilient against attempts to alter their use, outputs, or performance, including attacks on data and models.  
- **System testing (ISO 27001 A.14.2.8):** requires security functionality to be tested during development and before release.

**Plain-English answer**  
Yes — while many security practices for AI overlap with standard software security, AI introduces unique risks such as adversarial inputs, model extraction, and data poisoning. The EU AI Act requires high-risk AI to have robust protections against these threats. ISO 27001 complements this with secure development, testing, and validation processes. Together, they mean your security controls must cover both traditional vulnerabilities and AI-specific attack vectors.

**Applies to**  
- **Primary:** EU AI Act Article 15; ISO/IEC 27001:2022 Annex A.14.2.8  
- **Also relevant/Overlaps:** ISO/IEC 27002:2022 Clause 14.2

**Why it matters**  
Failing to address AI-specific risks can leave systems vulnerable, even if they meet general software security standards.

**Do next in our platform**  
- Review the **Security Controls Register** to ensure AI-specific threats are covered.  
- Launch the **AI Security Review** workflow to map and test relevant controls.

**How our platform will help**  
- **[Register]** Maintain a library of AI and non-AI security controls.  
- **[Draft Doc]** Generate AI-specific security test plans and procedures.

**Likely follow-ups**  
- “What are examples of AI-specific attacks we should test for?” (e.g., data poisoning, adversarial examples, model inversion)

**Sources**  
- EU AI Act Article 15  
- ISO/IEC 27001:2022 Annex A.14.2.8  
- ISO/IEC 27002:2022 Clause 14.2
