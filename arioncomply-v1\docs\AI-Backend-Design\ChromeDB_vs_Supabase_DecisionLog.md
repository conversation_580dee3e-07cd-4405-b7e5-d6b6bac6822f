# ChromeDB vs Supabase VectorDB – Decision Log (Placeholder)

Decision Criteria
- Performance: latency, throughput under target workloads.
- Security: RLS/tenant isolation, encryption, access control.
- Operations: provisioning, backups, monitoring, migration tooling.
- Cost: infra + ops; predictable scaling.

Open Questions
- Benchmarks and datasets; operational complexity; vendor lock-in.

Next Steps
- Define benchmark plan and run small POC on both options.
- Keep vector client abstract to allow swapping.

