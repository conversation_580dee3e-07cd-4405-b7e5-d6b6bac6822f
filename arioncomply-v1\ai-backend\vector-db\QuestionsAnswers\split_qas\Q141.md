id: Q141
query: >-
  What if we discover problems during our preparation — should we fix them or report them?
packs:
  - "ISO27001:2022"
  - "GDPR:2016"
primary_ids:
  - "ISO27001:2022/10.2"
  - "GDPR:2016/Art.33"
overlap_ids: []
capability_tags:
  - "Workflow"
  - "Report"
flags:
  - "LOCAL LAW CHECK"
sources:
  - title: "ISO/IEC 27001:2022 — Corrective Action"
    id: "ISO27001:2022/10.2"
    locator: "Clause 10.2"
  - title: "GDPR — Notification of Personal Data Breach"
    id: "GDPR:2016/Art.33"
    locator: "Article 33"
ui:
  cards_hint:
    - "Issue management guide"
  actions:
    - type: "start_workflow"
      target: "corrective_action"
      label: "Initiate Corrective Action"
output_mode: "both"
graph_required: false
notes: "Fix internally; report only if it constitutes a notifiable breach"
---
### 141) What if we discover problems during our preparation — should we fix them or report them?

**Standard terms)**  
- **Corrective action (ISO 27001 Cl.10.2):** internal process to address nonconformities.  
- **Breach notification (GDPR Art.33):** obligation if a security incident affects personal data.

**Plain-English answer**  
For internal gaps, run corrective actions—no reporting needed. If the issue is a personal data breach under GDPR or an ICT incident under NIS2, follow the notification timelines. Otherwise, document fixes and move on.

**Applies to**  
- **Primary:** ISO/IEC 27001:2022 Clause 10.2; GDPR Article 33

**Why it matters**  
Ensures you meet both internal improvement and legal obligations.

**Do next in our platform**  
- Start the **Corrective Action** workflow.  
- Tag issues in the **Non-Compliance Log**.

**How our platform will help**  
- **[Workflow]** Guides root-cause analysis and fixes.  
- **[Report]** Flags reportable breaches and deadlines.

**Likely follow-ups**  
- “What qualifies as a GDPR breach?” (Consult breach criteria guide **[LOCAL LAW CHECK]**)

**Sources**  
- ISO/IEC 27001:2022 Clause 10.2; GDPR Article 33  
