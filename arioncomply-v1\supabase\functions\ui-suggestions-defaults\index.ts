// File: arioncomply-v1/supabase/functions/ui-suggestions-defaults/index.ts
// File Description: Returns default suggested follow-ups for the chat UI
// Purpose: Provide an educational, platform/standards-oriented default set
// Input: GET with Authorization header (Bearer token)
// Output: JSON { suggestions: [{ text, action?: { type, target, url? } }] }

// @ts-ignore
import { serve } from "https://deno.land/std@0.224.0/http/server.ts";
// @ts-ignore
import { corsHeaders } from "../_shared/cors.ts";

type Suggestion = { text: string; action?: { type: string; target: string; url?: string } };

const DEFAULTS: Suggestion[] = [
  { text: "Start an assessment", action: { type: "nav", target: "assessment" } },
  { text: "What is ISO 27001 and where do I start?", action: { type: "nav", target: "standards/iso27001" } },
  { text: "Show recommended next steps for my organization" },
  { text: "Explore compliance registers", action: { type: "nav", target: "registers" } },
  // Secondary items (client shows only the first 4, but we keep extras for rotation/future use)
  { text: "Show related documents", action: { type: "nav", target: "documents" } },
  { text: "Explain your reasoning" },
  { text: "Summarize the key points" },
  { text: "What is GDPR (overview)?", action: { type: "nav", target: "standards/gdpr" } },
];

serve(async (req: Request) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  if (req.method !== "GET") {
    return new Response(JSON.stringify({ error: "Only GET allowed." }), {
      status: 405,
      headers: { ...corsHeaders, "content-type": "application/json" },
    });
  }

  const auth = req.headers.get("Authorization");
  if (!auth || !/^Bearer\s+.+/i.test(auth)) {
    return new Response(JSON.stringify({ error: "Missing or invalid Authorization header." }), {
      status: 401,
      headers: { ...corsHeaders, "content-type": "application/json" },
    });
  }

  return new Response(JSON.stringify({ suggestions: DEFAULTS }), {
    status: 200,
    headers: { ...corsHeaders, "content-type": "application/json" },
  });
});

