-- File: arioncomply-v1/ai-backend/supabase_migrations/vector/0001_vector_schema.sql
-- File Description: Migration 0001 for vector project schema with org RLS
-- Purpose: Create documents, chunks, embeddings, and match function for vector search
-- Security/RLS: ENABLE RLS; org_id required; policies use app_current_org_id() / app_has_role('admin')
-- Notes: Adjust vector dimension if using a different embedding model than 768-dim

BEGIN;

-- Extensions
CREATE EXTENSION IF NOT EXISTS pgcrypto;
CREATE EXTENSION IF NOT EXISTS vector;

-- Documents
CREATE TABLE IF NOT EXISTS documents (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL,
  title text,
  source text,
  version text,
  hash text,
  metadata jsonb,
  created_at timestamptz NOT NULL DEFAULT now()
);

-- Chunks
CREATE TABLE IF NOT EXISTS chunks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL,
  doc_id uuid NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
  seq int NOT NULL,
  text text NOT NULL,
  tokens int,
  metadata jsonb,
  created_at timestamptz NOT NULL DEFAULT now()
);
CREATE INDEX IF NOT EXISTS idx_chunks_doc ON chunks (doc_id);

-- Embeddings (default 768-dim)
CREATE TABLE IF NOT EXISTS embeddings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id uuid NOT NULL,
  chunk_id uuid NOT NULL REFERENCES chunks(id) ON DELETE CASCADE,
  model text NOT NULL,
  dim int NOT NULL,
  embedding vector(768) NOT NULL,
  created_at timestamptz NOT NULL DEFAULT now()
);
CREATE INDEX IF NOT EXISTS idx_embeddings_chunk ON embeddings (chunk_id);
-- ivfflat index (tune lists per dataset size)
CREATE INDEX IF NOT EXISTS idx_embeddings_vec ON embeddings USING ivfflat (embedding vector_l2_ops) WITH (lists = 100);

-- RLS
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE chunks ENABLE ROW LEVEL SECURITY;
ALTER TABLE embeddings ENABLE ROW LEVEL SECURITY;

CREATE POLICY documents_all ON documents FOR ALL USING (org_id = app_current_org_id() OR app_has_role('admin')) WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY chunks_all ON chunks FOR ALL USING (org_id = app_current_org_id() OR app_has_role('admin')) WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));
CREATE POLICY embeddings_all ON embeddings FOR ALL USING (org_id = app_current_org_id() OR app_has_role('admin')) WITH CHECK (org_id = app_current_org_id() OR app_has_role('admin'));

-- Search function: match_chunks
-- Input: org uuid, query vector(768), limit int, min_score float (optional)
-- Output: chunk_id, doc_id, score (lower is better for L2), text, metadata
CREATE OR REPLACE FUNCTION match_chunks(p_org uuid, p_query vector(768), p_limit int, p_min_score float DEFAULT NULL)
RETURNS TABLE(
  chunk_id uuid,
  doc_id uuid,
  score float,
  text text,
  metadata jsonb
) LANGUAGE sql STABLE AS $$
  WITH ranked AS (
    SELECT e.chunk_id,
           c.doc_id,
           (e.embedding <-> p_query) AS score,
           c.text,
           c.metadata
    FROM embeddings e
    JOIN chunks c ON c.id = e.chunk_id
    WHERE e.org_id = p_org AND c.org_id = p_org
    ORDER BY e.embedding <-> p_query
    LIMIT p_limit
  )
  SELECT r.chunk_id, r.doc_id, r.score, r.text, r.metadata
  FROM ranked r
  WHERE p_min_score IS NULL OR r.score <= p_min_score
$$;

COMMIT;
