id: Q160
query: >-
  What's the difference between where data is stored vs. where it's processed vs. where the company is located?
packs:
  - "GDPR:2016"
primary_ids:
  - "GDPR:2016/Art.3"
overlap_ids:
  - "ISO27701:2019/6.2.1"
capability_tags:
  - "Register"
  - "Report"
flags: []
sources:
  - title: "GDPR — Territorial Scope"
    id: "GDPR:2016/Art.3"
    locator: "Article 3"
  - title: "ISO/IEC 27701:2019 — Determining PII jurisdiction"
    id: "ISO27701:2019/6.2.1"
    locator: "Clause 6.2.1"
ui:
  cards_hint:
    - "Jurisdiction mapping"
  actions:
    - type: "open_register"
      target: "data_locations"
      label: "View Data Location Register"
    - type: "report"
      target: "jurisdiction_analysis"
      label: "Generate Jurisdiction Report"
output_mode: "both"
graph_required: false
notes: "Clarifies concepts often confused in compliance: storage, processing, and corporate domicile."
---
### 160) What's the difference between where data is stored vs. where it's processed vs. where the company is located?

**Standard term(s)**  
- **Data storage location:** the physical or cloud region where data resides at rest.  
- **Data processing location:** where data is actively accessed, used, or modified.  
- **Company location (domicile):** the legal jurisdiction where the entity is registered.

**Plain-English answer**  
Where data is **stored** refers to the physical location of servers or cloud regions holding the data. Where it is **processed** refers to the location(s) from which systems or people access and handle the data — this can differ from the storage site. Where the **company is located** is the legal base of the business, which may be in yet another jurisdiction. Under GDPR and other laws, obligations can apply based on any of these factors.

**Applies to**  
- **Primary:** GDPR Article 3  
- **Also relevant/Overlaps:** ISO/IEC 27701:2019 Clause 6.2.1

**Why it matters**  
Jurisdiction determines applicable privacy laws, cross-border transfer rules, and regulator authority.

**Do next in our platform**  
- Review the **Data Location Register** for storage and processing locations.  
- Generate a **Jurisdiction Analysis** report to identify applicable laws.

**How our platform will help**  
- **[Register]** Maintain authoritative records of storage and processing sites.  
- **[Report]** Map applicable regulations to each data location.

**Likely follow-ups**  
- “If our company is in the US but processes EU data, does GDPR apply?” (Yes — see GDPR Art. 3) [LOCAL LAW CHECK]

**Sources**  
- GDPR Article 3  
- ISO/IEC 27701:2019 Clause 6.2.1